# START OF FILE trade_logic.py

import csv
import json
import os
import math
import logging
import time
import threading
from contextlib import contextmanager
from datetime import datetime, UTC, timedelta, date
from decimal import Decimal, InvalidOperation, ROUND_DOWN, ROUND_HALF_UP

from config import (
    MIN_ORDER_VALUE_USD_FLOOR, 
    MAX_TRADE_VALUE_USD, 
    MAX_SCALP_TRADE_VALUE_USD,
    MIN_TRADE_QTY_BTC, 
    CASH_RESERVE_USD, 
    MIN_SELL_QTY_THRESHOLD,
    MIN_PROFIT_THRESHOLD_USD, 
    MAX_AI_LOSS_EXIT_PCT, 
    STOP_PERCENT,
    DUST_THRESHOLD_QTY, 
    COOLDOWN_SELL_AFTER_BUY,
    COOLDOWN_BUY_AFTER_SELL,
    FEE_PERCENT, 
    ENABLE_GUARDIAN_VETO, 
    GUARDIAN_RSI_OVERBOUGHT,
    GU<PERSON><PERSON><PERSON>_RSI_OVERSOLD, 
    MIN_QTY_PCT_CONFIG, 
    TRAIL_PROFIT_BUFFER_PCT,
    REFERENCE_ATR, 
    GRANULAR_TAKE_PROFIT_PCT,
    DIP_BUY_PROFIT_TARGET_PCT, 
    MOMENTUM_BUY_NET_PROFIT_TARGET_PCT
) 
import config

import alpaca_service
from email_alert import send_alert
from bot_state import load_bot_state, save_bot_state
import order_tracker_service

logger = logging.getLogger("TradingBotApp.TradeLogic")
debug_logger = logging.getLogger("TradingBotApp.Debug")

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
LOTS_LEDGER_FILE = os.path.join(BASE_DIR, "open_lots.json")

_open_lots_lock = threading.RLock()
open_lots = []

@contextmanager
def lots():
    with _open_lots_lock:
        yield

def atr_adjusted_value(base_value: Decimal, atr: Decimal, reference_atr: Decimal = config.REFERENCE_ATR) -> Decimal:
    if atr <= 0: return base_value
    factor = Decimal(str(reference_atr)) / max(Decimal(str(atr)), Decimal("1e-8"))
    adjusted = Decimal(str(base_value)) * factor
    logger.info(f"ATR-Sizing: Base=${base_value:.2f}, ATR={atr:.5f}, Factor=%.4f, Adjusted=${adjusted:.2f}", factor)
    return adjusted

def _load_json_lots(path: str, default_value: list) -> list[dict]:
    with lots():
        if not os.path.exists(path) or os.path.getsize(path) == 0:
            logger.warning(f"Lot ledger file {path} not found or is empty. Returning default.")
            return default_value
        try:
            with open(path, 'r') as f: data = json.load(f)
            if not isinstance(data, list): return default_value
            
            processed_lots = []
            for item in data:
                if not isinstance(item, dict):
                    logger.warning(f"Skipping non-dictionary item in lots file: {item}")
                    continue
                
                lot = {}
                lot['lot_id'] = str(item.get('lot_id', item.get('order_id', f"MISSING_LOT_ID_{datetime.now(UTC).timestamp()}")))
                lot['buy_order_id'] = str(item.get('buy_order_id', lot['lot_id']))
                
                buy_timestamp_str = item.get('buy_timestamp') or item.get('last_buy_timestamp')
                lot['buy_timestamp'] = datetime.fromisoformat(str(buy_timestamp_str)).astimezone(UTC) if buy_timestamp_str else None

                lot['original_qty'] = Decimal(str(item.get('original_qty', item.get('qty', '0'))))
                lot['remaining_qty'] = Decimal(str(item.get('remaining_qty', item.get('qty', '0'))))
                lot['buy_price'] = Decimal(str(item.get('buy_price', item.get('entry_price', '0'))))
                lot['buy_fee_usd'] = Decimal(str(item.get('buy_fee_usd', '0')))
                
                cost_basis_str = item.get('cost_basis_per_unit')
                if lot['original_qty'] > 0 and lot['buy_price'] > 0 and cost_basis_str is None:
                    lot['cost_basis_per_unit'] = ((lot['buy_price'] * lot['original_qty']) + lot['buy_fee_usd']) / lot['original_qty']
                else:
                    lot['cost_basis_per_unit'] = Decimal(str(cost_basis_str or '0'))

                lot['initial_stop'] = Decimal(str(item.get('initial_stop', item.get('stop', '0'))))
                lot['current_stop'] = Decimal(str(item.get('current_stop', item.get('stop', lot.get('initial_stop','0')))))
                
                lot['type'] = str(item.get('type', "unknown_lot"))
                
                lot['strategy_type'] = str(item.get('strategy_type', 'MOMENTUM_BUY')) 

                raw_loaded_tp = item.get('take_profit_price')
                needs_recalculation = False
                try:
                    loaded_tp_dec = Decimal(str(raw_loaded_tp))
                    if loaded_tp_dec == Decimal("0"):
                        needs_recalculation = True
                except (InvalidOperation, TypeError):
                    needs_recalculation = True

                if needs_recalculation:
                    base_price_for_tp = lot['cost_basis_per_unit'] if lot['cost_basis_per_unit'] > 0 else lot['buy_price']
                    
                    if base_price_for_tp > 0:
                        if lot['strategy_type'] == "MOMENTUM_BUY" or lot['strategy_type'] == "SYNTHESIZED": 
                            effective_profit_multiplier = (Decimal("1") + config.MOMENTUM_BUY_NET_PROFIT_TARGET_PCT) / (Decimal("1") - config.FEE_PERCENT)
                            lot['take_profit_price'] = (base_price_for_tp * effective_profit_multiplier).quantize(Decimal("0.0001"))
                            logger.warning(f"Lot {lot['lot_id']} (Type: {lot['strategy_type']}) had missing/zero TP. Defaulting to {config.MOMENTUM_BUY_NET_PROFIT_TARGET_PCT*100:.2f}% NET profit: ${lot['take_profit_price']:.2f}.")
                        else: # DIP_ACCUMULATION or other types
                            lot['take_profit_price'] = (base_price_for_tp * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))
                            logger.warning(f"Lot {lot['lot_id']} (Type: {lot['strategy_type']}) had missing/zero TP. Defaulting to {config.DIP_BUY_PROFIT_TARGET_PCT*100:.2f}% GROSS profit: ${lot['take_profit_price']:.2f}.")
                    else:
                        lot['take_profit_price'] = Decimal("0")
                else:
                    lot['take_profit_price'] = loaded_tp_dec

                lot['original_order_ids'] = item.get('original_order_ids', [])
                if not isinstance(lot['original_order_ids'], list): lot['original_order_ids'] = []
                
                processed_lots.append(lot)
            return processed_lots
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON from {path}. The file is likely corrupted. Returning default.")
            return default_value
        except Exception as e: 
            logger.warning(f"Error loading JSON {path}: {e}")
            return default_value

def _save_json_lots(path: str, data: list[dict]):
    with lots():
        tmp_path = path + ".tmp"
        try:
            with open(tmp_path, "w") as f:
                serializable_data = [{k: str(v) if isinstance(v, Decimal) else (v.isoformat() if isinstance(v, datetime) else v) for k, v in item.items()} for item in data]
                json.dump(serializable_data, f, indent=2)
            os.replace(tmp_path, path)
        except Exception as e:
            logger.error(f"Error saving JSON {path}: {e}")

def calculate_fees(qty: Decimal, price: Decimal) -> Decimal:
    return (Decimal(str(qty)) * Decimal(str(price)) * FEE_PERCENT).quantize(Decimal("0.********"), rounding=ROUND_HALF_UP)

def log_trade_cycle(trade_cycle_log_buffer: list, context: dict):
    log_data = context.copy()
    if 'open_lots' in log_data and isinstance(log_data['open_lots'], list):
        log_data['open_lots'] = json.dumps([
            {k: str(v) if isinstance(v, Decimal) else v.isoformat() if isinstance(v, datetime) else v for k, v in lot.items()}
            for lot in log_data['open_lots']
        ])
    trade_cycle_log_buffer.append(log_data)

def log_trade_to_csv(trade_history_buffer: list, ts: str, action: str, qty: Decimal, price: Decimal, order_id: str, status: str, realized_pl: Decimal, fee: Decimal, exploratory: bool = False, entry_price: Decimal = None, lot_id: str = None, strategy_type: str = None):
    header = ["timestamp", "action", "quantity", "price", "order_id", "status", "realized_pl", "fee", "exploratory", "entry_price", "lot_id", "strategy_type"]
    row_data = {
        "timestamp": ts, "action": action, "quantity": str(qty), "price": str(price),
        "order_id": order_id, "status": status, "realized_pl": float(realized_pl),
        "fee": float(fee), "exploratory": str(exploratory), "entry_price": str(entry_price or ''),
        "lot_id": str(lot_id or ''), "strategy_type": str(strategy_type or '')
    }
    trade_history_buffer.append(row_data)

def get_trade_history_for_dynamic_params() -> list[dict]:
    if not os.path.exists(TRADE_HISTORY_FILE): return []
    try:
        with open(TRADE_HISTORY_FILE, 'r', newline='') as f:
            reader = csv.DictReader(f)
            return [{
                **row,
                'quantity': Decimal(row.get('quantity', '0')),
                'price': Decimal(row.get('price', '0')),
                'realized_pl': Decimal(row.get('realized_pl', '0')),
                'fee': Decimal(row.get('fee', '0')),
                'entry_price': Decimal(row.get('entry_price', '0')) if row.get('entry_price') else None,
                'strategy_type': row.get('strategy_type')
            } for row in reader]
    except Exception as e:
        logger.error(f"Error reading trade history: {e}")
        return []

def _update_session_stats_for_sell(session_stats: dict, pnl_this_trade: Decimal, order_id: str, action_reason: str):
    session_stats["net_pl"] = session_stats.get("net_pl", Decimal("0.0")) + pnl_this_trade
    if pnl_this_trade > 0:
        session_stats["win_count"] += 1
        session_stats["win_streak"] += 1
        session_stats["loss_streak"] = 0
    else:
        session_stats["loss_count"] += 1
        if session_stats.get("last_action_was_loss", False):
            session_stats["loss_streak"] += 1
        else:
            session_stats["loss_streak"] = 1
        session_stats["win_streak"] = 0
    session_stats["last_action_was_loss"] = (pnl_this_trade < 0)
    logger.info(f"Order {order_id} ({action_reason}) confirmed FILLED. Updated session stats with PnL: {pnl_this_trade:.6f}.")


def load_open_lots() -> list[dict]:
    global open_lots
    with lots():
        open_lots = _load_json_lots(LOTS_LEDGER_FILE, [])
    logger.info(f"Loaded {len(open_lots)} lots from {LOTS_LEDGER_FILE}.")
    return open_lots

def save_open_lots():
    with lots():
        _save_json_lots(LOTS_LEDGER_FILE, open_lots)

def prune_lots_to_available(api, pos: dict, ind: dict):
    global open_lots
    with lots():
        debug_logger.debug(f"Executing lot quantity check. Initial in-memory lots: {len(open_lots)}")
        try:
            api_total_qty = Decimal(pos.get("qty", "0.0")) if pos else Decimal("0.0")
            bot_total_qty = sum(l.get("remaining_qty", Decimal("0")) for l in open_lots)
            
            logger.info(f"[Lot Sync] Exchange reports TOTAL position quantity of: {api_total_qty:.8f} BTC")
            logger.info(f"[Lot Sync] Bot's internal TOTAL remaining quantity: {bot_total_qty:.8f} BTC from {len(open_lots)} lot(s).")
            
            discrepancy = api_total_qty - bot_total_qty # Positive if Alpaca has more, negative if bot has more

            if api_total_qty <= config.DUST_THRESHOLD_QTY and bot_total_qty > config.DUST_THRESHOLD_QTY:
                logger.warning(f"[Lot Sync] Exchange position is effectively zero ({api_total_qty:.8f} BTC), but bot has {bot_total_qty:.8f} BTC in lots. Wiping internal lots to match exchange.")
                open_lots.clear()
                save_open_lots() # Save immediately after wiping
                send_alert(subject="AI Bot: Lot Sync - Bot Wiping Lots", message=f"Alpaca position {api_total_qty:.8f} is effectively zero. Bot had {bot_total_qty:.8f} in lots; wiping internal ledger to match.")

            elif abs(discrepancy) <= config.DUST_THRESHOLD_QTY:
                logger.info(f"[Lot Sync] Discrepancy ({discrepancy:.8f} BTC) is within dust threshold ({config.DUST_THRESHOLD_QTY:.8f}). Assuming sync for now.")
                return

            elif api_total_qty > config.MIN_SELL_QTY_THRESHOLD and bot_total_qty <= config.DUST_THRESHOLD_QTY:
                logger.critical("[Lot Sync] STATE DISCREPANCY RECOVERY: Exchange has a position but bot has none (or dust). Synthesizing lot.")
                avg_entry_price = Decimal(pos.get('avg_entry_price', '0.0'))
                
                if avg_entry_price <= 0:
                    logger.error(f"[Lot Sync] CRITICAL: Cannot synthesize lot: Alpaca reports invalid avg_entry_price ({avg_entry_price:.2f}). Manual review needed for position {api_total_qty:.8f} BTC.")
                    send_alert(subject="AI Bot: Lot Sync ERROR - Invalid Alpaca Entry Price", message=f"Cannot synthesize lot for {api_total_qty:.8f} BTC: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual intervention required to establish cost basis.")
                    return

                # Use the oldest known lot's timestamp if available, otherwise now.
                base_timestamp = min(l['buy_timestamp'] for l in open_lots) if open_lots else datetime.now(UTC)

                synthetic_lot_id = f"synthesized_recovery_{datetime.now(UTC).timestamp()}"
                initial_stop_calc = (avg_entry_price * (Decimal("1") - config.STOP_PERCENT)).quantize(Decimal("0.0001"))
                take_profit_calc = (avg_entry_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))

                synthetic_lot = {
                    "lot_id": synthetic_lot_id, "buy_order_id": "synthesized",
                    "buy_timestamp": base_timestamp,
                    "original_qty": api_total_qty,
                    "remaining_qty": api_total_qty,
                    "buy_price": avg_entry_price,
                    "buy_fee_usd": Decimal("0.0"),
                    "cost_basis_per_unit": avg_entry_price,
                    "initial_stop": initial_stop_calc,
                    "current_stop": initial_stop_calc, 
                    "take_profit_price": take_profit_calc,
                    "type": "synthesized_recovery",
                    "strategy_type": "SYNTHESIZED",
                    "original_order_ids": []
                }
                open_lots.append(synthetic_lot)
                save_open_lots() # Save immediately after synthesizing
                logger.warning(f"[Lot Sync] Successfully synthesized lot {synthetic_lot_id}: Qty {api_total_qty:.8f} @ avg_entry ${avg_entry_price:.2f}. Please verify on Alpaca.")
                send_alert(subject="AI Bot: Synthesized Lot Recovery", message=f"Bot found discrepancy (Alpaca has position, bot has none). Synthesized lot {synthetic_lot_id} for {api_total_qty:.8f} @ ${avg_entry_price:.2f}.")

            elif discrepancy > config.DUST_THRESHOLD_QTY:
                logger.critical(f"CRITICAL: Exchange has MORE BTC ({api_total_qty:.8f}) than bot thinks ({bot_total_qty:.8f}). Discrepancy: {discrepancy:.8f} BTC. Attempting to add synthetic lot for the excess.")
                
                avg_entry_price = Decimal(pos.get('avg_entry_price', '0.0'))
                if avg_entry_price <= 0:
                    logger.error(f"[Lot Sync] CRITICAL: Cannot synthesize excess lot for {discrepancy:.8f} BTC: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual review needed.")
                    send_alert(subject="AI Bot: Lot Sync ERROR - Invalid Alpaca Entry Price for Excess", message=f"Cannot synthesize lot for {discrepancy:.8f} BTC excess: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual intervention required to establish cost basis.")
                    return

                base_timestamp = min(l['buy_timestamp'] for l in open_lots) if open_lots else datetime.now(UTC)

                excess_lot_id = f"synthesized_excess_{datetime.now(UTC).timestamp()}"
                initial_stop_calc = (avg_entry_price * (Decimal("1") - config.STOP_PERCENT)).quantize(Decimal("0.0001"))
                take_profit_calc = (avg_entry_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))

                synthetic_excess_lot = {
                    "lot_id": excess_lot_id, "buy_order_id": "synthesized_excess",
                    "buy_timestamp": base_timestamp,
                    "original_qty": discrepancy,
                    "remaining_qty": discrepancy,
                    "buy_price": avg_entry_price,
                    "buy_fee_usd": Decimal("0.0"),
                    "cost_basis_per_unit": avg_entry_price,
                    "initial_stop": initial_stop_calc,
                    "current_stop": initial_stop_calc,
                    "take_profit_price": take_profit_calc,
                    "type": "synthesized_excess_recovery",
                    "strategy_type": "SYNTHESIZED",
                    "original_order_ids": []
                }
                open_lots.append(synthetic_excess_lot)
                save_open_lots() # Save immediately after synthesizing
                logger.critical(f"[Lot Sync] Synthesized new lot {excess_lot_id} for {discrepancy:.8f} BTC excess at Alpaca's average price ${avg_entry_price:.2f}.")
                send_alert(subject="AI Bot: Lot Sync - Excess BTC Found", message=f"Alpaca has {discrepancy:.8f} BTC more than bot. Synthesized lot {excess_lot_id} at ${avg_entry_price:.2f}. Manual check advised.")

            elif discrepancy < -config.DUST_THRESHOLD_QTY:
                abs_discrepancy = abs(discrepancy)
                error_msg = f"CRITICAL: Bot thinks it has MORE BTC ({bot_total_qty:.8f}) than Alpaca ({api_total_qty:.8f}). Discrepancy: {abs_discrepancy:.8f} BTC. MANUAL INTERVENTION REQUIRED. Trading paused."
                logger.critical(error_msg)
                send_alert(subject="AI Bot: CRITICAL Lot Sync Discrepancy - MANUAL INTERVENTION", message=error_msg)
                # Set safety flags to pause trading in main_bot.py
                # Note: session_stats is passed by reference, so changes here affect the main_bot's session_stats
                # This requires main_bot to handle the safety_triggered flag appropriately.
                # Assuming main_bot.py's robust_run_trade_cycle checks session_stats["safety_triggered"]
                import main_bot # Import here to avoid circular dependency at top level
                main_bot.session_stats["safety_triggered"] = True
                main_bot.session_stats["safety_reason"] = "Critical Lot Discrepancy"
                # Do NOT modify open_lots here, as it would incorrectly reflect the exchange state.
                # The bot should halt and wait for manual correction of open_lots.json and actual positions.
            else:
                logger.info("[Lot Sync] Bot and exchange quantities are in sync (or within dust threshold).")
        except Exception as e:
            logger.critical(f"CRITICAL: Error during lot quantity check: {e}.", exc_info=True)
            send_alert(subject="AI Bot: CRITICAL Lot Sync Error", message=f"An unexpected error occurred during lot reconciliation: {e}. Manual intervention may be required.")


def process_ai_decision_and_trade(api, ai: dict, acct: dict, pos: dict, ind: dict, buy_cnt: int, sell_cnt: int, dynamic_params: dict, bot_state_data: dict, session_stats: dict, trade_cycle_log_buffer: list, trade_history_buffer: list) -> tuple[str, int, int]:
    global open_lots
    with lots():
        decision = ai.get("decision")
        trade_action_summary = "No action"
        current_price = Decimal(str(ind.get("avg_price", "0.0")))
        current_atr = Decimal(str(ind.get("atr", "0.0")))
        now_ts = datetime.now(UTC)
        current_rsi = Decimal(str(ind.get("rsi", "50.0")))

        current_rsi_val = ind.get("rsi")
        rsi_slope_val = ind.get("rsi_slope")
        macd_hist_val = ind.get("macd_hist")
        volume_spike_val = ind.get("volume_spike")
        trend_5m_val = ind.get("trend_5m")

        current_rsi_safe = Decimal(str(current_rsi_val)) if current_rsi_val is not None else Decimal("50.0")
        rsi_slope_safe = Decimal(str(rsi_slope_val)) if rsi_slope_val is not None else Decimal("0.0")
        macd_hist_safe = Decimal(str(macd_hist_val)) if macd_hist_val is not None else Decimal("0.0")
        volume_spike_safe = Decimal(str(volume_spike_val)) if volume_spike_val is not None else Decimal("0.0")
        
        overall_unrealized_pl = Decimal(str(ind.get("total_unrealized_pl", "0.0"))) 
        in_losing_overall_position = len(open_lots) > 0 and overall_unrealized_pl < Decimal("0.0")

        debug_logger.debug(f"--- Trade Cycle Start for Decision ---")
        debug_logger.debug(f"Current Price: ${current_price:.2f}, Current ATR: {current_atr:.2f}")
        debug_logger.debug(f"Market Indicators: RSI={current_rsi_safe:.2f}, RSI_Slope={rsi_slope_safe:.2f}, MACD_Hist={macd_hist_safe:.2f}, Volume_Spike={volume_spike_safe:.2f}, Trend_5m={trend_5m_val}")
        debug_logger.debug(f"Overall Position: {len(open_lots)} lots, Total Unrealized P/L: ${overall_unrealized_pl:.2f}, In Losing Position: {in_losing_overall_position}")
        debug_logger.debug(f"AI's Initial Decision (from cache or fresh): {ai.get('decision')}, AI is_dip_buy: {ai.get('is_dip_buy')}, AI Confidence: {ai.get('confidence_level')}")

        logger.info(f"Processing AI decision: {decision} with {len(open_lots)} open lot(s).")

        last_trade_timestamp = bot_state_data.get("last_trade_timestamp")
        last_trade_side = bot_state_data.get("last_trade_side")
        if last_trade_timestamp:
            time_since_last_trade = now_ts - last_trade_timestamp
            if decision == "BUY" and last_trade_side == "SELL" and time_since_last_trade < COOLDOWN_BUY_AFTER_SELL:
                logger.warning(f"Cooldown VETO: AI 'BUY' overruled. In BUY cooldown for another {COOLDOWN_BUY_AFTER_SELL - time_since_last_trade}.")
                debug_logger.debug(f"Cooldown VETO applied. Original AI decision: {decision}. New decision: HOLD (for BUY).")
                return f"No action (BUY cooldown)", buy_cnt, sell_cnt
            if decision == "SELL" and last_trade_side == "BUY" and time_since_last_trade < COOLDOWN_SELL_AFTER_BUY:
                logger.warning(f"Cooldown VETO: AI 'SELL' overruled. In SELL cooldown for another {COOLDOWN_SELL_AFTER_BUY - time_since_last_trade}.")
                debug_logger.debug(f"Cooldown VETO applied. Original AI decision: {decision}. New decision: HOLD (for SELL).")
                return f"No action (SELL cooldown)", buy_cnt, sell_cnt

        if ENABLE_GUARDIAN_VETO:
            guardian_veto_reason = None
            if decision == "BUY" and current_rsi is not None and current_rsi > GUARDIAN_RSI_OVERBOUGHT:
                guardian_veto_reason = f"Guardian VETO: AI 'BUY' overruled. RSI ({current_rsi:.2f}) > {GUARDIAN_RSI_OVERBOUGHT}."
            if decision == "SELL" and open_lots and current_rsi is not None and current_rsi < GUARDIAN_RSI_OVERSOLD:
                guardian_veto_reason = f"Guardian VETO: AI 'SELL' overruled. RSI ({current_rsi:.2f}) < {GUARDIAN_RSI_OVERSOLD}."
            if guardian_veto_reason:
                logger.critical(guardian_veto_reason)
                debug_logger.debug(f"Guardian VETO applied. Original AI decision: {decision}. New decision: HOLD. Reason: {guardian_veto_reason}")
                decision = "HOLD"
                trade_action_summary = guardian_veto_reason

        # Granular Take-Profit Check (MODIFIED FOR ROBUST LEDGER UPDATES)
        if open_lots and config.GRANULAR_TAKE_PROFIT_PCT > Decimal("0"):
            lots_to_keep_after_tp_sells = []
            any_tp_sell_occurred_this_cycle = False
            for lot_data in list(open_lots): 
                lot_tp_price = lot_data.get('take_profit_price', Decimal("0"))
                lot_remaining_qty = lot_data.get('remaining_qty', Decimal("0"))
                
                if lot_remaining_qty <= DUST_THRESHOLD_QTY:
                    debug_logger.debug(f"Skipping Lot {lot_data.get('lot_id')} for TP check: Remaining Qty {lot_remaining_qty:.8f} is below dust threshold. Removing from active lots if present.")
                    continue 
                
                if current_price >= lot_tp_price and lot_tp_price > Decimal("0"):
                    logger.info(f"GRANULAR TAKE-PROFIT TRIGGERED for lot {lot_data.get('lot_id', 'N/A')} (Type: {lot_data.get('strategy_type')}). TP: ${lot_tp_price:.2f}, Current Price: ${current_price:.2f}")
                    
                    qty_to_sell_tp = lot_remaining_qty 
                    
                    if qty_to_sell_tp < MIN_SELL_QTY_THRESHOLD:
                        logger.warning(f"Granular TP for lot {lot_data.get('lot_id', 'N/A')}: Qty {qty_to_sell_tp:.8f} is below minimum ({MIN_SELL_QTY_THRESHOLD:.8f}). Cannot submit sell order for this lot. Lot remains.")
                        lots_to_keep_after_tp_sells.append(lot_data) 
                        continue 
                    
                    try:
                        tp_sell_order = api.submit_order(symbol="BTCUSD", qty=str(qty_to_sell_tp), side="sell", type="market", time_in_force="gtc")
                        
                        filled_tp_sell_order = alpaca_service.wait_for_order_fill(api, tp_sell_order.id)
                        
                        if filled_tp_sell_order and filled_tp_sell_order.status == 'filled':
                            actual_sold_qty_tp = Decimal(str(filled_tp_sell_order.filled_qty))
                        else:
                            logger.error(f"Granular TP SELL order {tp_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} did not fill in time or failed. Status: {filled_tp_sell_order.status if filled_tp_sell_order else 'UNKNOWN'}.")
                            lots_to_keep_after_tp_sells.append(lot_data)
                            continue
                        
                        actual_sold_qty_tp = Decimal(str(filled_tp_sell_order.filled_qty))
                        
                        if actual_sold_qty_tp > Decimal("0"): 
                            order_tracker_service.remove_order(tp_sell_order.id)
                            avg_sell_price_tp = Decimal(str(filled_tp_sell_order.filled_avg_price))
                            cost_basis_unit_tp = lot_data['cost_basis_per_unit']
                            sell_fee_tp = calculate_fees(actual_sold_qty_tp, avg_sell_price_tp)
                            pnl_this_lot_tp = (avg_sell_price_tp - cost_basis_unit_tp) * actual_sold_qty_tp - sell_fee_tp
                            
                            log_action = "GRANULAR_TP_SELL"
                            if filled_tp_sell_order.status == 'partially_filled':
                                log_action = "GRANULAR_TP_SELL_PARTIAL"
                                logger.warning(f"Lot {lot_data.get('lot_id', 'N/A')} PARTIALLY FILLED: Sold {actual_sold_qty_tp:.8f} @ ${avg_sell_price_tp:.2f}. Remaining Qty: {lot_remaining_qty - actual_sold_qty_tp:.8f}")

                            log_trade_to_csv(trade_history_buffer, ts=now_ts.isoformat(), action=log_action, qty=actual_sold_qty_tp, price=avg_sell_price_tp, order_id=tp_sell_order.id, status=str(filled_tp_sell_order.status), realized_pl=pnl_this_lot_tp, fee=sell_fee_tp, entry_price=lot_data['buy_price'], lot_id=lot_data.get('lot_id', 'N/A'), strategy_type=lot_data.get('strategy_type'))
                            _update_session_stats_for_sell(session_stats, pnl_this_lot_tp, tp_sell_order.id, "Granular TP")
                            
                            lot_data['remaining_qty'] -= actual_sold_qty_tp
                            
                            trade_action_summary = f"Granular TP Sell: Lot {lot_data.get('lot_id', 'N/A')} Qty {actual_sold_qty_tp:.8f} @ ${avg_sell_price_tp:.2f}. P/L: ${pnl_this_lot_tp:.2f}. Status: {filled_tp_sell_order.status}"
                            logger.info(trade_action_summary)
                            sell_cnt += 1
                            bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "SELL"})
                            any_tp_sell_occurred_this_cycle = True
                            
                            if lot_data['remaining_qty'] > DUST_THRESHOLD_QTY:
                                lots_to_keep_after_tp_sells.append(lot_data)
                            else:
                                logger.info(f"Lot {lot_data.get('lot_id', 'N/A')} fully sold or reduced to dust threshold ({lot_data['remaining_qty']:.8f}). Removing from active lots.")

                        else: 
                            logger.warning(f"Granular TP SELL order {tp_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} received 0 filled_qty. Status: {filled_tp_sell_order.status}. Lot remains unchanged for next attempt.")
                            lots_to_keep_after_tp_sells.append(lot_data) 
                        
                    except Exception as e_tp_sell:
                        logger.error(f"Granular TP SELL attempt for lot {lot_data.get('lot_id', 'N/A')} failed due to exception: {e_tp_sell}", exc_info=True)
                        lots_to_keep_after_tp_sells.append(lot_data) 
                else: 
                    lots_to_keep_after_tp_sells.append(lot_data) 
            
            if any_tp_sell_occurred_this_cycle:
                open_lots[:] = lots_to_keep_after_tp_sells
                save_open_lots()
            debug_logger.debug(f"Finished Granular TP check. Lots remaining after check: {len(open_lots)}")


        if current_price <= Decimal("0"):
            return "No action: Invalid Price", buy_cnt, sell_cnt

        debug_logger.debug(f"DEBUG: Dip Accumulation Rule Check -")
        debug_logger.debug(f"  current_rsi_safe: {current_rsi_safe:.2f}")
        debug_logger.debug(f"  rsi_slope_safe: {rsi_slope_safe:.2f}")
        debug_logger.debug(f"  macd_hist_safe: {macd_hist_safe:.2f}")
        debug_logger.debug(f"  volume_spike_safe: {volume_spike_safe:.2f}")
        debug_logger.debug(f"  overall_unrealized_pl (from ind): {overall_unrealized_pl:.2f}")
        debug_logger.debug(f"  in_losing_overall_position: {in_losing_overall_position}")
        debug_logger.debug(f"  AI's current decision: {decision}")

        force_buy_decision = False
        buy_strategy_reason = "No_Strategy_Match"
        buy_strategy_type = "UNKNOWN_BUY_TYPE" 

        if (current_rsi_safe < Decimal("45.0") and rsi_slope_safe > Decimal("0.0")):
            debug_logger.debug(f"  Condition 1 met: current_rsi_safe < 45.0 ({current_rsi_safe}) AND rsi_slope_safe > 0.0 ({rsi_slope_safe})")
            force_buy_decision = True
            buy_strategy_reason = "DIP_ACCUMULATION_RSI_Slope_Initial"
            debug_logger.debug(f"    -> DIP_ACCUMULATION_RSI_Slope triggered.")
            
        elif (current_rsi_safe < Decimal("45.0") and macd_hist_safe > Decimal("-5.0")):
            debug_logger.debug(f"  Condition 2 met: current_rsi_safe < 45.0 ({current_rsi_safe}) AND macd_hist_safe > -5.0 ({macd_hist_safe})")
            force_buy_decision = True
            buy_strategy_reason = "DIP_ACCUMULATION_MACD_Hist_Initial"
            debug_logger.debug(f"    -> DIP_ACCUMULATION_MACD_Hist triggered.")

        elif (volume_spike_safe > Decimal("1.5") and current_rsi_safe < Decimal("45.0")):
            debug_logger.debug(f"  Condition 3 met: volume_spike_safe > 1.5 ({volume_spike_safe}) AND current_rsi_safe < 45.0 ({current_rsi_safe})")
            force_buy_decision = True
            buy_strategy_reason = "DIP_ACCUMULATION_Volume_Spike_Initial"
            debug_logger.debug(f"    -> DIP_ACCUMULATION_Volume_Spike triggered.")
        
        if force_buy_decision:
            buy_strategy_type = buy_strategy_reason 
            if decision != "BUY":
                logger.warning(f"RULE OVERRIDE: Forcing BUY decision for {buy_strategy_reason} despite AI's {decision}.")
                decision = "BUY"
                ai["decision"] = "BUY"
                ai["is_dip_buy"] = True
                ai["reasoning"] = f"Forced BUY: {buy_strategy_reason} (AI original: {ai.get('reasoning', 'N/A')})"
                ai["quantity_percentage"] = float(config.MIN_QTY_PCT_CONFIG)
                ai["confidence_level"] = "HIGH"
                debug_logger.debug(f"Rule-based override applied. Original AI decision: {decision}. Forced to BUY for '{buy_strategy_reason}'. AI 'is_dip_buy' set to True, Qty Pct: {ai['quantity_percentage']}.")
            else:
                debug_logger.debug(f"Rule-based confirmation: AI already decided BUY. Confirmed strategy as '{buy_strategy_reason}'. AI 'is_dip_buy' confirmed True.")
                ai["is_dip_buy"] = True
                ai["reasoning"] = f"AI BUY confirmed by rule: {buy_strategy_reason} (AI original: {ai.get('reasoning', 'N/A')})"

        debug_logger.debug(f"DEBUG: Momentum Buy Rule Check -")
        debug_logger.debug(f"  RSI: {current_rsi_safe:.2f} (Target 50-85)") 
        debug_logger.debug(f"  RSI_Slope: {rsi_slope_safe:.2f} (Target > -1.0)")
        debug_logger.debug(f"  MACD_Hist: {macd_hist_safe:.2f} (Target > -5.0)")
        debug_logger.debug(f"  Volume_Spike: {volume_spike_safe:.2f} (Target >= 0.9)") 
        debug_logger.debug(f"  Trend_5m: {trend_5m_val} (Target bullish)")
        debug_logger.debug(f"  in_losing_overall_position: {in_losing_overall_position}")
        debug_logger.debug(f"  AI's current decision: {decision}")

        momentum_conditions_met = (
            current_rsi_safe >= Decimal("50.0") and current_rsi_safe <= Decimal("85.0") and 
            rsi_slope_safe > Decimal("-1.0") and                                          
            macd_hist_safe > Decimal("-5.0") and 
            volume_spike_safe >= Decimal("0.9") and                                       
            trend_5m_val == "bullish"
        )
        
        if momentum_conditions_met and (not in_losing_overall_position):
            debug_logger.debug(f"Momentum Buy conditions met: {momentum_conditions_met}. Not in losing position: {not in_losing_overall_position}.")
            if decision != "BUY" or not ai.get("is_dip_buy"):
                logger.warning(f"RULE OVERRIDE: Forcing BUY decision for MOMENTUM_BUY despite AI's HOLD.")
                decision = "BUY"
                ai["decision"] = "BUY"
                ai["is_dip_buy"] = True
                buy_strategy_type = "MOMENTUM_BUY"
                ai["reasoning"] = f"Forced BUY: MOMENTUM_BUY Rule (AI original: {ai.get('reasoning', 'N/A')})"
                ai["quantity_percentage"] = float(config.MIN_QTY_PCT_CONFIG)
                ai["confidence_level"] = "HIGH"
                debug_logger.debug(f"Rule-based override applied. Original AI decision: {decision}. Forced to BUY for 'MOMENTUM_BUY'. AI 'is_dip_buy' set to True, Qty Pct: {ai['quantity_percentage']}.")
            else:
                buy_strategy_type = "MOMENTUM_BUY"
                debug_logger.debug(f"Rule-based confirmation: AI already decided BUY. Confirmed strategy as '{buy_strategy_type}'. AI 'is_dip_buy' confirmed True.")
        else:
            debug_logger.debug(f"Momentum Buy conditions NOT met. Result: {momentum_conditions_met}. In losing position: {in_losing_overall_position}.")
            if not (current_rsi_safe >= Decimal("50.0") and current_rsi_safe <= Decimal("85.0")):
                debug_logger.debug(f"  - RSI ({current_rsi_safe:.2f}) not in range 50-85.")
            if not (rsi_slope_safe > Decimal("-1.0")):
                debug_logger.debug(f"  - RSI_Slope ({rsi_slope_safe:.2f}) not > -1.0.")
            if not (macd_hist_safe > Decimal("-5.0")):
                debug_logger.debug(f"  - MACD_Hist ({macd_hist_safe:.2f}) not > -5.0.") 
            if not (volume_spike_safe >= Decimal("0.9")):
                debug_logger.debug(f"  - Volume_Spike ({volume_spike_safe:.2f}) not >= 0.9.")
            if not (trend_5m_val == "bullish"):
                debug_logger.debug(f"  - Trend_5m ({trend_5m_val}) not bullish.")
            if in_losing_overall_position:
                debug_logger.debug(f"  - Overall Position is losing, preventing Momentum Buy.")

        if decision == "BUY":
            if ai.get("is_dip_buy") == True and buy_strategy_type == "UNKNOWN_BUY_TYPE":
                if in_losing_overall_position:
                    buy_strategy_type = "AI_DIP_ACCUMULATION"
                else:
                    buy_strategy_type = "MOMENTUM_BUY"
                    logger.info(f"AI BUY determined as '{buy_strategy_type}' (AI-driven momentum/initial buy).")
            elif buy_strategy_type == "UNKNOWN_BUY_TYPE":
                logger.critical("Unexpected: AI decision is BUY, but is_dip_buy is false and not forced by rule. Defaulting to UNKNOWN_BUY_TYPE.")
                buy_strategy_type = "UNKNOWN_BUY_TYPE"

            debug_logger.debug(f"Final determined buy strategy type for this BUY decision: '{buy_strategy_type}'")

            if current_atr is None or current_atr <= Decimal("0"):
                logger.error("VETO: Cannot BUY, ATR is zero or invalid.")
                return "No action (VETO: Invalid ATR)", buy_cnt, sell_cnt


            buying_power = Decimal(str(acct.get("buying_power", "0.0")))
            account_equity = Decimal(str(acct.get("equity", "0.0")))

            market_regime = ind.get("regime")
            base_trade_value = MAX_SCALP_TRADE_VALUE_USD if market_regime in ["ranging", "quiet", "volatile"] else MAX_TRADE_VALUE_USD

            atr_adjusted_trade_value = atr_adjusted_value(base_trade_value, current_atr)
            ai_suggested_qty_pct = Decimal(str(ai.get("quantity_percentage", float(config.MIN_QTY_PCT_CONFIG))))
            ai_suggested_usd = account_equity * ai_suggested_qty_pct if account_equity > Decimal("0") else MIN_ORDER_VALUE_USD_FLOOR

            TARGET_USD = min(
                atr_adjusted_trade_value,
                ai_suggested_usd,
                base_trade_value,
                buying_power - CASH_RESERVE_USD
            )
            TARGET_USD = max(TARGET_USD, MIN_ORDER_VALUE_USD_FLOOR)
            TARGET_USD = max(TARGET_USD, Decimal("0"))

            if TARGET_USD < MIN_ORDER_VALUE_USD_FLOOR:
                return f"No action: Insufficient cash for a valid trade size after adjustments (Target: ${TARGET_USD:.2f})", buy_cnt, sell_cnt

            buy_qty = (TARGET_USD / current_price).quantize(Decimal("0.********"), ROUND_DOWN)

            if buy_qty < MIN_TRADE_QTY_BTC:
                return f"No action: BUY Qty {buy_qty:.8f} < min", buy_cnt, sell_cnt

            logger.info(f"Attempting to BUY {buy_qty:.8f} BTC (Target USD: ${TARGET_USD:.2f}, Strategy: {buy_strategy_type})")
            debug_logger.debug(f"Calculated BUY parameters: Account Equity=${account_equity:.2f}, AI suggested Qty %={ai_suggested_qty_pct:.4f}, AI suggested USD=${ai_suggested_usd:.2f}")
            debug_logger.debug(f"Base Trade Value=${base_trade_value:.2f}, ATR Adjusted Trade Value=${atr_adjusted_trade_value:.2f}, Final TARGET_USD=${TARGET_USD:.2f}, Calculated Buy Qty={buy_qty:.8f}.")
            try:
                order = api.submit_order(symbol="BTCUSD", qty=str(buy_qty), side="buy", type="market", time_in_force="gtc")
                
                filled_order_details = alpaca_service.wait_for_order_fill(api, order.id)

                if filled_order_details and filled_order_details.status == 'filled':
                    filled_price = Decimal(str(filled_order_details.filled_avg_price))
                    actual_filled_qty = Decimal(str(filled_order_details.filled_qty))
                    buy_fee_this_lot = calculate_fees(actual_filled_qty, filled_price)
                    cost_basis_this_lot = ((filled_price * actual_filled_qty) + buy_fee_this_lot) / actual_filled_qty if actual_filled_qty > Decimal("0") else filled_price

                    stop_percent_decimal = Decimal(str(dynamic_params["stop_percent"]))
                    initial_stop_price = (filled_price * (Decimal("1") - stop_percent_decimal)).quantize(Decimal("0.0001"))
                    
                    take_profit_price_for_lot = Decimal("0.0")
                    if buy_strategy_type == "MOMENTUM_BUY":
                        effective_profit_multiplier = (Decimal("1") + config.MOMENTUM_BUY_NET_PROFIT_TARGET_PCT) / (Decimal("1") - config.FEE_PERCENT)
                        take_profit_price_for_lot = (cost_basis_this_lot * effective_profit_multiplier).quantize(Decimal("0.0001"))
                        logger.info(f"Momentum Buy TP: Cost Basis ${cost_basis_this_lot:.2f}, Effective Multiplier {effective_profit_multiplier:.4f}, Target TP ${take_profit_price_for_lot:.2f} (1%% Net)")
                    elif buy_strategy_type.startswith("DIP_ACCUMULATION") or buy_strategy_type == "SYNTHESIZED":
                        take_profit_price_for_lot = (filled_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))
                        logger.info(f"Dip Buy TP: Buy Price ${filled_price:.2f}, Target {config.DIP_BUY_PROFIT_TARGET_PCT*100:.2f}%% Gross, Target TP ${take_profit_price_for_lot:.2f}")
                    else:
                        take_profit_price_for_lot = (filled_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))
                        logger.warning(f"Unknown buy strategy type '{buy_strategy_type}'. Defaulting TP to DIP_BUY_PROFIT_TARGET_PCT.")

                    new_lot = {
                        "lot_id": str(order.id), "buy_order_id": str(order.id), "buy_timestamp": now_ts,
                        "original_qty": actual_filled_qty, "remaining_qty": actual_filled_qty,
                        "buy_price": filled_price, "buy_fee_usd": buy_fee_this_lot,
                        "cost_basis_per_unit": cost_basis_this_lot.quantize(Decimal("0.000001")),
                        "initial_stop": initial_stop_price, "current_stop": initial_stop_price,
                        "take_profit_price": take_profit_price_for_lot, 
                        "type": buy_strategy_type,
                        "strategy_type": buy_strategy_type,
                        "original_order_ids": [str(order.id)]
                    }
                    open_lots.append(new_lot)
                    # save_open_lots() # Defer saving to end of backtest

                    log_trade_to_csv(trade_history_buffer, ts=now_ts.isoformat(), action="BUY", qty=actual_filled_qty, price=filled_price, order_id=str(order.id), status=str(filled_order_details.status), realized_pl=Decimal("0.0"), fee=buy_fee_this_lot, entry_price=filled_price, lot_id=str(order.id), strategy_type=buy_strategy_type)
                    buy_cnt += 1
                    trade_action_summary = f"AI BUY {actual_filled_qty:.8f} BTC @ ${filled_price:.2f} (Strategy: {buy_strategy_type})"
                    bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "BUY"})
                    save_bot_state(bot_state_data)
                else:
                    logger.warning(f"BUY order {order.id} not filled immediately (0 filled_qty). Status: {filled_order_details.status}.")
                    trade_action_summary = f"BUY order {order.id} submitted, status: {filled_order_details.status} (0 filled_qty)"
            except Exception as e:
                logger.error(f"BUY order/processing failed: {e}")
                trade_action_summary = "No action: BUY order failed"
            return trade_action_summary, buy_cnt, sell_cnt

        elif decision == "HOLD":
            if trade_action_summary == "No action":
                trade_action_summary = "No action (HOLD)"
        
        return trade_action_summary, buy_cnt, sell_cnt


def update_trailing_stops(api, current_price_dec: Decimal, dynamic_stop_percent_dec: Decimal):
    with lots():
        if not open_lots: return
        updated_count = 0
        for lot in open_lots:
            cost_basis = lot.get("cost_basis_per_unit", lot.get("buy_price", Decimal("0")))
            if cost_basis > 0 and current_price_dec > cost_basis * (Decimal("1") + TRAIL_PROFIT_BUFFER_PCT):
                new_stop_for_lot = current_price_dec * (Decimal("1") - dynamic_stop_percent_dec)
                # Ensure stop never goes below cost basis
                new_stop_for_lot = max(new_stop_for_lot, cost_basis)
                if new_stop_for_lot > lot.get("current_stop", Decimal("0")):
                    lot["current_stop"] = new_stop_for_lot.quantize(Decimal("0.01"))
                    logger.info(f"Trailing stop for lot {lot.get('lot_id', 'N/A')} (Type: {lot.get('strategy_type')}) updated to ${lot['current_stop']:.2f}")
                    updated_count += 1
        if updated_count > 0:
            # save_open_lots() # Defer saving to end of backtest
            pass

def execute_kill_switch(api):
    """Liquidates all open positions immediately and ensures internal state reflects actual fills."""
    global open_lots
    with lots():
        if not open_lots:
            logger.warning("Kill switch activated, but no open lots to close.")
            return

        logger.critical(f"Executing kill switch: Attempting to close all {len(open_lots)} lots.")
        
        orders_to_track = []
        lots_to_process = list(open_lots) # Operate on a copy as we'll modify open_lots
        
        # 1. Submit sell orders for all remaining quantities
        for lot in lots_to_process:
            if lot['remaining_qty'] > config.MIN_SELL_QTY_THRESHOLD:
                try:
                    logger.info(f"Submitting market sell for lot {lot['lot_id']} (Qty: {lot['remaining_qty']:.8f})")
                    order = api.submit_order(
                        symbol="BTCUSD",
                        qty=str(lot['remaining_qty']),
                        side="sell",
                        type="market",
                        time_in_force="gtc"
                    )
                    orders_to_track.append({"order_id": str(order.id), "lot_id": lot['lot_id'], "submitted_qty": lot['remaining_qty']})
                    order_tracker_service.add_order(order.id, "SELL", lot['remaining_qty'], lot['buy_price'], lot['lot_id'])
                except Exception as e:
                    logger.error(f"Failed to submit sell order for lot {lot['lot_id']} during kill switch: {e}", exc_info=True)
                    send_alert("CRITICAL: Kill Switch Order Submission FAILED", f"Failed to submit sell order for lot {lot['lot_id']}: {e}")
            else:
                logger.info(f"Skipping lot {lot['lot_id']} as its quantity {lot['remaining_qty']:.8f} is below the minimum sell threshold.")
        
        # 2. Wait for and process fills
        final_open_lots = []
        for order_info in orders_to_track:
            order_id = order_info["order_id"]
            lot_id = order_info["lot_id"]
            submitted_qty = order_info["submitted_qty"]
            
            try:
                logger.info(f"Waiting for fill confirmation for kill switch order {order_id} (Lot: {lot_id})...")
                filled_order = alpaca_service.wait_for_order_fill(api, order_id, timeout=30) # Increased timeout
                
                if filled_order and filled_order.status == 'filled':
                    actual_sold_qty = Decimal(str(filled_order.filled_qty))
                    logger.critical(f"Kill switch order {order_id} (Lot: {lot_id}) FULLY FILLED. Sold {actual_sold_qty:.8f} BTC.")
                    # Lot is fully closed, remove it from consideration for open_lots
                    order_tracker_service.remove_order(order_id)
                elif filled_order and filled_order.status == 'partially_filled':
                    actual_sold_qty = Decimal(str(filled_order.filled_qty))
                    remaining_qty_on_alpaca = submitted_qty - actual_sold_qty
                    logger.critical(f"Kill switch order {order_id} (Lot: {lot_id}) PARTIALLY FILLED. Sold {actual_sold_qty:.8f} BTC, {remaining_qty_on_alpaca:.8f} BTC remaining. MANUAL REVIEW NEEDED.")
                    send_alert("CRITICAL: Kill Switch Order PARTIALLY FILLED", f"Order {order_id} for lot {lot_id} partially filled. {remaining_qty_on_alpaca:.8f} BTC remaining. Manual review required.")
                    # Update the lot's remaining quantity and keep it in the list
                    original_lot = next((l for l in lots_to_process if l['lot_id'] == lot_id), None)
                    if original_lot:
                        original_lot['remaining_qty'] = remaining_qty_on_alpaca
                        final_open_lots.append(original_lot)
                    order_tracker_service.update_order_status(order_id, "partially_filled", actual_sold_qty)
                else:
                    logger.critical(f"Kill switch order {order_id} (Lot: {lot_id}) NOT FILLED or FAILED. Status: {filled_order.status if filled_order else 'UNKNOWN'}. MANUAL REVIEW NEEDED.")
                    send_alert("CRITICAL: Kill Switch Order NOT FILLED", f"Order {order_id} for lot {lot_id} not filled. Status: {filled_order.status if filled_order else 'UNKNOWN'}. Manual review required.")
                    # Keep the lot in the list as it's still open on Alpaca
                    original_lot = next((l for l in lots_to_process if l['lot_id'] == lot_id), None)
                    if original_lot:
                        final_open_lots.append(original_lot)
                    order_tracker_service.update_order_status(order_id, filled_order.status if filled_order else "failed")

            except Exception as e:
                logger.error(f"Error processing kill switch order {order_id} (Lot: {lot_id}): {e}", exc_info=True)
                send_alert("CRITICAL: Kill Switch Order Processing Error", f"Error processing order {order_id} for lot {lot_id}: {e}. Manual review required.")
                # Assume lot is still open if an error occurred during processing
                original_lot = next((l for l in lots_to_process if l['lot_id'] == lot_id), None)
                if original_lot:
                    final_open_lots.append(original_lot)

        # Update the global open_lots with the actual remaining lots
        open_lots[:] = final_open_lots
        save_open_lots()
        order_tracker_service.save_active_orders() # Ensure active_orders.json is also saved
        
        if not final_open_lots:
            logger.critical("All open lots successfully liquidated and internal ledgers cleared.")
            send_alert(subject="AI Bot: Kill Switch Complete", message="All open positions have been liquidated.")
        else:
            logger.critical(f"Kill switch completed with {len(final_open_lots)} lots remaining. Manual intervention required to resolve remaining positions.")
            send_alert(subject="AI Bot: Kill Switch Partial Liquidation", message=f"Kill switch completed with {len(final_open_lots)} lots remaining. Manual intervention required.")

def _check_and_execute_stop_losses(api, current_price_dec: Decimal, session_stats: dict, trade_history_buffer: list, bot_state_data: dict, sell_cnt: int) -> int:
    global open_lots
    with lots():
        lots_to_keep = []
        for lot in list(open_lots): # Iterate over a copy to allow modification
            if lot['remaining_qty'] <= DUST_THRESHOLD_QTY:
                lots_to_keep.append(lot)
                continue

            if current_price_dec <= lot['current_stop']:
                logger.critical(f"STOP-LOSS TRIGGERED for lot {lot.get('lot_id', 'N/A')} (Type: {lot.get('strategy_type')}). Stop: ${lot['current_stop']:.2f}, Current Price: ${current_price_dec:.2f}")
                
                qty_to_sell_sl = lot['remaining_qty']
                
                if qty_to_sell_sl < MIN_SELL_QTY_THRESHOLD:
                    logger.warning(f"Stop-loss for lot {lot.get('lot_id', 'N/A')}: Qty {qty_to_sell_sl:.8f} is below minimum ({MIN_SELL_QTY_THRESHOLD:.8f}). Cannot submit sell order for this lot. Lot remains.")
                    lots_to_keep.append(lot)
                    continue

                try:
                    sl_sell_order = api.submit_order(symbol="BTCUSD", qty=str(qty_to_sell_sl), side="sell", type="market", time_in_force="gtc")
                    filled_sl_sell_order = alpaca_service.wait_for_order_fill(api, sl_sell_order.id)

                    if filled_sl_sell_order and filled_sl_sell_order.status == 'filled':
                        actual_sold_qty_sl = Decimal(str(filled_sl_sell_order.filled_qty))
                    else:
                        logger.error(f"Stop-loss SELL order {sl_sell_order.id} for lot {lot.get('lot_id', 'N/A')} did not fill in time or failed. Status: {filled_sl_sell_order.status if filled_sl_sell_order else 'UNKNOWN'}.")
                        lots_to_keep.append(lot)
                        continue

                    if actual_sold_qty_sl > Decimal("0"):
                        order_tracker_service.remove_order(sl_sell_order.id)
                        avg_sell_price_sl = Decimal(str(filled_sl_sell_order.filled_avg_price))
                        cost_basis_unit_sl = lot['cost_basis_per_unit']
                        sell_fee_sl = calculate_fees(actual_sold_qty_sl, avg_sell_price_sl)
                        pnl_this_lot_sl = (avg_sell_price_sl - cost_basis_unit_sl) * actual_sold_qty_sl - sell_fee_sl
                        
                        log_trade_to_csv(trade_history_buffer, ts=datetime.now(UTC).isoformat(), action="STOP_LOSS_SELL", qty=actual_sold_qty_sl, price=avg_sell_price_sl, order_id=sl_sell_order.id, status=str(filled_sl_sell_order.status), realized_pl=pnl_this_lot_sl, fee=sell_fee_sl, entry_price=lot['buy_price'], lot_id=lot.get('lot_id', 'N/A'), strategy_type=lot.get('strategy_type'))
                        _update_session_stats_for_sell(session_stats, pnl_this_lot_sl, sl_sell_order.id, "Stop Loss")
                        
                        lot['remaining_qty'] -= actual_sold_qty_sl
                        sell_cnt += 1
                        bot_state_data.update({"last_trade_timestamp": datetime.now(UTC), "last_trade_side": "SELL"})

                        if lot['remaining_qty'] > DUST_THRESHOLD_QTY:
                            lots_to_keep.append(lot)
                        else:
                            logger.info(f"Lot {lot.get('lot_id', 'N/A')} fully sold or reduced to dust threshold ({lot['remaining_qty']:.8f}). Removing from active lots.")
                    else:
                        logger.warning(f"Stop-loss SELL order {sl_sell_order.id} for lot {lot.get('lot_id', 'N/A')} received 0 filled_qty. Status: {filled_sl_sell_order.status}. Lot remains unchanged for next attempt.")
                        lots_to_keep.append(lot)
                except Exception as e_sl_sell:
                    logger.error(f"Stop-loss SELL attempt for lot {lot.get('lot_id', 'N/A')} failed due to exception: {e_sl_sell}", exc_info=True)
                    lots_to_keep.append(lot)
            else:
                lots_to_keep.append(lot)
        open_lots[:] = lots_to_keep
        return sell_cnt

def run_trade_cycle(api, acct: dict, pos: dict, ind: dict, ai: dict, buy_cnt: int, sell_cnt: int, dynamic_params: dict, session_stats: dict, bot_state_data: dict, trade_cycle_log_buffer: list, trade_history_buffer: list) -> tuple[dict, int, int]:
    logger.info(f"Dynamic Parameters for cycle: Stop={dynamic_params['stop_percent']:.4f}, Risk=0.0400")

    with lots():
        current_price = Decimal(str(ind.get("avg_price", "0.0")))
        if current_price <= Decimal("0"):
            logger.error("Invalid current price (<=0). Skipping trade cycle.")
            return "No action: Invalid Price", buy_cnt, sell_cnt

        # --- KILL SWITCH CHECK (DISABLED FOR AUTOMATIC LOSS LIQUIDATION) ---
        # total_unrealized_pl = Decimal(ind.get("total_unrealized_pl", "0.0"))
        # account_equity = Decimal(acct.get("equity", "0.0"))
        # 
        # if account_equity > 0:
        #     unrealized_pl_pct = (total_unrealized_pl / account_equity)
        #     max_daily_loss_pct = Decimal(dynamic_params.get("max_daily_loss_pct", "0.0"))
        #     if unrealized_pl_pct < -max_daily_loss_pct:
        #         logger.critical(f"KILL SWITCH ENGAGED: Unrealized P/L percentage ({unrealized_pl_pct:.2%}) has exceeded the max daily loss ({max_daily_loss_pct:.2%}).")
        #         send_alert(
        #             subject="AI Bot: KILL SWITCH ACTIVATED - MAX LOSS EXCEEDED",
        #             message=f"Total unrealized P/L of ${total_unrealized_pl:.2f} is {unrealized_pl_pct:.2%} of equity, exceeding the -{max_daily_loss_pct:.2%} threshold. Liquidating all positions."
        #         )
        #         execute_kill_switch(api)
        #         # We should stop further processing in this cycle after activating the kill switch.
        #         return "KILL SWITCH ACTIVATED", buy_cnt, sell_cnt

        # 1. Update trailing stops for existing positions
        if len(open_lots) > 0:
            update_trailing_stops(api, current_price, Decimal(str(dynamic_params["stop_percent"])))

        # 2. Check and execute stop-losses
        sell_cnt = _check_and_execute_stop_losses(api, current_price, session_stats, trade_history_buffer, bot_state_data, sell_cnt)

    # 3. Process AI decision and potentially open new trades
    action_summary, buy_cnt, sell_cnt = process_ai_decision_and_trade(
        api, ai, acct, pos, ind, buy_cnt, sell_cnt, dynamic_params, bot_state_data, session_stats, trade_cycle_log_buffer, trade_history_buffer
    )

    acct_after_trade = alpaca_service.get_account_info(api)
    
    with lots():
        open_lots_copy = [lot.copy() for lot in open_lots]
        current_price_for_log = Decimal(str(ind.get("avg_price", "0.0")))
        unrealized_pl = sum((current_price_for_log - l.get('cost_basis_per_unit', l.get('buy_price', Decimal("0")))) * l['remaining_qty'] for l in open_lots_copy)
    
    context = {
        "timestamp": datetime.now(UTC).isoformat(),
        "action": action_summary,
        "decision": ai.get("decision", "UNKNOWN"),
        "equity": float(acct_after_trade.get("equity", 0)),
        "open_lots": open_lots_copy,
        "profit_loss": float(unrealized_pl)
    }
    log_trade_cycle(trade_cycle_log_buffer, context)
    return context, buy_cnt, sell_cnt

load_open_lots()
# END OF FILE trade_logic.py