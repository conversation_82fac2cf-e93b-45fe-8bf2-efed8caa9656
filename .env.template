# Bitcoin AI Trading Bot - Secure Configuration Template
# 
# IMPORTANT: Real API keys are now stored in encrypted format
# This template shows environment variables that can be used as fallbacks
#
# To set master password (optional - will prompt if not set):
# TRADING_BOT_MASTER_PASSWORD="your_secure_master_password"
#
# Fallback environment variables (only used if secure storage fails):
# GEMINI_API_KEY="your_gemini_api_key"
# ALPACA_LIVE_API_KEY_ID="your_alpaca_live_key_id"
# ALPACA_LIVE_SECRET_KEY="your_alpaca_live_secret"
# ALPACA_PAPER_API_KEY_ID="your_alpaca_paper_key_id"
# ALPACA_PAPER_SECRET_KEY="your_alpaca_paper_secret"
#
# Non-sensitive configuration:
TRADING_BOT_LOG_LEVEL="INFO"
TRADING_BOT_ENVIRONMENT="production"
