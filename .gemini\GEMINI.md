## Gemini Added Memories
- User wants to implement the following improvements: 1. Partial Take-Profits. 2. Adaptive Trailing Stops (more aggressive in profit, not below cost basis). 3. Time-Based Profit Taking (conditional, only if profitable).
- We have completed Step 1 of the AI Decision Service Enhancement Plan: Enhanced Prompt Engineering.
- We have completed Step 2 of the AI Decision Service Enhancement Plan: Dynamic Prompting.
- We have completed Step 3 of the AI Decision Service Enhancement Plan: Robust Error Handling.
- We have completed all steps of the AI Decision Service Enhancement Plan.
- Completed Step 2 of the Volatility-Aware Enhancements: Volatility-Adjusted Position Sizing.
- Implemented improvements to encourage strategic dip buying by modifying AI philosophy and prompt.
