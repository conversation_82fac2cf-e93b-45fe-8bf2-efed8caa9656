# indicator_service.py
import pandas as pd
import pandas_ta as ta # For calculating technical indicators
import logging

logger = logging.getLogger(__name__)

def calculate_technical_indicators(ohlcv_df, 
                                   rsi_period=14, 
                                   short_sma_period=20, 
                                   long_sma_period=50):
    """
    Calculates technical indicators from OHLCV data.
    Args:
        ohlcv_df: Pandas DataFrame with 'open', 'high', 'low', 'close', 'volume' columns
                  and a DatetimeIndex. Expects lowercase column names.
        rsi_period: The period for RSI calculation.
        short_sma_period: The period for the short-term SMA.
        long_sma_period: The period for the long-term SMA.
    Returns:
        A dictionary containing the latest calculated indicator values,
        or an empty dictionary if calculation fails or data is insufficient.
        Example: 
        {
            'rsi_14': 65.0, 
            'sma_20': 45000.0, 
            'sma_50': 44500.0,
            'sma_signal': 'BULLISH_CROSS' # or 'BEARISH_CROSS', 'HOLD_NO_CROSS', 'HOLD_SHORT_ABOVE', 'HOLD_SHORT_BELOW'
        }
    """
    if ohlcv_df is None or ohlcv_df.empty:
        logger.error("OHLCV DataFrame is None or empty. Cannot calculate indicators.")
        return {}

    # Ensure we have enough data for the longest period required by indicators
    # Add a small buffer, e.g., +1, because some indicators might need one extra previous point.
    min_data_points = max(rsi_period, short_sma_period, long_sma_period) + 1 
    if len(ohlcv_df) < min_data_points:
        logger.warning(f"Insufficient data for indicators. Need {min_data_points} bars, got {len(ohlcv_df)}.")
        return {}

    # Make a copy to avoid SettingWithCopyWarning if ohlcv_df is a slice from another DataFrame
    df = ohlcv_df.copy()

    try:
        # Calculate RSI
        # pandas_ta appends the column by default if `append=True`
        df.ta.rsi(length=rsi_period, append=True) 
        # Column name will be 'RSI_14' if rsi_period is 14

        # Calculate SMAs
        df.ta.sma(length=short_sma_period, append=True) 
        # Column name will be 'SMA_20'
        df.ta.sma(length=long_sma_period, append=True) 
        # Column name will be 'SMA_50'

        # Get the latest values
        latest_indicators = {}
        rsi_col_name = f'RSI_{rsi_period}'
        sma_short_col_name = f'SMA_{short_sma_period}'
        sma_long_col_name = f'SMA_{long_sma_period}'

        if rsi_col_name in df.columns:
            latest_indicators['rsi'] = round(df[rsi_col_name].iloc[-1], 2)
        else:
            logger.warning(f"Column {rsi_col_name} not found after RSI calculation.")
            latest_indicators['rsi'] = None
            
        if sma_short_col_name in df.columns:
            latest_indicators['sma_short'] = round(df[sma_short_col_name].iloc[-1], 2)
        else:
            logger.warning(f"Column {sma_short_col_name} not found after SMA calculation.")
            latest_indicators['sma_short'] = None

        if sma_long_col_name in df.columns:
            latest_indicators['sma_long'] = round(df[sma_long_col_name].iloc[-1], 2)
        else:
            logger.warning(f"Column {sma_long_col_name} not found after SMA calculation.")
            latest_indicators['sma_long'] = None

        # Determine SMA Crossover Signal
        # We need at least two points for the SMAs to compare current and previous state for a "cross"
        sma_signal = "DATA_INSUFFICIENT"
        if latest_indicators['sma_short'] is not None and latest_indicators['sma_long'] is not None and len(df) >= 2:
            current_short_sma = df[sma_short_col_name].iloc[-1]
            current_long_sma = df[sma_long_col_name].iloc[-1]
            prev_short_sma = df[sma_short_col_name].iloc[-2]
            prev_long_sma = df[sma_long_col_name].iloc[-2]

            # Check for bullish crossover (short MA crosses above long MA)
            if prev_short_sma <= prev_long_sma and current_short_sma > current_long_sma:
                sma_signal = "BULLISH_CROSS"
            # Check for bearish crossover (short MA crosses below long MA)
            elif prev_short_sma >= prev_long_sma and current_short_sma < current_long_sma:
                sma_signal = "BEARISH_CROSS"
            # No cross, determine current state
            elif current_short_sma > current_long_sma:
                sma_signal = "HOLD_SHORT_ABOVE" # Short MA is already above Long MA
            elif current_short_sma < current_long_sma:
                sma_signal = "HOLD_SHORT_BELOW" # Short MA is already below Long MA
            else: # SMAs are equal or something unexpected
                sma_signal = "HOLD_EQUAL"
        
        latest_indicators['sma_signal'] = sma_signal
        
        logger.info(f"Calculated indicators: {latest_indicators}")
        return latest_indicators

    except Exception as e:
        logger.error(f"Error calculating technical indicators: {e}", exc_info=True)
        return {}

if __name__ == '__main__':
    # This block is for testing this module directly.
    # You would need to create a dummy OHLCV DataFrame here.
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    
    logger.info("Testing indicator_service.py with dummy data...")

    # Create a sample DataFrame similar to what Alpaca might return
    # For a real test, you'd fetch this using alpaca_service.get_historical_crypto_data
    data = {
        'open': [i for i in range(100, 100 + 60)],  # 60 data points
        'high': [i + 2 for i in range(100, 100 + 60)],
        'low': [i - 2 for i in range(100, 100 + 60)],
        'close': [i + 1 for i in range(100, 100 + 60)],
        'volume': [1000 + (i * 10) for i in range(60)]
    }
    # Create a DatetimeIndex
    index = pd.date_range(end=pd.Timestamp.now(tz='UTC'), periods=60, freq='h')
    dummy_ohlcv_df = pd.DataFrame(data, index=index)

    logger.info(f"Dummy DataFrame head:\n{dummy_ohlcv_df.head()}")
    logger.info(f"Dummy DataFrame tail:\n{dummy_ohlcv_df.tail()}")
    logger.info(f"Dummy DataFrame length: {len(dummy_ohlcv_df)}")


    indicators = calculate_technical_indicators(dummy_ohlcv_df)
    if indicators:
        logger.info(f"Calculated indicators from dummy data: {indicators}")
    else:
        logger.error("Failed to calculate indicators from dummy data.")

    logger.info("Testing with insufficient data...")
    insufficient_df = dummy_ohlcv_df.head(10) # Only 10 data points
    indicators_insufficient = calculate_technical_indicators(insufficient_df)
    if not indicators_insufficient:
        logger.info("Correctly handled insufficient data (returned empty dict or logged warning).")
    else:
        logger.error(f"Incorrectly processed insufficient data: {indicators_insufficient}")