import os
import json
import logging
from datetime import datetime
from decimal import Decimal
import time

logger = logging.getLogger("TradingBotApp.OrderTracker")

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
ACTIVE_ORDERS_FILE = os.path.join(BASE_DIR, "active_orders.json")

_active_orders = []

def _decimal_to_str(obj):
    if isinstance(obj, Decimal):
        return str(obj)
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

def load_active_orders():
    logger.debug(f"Attempting to load active orders from {ACTIVE_ORDERS_FILE}.")
    global _active_orders
    try:
        if os.path.exists(ACTIVE_ORDERS_FILE) and os.path.getsize(ACTIVE_ORDERS_FILE) > 0:
            with open(ACTIVE_ORDERS_FILE, 'r') as f:
                orders_from_file = json.load(f)
                _active_orders = orders_from_file
            logger.info(f"Loaded {len(_active_orders)} active order(s) from {ACTIVE_ORDERS_FILE}.")
        else:
            _active_orders = []
            logger.info(f"No active orders file found or file is empty at {ACTIVE_ORDERS_FILE}. Starting with an empty list.")
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Error loading active orders from {ACTIVE_ORDERS_FILE}: {e}. Starting with an empty list.")
        _active_orders = []
    except Exception as e:
        logger.critical(f"An unexpected error occurred while loading active orders: {e}", exc_info=True)
        _active_orders = []
    return _active_orders

def save_active_orders():
    logger.debug(f"Attempting to save active orders to {ACTIVE_ORDERS_FILE}.")
    global _active_orders
    temp_file = ACTIVE_ORDERS_FILE + ".tmp"
    try:
        with open(temp_file, 'w') as f:
            json.dump(_active_orders, f, indent=4, default=_decimal_to_str)
        os.replace(temp_file, ACTIVE_ORDERS_FILE)
        logger.info(f"Saved {len(_active_orders)} active order(s) to {ACTIVE_ORDERS_FILE}.")
    except IOError as e:
        logger.error(f"Error saving active orders to {ACTIVE_ORDERS_FILE}: {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred while saving active orders: {e}", exc_info=True)

def add_order(order_data: dict):
    global _active_orders
    logger.debug(f"Attempting to add order {order_data.get('id', 'N/A')} to active tracker.")
    if not isinstance(order_data, dict) or 'id' not in order_data:
        logger.error(f"Attempted to add invalid order data: {order_data}")
        return

    if any(o['id'] == order_data['id'] for o in _active_orders):
        logger.warning(f"Order {order_data['id']} is already in the active order list. Not adding again.")
        return

    logger.info(f"Adding new order {order_data['id']} to active order tracker. Side: {order_data.get('side')}, Qty: {order_data.get('qty')}")
    _active_orders.append(order_data)
    save_active_orders()

def remove_order(order_id: str):
    global _active_orders
    logger.debug(f"Attempting to remove order {order_id} from active tracker.")
    initial_count = len(_active_orders)
    _active_orders = [o for o in _active_orders if o.get('id') != order_id]
    
    if len(_active_orders) < initial_count:
        logger.info(f"Removed completed order {order_id} from active order tracker.")
        save_active_orders()
    else:
        logger.warning(f"Attempted to remove order {order_id}, but it was not found in the active list.")

def get_active_orders() -> list:
    logger.debug("Retrieving current active orders.")
    global _active_orders
    return list(_active_orders)

def reconcile_open_orders(api):
    global _active_orders
    logger.info(f"[Order Rec] Starting reconciliation for {len(_active_orders)} tracked order(s).")
    
    if not _active_orders:
        logger.debug("[Order Rec] No tracked orders to reconcile. Skipping.")
        return

    orders_to_remove = []
    
    for order_to_check in _active_orders:
        order_id = order_to_check.get('id')
        if not order_id:
            logger.warning(f"[Order Rec] Skipping order with missing ID: {order_to_check}")
            continue

        try:
            exchange_order_status = api.get_order(order_id)
            final_states = ['filled', 'canceled', 'expired', 'rejected']
            if exchange_order_status.status in final_states:
                logger.info(f"[Order Rec] Order {order_id} has reached a final state: '{exchange_order_status.status}'. Removing from active tracker.")
                orders_to_remove.append(order_id)
            else:
                logger.debug(f"[Order Rec] Order {order_id} is still active with status: {exchange_order_status.status}.")

        except Exception as e:
            logger.warning(f"[Order Rec] Could not get status for order {order_id} from exchange: {e}. Assuming it is final and removing from active tracker.", exc_info=True)
            orders_to_remove.append(order_id)

    if orders_to_remove:
        current_active_ids = {o['id'] for o in _active_orders}
        ids_to_remove = set(orders_to_remove)
        
        _active_orders = [o for o in _active_orders if o['id'] not in ids_to_remove]
        save_active_orders()

        logger.info(f"[Order Rec] Reconciliation complete. Removed {len(ids_to_remove)} order(s).")
    else:
        logger.info("[Order Rec] Reconciliation complete. All tracked orders are still active.")

def clear_all_orders():
    logger.info("Clearing all orders from internal tracker.")
    global _active_orders
    if not _active_orders:
        logger.info("Internal order tracker is already empty. No action needed.")
        return
        
    logger.warning(f"Clearing all {len(_active_orders)} orders from internal tracker.")
    _active_orders = []
    save_active_orders()

load_active_orders()
