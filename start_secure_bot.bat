@echo off
REM Bitcoin AI Trading Bot - Secure Startup Script
REM This script ensures the virtual environment is used correctly

echo.
echo ========================================================
echo   Bitcoin AI Trading Bot - Secure Startup
echo ========================================================
echo.
echo 🔐 Starting bot with secure configuration...
echo 📁 Using virtual environment: venv\Scripts\python.exe
echo.

REM Change to the bot directory
cd /d "%~dp0"

REM Run the bot using the virtual environment Python
venv\Scripts\python.exe main_bot.py

echo.
echo ========================================================
echo   <PERSON><PERSON> has stopped. Press any key to exit.
echo ========================================================
pause > nul
