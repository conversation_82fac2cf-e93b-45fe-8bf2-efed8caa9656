"""
AI Performance Analyzer Configuration
====================================

Configuration settings for the AI Performance Analyzer system.
"""

import os
from pathlib import Path
from decimal import Decimal

# Base paths
BASE_DIR = Path(__file__).parent.parent
ANALYZER_DIR = Path(__file__).parent
DATA_DIR = ANALYZER_DIR / "data"
REPORTS_DIR = ANALYZER_DIR / "reports"
INSIGHTS_DIR = ANALYZER_DIR / "insights"
LOGS_DIR = ANALYZER_DIR / "logs"

# Ensure directories exist
for directory in [DATA_DIR, REPORTS_DIR, INSIGHTS_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# Data Collection Settings
DATA_COLLECTION = {
    "trade_log_path": BASE_DIR / "trading_bot.log",
    "bot_state_path": BASE_DIR / "bot_state.json",
    "open_lots_path": BASE_DIR / "open_lots.json",
    "session_stats_path": BASE_DIR / "session_stats.json",
    "collection_interval_hours": 24,  # How often to collect data
    "max_log_lines": 50000,  # Maximum log lines to process
    "data_retention_days": 365  # How long to keep historical data
}

# Analysis Engine Settings
ANALYSIS = {
    "min_trades_for_analysis": 10,  # Minimum trades needed for meaningful analysis
    "confidence_threshold": 0.7,  # Minimum confidence for pattern recognition
    "pattern_lookback_days": 30,  # Days to look back for pattern analysis
    "statistical_significance": 0.05,  # P-value threshold for statistical tests
    "moving_average_periods": [7, 14, 30],  # Periods for performance moving averages
    "volatility_window": 14  # Days for volatility calculations
}

# Performance Metrics Configuration
METRICS = {
    "win_rate_target": Decimal("0.75"),  # Target win rate (75%)
    "sharpe_ratio_target": Decimal("1.5"),  # Target Sharpe ratio
    "max_drawdown_limit": Decimal("0.10"),  # Maximum acceptable drawdown (10%)
    "profit_factor_target": Decimal("2.0"),  # Target profit factor
    "risk_reward_target": Decimal("1.5"),  # Target risk/reward ratio
}

# Reporting Settings
REPORTING = {
    "daily_report_time": "23:30",  # Time to generate daily reports (HH:MM)
    "weekly_report_day": "Sunday",  # Day to generate weekly reports
    "monthly_report_day": 1,  # Day of month to generate monthly reports
    "report_formats": ["markdown", "json", "html"],  # Output formats
    "include_charts": True,  # Whether to generate charts
    "chart_format": "png",  # Chart image format
    "max_report_history": 90  # Days of reports to keep
}

# Feedback Integration Settings
FEEDBACK = {
    "enable_feedback": False,  # Initially disabled for safety
    "feedback_confidence_threshold": 0.8,  # Minimum confidence to apply feedback
    "max_confidence_adjustment": 0.2,  # Maximum adjustment to AI confidence
    "feedback_learning_rate": 0.1,  # How quickly to adapt based on feedback
    "validation_period_days": 7,  # Days to validate feedback before applying
    "rollback_threshold": 0.05  # Performance drop threshold for rollback
}

# Logging Configuration
LOGGING = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_handler": True,
    "console_handler": True,
    "max_file_size": "10MB",
    "backup_count": 5
}

# Database Settings (SQLite for historical data)
DATABASE = {
    "path": DATA_DIR / "performance_history.db",
    "backup_interval_hours": 24,
    "vacuum_interval_days": 7,
    "max_size_mb": 500
}

# Alert Settings
ALERTS = {
    "performance_degradation_threshold": 0.1,  # 10% performance drop
    "unusual_pattern_threshold": 2.0,  # Standard deviations from normal
    "email_alerts": False,  # Initially disabled
    "alert_cooldown_hours": 6  # Minimum time between similar alerts
}

# Feature Engineering Settings
FEATURES = {
    "technical_indicators": [
        "rsi_14", "macd", "bollinger_bands", "moving_averages",
        "volume_profile", "momentum", "volatility"
    ],
    "market_conditions": [
        "trend_direction", "volatility_regime", "volume_regime",
        "time_of_day", "day_of_week", "market_session"
    ],
    "bot_state_features": [
        "cash_ratio", "exposure_level", "open_lots_count",
        "unrealized_pnl", "recent_performance", "confidence_level"
    ]
}
