import pandas as pd

# Files to merge
numeric_file = "prepared_training_data.csv"  # output from prepare_ml_data.py (large)
labels_file = "prepared_training_data_labeled.csv"  # renamed output from label_indicators.py

# Output merged file
output_file = "full_training_data.csv"

# Load datasets
numeric_df = pd.read_csv(numeric_file)
labels_df = pd.read_csv(labels_file)

# Check if both have 'open_time' column for merging
if 'open_time' in numeric_df.columns and 'open_time' in labels_df.columns:
    merged_df = pd.merge(numeric_df, labels_df.drop(columns=[col for col in numeric_df.columns if col != 'open_time']), on='open_time', how='left')
else:
    # Fallback to index-based merge if no timestamp column
    merged_df = pd.merge(numeric_df, labels_df, left_index=True, right_index=True, how='left')

# Save merged dataset
merged_df.to_csv(output_file, index=False)
print(f"Merged dataset saved as {output_file} with {merged_df.shape[0]} rows and {merged_df.shape[1]} columns.")
