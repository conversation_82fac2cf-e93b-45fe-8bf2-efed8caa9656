import csv
from collections import Counter, defaultdict

LOG_FILE = "trade_cycle_log.csv"

def try_float(x):
    try:
        return float(x)
    except:
        return 0.0

def main():
    with open(LOG_FILE, newline="") as csvfile:
        reader = csv.DictReader(csvfile)
        data = [row for row in reader]
    if not data:
        print("No data found in log!")
        return

    # Only analyze completed trades (where trade_action is BUY/SELL and session_pnl is not "")
    trades = [row for row in data if row.get("trade_action", "").upper() in ("BUY", "SELL")]

    # Signal & outcome stats
    reason_counter = Counter()
    source_counter = Counter()
    regime_results = defaultdict(lambda: {"win": 0, "loss": 0, "trades": 0})
    conf_results = defaultdict(lambda: {"win": 0, "loss": 0, "trades": 0})
    result_by_signal = defaultdict(lambda: {"win": 0, "loss": 0, "trades": 0})

    for row in trades:
        regime = row.get("regime", "")
        conf = row.get("confidence", "")
        reason = row.get("reason_for_action", "")
        signal = row.get("signal_source", "")
        pnl = try_float(row.get("session_pnl", 0))
        action = row.get("trade_action", "").upper()

        # Basic win/loss determination
        is_win = pnl > 0.01
        is_loss = pnl < -0.01

        # Tally by regime
        regime_results[regime]["trades"] += 1
        if is_win:
            regime_results[regime]["win"] += 1
        elif is_loss:
            regime_results[regime]["loss"] += 1

        # Tally by confidence
        conf_results[conf]["trades"] += 1
        if is_win:
            conf_results[conf]["win"] += 1
        elif is_loss:
            conf_results[conf]["loss"] += 1

        # Tally by reason/signal
        reason_counter[reason] += 1
        source_counter[signal] += 1
        result_by_signal[signal]["trades"] += 1
        if is_win:
            result_by_signal[signal]["win"] += 1
        elif is_loss:
            result_by_signal[signal]["loss"] += 1

    print("="*44)
    print("WHAT WORKED? TRADE OUTCOME ANALYSIS")
    print("="*44)

    print("Wins/Losses by Market Regime:")
    for regime, stats in sorted(regime_results.items()):
        win, loss, trades = stats["win"], stats["loss"], stats["trades"]
        print(f"  {regime or '(blank)'}: {win} wins / {loss} losses / {trades} trades" +
              (f"  Win rate: {win/(win+loss)*100:.1f}%" if (win+loss) else ""))

    print("\nWins/Losses by AI Confidence Level:")
    for conf, stats in sorted(conf_results.items()):
        win, loss, trades = stats["win"], stats["loss"], stats["trades"]
        print(f"  {conf or '(blank)'}: {win} wins / {loss} losses / {trades} trades" +
              (f"  Win rate: {win/(win+loss)*100:.1f}%" if (win+loss) else ""))

    print("\nMost Common Reason for Action:")
    for reason, count in reason_counter.most_common(5):
        if reason:
            print(f"  {reason}: {count} times")

    print("\nSignal Source Effectiveness:")
    for sig, stats in sorted(result_by_signal.items(), key=lambda x: x[1]["trades"], reverse=True):
        trades = stats["trades"]
        win, loss = stats["win"], stats["loss"]
        if trades:
            print(f"  {sig or '(blank)'}: {win} wins / {loss} losses / {trades} trades" +
                  (f"  Win rate: {win/(win+loss)*100:.1f}%" if (win+loss) else ""))

    print("\nMost Common Signal Sources:")
    for sig, count in source_counter.most_common(5):
        if sig:
            print(f"  {sig}: {count} times")

    print("="*44)
    print("For more trades/data, results will be more meaningful!")
    print("="*44)

if __name__ == "__main__":
    main()
