import sys

# Hardcode the file path to avoid shell quoting issues
file_path = "C:\Users\<USER>\Documents\Bitcoin ai agent\binance_btcusdt_1m.csv"
num_lines = int(sys.argv[1])
mode = sys.argv[2] # 'head' or 'tail'

try:
    with open(file_path, 'r') as f:
        if mode == 'head':
            for i, line in enumerate(f):
                if i >= num_lines:
                    break
                print(line.strip())
        elif mode == 'tail':
            lines = f.readlines()
            for line in lines[-num_lines:]:
                print(line.strip())
except Exception as e:
    print(f"Error: {e}")