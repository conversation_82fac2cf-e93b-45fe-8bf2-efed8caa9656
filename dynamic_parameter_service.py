import os
import json
import requests
import config
from google.generativeai import GenerativeModel
import time
from decimal import Decimal, InvalidOperation
from datetime import datetime
import logging

logger = logging.getLogger("TradingBotApp.DynamicParams")
debug_logger = logging.getLogger("TradingBotApp.Debug")

def _json_default_serializer(obj):
    if isinstance(obj, Decimal):
        return str(obj)
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

def get_dynamic_parameters(market_data: dict, ai_data: dict, trade_history: list[dict] = None) -> dict:
    logger.info("Calculating dynamic trading parameters.")

    # Ensure market_data and ai_data are dictionaries
    if not isinstance(market_data, dict):
        logger.error(f"market_data is not a dict: {type(market_data)}, value: {market_data}")
        market_data = {}

    if not isinstance(ai_data, dict):
        logger.error(f"ai_data is not a dict: {type(ai_data)}, value: {ai_data}")
        ai_data = {"confidence_level": "MEDIUM", "decision": "HOLD"}

    debug_logger.debug(f"Input market_data: {json.dumps(market_data, default=_json_default_serializer)}")
    debug_logger.debug(f"Input ai_data: {json.dumps(ai_data, default=_json_default_serializer)}")
    debug_logger.debug(f"Input trade_history (last 5): {json.dumps(trade_history[-5:] if trade_history else [], default=_json_default_serializer)}")

    risk_percent = config.RISK_PERCENT
    stop_percent = config.STOP_PERCENT
    profit_targets = [Decimal(str(pt)) for pt in config.TAKE_PROFITS]
    profit_shares = [Decimal(str(ps)) for ps in config.TAKE_PROFIT_SHARES]

    regime = market_data.get("regime", "unknown")
    volatility = Decimal(str(market_data.get("volatility", "0.0")))
    ai_conf = ai_data.get("confidence_level", "MEDIUM")
    
    recent_outcomes = []
    if trade_history:
        recent_outcomes = [Decimal(str(t.get("realized_pl", "0.0"))) for t in trade_history[-5:] if "realized_pl" in t]

    logger.debug(f"Initial rules-based parameters: Risk={risk_percent:.4f}, Stop={stop_percent:.4f}, Profit Targets={profit_targets}, Shares={profit_shares}")
    logger.debug(f"Market Regime: {regime}, Volatility: {volatility:.2f}, AI Confidence: {ai_conf}")

    if regime in ("trending", "trending_volatile") and ai_conf == "HIGH":
        risk_percent = Decimal("0.015")
        profit_targets = [Decimal("0.013"), Decimal("0.022"), Decimal("0.035")]
        logger.debug("Adjusting parameters for trending/volatile regime with HIGH AI confidence.")
    elif regime in ("quiet", "ranging") or ai_conf == "LOW":
        risk_percent = Decimal("0.006")
        profit_targets = [Decimal("0.008"), Decimal("0.014"), Decimal("0.022")]
        logger.debug("Adjusting parameters for quiet/ranging regime or LOW AI confidence.")

    if volatility > Decimal("60"):
        stop_percent = Decimal("0.028")
        logger.debug(f"Adjusting stop_percent for high volatility ({volatility:.2f}).")
    elif volatility < Decimal("20"):
        stop_percent = Decimal("0.013")
        logger.debug(f"Adjusting stop_percent for low volatility ({volatility:.2f}).")

    if recent_outcomes and sum(1 for x in recent_outcomes[-3:] if x < Decimal("0")) >= 3:
        risk_percent = max(risk_percent * Decimal("0.6"), config.MIN_DYNAMIC_RISK_PERCENT)
        profit_targets = [max(pt * Decimal("0.8"), config.MIN_PROFIT_TARGET_PCT) for pt in profit_targets]
        logger.warning("Adjusting parameters for recent loss streak (3+ losses).")

    logger.debug(f"Rules-based adjusted parameters: Risk={risk_percent:.4f}, Stop={stop_percent:.4f}, Profit Targets={profit_targets}, Shares={profit_shares}")

    gemini_response = call_gemini_for_params(market_data, ai_data, trade_history)
    
    if gemini_response and isinstance(gemini_response, dict):
        logger.info("Received Gemini response for dynamic parameters. Applying refinements.")
        try:
            gemini_risk = Decimal(str(gemini_response.get("risk_percent", float(risk_percent))))
            gemini_stop = Decimal(str(gemini_response.get("stop_percent", float(stop_percent))))
            
            gemini_profit_targets_raw = gemini_response.get("profit_targets", [float(pt) for pt in profit_targets])
            gemini_profit_targets = [Decimal(str(p)) for p in gemini_profit_targets_raw]

            gemini_profit_shares_raw = gemini_response.get("profit_shares", [float(ps) for ps in profit_shares])
            gemini_profit_shares = [Decimal(str(s)) for s in gemini_profit_shares_raw]

            risk_percent = max(config.MIN_DYNAMIC_RISK_PERCENT, min(gemini_risk, config.MAX_DYNAMIC_RISK_PERCENT))
            stop_percent = max(config.MIN_STOP_LOSS_PCT, min(gemini_stop, config.MAX_STOP_LOSS_PCT))
            
            clamped_profit_targets = []
            for p_target in gemini_profit_targets:
                clamped_target = max(config.MIN_PROFIT_TARGET_PCT, min(p_target, config.MAX_PROFIT_TARGET_PCT))
                clamped_profit_targets.append(clamped_target)
            profit_targets = clamped_profit_targets
            
            if gemini_profit_shares and sum(gemini_profit_shares) > Decimal("0"):
                sum_shares = sum(gemini_profit_shares)
                if abs(sum_shares - Decimal("1.0")) > Decimal("1e-9"):
                     profit_shares = [s / sum_shares for s in gemini_profit_shares]
                     logger.debug(f"Normalized Gemini profit shares. Original sum: {sum_shares:.4f}.")
                else:
                    profit_shares = gemini_profit_shares
            else:
                logger.warning("Gemini provided invalid or empty profit shares. Using default shares.")
                profit_shares = [Decimal("0.5"), Decimal("0.3"), Decimal("0.2")]

            logger.info(f"Gemini refined parameters: Risk={risk_percent:.4f}, Stop={stop_percent:.4f}, Profit Targets={profit_targets}, Shares={profit_shares}")

        except (InvalidOperation, TypeError) as e:
            logger.error(f"Gemini API (Params): Invalid number format in response: {e}. Using rules-based values.")
        except Exception as e:
            logger.error(f"Gemini API (Params): Error processing Gemini dynamic parameters: {e}. Using rules-based values.", exc_info=True)

    final_params = {
        "risk_percent": float(risk_percent.quantize(Decimal("0.00001"))),
        "stop_percent": float(stop_percent.quantize(Decimal("0.00001"))),
        "profit_targets": [float(pt.quantize(Decimal("0.00001"))) for pt in profit_targets],
        "profit_shares": [float(ps.quantize(Decimal("0.00001"))) for ps in profit_shares]
    }
    logger.info(f"Final dynamic parameters for cycle: {final_params}")
    return final_params

def call_gemini_for_params(market_data: dict, ai_data: dict, trade_history: list[dict] | None) -> dict | None:
    logger.debug("Calling Gemini model for dynamic parameter suggestions.")
    try:
        if not hasattr(call_gemini_for_params, "model"):
            call_gemini_for_params.model = GenerativeModel(config.GEMINI_MODEL if hasattr(config, "GEMINI_MODEL") else "gemini-1.5-flash")
            logger.info(f"Initialized Gemini model for dynamic parameters: {call_gemini_for_params.model.model_name}")

        prompt_data_serializable = json.dumps({
            "market_data": market_data,
            "ai_main_decision_context": ai_data,
            "recent_trade_history": trade_history[-10:] if trade_history else [],
        }, indent=2, default=_json_default_serializer)

        prompt = (
            "You are an expert financial parameter adjustment AI. "
            "Given the current market conditions, the main AI's trading decision, "
            "details of any open lots (including individual P/L), and recent trade history, "
            "suggest optimal trading parameters for the NEXT trade cycle. "
            "Focus on capital preservation and adapting to volatility and performance.\n\n"
            f"Context:\n{prompt_data_serializable}\n\n"
            "Your task is to analyze this data and provide a JSON object with these fields:\n"
            "  - risk_percent: float (e.g., 0.01 for 1% of equity for new trades, considering current exposure from open lots)\n"
            "  - stop_percent: float (e.g., 0.02 for 2% stop-loss from entry for new trades, or adjustment for existing lots)\n"
            "  - profit_targets: list of 2-3 floats (e.g., [0.01, 0.02, 0.03]) for staggered profit taking.\n"
            "  - profit_shares: list of 2-3 floats (e.g., [0.5, 0.3, 0.2]) - must sum to 1.0, corresponding to profit_targets.\n\n"
            "If market_data.open_lots_details is not empty, consider the 'total_unrealized_pl' and individual 'unrealized_pl_lot' to adjust risk. "
            "If 'total_unrealized_pl' is significantly negative, suggest more conservative parameters (lower risk_percent, tighter stop_percent). "
            "If 'total_unrealized_pl' is positive, parameters can be less conservative but still prudent. "
            "Base your suggestions on the provided 'market_data.regime' and 'market_data.volatility'.\n"
            "Respond ONLY with the JSON. No extra text or markdown."
        )
        debug_logger.debug(f"Prompt for dynamic parameters: {prompt}")

        response_gen_obj = None
        retries = 3
        for attempt in range(retries):
            try:
                logger.debug(f"Attempt {attempt + 1}/{retries} to get dynamic parameters from Gemini.")
                response_gen_obj = call_gemini_for_params.model.generate_content(
                    prompt,
                    request_options={"timeout": config.GEMINI_TIMEOUT if hasattr(config, "GEMINI_TIMEOUT") else 60}
                )
                response_text = response_gen_obj.text.strip()
                logger.debug(f"Raw Gemini response for dynamic parameters: {response_text}")
                
                json_start = response_text.find('{')
                json_end = response_text.rfind('}')
                if json_start != -1 and json_end != -1 and json_end > json_start:
                    response_json_str = response_text[json_start:json_end + 1]
                else:
                    response_json_str = response_text
                    logger.warning("Could not extract JSON block from Gemini response. Attempting to parse full response.")

                parsed_params = json.loads(response_json_str)
                
                if not all(k in parsed_params for k in ["risk_percent", "stop_percent", "profit_targets", "profit_shares"]):
                    raise ValueError("Missing one or more required keys in Gemini parameter response.")
                if not (isinstance(parsed_params["profit_targets"], list) and isinstance(parsed_params["profit_shares"], list) and \
                        len(parsed_params["profit_targets"]) == len(parsed_params["profit_shares"])):
                    raise ValueError("profit_targets and profit_shares must be lists of the same length.")

                logger.info(f"Successfully parsed Gemini dynamic parameters: {parsed_params}")
                return parsed_params
            
            except json.JSONDecodeError as e:
                raw_text_for_log = getattr(response_gen_obj, 'text', '[No response text attribute]')
                logger.error(f"Gemini API (Params): Failed to decode response as JSON (attempt {attempt + 1}/{retries}): {e}. Raw: '{raw_text_for_log[:500]}...'", exc_info=True)
                if attempt < retries - 1: time.sleep(2 * (attempt + 1))
            except ValueError as ve:
                logger.error(f"Gemini API (Params): Invalid structure in response (attempt {attempt + 1}/{retries}): {ve}")
                if attempt < retries - 1: time.sleep(2 * (attempt + 1))
            except Exception as e:
                logger.error(f"Gemini API (Params) error (attempt {attempt + 1}/{retries}): {type(e).__name__} - {e}", exc_info=True)
                if attempt < retries - 1: time.sleep(2 * (attempt + 1))
        
        logger.error(f"Gemini API (Params): Failed to get valid JSON from Gemini after {retries} attempts for dynamic parameters. Returning None.")
        return None

    except Exception as e:
        logger.critical(f"Gemini API (Params) configuration/call error in dynamic_parameter_service: {e}. Check API key and network.", exc_info=True)
        return None