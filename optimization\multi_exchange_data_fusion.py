#!/usr/bin/env python3
"""
Multi-Exchange Data Fusion System - Phase 5 Implementation

This system collects and fuses data from multiple cryptocurrency exchanges to provide:
1. Higher quality price signals through consensus
2. Arbitrage opportunity detection
3. Cross-exchange volume analysis
4. Reduced single-point-of-failure risk
5. Enhanced market microstructure insights

Supported Exchanges:
- Binance (primary - WebSocket real-time)
- Coinbase Pro (REST API)
- Kraken (REST API)
- Bitfinex (REST API)

Author: Bitcoin AI Trading Bot - Phase 5 Multi-Exchange Fusion
Date: July 30, 2025
"""

import asyncio
import aiohttp
import json
import logging
import threading
import time
import websocket
from collections import deque, defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from statistics import mean, median, stdev
import pandas as pd

from error_handler import ErrorSeverity, ErrorCategory, handle_error
from error_utils import SystemError, DataError, safe_execute

logger = logging.getLogger("TradingBotApp.MultiExchangeFusion")

@dataclass
class ExchangeData:
    """Standardized exchange data structure."""
    exchange: str
    symbol: str
    price: float
    bid: float
    ask: float
    volume_24h: float
    timestamp: datetime
    spread: float
    quality_score: float = 1.0
    
    def __post_init__(self):
        """Calculate derived metrics."""
        if self.bid > 0 and self.ask > 0:
            self.spread = ((self.ask - self.bid) / self.price) * 100
        else:
            self.spread = 0.0

@dataclass
class FusedMarketData:
    """Fused market data from multiple exchanges."""
    consensus_price: float
    weighted_price: float
    price_variance: float
    best_bid: float
    best_ask: float
    total_volume_24h: float
    exchange_count: int
    arbitrage_opportunities: List[Dict[str, Any]]
    quality_score: float
    timestamp: datetime
    exchange_data: Dict[str, ExchangeData]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for compatibility."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['exchange_data'] = {k: asdict(v) for k, v in self.exchange_data.items()}
        return result

class MultiExchangeDataFusion:
    """
    Multi-exchange data fusion system for enhanced market analysis.
    
    This system provides:
    - Real-time data from multiple exchanges
    - Consensus price calculation
    - Arbitrage opportunity detection
    - Quality scoring and outlier detection
    - Fallback mechanisms for exchange failures
    """
    
    def __init__(self):
        """Initialize the multi-exchange data fusion system."""
        
        # Exchange configurations
        self.exchanges = {
            'binance': {
                'name': 'Binance',
                'symbol': 'BTCUSDT',
                'weight': 0.4,  # Highest weight due to volume
                'api_url': 'https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT',
                'websocket_url': 'wss://stream.binance.com:9443/ws/btcusdt@ticker',
                'enabled': True
            },
            'coinbase': {
                'name': 'Coinbase Pro',
                'symbol': 'BTC-USD',
                'weight': 0.25,
                'api_url': 'https://api.exchange.coinbase.com/products/BTC-USD/ticker',
                'enabled': True
            },
            'kraken': {
                'name': 'Kraken',
                'symbol': 'XBTUSD',
                'weight': 0.2,
                'api_url': 'https://api.kraken.com/0/public/Ticker?pair=XBTUSD',
                'enabled': True
            },
            'bitfinex': {
                'name': 'Bitfinex',
                'symbol': 'tBTCUSD',
                'weight': 0.15,
                'api_url': 'https://api-pub.bitfinex.com/v2/ticker/tBTCUSD',
                'enabled': True
            }
        }
        
        # Data storage
        self.exchange_data: Dict[str, ExchangeData] = {}
        self.historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.fused_data_history: deque = deque(maxlen=500)
        
        # Threading and synchronization
        self.data_lock = threading.RLock()
        self.shutdown_event = threading.Event()
        self.update_threads: Dict[str, threading.Thread] = {}
        
        # Configuration
        self.update_interval = 5  # seconds
        self.arbitrage_threshold = 0.1  # 0.1% minimum arbitrage opportunity
        self.quality_threshold = 0.7  # Minimum quality score to include data
        self.max_price_deviation = 2.0  # 2% maximum deviation from consensus
        
        # Performance tracking
        self.stats = {
            'total_updates': 0,
            'successful_updates': 0,
            'arbitrage_opportunities_found': 0,
            'exchange_failures': defaultdict(int),
            'last_update': None
        }
        
        logger.info("Multi-Exchange Data Fusion System initialized")
        logger.info(f"Enabled exchanges: {[ex['name'] for ex in self.exchanges.values() if ex['enabled']]}")
    
    def start(self):
        """Start the multi-exchange data collection system."""
        
        logger.info("Starting Multi-Exchange Data Fusion System...")
        
        # Start data collection threads for each exchange
        for exchange_id, config in self.exchanges.items():
            if config['enabled']:
                thread = threading.Thread(
                    target=self._run_exchange_collector,
                    args=(exchange_id, config),
                    daemon=True,
                    name=f"ExchangeCollector-{exchange_id}"
                )
                thread.start()
                self.update_threads[exchange_id] = thread
                logger.info(f"Started data collector for {config['name']}")
        
        # Start data fusion thread
        fusion_thread = threading.Thread(
            target=self._run_data_fusion,
            daemon=True,
            name="DataFusion"
        )
        fusion_thread.start()
        self.update_threads['fusion'] = fusion_thread
        
        logger.info("Multi-Exchange Data Fusion System started successfully")
    
    def stop(self):
        """Stop the multi-exchange data collection system."""
        
        logger.info("Stopping Multi-Exchange Data Fusion System...")
        self.shutdown_event.set()
        
        # Wait for threads to finish
        for thread_name, thread in self.update_threads.items():
            if thread.is_alive():
                logger.info(f"Waiting for {thread_name} thread to stop...")
                thread.join(timeout=5)
        
        logger.info("Multi-Exchange Data Fusion System stopped")

    def _run_exchange_collector(self, exchange_id: str, config: Dict[str, Any]):
        """Run data collection for a specific exchange."""

        logger.info(f"Starting data collector for {config['name']}")

        while not self.shutdown_event.is_set():
            try:
                # Collect data from exchange
                exchange_data = self._fetch_exchange_data(exchange_id, config)

                if exchange_data:
                    with self.data_lock:
                        self.exchange_data[exchange_id] = exchange_data
                        self.historical_data[exchange_id].append(exchange_data)

                    logger.debug(f"Updated data for {config['name']}: ${exchange_data.price:.2f}")
                    self.stats['successful_updates'] += 1
                else:
                    self.stats['exchange_failures'][exchange_id] += 1
                    logger.warning(f"Failed to fetch data from {config['name']}")

                self.stats['total_updates'] += 1

            except Exception as e:
                self.stats['exchange_failures'][exchange_id] += 1
                logger.error(f"Error collecting data from {config['name']}: {e}")

                # Create error for tracking
                error = SystemError(
                    f"Exchange data collection error for {config['name']}: {e}",
                    ErrorSeverity.MEDIUM,
                    {"exchange": exchange_id, "operation": "data_collection"},
                    e
                )
                handle_error(error)

            # Wait before next update
            self.shutdown_event.wait(self.update_interval)

        logger.info(f"Data collector for {config['name']} stopped")

    def _fetch_exchange_data(self, exchange_id: str, config: Dict[str, Any]) -> Optional[ExchangeData]:
        """Fetch data from a specific exchange."""

        try:
            if exchange_id == 'binance':
                return self._fetch_binance_data(config)
            elif exchange_id == 'coinbase':
                return self._fetch_coinbase_data(config)
            elif exchange_id == 'kraken':
                return self._fetch_kraken_data(config)
            elif exchange_id == 'bitfinex':
                return self._fetch_bitfinex_data(config)
            else:
                logger.error(f"Unknown exchange: {exchange_id}")
                return None

        except Exception as e:
            logger.error(f"Error fetching data from {exchange_id}: {e}")
            return None

    def _fetch_binance_data(self, config: Dict[str, Any]) -> Optional[ExchangeData]:
        """Fetch data from Binance API."""

        import requests

        try:
            response = requests.get(config['api_url'], timeout=10)
            response.raise_for_status()
            data = response.json()

            return ExchangeData(
                exchange='binance',
                symbol=config['symbol'],
                price=float(data['lastPrice']),
                bid=float(data['bidPrice']),
                ask=float(data['askPrice']),
                volume_24h=float(data['volume']),
                timestamp=datetime.now(),
                spread=0.0  # Will be calculated in __post_init__
            )

        except Exception as e:
            logger.error(f"Error fetching Binance data: {e}")
            return None

    def _fetch_coinbase_data(self, config: Dict[str, Any]) -> Optional[ExchangeData]:
        """Fetch data from Coinbase Pro API."""

        import requests

        try:
            response = requests.get(config['api_url'], timeout=10)
            response.raise_for_status()
            data = response.json()

            return ExchangeData(
                exchange='coinbase',
                symbol=config['symbol'],
                price=float(data['price']),
                bid=float(data['bid']),
                ask=float(data['ask']),
                volume_24h=float(data['volume']),
                timestamp=datetime.now(),
                spread=0.0
            )

        except Exception as e:
            logger.error(f"Error fetching Coinbase data: {e}")
            return None

    def _fetch_kraken_data(self, config: Dict[str, Any]) -> Optional[ExchangeData]:
        """Fetch data from Kraken API."""

        import requests

        try:
            response = requests.get(config['api_url'], timeout=10)
            response.raise_for_status()
            data = response.json()

            if 'result' in data and 'XXBTZUSD' in data['result']:
                ticker = data['result']['XXBTZUSD']

                return ExchangeData(
                    exchange='kraken',
                    symbol=config['symbol'],
                    price=float(ticker['c'][0]),  # Last trade price
                    bid=float(ticker['b'][0]),    # Best bid
                    ask=float(ticker['a'][0]),    # Best ask
                    volume_24h=float(ticker['v'][1]),  # 24h volume
                    timestamp=datetime.now(),
                    spread=0.0
                )
            else:
                logger.error("Unexpected Kraken API response format")
                return None

        except Exception as e:
            logger.error(f"Error fetching Kraken data: {e}")
            return None

    def _fetch_bitfinex_data(self, config: Dict[str, Any]) -> Optional[ExchangeData]:
        """Fetch data from Bitfinex API."""

        import requests

        try:
            response = requests.get(config['api_url'], timeout=10)
            response.raise_for_status()
            data = response.json()

            if len(data) >= 10:
                return ExchangeData(
                    exchange='bitfinex',
                    symbol=config['symbol'],
                    price=float(data[6]),   # Last price
                    bid=float(data[0]),     # Bid
                    ask=float(data[2]),     # Ask
                    volume_24h=float(data[7]),  # 24h volume
                    timestamp=datetime.now(),
                    spread=0.0
                )
            else:
                logger.error("Unexpected Bitfinex API response format")
                return None

        except Exception as e:
            logger.error(f"Error fetching Bitfinex data: {e}")
            return None

    def _run_data_fusion(self):
        """Run the data fusion process to combine exchange data."""

        logger.info("Starting data fusion process")

        while not self.shutdown_event.is_set():
            try:
                fused_data = self._create_fused_data()

                if fused_data:
                    with self.data_lock:
                        self.fused_data_history.append(fused_data)

                    self.stats['last_update'] = datetime.now()

                    # Log arbitrage opportunities
                    if fused_data.arbitrage_opportunities:
                        self.stats['arbitrage_opportunities_found'] += len(fused_data.arbitrage_opportunities)
                        for arb in fused_data.arbitrage_opportunities:
                            logger.info(f"Arbitrage opportunity: {arb['profit_pct']:.3f}% between {arb['buy_exchange']} and {arb['sell_exchange']}")

            except Exception as e:
                logger.error(f"Error in data fusion process: {e}")

                error = SystemError(
                    f"Data fusion process error: {e}",
                    ErrorSeverity.MEDIUM,
                    {"operation": "data_fusion"},
                    e
                )
                handle_error(error)

            # Wait before next fusion
            self.shutdown_event.wait(2)  # Faster fusion updates

        logger.info("Data fusion process stopped")

    def _create_fused_data(self) -> Optional[FusedMarketData]:
        """Create fused market data from all exchanges."""

        with self.data_lock:
            if not self.exchange_data:
                return None

            # Filter valid data
            valid_data = {}
            for exchange_id, data in self.exchange_data.items():
                if self._is_data_valid(data):
                    valid_data[exchange_id] = data

            if len(valid_data) < 2:
                logger.warning(f"Insufficient valid exchange data: {len(valid_data)} exchanges")
                return None

            # Calculate consensus metrics
            prices = [data.price for data in valid_data.values()]
            volumes = [data.volume_24h for data in valid_data.values()]

            # Weighted price calculation
            total_weight = sum(self.exchanges[ex_id]['weight'] for ex_id in valid_data.keys())
            weighted_price = sum(
                data.price * self.exchanges[ex_id]['weight']
                for ex_id, data in valid_data.items()
            ) / total_weight

            # Consensus price (median for robustness)
            consensus_price = median(prices)

            # Price variance
            price_variance = stdev(prices) if len(prices) > 1 else 0.0

            # Best bid/ask across exchanges
            best_bid = max(data.bid for data in valid_data.values())
            best_ask = min(data.ask for data in valid_data.values())

            # Total volume
            total_volume = sum(volumes)

            # Quality score based on data freshness and consistency
            quality_score = self._calculate_quality_score(valid_data, price_variance)

            # Detect arbitrage opportunities
            arbitrage_opportunities = self._detect_arbitrage_opportunities(valid_data)

            return FusedMarketData(
                consensus_price=consensus_price,
                weighted_price=weighted_price,
                price_variance=price_variance,
                best_bid=best_bid,
                best_ask=best_ask,
                total_volume_24h=total_volume,
                exchange_count=len(valid_data),
                arbitrage_opportunities=arbitrage_opportunities,
                quality_score=quality_score,
                timestamp=datetime.now(),
                exchange_data=valid_data
            )

    def _is_data_valid(self, data: ExchangeData) -> bool:
        """Check if exchange data is valid and recent."""

        # Check data age (should be recent)
        age_seconds = (datetime.now() - data.timestamp).total_seconds()
        if age_seconds > 60:  # Data older than 1 minute
            return False

        # Check price sanity (should be reasonable for Bitcoin)
        if data.price < 1000 or data.price > 1000000:
            return False

        # Check bid/ask sanity
        if data.bid <= 0 or data.ask <= 0 or data.bid >= data.ask:
            return False

        # Check spread sanity (should be reasonable)
        if data.spread > 5.0:  # More than 5% spread is suspicious
            return False

        return True

    def _calculate_quality_score(self, valid_data: Dict[str, ExchangeData], price_variance: float) -> float:
        """Calculate overall quality score for the fused data."""

        # Base score
        score = 1.0

        # Penalize high price variance (inconsistency between exchanges)
        if price_variance > 0:
            variance_penalty = min(price_variance / 100, 0.5)  # Max 50% penalty
            score -= variance_penalty

        # Reward more exchanges
        exchange_bonus = min(len(valid_data) * 0.1, 0.3)  # Max 30% bonus
        score += exchange_bonus

        # Penalize wide spreads
        avg_spread = mean(data.spread for data in valid_data.values())
        spread_penalty = min(avg_spread / 10, 0.2)  # Max 20% penalty
        score -= spread_penalty

        return max(0.0, min(1.0, score))

    def _detect_arbitrage_opportunities(self, valid_data: Dict[str, ExchangeData]) -> List[Dict[str, Any]]:
        """Detect arbitrage opportunities between exchanges."""

        opportunities = []

        # Compare all exchange pairs
        exchanges = list(valid_data.keys())
        for i in range(len(exchanges)):
            for j in range(i + 1, len(exchanges)):
                ex1_id, ex2_id = exchanges[i], exchanges[j]
                ex1_data, ex2_data = valid_data[ex1_id], valid_data[ex2_id]

                # Calculate potential profit in both directions
                # Buy on ex1, sell on ex2
                profit_1_to_2 = ((ex2_data.bid - ex1_data.ask) / ex1_data.ask) * 100

                # Buy on ex2, sell on ex1
                profit_2_to_1 = ((ex1_data.bid - ex2_data.ask) / ex2_data.ask) * 100

                # Check if profitable above threshold
                if profit_1_to_2 > self.arbitrage_threshold:
                    opportunities.append({
                        'buy_exchange': self.exchanges[ex1_id]['name'],
                        'sell_exchange': self.exchanges[ex2_id]['name'],
                        'buy_price': ex1_data.ask,
                        'sell_price': ex2_data.bid,
                        'profit_pct': profit_1_to_2,
                        'profit_usd_per_btc': ex2_data.bid - ex1_data.ask,
                        'timestamp': datetime.now()
                    })

                if profit_2_to_1 > self.arbitrage_threshold:
                    opportunities.append({
                        'buy_exchange': self.exchanges[ex2_id]['name'],
                        'sell_exchange': self.exchanges[ex1_id]['name'],
                        'buy_price': ex2_data.ask,
                        'sell_price': ex1_data.bid,
                        'profit_pct': profit_2_to_1,
                        'profit_usd_per_btc': ex1_data.bid - ex2_data.ask,
                        'timestamp': datetime.now()
                    })

        return opportunities

    def get_latest_fused_data(self) -> Optional[FusedMarketData]:
        """Get the latest fused market data."""

        with self.data_lock:
            if self.fused_data_history:
                return self.fused_data_history[-1]
            return None

    def get_consensus_price(self) -> Optional[float]:
        """Get the current consensus price."""

        latest_data = self.get_latest_fused_data()
        return latest_data.consensus_price if latest_data else None

    def get_weighted_price(self) -> Optional[float]:
        """Get the current weighted price."""

        latest_data = self.get_latest_fused_data()
        return latest_data.weighted_price if latest_data else None

    def get_arbitrage_opportunities(self) -> List[Dict[str, Any]]:
        """Get current arbitrage opportunities."""

        latest_data = self.get_latest_fused_data()
        return latest_data.arbitrage_opportunities if latest_data else []

    def get_exchange_data(self, exchange_id: str) -> Optional[ExchangeData]:
        """Get data for a specific exchange."""

        with self.data_lock:
            return self.exchange_data.get(exchange_id)

    def get_system_stats(self) -> Dict[str, Any]:
        """Get system performance statistics."""

        with self.data_lock:
            stats = self.stats.copy()

            # Calculate success rate
            if stats['total_updates'] > 0:
                stats['success_rate'] = stats['successful_updates'] / stats['total_updates']
            else:
                stats['success_rate'] = 0.0

            # Add current data status
            stats['active_exchanges'] = len(self.exchange_data)
            stats['data_age_seconds'] = {}

            for ex_id, data in self.exchange_data.items():
                age = (datetime.now() - data.timestamp).total_seconds()
                stats['data_age_seconds'][ex_id] = age

            return stats

    def is_healthy(self) -> bool:
        """Check if the system is healthy and providing good data."""

        latest_data = self.get_latest_fused_data()

        if not latest_data:
            return False

        # Check data freshness
        age_seconds = (datetime.now() - latest_data.timestamp).total_seconds()
        if age_seconds > 30:  # Data should be fresh
            return False

        # Check minimum exchange count
        if latest_data.exchange_count < 2:
            return False

        # Check quality score
        if latest_data.quality_score < self.quality_threshold:
            return False

        return True

    def get_enhanced_market_data(self) -> Dict[str, Any]:
        """
        Get enhanced market data for integration with existing bot systems.

        This method provides a drop-in replacement for existing price data
        with additional multi-exchange insights.
        """

        latest_data = self.get_latest_fused_data()

        if not latest_data:
            logger.warning("No fused data available, returning empty dict")
            return {}

        # Create enhanced data structure compatible with existing systems
        enhanced_data = {
            # Core price data (compatible with existing systems)
            'price': latest_data.consensus_price,
            'avg_price': latest_data.weighted_price,
            'current_price': latest_data.consensus_price,

            # Enhanced multi-exchange data
            'consensus_price': latest_data.consensus_price,
            'weighted_price': latest_data.weighted_price,
            'price_variance': latest_data.price_variance,
            'price_variance_pct': (latest_data.price_variance / latest_data.consensus_price) * 100,

            # Best bid/ask across all exchanges
            'best_bid': latest_data.best_bid,
            'best_ask': latest_data.best_ask,
            'cross_exchange_spread': ((latest_data.best_ask - latest_data.best_bid) / latest_data.consensus_price) * 100,

            # Volume data
            'total_volume_24h': latest_data.total_volume_24h,
            'exchange_count': latest_data.exchange_count,

            # Quality metrics
            'data_quality_score': latest_data.quality_score,
            'data_freshness': (datetime.now() - latest_data.timestamp).total_seconds(),

            # Arbitrage data
            'arbitrage_opportunities': len(latest_data.arbitrage_opportunities),
            'max_arbitrage_profit_pct': max([arb['profit_pct'] for arb in latest_data.arbitrage_opportunities], default=0.0),

            # Individual exchange data
            'exchange_prices': {ex_id: data.price for ex_id, data in latest_data.exchange_data.items()},
            'exchange_spreads': {ex_id: data.spread for ex_id, data in latest_data.exchange_data.items()},

            # Metadata
            'timestamp': latest_data.timestamp.isoformat(),
            'data_source': 'multi_exchange_fusion',
            'fusion_enabled': True
        }

        return enhanced_data


# Global instance for the bot to use
_multi_exchange_fusion = None

def get_multi_exchange_fusion() -> MultiExchangeDataFusion:
    """Get the global multi-exchange fusion instance."""

    global _multi_exchange_fusion

    if _multi_exchange_fusion is None:
        _multi_exchange_fusion = MultiExchangeDataFusion()

    return _multi_exchange_fusion

def start_multi_exchange_system():
    """Start the multi-exchange data fusion system."""

    fusion_system = get_multi_exchange_fusion()
    fusion_system.start()
    return fusion_system

def stop_multi_exchange_system():
    """Stop the multi-exchange data fusion system."""

    global _multi_exchange_fusion

    if _multi_exchange_fusion:
        _multi_exchange_fusion.stop()

def get_enhanced_market_data() -> Dict[str, Any]:
    """
    Get enhanced market data from the multi-exchange fusion system.

    This is the main interface function for integration with the bot.
    """

    fusion_system = get_multi_exchange_fusion()
    return fusion_system.get_enhanced_market_data()

def get_consensus_price() -> Optional[float]:
    """Get the current consensus price from all exchanges."""

    fusion_system = get_multi_exchange_fusion()
    return fusion_system.get_consensus_price()

def get_arbitrage_opportunities() -> List[Dict[str, Any]]:
    """Get current arbitrage opportunities."""

    fusion_system = get_multi_exchange_fusion()
    return fusion_system.get_arbitrage_opportunities()

def is_system_healthy() -> bool:
    """Check if the multi-exchange system is healthy."""

    fusion_system = get_multi_exchange_fusion()
    return fusion_system.is_healthy()
