# Target‑Return Governor (ISA‑Style) — Implementation Guide for **Gemini CLI**

**Objective**  
Implement a daily performance‑target “governor” so the bot stops opening **new BUY trades** once today’s realised + unrealised P&L reaches ~5 % annualised (≈ 0.013 % per day), while leaving all other behaviour unchanged.

---

## Global Rules

1. **One step at a time** — complete the current step, run tests, confirm with the user, *then* continue.  
2. **No collateral edits** — do not touch unrelated code, imports, logging, or whitespace.  
3. All monetary/percentage literals **must use `Decimal`**.  
4. Maintain existing logging patterns.  
5. After *each* step, the entire test‑suite **must pass** before proceeding.

---

## Step 1 – Add Constants (`config.py`)

Insert exactly this block (single paste) near the other risk constants:

```python
# ===== Performance‑target governor =====
from decimal import Decimal

TARGET_ANNUAL_RETURN_PCT = Decimal("0.05")      # 5 % p.a.
TARGET_DAILY_RETURN_PCT  = (TARGET_ANNUAL_RETURN_PCT / Decimal("365")).quantize(Decimal("0.000001"))
```

Commit: `git add config.py && git commit -m "feat(governor): add target return constants"`

---

## Step 2 – Store Opening Equity (`main_bot.py`)

1. Add global placeholders:

```python
equity_day_open: Decimal | None = None
day_open_date = None
```

2. After **Alpaca equity log line**:

```python
from datetime import datetime, timezone
equity_day_open = Decimal(str(acct["equity"])).quantize(Decimal("0.01"))
day_open_date   = datetime.now(timezone.utc).date()
```

3. Add helper before the scheduler loop:

```python
def _rollover_day_open(current_equity: Decimal):
    \"\"\"Reset open-of-day equity at UTC 00:00 and clear pause flag.\"\"\"
    from datetime import datetime, timezone
    global equity_day_open, day_open_date
    today = datetime.now(timezone.utc).date()
    if today != day_open_date:
        equity_day_open = current_equity
        day_open_date   = today
        bot_state["pause_trading_today"] = False
        logger.info("Day rollover: trading re-enabled; equity_day_open reset.")
```

4. Call `_rollover_day_open(session_stats["equity_now"])` at the **very start** of every `robust_run_trade_cycle()`.

Commit.

---

## Step 3 – Daily P&L Gate (`main_bot.py`)

Insert **just before** the cycle summary printout:

```python
pnl_today  = session_stats["equity_now"] - equity_day_open
target_usd = (equity_day_open * config.TARGET_DAILY_RETURN_PCT).quantize(Decimal("0.01"))

if pnl_today >= target_usd and not bot_state.get("pause_trading_today"):
    bot_state["pause_trading_today"] = True
    logger.warning(f"Daily target met (+{pnl_today} ≥ {target_usd}); new buys paused.")
    save_bot_state()  # existing helper
```

Commit.

---

## Step 4 – Enforce Pause (`trade_logic.py`)

Add **top** of `process_ai_decision_and_trade()`:

```python
from bot_state import bot_state
if bot_state.get("pause_trading_today"):
    logger.info("Governor active: skipping new trades today.")
    return "Daily target met – holding", buy_cnt, sell_cnt
```

No other edits. Commit.

---

## Step 5 – Unit Test

Create `tests/test_governor.py`:

```python
from decimal import Decimal
import bot_state, config

def test_daily_gate_triggers(monkeypatch):
    from main_bot import equity_day_open
    monkeypatch.setattr("main_bot.equity_day_open", Decimal("1000"))
    bot_state.bot_state["pause_trading_today"] = False
    target = (Decimal("1000") * config.TARGET_DAILY_RETURN_PCT).quantize(Decimal("0.01"))

    # simulate hitting the target
    import main_bot
    main_bot.session_stats = {"equity_now": Decimal("1000") + target + Decimal("0.01")}

    # call the internal check function (to be factored out of Step 3)
    main_bot._check_governor()

    assert bot_state.bot_state["pause_trading_today"] is True
```

Run `pytest -q`. Commit.

---

## Step 6 – Docs

* Append to **CHANGELOG.md** under `[Unreleased]`:  
  `• Added daily ISA‑style governor that pauses buying once P&L ≥ 0.013 % of opening equity.`
* Add bullet to **README.md → Features**.

---

## Validation Checklist

* Code compiles: `python -m py_compile $(git ls-files '*.py')`  
* `pytest` green.  
* Bot starts, trades as normal, and logs *“Daily target met … buying paused”* after hitting target in a test session.  
* No other behaviour changes.

---

**End of file — Gemini CLI: execute one step, test, ask user, repeat.**
