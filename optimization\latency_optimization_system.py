"""
Latency Optimization System

This system implements comprehensive latency optimization for the Bitcoin AI trading bot,
providing maximum execution speed through connection pooling, intelligent caching,
parallel processing, and network optimization techniques.

Key Features:
- Connection pooling for API endpoints
- Intelligent data caching with TTL
- Parallel processing for independent operations
- Network optimization and compression
- Memory pool management
- Asynchronous I/O operations
- Performance monitoring and metrics
- Adaptive timeout management

Author: Augment Agent
Date: 2025-07-30
"""

import asyncio
import aiohttp
import aiofiles
import threading
import time
import json
import pickle
import gzip
import logging
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import wraps, lru_cache
import weakref
import gc
import psutil
import os
import hashlib
import sqlite3
from contextlib import asynccontextmanager, contextmanager
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LatencyMetrics:
    """Performance metrics for latency optimization."""
    operation_name: str
    start_time: float
    end_time: float
    duration_ms: float
    cache_hit: bool = False
    parallel_execution: bool = False
    compression_used: bool = False
    connection_reused: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)

@dataclass
class CacheEntry:
    """Cache entry with TTL and metadata."""
    data: Any
    timestamp: float
    ttl_seconds: float
    access_count: int = 0
    size_bytes: int = 0
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        return time.time() - self.timestamp > self.ttl_seconds
    
    def access(self) -> Any:
        """Access cached data and update metrics."""
        self.access_count += 1
        return self.data

@dataclass
class ConnectionPoolConfig:
    """Configuration for connection pools."""
    max_connections: int = 20
    max_connections_per_host: int = 10
    timeout_seconds: float = 30.0
    retry_attempts: int = 3
    backoff_factor: float = 0.3
    enable_compression: bool = True
    enable_keep_alive: bool = True

class HighPerformanceCache:
    """
    High-performance cache with TTL, LRU eviction, and compression.
    
    Features:
    - Time-to-live (TTL) expiration
    - LRU eviction policy
    - Optional compression for large objects
    - Memory usage monitoring
    - Access pattern analytics
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 default_ttl: float = 300.0,
                 compression_threshold: int = 1024,
                 enable_analytics: bool = True):
        """
        Initialize high-performance cache.
        
        Args:
            max_size: Maximum number of cache entries
            default_ttl: Default time-to-live in seconds
            compression_threshold: Compress objects larger than this size
            enable_analytics: Enable access pattern analytics
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.compression_threshold = compression_threshold
        self.enable_analytics = enable_analytics
        
        # Cache storage
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: deque = deque()
        self._lock = threading.RLock()
        
        # Analytics
        self.hit_count = 0
        self.miss_count = 0
        self.eviction_count = 0
        self.compression_count = 0
        
        # Memory monitoring
        self.total_size_bytes = 0
        self.max_memory_mb = 100  # 100MB default limit
        
        logger.info(f"High-performance cache initialized: max_size={max_size}, ttl={default_ttl}s")
    
    def _calculate_size(self, obj: Any) -> int:
        """Calculate object size in bytes."""
        try:
            if isinstance(obj, (str, bytes)):
                return len(obj)
            elif isinstance(obj, (dict, list)):
                return len(pickle.dumps(obj))
            else:
                return len(str(obj))
        except Exception:
            return 1024  # Default estimate
    
    def _compress_data(self, data: Any) -> bytes:
        """Compress data using gzip."""
        try:
            serialized = pickle.dumps(data)
            if len(serialized) > self.compression_threshold:
                compressed = gzip.compress(serialized)
                self.compression_count += 1
                return compressed
            return serialized
        except Exception as e:
            logger.warning(f"Compression failed: {e}")
            return pickle.dumps(data)
    
    def _decompress_data(self, data: bytes) -> Any:
        """Decompress data."""
        try:
            # Try gzip decompression first
            try:
                decompressed = gzip.decompress(data)
                return pickle.loads(decompressed)
            except gzip.BadGzipFile:
                # Not compressed, try direct pickle
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return None
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries."""
        while len(self._cache) >= self.max_size and self._access_order:
            lru_key = self._access_order.popleft()
            if lru_key in self._cache:
                entry = self._cache.pop(lru_key)
                self.total_size_bytes -= entry.size_bytes
                self.eviction_count += 1
    
    def _cleanup_expired(self) -> None:
        """Remove expired entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if current_time - entry.timestamp > entry.ttl_seconds
        ]
        
        for key in expired_keys:
            entry = self._cache.pop(key, None)
            if entry:
                self.total_size_bytes -= entry.size_bytes
                # Remove from access order
                try:
                    self._access_order.remove(key)
                except ValueError:
                    pass
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached value."""
        with self._lock:
            # Cleanup expired entries periodically
            if len(self._cache) % 100 == 0:
                self._cleanup_expired()
            
            entry = self._cache.get(key)
            if entry is None:
                self.miss_count += 1
                return None
            
            # Check expiration
            if entry.is_expired():
                self._cache.pop(key)
                self.total_size_bytes -= entry.size_bytes
                try:
                    self._access_order.remove(key)
                except ValueError:
                    pass
                self.miss_count += 1
                return None
            
            # Update access order
            try:
                self._access_order.remove(key)
            except ValueError:
                pass
            self._access_order.append(key)

            self.hit_count += 1
            data = entry.access()

            # Decompress if needed
            if isinstance(data, bytes):
                try:
                    data = self._decompress_data(data)
                except Exception:
                    pass  # Return as-is if decompression fails

            return data
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Set cached value."""
        try:
            with self._lock:
                ttl = ttl or self.default_ttl
                
                # Calculate size
                size_bytes = self._calculate_size(value)
                
                # Check memory limit
                if self.total_size_bytes + size_bytes > self.max_memory_mb * 1024 * 1024:
                    self._evict_lru()
                
                # Compress if needed
                if size_bytes > self.compression_threshold:
                    compressed_value = self._compress_data(value)
                    actual_size = len(compressed_value)
                else:
                    compressed_value = value
                    actual_size = size_bytes
                
                # Remove existing entry if present
                if key in self._cache:
                    old_entry = self._cache[key]
                    self.total_size_bytes -= old_entry.size_bytes
                
                # Create new entry
                entry = CacheEntry(
                    data=compressed_value,
                    timestamp=time.time(),
                    ttl_seconds=ttl,
                    size_bytes=actual_size
                )
                
                # Evict if necessary
                self._evict_lru()
                
                # Store entry
                self._cache[key] = entry
                self.total_size_bytes += actual_size
                
                # Update access order
                try:
                    self._access_order.remove(key)
                except ValueError:
                    pass
                self._access_order.append(key)
                
                return True
                
        except Exception as e:
            logger.error(f"Cache set failed for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete cached value."""
        with self._lock:
            entry = self._cache.pop(key, None)
            if entry:
                self.total_size_bytes -= entry.size_bytes
                try:
                    self._access_order.remove(key)
                except ValueError:
                    pass
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cached values."""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self.total_size_bytes = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'eviction_count': self.eviction_count,
            'compression_count': self.compression_count,
            'total_size_mb': self.total_size_bytes / (1024 * 1024),
            'max_memory_mb': self.max_memory_mb
        }

class ConnectionPoolManager:
    """
    Advanced connection pool manager for HTTP requests.
    
    Features:
    - Per-host connection pooling
    - Automatic retry with exponential backoff
    - Connection keep-alive
    - Request/response compression
    - Performance monitoring
    """
    
    def __init__(self, config: ConnectionPoolConfig = None):
        """Initialize connection pool manager."""
        self.config = config or ConnectionPoolConfig()
        
        # Create session with optimized settings
        self.session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.config.retry_attempts,
            backoff_factor=self.config.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )
        
        # Configure adapter
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.config.max_connections,
            pool_maxsize=self.config.max_connections_per_host
        )
        
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        headers = {
            'Connection': 'keep-alive',
            'User-Agent': 'Bitcoin-AI-Trading-Bot/1.0'
        }
        
        if self.config.enable_compression:
            headers['Accept-Encoding'] = 'gzip, deflate'
        
        self.session.headers.update(headers)
        
        # Performance tracking
        self.request_count = 0
        self.total_response_time = 0.0
        self.connection_reuse_count = 0
        
        logger.info("Connection pool manager initialized")
    
    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with connection pooling."""
        start_time = time.time()
        
        try:
            # Set timeout if not provided
            if 'timeout' not in kwargs:
                kwargs['timeout'] = self.config.timeout_seconds
            
            # Make request
            response = self.session.request(method, url, **kwargs)
            
            # Track performance
            response_time = time.time() - start_time
            self.request_count += 1
            self.total_response_time += response_time
            
            # Check if connection was reused
            if hasattr(response.raw, '_connection') and response.raw._connection:
                self.connection_reuse_count += 1
            
            return response
            
        except Exception as e:
            logger.error(f"Request failed: {method} {url} - {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        avg_response_time = (
            self.total_response_time / self.request_count 
            if self.request_count > 0 else 0
        )
        
        reuse_rate = (
            self.connection_reuse_count / self.request_count 
            if self.request_count > 0 else 0
        )
        
        return {
            'request_count': self.request_count,
            'avg_response_time_ms': avg_response_time * 1000,
            'connection_reuse_rate': reuse_rate,
            'total_response_time': self.total_response_time
        }
    
    def close(self) -> None:
        """Close connection pool."""
        self.session.close()

class ParallelExecutor:
    """
    Parallel execution manager for independent operations.
    
    Features:
    - Thread pool for CPU-bound tasks
    - Async execution for I/O-bound tasks
    - Intelligent task scheduling
    - Performance monitoring
    """
    
    def __init__(self, max_workers: int = None):
        """Initialize parallel executor."""
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Performance tracking
        self.task_count = 0
        self.parallel_task_count = 0
        self.total_execution_time = 0.0
        
        logger.info(f"Parallel executor initialized with {self.max_workers} workers")
    
    def execute_parallel(self, tasks: List[Callable], timeout: float = 30.0) -> List[Any]:
        """Execute multiple tasks in parallel."""
        if not tasks:
            return []
        
        start_time = time.time()
        self.task_count += len(tasks)
        self.parallel_task_count += len(tasks)
        
        try:
            # Submit all tasks
            futures = [self.thread_pool.submit(task) for task in tasks]

            # Collect results in order
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=timeout)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Parallel task failed: {e}")
                    results.append(None)

            execution_time = time.time() - start_time
            self.total_execution_time += execution_time

            return results
            
        except Exception as e:
            logger.error(f"Parallel execution failed: {e}")
            return [None] * len(tasks)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get parallel execution statistics."""
        avg_execution_time = (
            self.total_execution_time / self.task_count 
            if self.task_count > 0 else 0
        )
        
        return {
            'max_workers': self.max_workers,
            'task_count': self.task_count,
            'parallel_task_count': self.parallel_task_count,
            'avg_execution_time_ms': avg_execution_time * 1000,
            'total_execution_time': self.total_execution_time
        }
    
    def shutdown(self) -> None:
        """Shutdown parallel executor."""
        self.thread_pool.shutdown(wait=True)

class MemoryPoolManager:
    """
    Memory pool manager for efficient object allocation.

    Features:
    - Object pooling for frequently used types
    - Memory usage monitoring
    - Garbage collection optimization
    - Memory leak detection
    """

    def __init__(self, pool_sizes: Dict[str, int] = None):
        """Initialize memory pool manager."""
        self.pool_sizes = pool_sizes or {
            'dict': 100,
            'list': 100,
            'dataframe': 20,
            'numpy_array': 50
        }

        # Object pools
        self.pools: Dict[str, deque] = {
            pool_type: deque(maxlen=size)
            for pool_type, size in self.pool_sizes.items()
        }

        # Track allocation count instead of weak references
        self.allocation_tracking = True

        # Performance tracking
        self.allocation_count = 0
        self.pool_hit_count = 0
        self.gc_count = 0

        logger.info("Memory pool manager initialized")

    def get_dict(self) -> dict:
        """Get dictionary from pool or create new."""
        self.allocation_count += 1

        if self.pools['dict']:
            obj = self.pools['dict'].popleft()
            obj.clear()
            self.pool_hit_count += 1
            return obj

        obj = {}
        return obj

    def return_dict(self, obj: dict) -> None:
        """Return dictionary to pool."""
        if len(self.pools['dict']) < self.pool_sizes['dict']:
            obj.clear()
            self.pools['dict'].append(obj)

    def get_list(self) -> list:
        """Get list from pool or create new."""
        self.allocation_count += 1

        if self.pools['list']:
            obj = self.pools['list'].popleft()
            obj.clear()
            self.pool_hit_count += 1
            return obj

        obj = []
        return obj

    def return_list(self, obj: list) -> None:
        """Return list to pool."""
        if len(self.pools['list']) < self.pool_sizes['list']:
            obj.clear()
            self.pools['list'].append(obj)

    def force_gc(self) -> Dict[str, int]:
        """Force garbage collection and return stats."""
        before_count = len(gc.get_objects())
        collected = gc.collect()
        after_count = len(gc.get_objects())

        self.gc_count += 1

        return {
            'objects_before': before_count,
            'objects_after': after_count,
            'collected': collected,
            'gc_count': self.gc_count
        }

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()

        pool_hit_rate = (
            self.pool_hit_count / self.allocation_count
            if self.allocation_count > 0 else 0
        )

        return {
            'allocation_count': self.allocation_count,
            'pool_hit_count': self.pool_hit_count,
            'pool_hit_rate': pool_hit_rate,
            'allocated_objects': self.allocation_count,
            'memory_rss_mb': memory_info.rss / (1024 * 1024),
            'memory_vms_mb': memory_info.vms / (1024 * 1024),
            'gc_count': self.gc_count
        }

class AsyncIOManager:
    """
    Asynchronous I/O manager for file operations.

    Features:
    - Async file read/write operations
    - Batch processing for multiple files
    - Compression support
    - Performance monitoring
    """

    def __init__(self):
        """Initialize async I/O manager."""
        self.operation_count = 0
        self.total_io_time = 0.0
        self.bytes_read = 0
        self.bytes_written = 0

        logger.info("Async I/O manager initialized")

    async def read_file_async(self, filepath: str, encoding: str = 'utf-8') -> Optional[str]:
        """Read file asynchronously."""
        start_time = time.time()

        try:
            async with aiofiles.open(filepath, 'r', encoding=encoding) as f:
                content = await f.read()

            self.operation_count += 1
            self.bytes_read += len(content.encode(encoding))
            self.total_io_time += time.time() - start_time

            return content

        except Exception as e:
            logger.error(f"Async file read failed: {filepath} - {e}")
            return None

    async def write_file_async(self, filepath: str, content: str, encoding: str = 'utf-8') -> bool:
        """Write file asynchronously."""
        start_time = time.time()

        try:
            async with aiofiles.open(filepath, 'w', encoding=encoding) as f:
                await f.write(content)

            self.operation_count += 1
            self.bytes_written += len(content.encode(encoding))
            self.total_io_time += time.time() - start_time

            return True

        except Exception as e:
            logger.error(f"Async file write failed: {filepath} - {e}")
            return False

    async def read_json_async(self, filepath: str) -> Optional[Dict[str, Any]]:
        """Read JSON file asynchronously."""
        content = await self.read_file_async(filepath)
        if content:
            try:
                return json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode failed: {filepath} - {e}")
        return None

    async def write_json_async(self, filepath: str, data: Dict[str, Any]) -> bool:
        """Write JSON file asynchronously."""
        try:
            content = json.dumps(data, indent=2)
            return await self.write_file_async(filepath, content)
        except Exception as e:
            logger.error(f"JSON encode failed: {filepath} - {e}")
            return False

    async def batch_read_files(self, filepaths: List[str]) -> Dict[str, Optional[str]]:
        """Read multiple files in parallel."""
        tasks = [self.read_file_async(filepath) for filepath in filepaths]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        return {
            filepath: result if not isinstance(result, Exception) else None
            for filepath, result in zip(filepaths, results)
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get I/O statistics."""
        avg_io_time = (
            self.total_io_time / self.operation_count
            if self.operation_count > 0 else 0
        )

        return {
            'operation_count': self.operation_count,
            'avg_io_time_ms': avg_io_time * 1000,
            'total_io_time': self.total_io_time,
            'bytes_read': self.bytes_read,
            'bytes_written': self.bytes_written,
            'total_bytes': self.bytes_read + self.bytes_written
        }

class LatencyOptimizationSystem:
    """
    Main latency optimization system that coordinates all optimization components.

    This system provides comprehensive latency optimization for the Bitcoin AI trading bot
    through intelligent caching, connection pooling, parallel processing, and memory management.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the latency optimization system."""
        self.config = config or self._get_default_config()

        # Initialize components
        self.cache = HighPerformanceCache(
            max_size=self.config.get('cache_max_size', 1000),
            default_ttl=self.config.get('cache_default_ttl', 300.0),
            compression_threshold=self.config.get('cache_compression_threshold', 1024)
        )

        self.connection_pool = ConnectionPoolManager(
            ConnectionPoolConfig(
                max_connections=self.config.get('max_connections', 20),
                max_connections_per_host=self.config.get('max_connections_per_host', 10),
                timeout_seconds=self.config.get('timeout_seconds', 30.0),
                retry_attempts=self.config.get('retry_attempts', 3)
            )
        )

        self.parallel_executor = ParallelExecutor(
            max_workers=self.config.get('max_workers', None)
        )

        self.memory_pool = MemoryPoolManager(
            pool_sizes=self.config.get('pool_sizes', None)
        )

        self.async_io = AsyncIOManager()

        # Performance tracking
        self.metrics: List[LatencyMetrics] = []
        self.optimization_enabled = True
        self.start_time = time.time()

        # Adaptive timeout management
        self.timeout_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.adaptive_timeouts: Dict[str, float] = {}

        logger.info("Latency optimization system initialized")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'cache_max_size': 1000,
            'cache_default_ttl': 300.0,
            'cache_compression_threshold': 1024,
            'max_connections': 20,
            'max_connections_per_host': 10,
            'timeout_seconds': 30.0,
            'retry_attempts': 3,
            'max_workers': None,
            'pool_sizes': None,
            'enable_adaptive_timeouts': True,
            'enable_performance_monitoring': True
        }

    def optimize_function(self, cache_key: str = None, ttl: float = None,
                         parallel: bool = False, timeout: float = None):
        """
        Decorator to optimize function execution with caching and performance monitoring.

        Args:
            cache_key: Key for caching results
            ttl: Time-to-live for cached results
            parallel: Whether function can be executed in parallel
            timeout: Custom timeout for the function
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                operation_name = f"{func.__module__}.{func.__name__}"

                # Generate cache key if caching enabled
                if cache_key and self.optimization_enabled:
                    key = f"{cache_key}:{hashlib.md5(str(args).encode()).hexdigest()}"
                    cached_result = self.cache.get(key)
                    if cached_result is not None:
                        # Record cache hit
                        duration_ms = (time.time() - start_time) * 1000
                        metric = LatencyMetrics(
                            operation_name=operation_name,
                            start_time=start_time,
                            end_time=time.time(),
                            duration_ms=duration_ms,
                            cache_hit=True
                        )
                        self.metrics.append(metric)
                        return cached_result

                # Execute function
                try:
                    if parallel and self.optimization_enabled:
                        # Execute in thread pool
                        future = self.parallel_executor.thread_pool.submit(func, *args, **kwargs)
                        result = future.result(timeout=timeout)
                        parallel_execution = True
                    else:
                        result = func(*args, **kwargs)
                        parallel_execution = False

                    # Cache result if caching enabled
                    if cache_key and self.optimization_enabled:
                        self.cache.set(key, result, ttl)

                    # Record performance metric
                    duration_ms = (time.time() - start_time) * 1000
                    metric = LatencyMetrics(
                        operation_name=operation_name,
                        start_time=start_time,
                        end_time=time.time(),
                        duration_ms=duration_ms,
                        cache_hit=False,
                        parallel_execution=parallel_execution
                    )
                    self.metrics.append(metric)

                    # Update adaptive timeout
                    if self.config.get('enable_adaptive_timeouts', True):
                        self.timeout_history[operation_name].append(duration_ms / 1000)
                        self._update_adaptive_timeout(operation_name)

                    return result

                except Exception as e:
                    logger.error(f"Optimized function failed: {operation_name} - {e}")
                    raise

            return wrapper
        return decorator

    def _update_adaptive_timeout(self, operation_name: str) -> None:
        """Update adaptive timeout based on historical performance."""
        history = self.timeout_history[operation_name]
        if len(history) >= 10:
            # Calculate 95th percentile + 50% buffer
            times = sorted(history)
            p95_index = int(len(times) * 0.95)
            p95_time = times[p95_index]
            adaptive_timeout = p95_time * 1.5

            self.adaptive_timeouts[operation_name] = adaptive_timeout

    def get_adaptive_timeout(self, operation_name: str, default: float = 30.0) -> float:
        """Get adaptive timeout for operation."""
        adaptive_timeout = self.adaptive_timeouts.get(operation_name)
        if adaptive_timeout is not None:
            return adaptive_timeout
        return default

    def optimize_api_call(self, method: str, url: str, cache_key: str = None,
                         ttl: float = None, **kwargs) -> requests.Response:
        """
        Optimized API call with connection pooling and caching.

        Args:
            method: HTTP method
            url: Request URL
            cache_key: Cache key for response
            ttl: Cache TTL
            **kwargs: Additional request parameters
        """
        start_time = time.time()

        # Check cache first
        if cache_key and self.optimization_enabled:
            cached_response = self.cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"API call cache hit: {method} {url}")
                return cached_response

        # Make request with connection pooling
        try:
            response = self.connection_pool.request(method, url, **kwargs)

            # Cache successful responses
            if cache_key and response.status_code == 200 and self.optimization_enabled:
                self.cache.set(cache_key, response, ttl)

            # Record metric
            duration_ms = (time.time() - start_time) * 1000
            metric = LatencyMetrics(
                operation_name=f"api_call_{method}_{url}",
                start_time=start_time,
                end_time=time.time(),
                duration_ms=duration_ms,
                cache_hit=False,
                connection_reused=True  # Assume reused with connection pool
            )
            self.metrics.append(metric)

            return response

        except Exception as e:
            logger.error(f"Optimized API call failed: {method} {url} - {e}")
            raise

    async def optimize_file_operations(self, operations: List[Tuple[str, str, Any]]) -> Dict[str, Any]:
        """
        Optimize multiple file operations in parallel.

        Args:
            operations: List of (operation_type, filepath, data) tuples
                       operation_type: 'read', 'write', 'read_json', 'write_json'

        Returns:
            Dictionary with operation results
        """
        start_time = time.time()
        results = {}

        try:
            tasks = []
            for op_type, filepath, data in operations:
                if op_type == 'read':
                    tasks.append(self.async_io.read_file_async(filepath))
                elif op_type == 'write':
                    tasks.append(self.async_io.write_file_async(filepath, data))
                elif op_type == 'read_json':
                    tasks.append(self.async_io.read_json_async(filepath))
                elif op_type == 'write_json':
                    tasks.append(self.async_io.write_json_async(filepath, data))
                else:
                    tasks.append(asyncio.coroutine(lambda: None)())

            # Execute all operations in parallel
            operation_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for i, (op_type, filepath, data) in enumerate(operations):
                result = operation_results[i]
                if isinstance(result, Exception):
                    logger.error(f"File operation failed: {op_type} {filepath} - {result}")
                    results[filepath] = None
                else:
                    results[filepath] = result

            # Record metric
            duration_ms = (time.time() - start_time) * 1000
            metric = LatencyMetrics(
                operation_name="batch_file_operations",
                start_time=start_time,
                end_time=time.time(),
                duration_ms=duration_ms,
                parallel_execution=True
            )
            self.metrics.append(metric)

            return results

        except Exception as e:
            logger.error(f"Batch file operations failed: {e}")
            return {}

    def optimize_data_processing(self, data: Any, operations: List[Callable]) -> Any:
        """
        Optimize data processing pipeline with parallel execution.

        Args:
            data: Input data
            operations: List of processing functions

        Returns:
            Processed data
        """
        start_time = time.time()

        try:
            # Check if operations can be parallelized
            if len(operations) > 1 and self.optimization_enabled:
                # Execute operations in parallel if they're independent
                tasks = [lambda op=op: op(data) for op in operations]
                results = self.parallel_executor.execute_parallel(tasks)

                # Combine results (assuming last operation is the final one)
                result = results[-1] if results else data
            else:
                # Sequential execution
                result = data
                for operation in operations:
                    result = operation(result)

            # Record metric
            duration_ms = (time.time() - start_time) * 1000
            metric = LatencyMetrics(
                operation_name="data_processing_pipeline",
                start_time=start_time,
                end_time=time.time(),
                duration_ms=duration_ms,
                parallel_execution=len(operations) > 1
            )
            self.metrics.append(metric)

            return result

        except Exception as e:
            logger.error(f"Data processing optimization failed: {e}")
            return data

    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        if not self.metrics:
            return {"message": "No performance data available"}

        # Calculate overall statistics
        total_operations = len(self.metrics)
        total_duration = sum(m.duration_ms for m in self.metrics)
        avg_duration = total_duration / total_operations

        cache_hits = sum(1 for m in self.metrics if m.cache_hit)
        cache_hit_rate = cache_hits / total_operations

        parallel_operations = sum(1 for m in self.metrics if m.parallel_execution)
        parallel_rate = parallel_operations / total_operations

        # Group by operation type
        operation_stats = defaultdict(list)
        for metric in self.metrics:
            operation_stats[metric.operation_name].append(metric.duration_ms)

        # Calculate per-operation statistics
        operation_summary = {}
        for op_name, durations in operation_stats.items():
            operation_summary[op_name] = {
                'count': len(durations),
                'avg_duration_ms': sum(durations) / len(durations),
                'min_duration_ms': min(durations),
                'max_duration_ms': max(durations),
                'total_duration_ms': sum(durations)
            }

        # Get component statistics
        cache_stats = self.cache.get_stats()
        connection_stats = self.connection_pool.get_stats()
        parallel_stats = self.parallel_executor.get_stats()
        memory_stats = self.memory_pool.get_memory_stats()
        io_stats = self.async_io.get_stats()

        uptime_seconds = time.time() - self.start_time

        return {
            'summary': {
                'total_operations': total_operations,
                'avg_duration_ms': avg_duration,
                'total_duration_ms': total_duration,
                'cache_hit_rate': cache_hit_rate,
                'parallel_execution_rate': parallel_rate,
                'uptime_seconds': uptime_seconds
            },
            'operations': operation_summary,
            'cache': cache_stats,
            'connections': connection_stats,
            'parallel_execution': parallel_stats,
            'memory': memory_stats,
            'async_io': io_stats,
            'adaptive_timeouts': dict(self.adaptive_timeouts)
        }

    def clear_cache(self) -> None:
        """Clear all caches."""
        self.cache.clear()
        logger.info("All caches cleared")

    def enable_optimization(self) -> None:
        """Enable optimization features."""
        self.optimization_enabled = True
        logger.info("Latency optimization enabled")

    def disable_optimization(self) -> None:
        """Disable optimization features."""
        self.optimization_enabled = False
        logger.info("Latency optimization disabled")

    def shutdown(self) -> None:
        """Shutdown the latency optimization system."""
        try:
            self.parallel_executor.shutdown()
            self.connection_pool.close()
            self.cache.clear()
            logger.info("Latency optimization system shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

# Global instance
_latency_optimizer: Optional[LatencyOptimizationSystem] = None

def initialize_latency_optimization(config: Dict[str, Any] = None) -> bool:
    """
    Initialize the global latency optimization system.

    Args:
        config: Configuration dictionary

    Returns:
        True if initialization successful
    """
    global _latency_optimizer

    try:
        _latency_optimizer = LatencyOptimizationSystem(config)
        logger.info("Global latency optimization system initialized")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize latency optimization: {e}")
        return False

def get_latency_optimizer() -> Optional[LatencyOptimizationSystem]:
    """Get the global latency optimization system."""
    return _latency_optimizer

def optimize_function(cache_key: str = None, ttl: float = None,
                     parallel: bool = False, timeout: float = None):
    """
    Global function optimization decorator.

    Args:
        cache_key: Key for caching results
        ttl: Time-to-live for cached results
        parallel: Whether function can be executed in parallel
        timeout: Custom timeout for the function
    """
    if _latency_optimizer:
        return _latency_optimizer.optimize_function(cache_key, ttl, parallel, timeout)
    else:
        # Return pass-through decorator if not initialized
        def decorator(func: Callable) -> Callable:
            return func
        return decorator

def optimize_api_call(method: str, url: str, cache_key: str = None,
                     ttl: float = None, **kwargs) -> requests.Response:
    """
    Global optimized API call function.

    Args:
        method: HTTP method
        url: Request URL
        cache_key: Cache key for response
        ttl: Cache TTL
        **kwargs: Additional request parameters
    """
    if _latency_optimizer:
        return _latency_optimizer.optimize_api_call(method, url, cache_key, ttl, **kwargs)
    else:
        # Fallback to regular requests
        import requests
        return requests.request(method, url, **kwargs)

async def optimize_file_operations(operations: List[Tuple[str, str, Any]]) -> Dict[str, Any]:
    """
    Global optimized file operations function.

    Args:
        operations: List of (operation_type, filepath, data) tuples

    Returns:
        Dictionary with operation results
    """
    if _latency_optimizer:
        return await _latency_optimizer.optimize_file_operations(operations)
    else:
        # Fallback to synchronous operations
        results = {}
        for op_type, filepath, data in operations:
            try:
                if op_type == 'read':
                    with open(filepath, 'r') as f:
                        results[filepath] = f.read()
                elif op_type == 'write':
                    with open(filepath, 'w') as f:
                        f.write(data)
                    results[filepath] = True
                elif op_type == 'read_json':
                    with open(filepath, 'r') as f:
                        results[filepath] = json.load(f)
                elif op_type == 'write_json':
                    with open(filepath, 'w') as f:
                        json.dump(data, f, indent=2)
                    results[filepath] = True
            except Exception as e:
                logger.error(f"File operation failed: {op_type} {filepath} - {e}")
                results[filepath] = None
        return results

def get_latency_performance_report() -> Dict[str, Any]:
    """Get global latency performance report."""
    if _latency_optimizer:
        return _latency_optimizer.get_performance_report()
    else:
        return {"message": "Latency optimization not initialized"}

def clear_latency_cache() -> None:
    """Clear global latency cache."""
    if _latency_optimizer:
        _latency_optimizer.clear_cache()

def get_adaptive_timeout(operation_name: str, default: float = 30.0) -> float:
    """Get adaptive timeout for operation."""
    if _latency_optimizer:
        return _latency_optimizer.get_adaptive_timeout(operation_name, default)
    else:
        return default

def shutdown_latency_optimization() -> None:
    """Shutdown global latency optimization system."""
    global _latency_optimizer
    if _latency_optimizer:
        _latency_optimizer.shutdown()
        _latency_optimizer = None
        logger.info("Global latency optimization system shutdown")
