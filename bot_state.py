import json
import os
import logging
from datetime import datetime, timezone

logger = logging.getLogger("TradingBotApp.BotState")

BOT_STATE_FILE = "bot_state.json"
_state_cache = None

def load_bot_state():
    logger.debug(f"Attempting to load bot state from {BOT_STATE_FILE}.")
    global _state_cache
    if _state_cache is not None:
        logger.debug("Bot state already loaded from cache.")
        return _state_cache

    default_state = {
        "last_cycles": [],
        "last_trade_timestamp": None,
        "last_trade_side": None
    }

    if not os.path.exists(BOT_STATE_FILE) or os.path.getsize(BOT_STATE_FILE) == 0:
        logger.warning(f"Bot state file '{BOT_STATE_FILE}' does not exist or is empty. Starting fresh with default state.")
        _state_cache = default_state
        return _state_cache

    try:
        with open(BOT_STATE_FILE, "r") as f:
            loaded_data = json.load(f)

        if isinstance(loaded_data, list):
            logger.warning("Bot state file is in old list format. Adapting to new dictionary format.")
            _state_cache = {
                "last_cycles": loaded_data,
                "last_trade_timestamp": None,
                "last_trade_side": None
            }
        else:
            _state_cache = loaded_data

        if "last_cycles" not in _state_cache or not isinstance(_state_cache["last_cycles"], list):
            logger.warning("Bot state 'last_cycles' is missing or malformed. Resetting to empty list.")
            _state_cache["last_cycles"] = []
        
        if "last_trade_timestamp" in _state_cache and _state_cache["last_trade_timestamp"] is not None:
            try:
                _state_cache["last_trade_timestamp"] = datetime.fromisoformat(_state_cache["last_trade_timestamp"])
                logger.debug(f"Successfully parsed last_trade_timestamp: {_state_cache['last_trade_timestamp']}")
            except ValueError as e:
                logger.warning(f"Failed to parse last_trade_timestamp '{_state_cache['last_trade_timestamp']}': {e}. Setting to None.")
                _state_cache["last_trade_timestamp"] = None
        else:
            _state_cache["last_trade_timestamp"] = None
            logger.debug("last_trade_timestamp not found or is None. Initializing to None.")

        if "last_trade_side" not in _state_cache:
            _state_cache["last_trade_side"] = None
            logger.debug("last_trade_side not found. Initializing to None.")

        logger.info(f"Bot state loaded successfully from '{BOT_STATE_FILE}'.")
        return _state_cache
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load bot state from '{BOT_STATE_FILE}': {e}. Starting fresh with default state.")
        _state_cache = default_state
        return _state_cache
    except Exception as e:
        logger.critical(f"An unexpected error occurred while loading bot state from '{BOT_STATE_FILE}': {e}", exc_info=True)
        _state_cache = default_state
        return _state_cache

def save_bot_state(state: dict):
    logger.debug(f"Attempting to save bot state to {BOT_STATE_FILE}.")
    global _state_cache
    try:
        _state_cache = state

        serializable_state = state.copy()
        if serializable_state.get("last_trade_timestamp") is not None:
            serializable_state["last_trade_timestamp"] = serializable_state["last_trade_timestamp"].isoformat()
            logger.debug(f"Converted last_trade_timestamp to ISO format for saving: {serializable_state['last_trade_timestamp']}")

        with open(BOT_STATE_FILE, "w") as f:
            json.dump(serializable_state, f, indent=2)
        logger.info(f"Bot state saved successfully to '{BOT_STATE_FILE}'.")
    except (IOError, TypeError) as e:
        logger.error(f"Failed to save bot state to '{BOT_STATE_FILE}': {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred while saving bot state to '{BOT_STATE_FILE}': {e}", exc_info=True)

load_bot_state()
