"""
Analysis Engine
===============

Core analysis engine that processes collected performance data to identify patterns,
calculate metrics, and generate insights for the AI Performance Analyzer system.
"""

import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from collections import defaultdict
import sqlite3

from .config import ANALYSIS, METRICS, DATABASE, LOGGING
from .utils import (
    setup_logger, safe_decimal, safe_float, parse_timestamp,
    calculate_sharpe_ratio, calculate_max_drawdown, calculate_win_rate,
    calculate_profit_factor, create_database_connection
)

class AnalysisEngine:
    """Core analysis engine for performance data processing."""
    
    def __init__(self):
        self.logger = setup_logger("AnalysisEngine", LOGGING)
        self.db_path = DATABASE["path"]
        self.analysis_results = {}
        
        # Initialize database
        self._initialize_database()
        
    def analyze_performance_data(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze collected performance data and generate insights."""
        self.logger.info("Starting performance data analysis...")
        
        try:
            # Extract data components
            trade_data = collected_data.get("trade_data", {})
            log_analysis = collected_data.get("log_analysis", {})
            bot_state = collected_data.get("bot_state", {})
            session_data = collected_data.get("session_data", {})
            
            # Perform different types of analysis
            analysis_results = {
                "analysis_timestamp": datetime.now().isoformat(),
                "trade_analysis": self._analyze_trades(trade_data),
                "decision_analysis": self._analyze_decisions(log_analysis),
                "performance_metrics": self._calculate_performance_metrics(trade_data, session_data),
                "pattern_recognition": self._identify_patterns(collected_data),
                "risk_analysis": self._analyze_risk_metrics(trade_data),
                "efficiency_analysis": self._analyze_efficiency(log_analysis),
                "insights": self._generate_insights(collected_data)
            }
            
            # Store results in database
            self._store_analysis_results(analysis_results)
            
            self.analysis_results = analysis_results
            self.logger.info("Performance analysis completed successfully")
            
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"Error during performance analysis: {e}")
            raise
    
    def _analyze_trades(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trade-related performance."""
        self.logger.info("Analyzing trade performance...")
        
        analysis = {
            "position_analysis": {},
            "lot_performance": {},
            "strategy_effectiveness": {},
            "holding_period_analysis": {}
        }
        
        try:
            open_lots = trade_data.get("open_lots", [])
            trade_summary = trade_data.get("trade_summary", {})
            
            if not open_lots:
                return analysis
            
            # Position analysis
            analysis["position_analysis"] = self._analyze_position_distribution(open_lots)
            
            # Lot performance analysis
            analysis["lot_performance"] = self._analyze_lot_performance(open_lots)
            
            # Strategy effectiveness
            analysis["strategy_effectiveness"] = self._analyze_strategy_types(open_lots)
            
            # Holding period analysis
            analysis["holding_period_analysis"] = self._analyze_holding_periods(open_lots)
            
        except Exception as e:
            self.logger.error(f"Error analyzing trades: {e}")
            
        return analysis
    
    def _analyze_decisions(self, log_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze AI decision patterns and quality."""
        self.logger.info("Analyzing AI decision patterns...")
        
        analysis = {
            "decision_distribution": {},
            "decision_timing": {},
            "decision_quality": {},
            "error_patterns": {}
        }
        
        try:
            ai_decisions = log_analysis.get("ai_decisions", [])
            trade_actions = log_analysis.get("trade_actions", [])
            errors = log_analysis.get("errors", [])
            
            # Decision distribution analysis
            analysis["decision_distribution"] = self._analyze_decision_distribution(ai_decisions)
            
            # Decision timing analysis
            analysis["decision_timing"] = self._analyze_decision_timing(ai_decisions)
            
            # Decision quality analysis
            analysis["decision_quality"] = self._analyze_decision_quality(ai_decisions, trade_actions)
            
            # Error pattern analysis
            analysis["error_patterns"] = self._analyze_error_patterns(errors)
            
        except Exception as e:
            self.logger.error(f"Error analyzing decisions: {e}")
            
        return analysis
    
    def _calculate_performance_metrics(self, trade_data: Dict[str, Any], session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        self.logger.info("Calculating performance metrics...")
        
        metrics = {
            "profitability": {},
            "risk_metrics": {},
            "efficiency_metrics": {},
            "benchmark_comparison": {}
        }
        
        try:
            # Extract relevant data
            trade_summary = trade_data.get("trade_summary", {})
            
            # Profitability metrics
            metrics["profitability"] = {
                "total_lots": trade_summary.get("total_lots", 0),
                "total_position_value": trade_summary.get("total_value", 0),
                "average_lot_size": self._calculate_average_lot_size(trade_data),
                "unrealized_pnl": 0  # Would need current market prices
            }
            
            # Risk metrics
            metrics["risk_metrics"] = {
                "position_concentration": self._calculate_position_concentration(trade_data),
                "strategy_diversification": self._calculate_strategy_diversification(trade_data),
                "holding_period_risk": self._calculate_holding_period_risk(trade_data)
            }
            
            # Efficiency metrics
            metrics["efficiency_metrics"] = {
                "cash_utilization": self._calculate_cash_utilization(session_data),
                "trade_frequency": self._calculate_trade_frequency(trade_data),
                "decision_efficiency": self._calculate_decision_efficiency(trade_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {e}")
            
        return metrics
    
    def _identify_patterns(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify patterns in trading behavior and performance."""
        self.logger.info("Identifying performance patterns...")
        
        patterns = {
            "temporal_patterns": {},
            "strategy_patterns": {},
            "market_condition_patterns": {},
            "decision_patterns": {}
        }
        
        try:
            # Temporal patterns
            patterns["temporal_patterns"] = self._identify_temporal_patterns(collected_data)
            
            # Strategy patterns
            patterns["strategy_patterns"] = self._identify_strategy_patterns(collected_data)
            
            # Market condition patterns
            patterns["market_condition_patterns"] = self._identify_market_patterns(collected_data)
            
            # Decision patterns
            patterns["decision_patterns"] = self._identify_decision_patterns(collected_data)
            
        except Exception as e:
            self.logger.error(f"Error identifying patterns: {e}")
            
        return patterns
    
    def _analyze_risk_metrics(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze risk-related metrics."""
        risk_analysis = {
            "exposure_analysis": {},
            "concentration_risk": {},
            "strategy_risk": {}
        }
        
        try:
            open_lots = trade_data.get("open_lots", [])
            
            if open_lots:
                # Exposure analysis
                total_value = sum(safe_float(lot.get("remaining_qty", 0)) * 
                                safe_float(lot.get("buy_price", 0)) for lot in open_lots)
                
                risk_analysis["exposure_analysis"] = {
                    "total_exposure": total_value,
                    "average_lot_exposure": total_value / len(open_lots) if open_lots else 0,
                    "largest_lot_exposure": max((safe_float(lot.get("remaining_qty", 0)) * 
                                               safe_float(lot.get("buy_price", 0))) for lot in open_lots) if open_lots else 0
                }
                
                # Concentration risk
                strategy_exposure = defaultdict(float)
                for lot in open_lots:
                    strategy = lot.get("type", "UNKNOWN")
                    lot_value = safe_float(lot.get("remaining_qty", 0)) * safe_float(lot.get("buy_price", 0))
                    strategy_exposure[strategy] += lot_value
                
                risk_analysis["concentration_risk"] = {
                    "strategy_concentration": dict(strategy_exposure),
                    "max_strategy_exposure": max(strategy_exposure.values()) if strategy_exposure else 0,
                    "strategy_count": len(strategy_exposure)
                }
                
        except Exception as e:
            self.logger.error(f"Error analyzing risk metrics: {e}")
            
        return risk_analysis
    
    def _analyze_efficiency(self, log_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze operational efficiency metrics."""
        efficiency = {
            "decision_efficiency": {},
            "execution_efficiency": {},
            "error_efficiency": {}
        }
        
        try:
            performance_metrics = log_analysis.get("performance_metrics", {})
            
            total_decisions = performance_metrics.get("total_decisions", 0)
            total_actions = performance_metrics.get("total_actions", 0)
            total_errors = performance_metrics.get("total_errors", 0)
            
            efficiency["decision_efficiency"] = {
                "decision_to_action_ratio": total_actions / total_decisions if total_decisions > 0 else 0,
                "total_decisions": total_decisions,
                "total_actions": total_actions
            }
            
            efficiency["error_efficiency"] = {
                "error_rate": total_errors / total_decisions if total_decisions > 0 else 0,
                "total_errors": total_errors,
                "errors_per_hour": 0  # Would need time window calculation
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing efficiency: {e}")
            
        return efficiency
    
    def _generate_insights(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate actionable insights from analysis."""
        self.logger.info("Generating performance insights...")
        
        insights = {
            "key_findings": [],
            "recommendations": [],
            "performance_alerts": [],
            "optimization_opportunities": []
        }
        
        try:
            trade_data = collected_data.get("trade_data", {})
            log_analysis = collected_data.get("log_analysis", {})
            
            # Generate key findings
            insights["key_findings"] = self._generate_key_findings(collected_data)
            
            # Generate recommendations
            insights["recommendations"] = self._generate_recommendations(collected_data)
            
            # Check for performance alerts
            insights["performance_alerts"] = self._check_performance_alerts(collected_data)
            
            # Identify optimization opportunities
            insights["optimization_opportunities"] = self._identify_optimization_opportunities(collected_data)
            
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            
        return insights
    
    def _initialize_database(self):
        """Initialize the analysis database."""
        try:
            conn = create_database_connection(self.db_path)
            
            # Create tables for storing analysis results
            conn.execute("""
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    analysis_type TEXT NOT NULL,
                    results TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_category TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("Analysis database initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
    
    def _store_analysis_results(self, results: Dict[str, Any]):
        """Store analysis results in database."""
        try:
            conn = create_database_connection(self.db_path)
            
            # Store main analysis results
            conn.execute("""
                INSERT INTO analysis_results (timestamp, analysis_type, results)
                VALUES (?, ?, ?)
            """, (results["analysis_timestamp"], "comprehensive", json.dumps(results, default=str)))
            
            conn.commit()
            conn.close()
            
            self.logger.info("Analysis results stored in database")
            
        except Exception as e:
            self.logger.error(f"Error storing analysis results: {e}")
    
    # Helper methods for specific analysis tasks
    def _analyze_position_distribution(self, open_lots: List[Dict]) -> Dict[str, Any]:
        """Analyze position distribution."""
        if not open_lots:
            return {}
        
        total_value = sum(safe_float(lot.get("remaining_qty", 0)) * 
                         safe_float(lot.get("buy_price", 0)) for lot in open_lots)
        
        return {
            "total_lots": len(open_lots),
            "total_value": total_value,
            "average_lot_value": total_value / len(open_lots),
            "value_distribution": "uniform"  # Could be enhanced with statistical analysis
        }
    
    def _analyze_lot_performance(self, open_lots: List[Dict]) -> Dict[str, Any]:
        """Analyze individual lot performance."""
        # This would require current market prices for accurate P&L calculation
        return {
            "total_lots": len(open_lots),
            "performance_summary": "Requires current market data for P&L calculation"
        }
    
    def _analyze_strategy_types(self, open_lots: List[Dict]) -> Dict[str, Any]:
        """Analyze effectiveness of different strategy types."""
        strategy_counts = defaultdict(int)
        strategy_values = defaultdict(float)
        
        for lot in open_lots:
            strategy = lot.get("type", "UNKNOWN")
            strategy_counts[strategy] += 1
            lot_value = safe_float(lot.get("remaining_qty", 0)) * safe_float(lot.get("buy_price", 0))
            strategy_values[strategy] += lot_value
        
        return {
            "strategy_distribution": dict(strategy_counts),
            "strategy_values": dict(strategy_values),
            "most_used_strategy": max(strategy_counts, key=strategy_counts.get) if strategy_counts else None
        }
    
    def _analyze_holding_periods(self, open_lots: List[Dict]) -> Dict[str, Any]:
        """Analyze holding period patterns."""
        holding_periods = []
        now = datetime.now()
        
        for lot in open_lots:
            buy_time_str = lot.get("buy_timestamp", "")
            buy_time = parse_timestamp(buy_time_str)
            if buy_time:
                holding_period = (now - buy_time.replace(tzinfo=None)).total_seconds() / 3600  # hours
                holding_periods.append(holding_period)
        
        if holding_periods:
            return {
                "average_holding_period": np.mean(holding_periods),
                "median_holding_period": np.median(holding_periods),
                "max_holding_period": max(holding_periods),
                "min_holding_period": min(holding_periods),
                "total_lots_analyzed": len(holding_periods)
            }
        
        return {}
    
    def _analyze_decision_distribution(self, ai_decisions: List[Dict]) -> Dict[str, Any]:
        """Analyze distribution of AI decisions."""
        decision_counts = defaultdict(int)
        
        for decision in ai_decisions:
            dec = decision.get("decision", "UNKNOWN")
            decision_counts[dec] += 1
        
        total_decisions = len(ai_decisions)
        
        return {
            "total_decisions": total_decisions,
            "decision_counts": dict(decision_counts),
            "decision_percentages": {k: (v/total_decisions)*100 for k, v in decision_counts.items()} if total_decisions > 0 else {}
        }
    
    def _analyze_decision_timing(self, ai_decisions: List[Dict]) -> Dict[str, Any]:
        """Analyze timing patterns in AI decisions."""
        # This would require more sophisticated time series analysis
        return {
            "total_decisions": len(ai_decisions),
            "analysis_note": "Timing analysis requires time series implementation"
        }
    
    def _analyze_decision_quality(self, ai_decisions: List[Dict], trade_actions: List[Dict]) -> Dict[str, Any]:
        """Analyze quality of AI decisions vs actual actions."""
        return {
            "total_decisions": len(ai_decisions),
            "total_actions": len(trade_actions),
            "decision_to_action_ratio": len(trade_actions) / len(ai_decisions) if ai_decisions else 0
        }
    
    def _analyze_error_patterns(self, errors: List[Dict]) -> Dict[str, Any]:
        """Analyze error patterns."""
        return {
            "total_errors": len(errors),
            "error_analysis": "Error pattern analysis requires error categorization"
        }
    
    # Additional helper methods would be implemented here for:
    # - _calculate_average_lot_size
    # - _calculate_position_concentration  
    # - _calculate_strategy_diversification
    # - _calculate_holding_period_risk
    # - _calculate_cash_utilization
    # - _calculate_trade_frequency
    # - _calculate_decision_efficiency
    # - _identify_temporal_patterns
    # - _identify_strategy_patterns
    # - _identify_market_patterns
    # - _identify_decision_patterns
    # - _generate_key_findings
    # - _generate_recommendations
    # - _check_performance_alerts
    # - _identify_optimization_opportunities
    
    def _calculate_average_lot_size(self, trade_data: Dict[str, Any]) -> float:
        """Calculate average lot size."""
        open_lots = trade_data.get("open_lots", [])
        if not open_lots:
            return 0.0
        
        total_value = sum(safe_float(lot.get("remaining_qty", 0)) * 
                         safe_float(lot.get("buy_price", 0)) for lot in open_lots)
        return total_value / len(open_lots)
    
    def _generate_key_findings(self, collected_data: Dict[str, Any]) -> List[str]:
        """Generate key findings from the analysis."""
        findings = []
        
        trade_data = collected_data.get("trade_data", {})
        trade_summary = trade_data.get("trade_summary", {})
        
        # Add findings based on data
        total_lots = trade_summary.get("total_lots", 0)
        if total_lots > 0:
            findings.append(f"Currently managing {total_lots} open positions")
            
            avg_holding = trade_summary.get("average_holding_period_hours", 0)
            if avg_holding > 0:
                findings.append(f"Average holding period: {avg_holding:.1f} hours")
        
        return findings
    
    def _generate_recommendations(self, collected_data: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Add basic recommendations
        recommendations.append("Continue monitoring performance patterns")
        recommendations.append("Consider implementing automated alerts for significant changes")
        
        return recommendations

    def _calculate_position_concentration(self, trade_data: Dict[str, Any]) -> float:
        """Calculate position concentration risk."""
        try:
            open_lots = trade_data.get("open_lots", [])
            if not open_lots:
                return 0.0

            total_value = sum(lot.get("current_value", 0) for lot in open_lots)
            if total_value == 0:
                return 0.0

            # Calculate Herfindahl-Hirschman Index for concentration
            concentrations = [(lot.get("current_value", 0) / total_value) ** 2 for lot in open_lots]
            hhi = sum(concentrations)
            return round(hhi, 4)
        except Exception as e:
            self.logger.error(f"Error calculating position concentration: {e}")
            return 0.0

    def _identify_temporal_patterns(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify temporal patterns in trading behavior."""
        try:
            return {
                "hourly_patterns": "Temporal analysis requires time-series data",
                "daily_patterns": "Daily pattern analysis not yet implemented",
                "weekly_patterns": "Weekly pattern analysis not yet implemented",
                "seasonal_patterns": "Seasonal analysis requires longer data history"
            }
        except Exception as e:
            self.logger.error(f"Error identifying temporal patterns: {e}")
            return {"error": str(e)}

    def _check_performance_alerts(self, collected_data: Dict[str, Any]) -> List[str]:
        """Check for performance-related alerts."""
        try:
            alerts = []
            trade_data = collected_data.get("trade_data", {})
            open_lots = trade_data.get("open_lots", [])

            # Check for high concentration risk
            if len(open_lots) > 50:
                alerts.append("High number of open positions detected")

            # Check for low cash availability
            bot_state = collected_data.get("bot_state", {})
            cash_available = bot_state.get("cash_available", 0)
            if cash_available < 15:
                alerts.append("Low cash availability for new trades")

            return alerts
        except Exception as e:
            self.logger.error(f"Error checking performance alerts: {e}")
            return [f"Alert check error: {str(e)}"]

    def _calculate_strategy_diversification(self, trade_data: Dict[str, Any]) -> float:
        """Calculate strategy diversification score."""
        try:
            open_lots = trade_data.get("open_lots", [])
            if not open_lots:
                return 0.0

            strategies = {}
            for lot in open_lots:
                strategy = lot.get("strategy", "unknown")
                strategies[strategy] = strategies.get(strategy, 0) + 1

            if len(strategies) <= 1:
                return 0.0

            total_lots = len(open_lots)
            diversity_score = 1 - sum((count/total_lots)**2 for count in strategies.values())
            return round(diversity_score, 4)
        except Exception as e:
            self.logger.error(f"Error calculating strategy diversification: {e}")
            return 0.0

    def _identify_strategy_patterns(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify strategy-related patterns."""
        try:
            return {
                "strategy_effectiveness": "Strategy pattern analysis requires historical data",
                "strategy_timing": "Timing analysis not yet implemented",
                "strategy_correlation": "Correlation analysis requires market data"
            }
        except Exception as e:
            self.logger.error(f"Error identifying strategy patterns: {e}")
            return {"error": str(e)}

    def _identify_optimization_opportunities(self, collected_data: Dict[str, Any]) -> List[str]:
        """Identify optimization opportunities."""
        try:
            opportunities = []
            trade_data = collected_data.get("trade_data", {})
            open_lots = trade_data.get("open_lots", [])

            if len(open_lots) > 50:
                opportunities.append("Consider position size optimization")

            opportunities.append("Monitor for strategy performance patterns")
            opportunities.append("Implement automated rebalancing alerts")

            return opportunities
        except Exception as e:
            self.logger.error(f"Error identifying optimization opportunities: {e}")
            return [f"Optimization analysis error: {str(e)}"]
