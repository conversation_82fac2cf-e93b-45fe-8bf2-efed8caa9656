# START OF FILE main_bot.py

import logging
from logging.handlers import RotatingFileHandler
from logging import StreamHandler
import time
import schedule
from datetime import datetime, date, timedelta, UTC
import os
import threading
import signal
import json
import csv
import subprocess
import pytz
from decimal import Decimal, InvalidOperation
import sys
from typing import Any, Dict

# Define timezone for display purposes
LONDON_TZ = pytz.timezone('Europe/London')

import config
import alpaca_service
import ai_decision_service
import binance_data_service
import market_analysis
from bot_state import save_bot_state, load_bot_state
from safety_net import check_safety_net
from email_alert import send_alert, send_enhanced_alert, AlertPriority, AlertCategory
import trade_logic
from trade_logic import update_trailing_stops
import dynamic_parameter_service
import order_tracker_service

# Phase 4: Live Integration Manager
try:
    from optimization.live_integration_manager import get_dynamic_parameters as live_get_dynamic_parameters
    PHASE4_AVAILABLE = True
    logger = logging.getLogger("TradingBotApp.Main")
    logger.info("Phase 4 Live Integration System loaded successfully")
except ImportError as e:
    PHASE4_AVAILABLE = False
    logger = logging.getLogger("TradingBotApp.Main")
    logger.warning(f"Phase 4 not available, using original system: {e}")

from error_handler import ErrorSeverity, ErrorCategory as ErrorCat, handle_error
from error_utils import (
    TradingLogicErrorHandler, SystemError, TradingError, safe_execute
)

# AI Performance Analyzer Integration
try:
    from ai_performance_analyzer.main import AIPerformanceAnalyzer
    PERFORMANCE_ANALYZER_AVAILABLE = True
    logger.info("AI Performance Analyzer loaded successfully")
except ImportError as e:
    PERFORMANCE_ANALYZER_AVAILABLE = False
    logger.warning(f"AI Performance Analyzer not available: {e}")
from security.access_control import require_authentication

from alpaca_trade_api.rest import REST, APIError
from requests.exceptions import RequestException
import google.generativeai as genai

# --- Import Centralized Configuration ---
# All configuration is now centralized in config.py

# --- Logger Setup ---
def setup_loggers():
    # Main application logger
    logger = logging.getLogger('TradingBotApp')
    logger.setLevel(logging.INFO)
    if not logger.handlers:
        fh = RotatingFileHandler(config.LOG_FILE_PATH, maxBytes=config.MAX_LOG_SIZE, backupCount=3)
        fmt = logging.Formatter('%(asctime)s - %(levelname)-8s - %(message)s')
        fh.setFormatter(fmt)
        logger.addHandler(fh)
        ch = StreamHandler()
        ch.setFormatter(fmt)
        ch.setLevel(logging.WARNING) # Set console output to WARNING level
        logger.addHandler(ch)

    # AI responses logger
    ai_logger = logging.getLogger("AI")
    ai_logger.setLevel(logging.WARNING) # Keep file logging at WARNING
    if not ai_logger.handlers:
        ai_fh = RotatingFileHandler(config.AI_LOG_PATH, maxBytes=config.MAX_LOG_SIZE, backupCount=3)
        ai_fh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)-8s - %(message)s'))
        ai_logger.addHandler(ai_fh)

    # Debug logger
    debug_logger = logging.getLogger("TradingBotApp.Debug")
    debug_logger.setLevel(logging.DEBUG) # Keep file logging at DEBUG
    if not debug_logger.handlers:
        debug_fh = RotatingFileHandler(config.DEBUG_LOG_FILE_PATH, maxBytes=config.MAX_LOG_SIZE, backupCount=3)
        debug_fh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)-8s - %(message)s'))
        debug_logger.addHandler(debug_fh)
        debug_ch = StreamHandler()
        debug_ch.setFormatter(fmt)
        debug_ch.setLevel(logging.ERROR) # Set console output to ERROR level
        debug_logger.addHandler(debug_ch)

setup_loggers()
logger = logging.getLogger('TradingBotApp')

class TradingBot:
    def __init__(self):
        self.shutdown_event = threading.Event()
        self._retrain_file_lock = threading.Lock()
        self.api_client = None
        self.binance_data_thread = None
        self.debug_logger = logging.getLogger("TradingBotApp.Debug")

        self.equity_day_open: Decimal | None = None
        self.day_open_date = None

        self.buy_count = 0
        self.sell_count = 0
        self.last_trade_action_summary = "Bot Started - No Cycle Yet"
        self.gemini_call_counter = 0

        self.session_stats = {
            "equity_start": None, "equity_max": None, "equity_min": None,
            "net_pl": Decimal("0.0"), "cycle_count": 0, "win_count": 0, "loss_count": 0,
            "max_drawdown": Decimal("0.0"), "win_streak": 0, "loss_streak": 0,
            "last_equity": None, "last_action": None,
            "daily_start_equity": None, "daily_start_date": None,
            "weekly_start_equity": None, "weekly_start_date": None,
            "safety_triggered": False, "safety_reason": "", "safety_day": None,
            "last_retrain_date": None, "pause_until": None
        }

        # AI Performance Analyzer
        self.performance_analyzer = None
        self.analyzer_thread = None

        self.last_ai_decision_cached = {"decision": "HOLD", "reasoning": "Bot startup default.", "confidence_level": "LOW", "is_dip_buy": False, "quantity_percentage": None, "stop_loss_price": None, "take_profit_price": None, "min_profit_override_usd": None}
        self.last_dynamic_params_cached = {
            "risk_percent": float(config.RISK_PERCENT),
            "stop_percent": float(config.STOP_PERCENT),
            "profit_targets": [float(p) for p in config.TAKE_PROFITS],
            "profit_shares": [float(s) for s in config.TAKE_PROFIT_SHARES]
        }

    def _save_session_stats(self):
        try:
            stats_to_save = {k: str(v) if isinstance(v, Decimal) else (v.isoformat() if isinstance(v, (datetime, date)) else v) for k, v in self.session_stats.items()}
            with open(config.SESSION_STATS_FILE, 'w') as f:
                json.dump(stats_to_save, f, indent=4)
            logger.debug(f"Session stats saved to {config.SESSION_STATS_FILE}")
        except (IOError, TypeError) as e:
            logger.error(f"Failed to save session stats: {e}")

    def _load_session_stats(self):
        if not os.path.exists(config.SESSION_STATS_FILE) or os.path.getsize(config.SESSION_STATS_FILE) == 0:
            logger.warning(f"Session stats file {config.SESSION_STATS_FILE} not found or is empty. Starting fresh with default state.")
            return
        try:
            with open(config.SESSION_STATS_FILE, 'r') as f:
                loaded_stats_raw = json.load(f)
            
            for k, v in loaded_stats_raw.items():
                if k in ["net_pl", "max_drawdown", "daily_start_equity", "weekly_start_equity"]:
                    try:
                        self.session_stats[k] = Decimal(str(v)) if v is not None else Decimal("0.0")
                    except InvalidOperation:
                        self.session_stats[k] = Decimal("0.0")
                elif k in ["equity_start", "equity_max", "equity_min", "last_equity"]:
                    try:
                        self.session_stats[k] = float(v) if v is not None else None
                    except (ValueError, TypeError):
                        self.session_stats[k] = None
                elif k in ["daily_start_date", "safety_day", "weekly_start_date", "last_retrain_date"]:
                    try:
                        self.session_stats[k] = datetime.fromisoformat(str(v)).date() if v else None
                    except (ValueError, TypeError):
                        self.session_stats[k] = None
                elif k == "pause_until" and v:
                    try:
                        self.session_stats[k] = datetime.fromisoformat(str(v))
                    except (ValueError, TypeError):
                        self.session_stats[k] = None
                else:
                    self.session_stats[k] = v
            logger.info(f"Session stats loaded from {config.SESSION_STATS_FILE}")
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error loading session stats from {config.SESSION_STATS_FILE}: {e}. Starting fresh with default state.")

    def _run_retrain_sequence_in_background(self):
        if not self._retrain_file_lock.acquire(blocking=False):
            logger.warning("Background Retrain: Could not acquire lock, another retrain is already in progress.")
            return
        try:
            logger.info("Background Retrain: Acquired lock, starting sequence.")
            python_exe = os.path.join(config.BASE_DIR, "venv", "Scripts", "python.exe")
            if not os.path.exists(python_exe):
                python_exe = sys.executable
            
            scripts_to_run = ["multi_exchange_downloader.py", "merge_data_files.py", "generate_rsi_labels.py", "prepare_ml_data.py", "train_trade_model.py"]
            for script_name in scripts_to_run:
                script_path = os.path.join(config.BASE_DIR, script_name)
                if not os.path.exists(script_path):
                    logger.error(f"Background Retrain: Script not found: {script_name}. Aborting.")
                    send_alert(subject="AI Bot: Daily Retrain FAILED (Script Missing)", message=f"Script '{script_name}' not found.")
                    return

                for attempt in range(config.RETRAIN_MAX_RETRIES):
                    logger.info(f"Background Retrain: Running script: {script_name} (Attempt {attempt + 1}/{config.RETRAIN_MAX_RETRIES})")
                    try:
                        result = subprocess.run([python_exe, script_path], capture_output=True, text=True, cwd=config.BASE_DIR, check=False, timeout=600)
                        if result.returncode != 0:
                            logger.error(f"Background Retrain: Script '{script_name}' failed. Stderr:\n{result.stderr}")
                            if attempt < config.RETRAIN_MAX_RETRIES - 1:
                                logger.warning(f"Background Retrain: Retrying {script_name} in {config.RETRAIN_RETRY_DELAY_SECONDS} seconds...")
                                time.sleep(config.RETRAIN_RETRY_DELAY_SECONDS)
                            else:
                                send_alert(subject=f"AI Bot: Daily Retrain FAILED ({script_name})", message=f"Exit Code: {result.returncode}\nStderr:\n{result.stderr}")
                                return
                        else:
                            logger.info(f"Background Retrain: Script '{script_name}' completed successfully.")
                            break # Exit retry loop on success
                    except subprocess.TimeoutExpired:
                        logger.error(f"Background Retrain: Script '{script_name}' timed out.")
                        if attempt < config.RETRAIN_MAX_RETRIES - 1:
                            logger.warning(f"Background Retrain: Retrying {script_name} in {config.RETRAIN_RETRY_DELAY_SECONDS} seconds due to timeout...")
                            time.sleep(config.RETRAIN_RETRY_DELAY_SECONDS)
                        else:
                            send_alert(subject=f"AI Bot: Daily Retrain FAILED (Timeout - {script_name})", message="The script took longer than 5 minutes to complete after multiple attempts.")
                            return
                    except Exception as e:
                        context = {
                            "script_name": script_name,
                            "attempt": attempt + 1,
                            "max_retries": config.RETRAIN_MAX_RETRIES
                        }
                        error = SystemError(
                            f"Background retrain script error for {script_name}: {e}",
                            ErrorSeverity.HIGH,
                            context,
                            e
                        )
                        handle_error(error)

                        if attempt < config.RETRAIN_MAX_RETRIES - 1:
                            logger.warning(f"Background Retrain: Retrying {script_name} in {config.RETRAIN_RETRY_DELAY_SECONDS} seconds due to error...")
                            time.sleep(config.RETRAIN_RETRY_DELAY_SECONDS)
                        else:
                            send_enhanced_alert(
                                f"Daily Retrain FAILED - {script_name}",
                                f"Script {script_name} failed after {config.RETRAIN_MAX_RETRIES} attempts. Error: {e}",
                                AlertPriority.HIGH,
                                AlertCategory.SYSTEM,
                                {"script_name": script_name, "error": str(e), "attempts": config.RETRAIN_MAX_RETRIES}
                            )
                            return
            
            logger.warning("Background Retrain: Finished automated retraining successfully.")
            send_alert(subject="AI Bot: Daily retrain complete", message=f"Daily retraining cycle finished successfully at {datetime.now().isoformat()}")

        finally:
            self._retrain_file_lock.release()
            logger.info("Background Retrain: Releasing lock.")

    def retrain_daily_thread(self):
        while not self.shutdown_event.is_set():
            now = datetime.now()
            current_day = now.date()
            retrain_is_due = (self.session_stats.get("last_retrain_date") is None) or (self.session_stats["last_retrain_date"] < current_day and now.hour >= 4)
            
            if retrain_is_due:
                if self._retrain_file_lock.locked():
                    logger.info("Daily retrain check: Retraining is due, but the process is already running. Skipping.")
                else:
                    logger.warning("Daily retrain: Triggering automated retraining process in a background thread.")
                    self.session_stats["last_retrain_date"] = current_day
                    self._save_session_stats()
                    
                    background_retrain_thread = threading.Thread(
                        target=self._run_retrain_sequence_in_background,
                        daemon=True,
                        name="RetrainSequenceThread"
                    )
                    background_retrain_thread.start()
            
            self.shutdown_event.wait(60 * 30)

    def initialize_services(self):
        logger.warning("Bot: Initializing services.")
        self._load_session_stats()
        self.session_stats["cycle_count"] = 0

        # Load bot_state for initialization
        bot_state_data = load_bot_state()
        logger.info("Bot startup: Trading freely following core safety rules (never sell at a loss).")
        
        if not config.GEMINI_API_KEY:
            logger.error("GEMINI_API_KEY not set. AI will fail.")
        
        logger.warning("Bot: Starting Binance Data Service thread...")
        binance_data_service.shutdown_event.clear()
        self.binance_data_thread = threading.Thread(target=binance_data_service.run_binance_websocket_client_persistent, daemon=True, name="BinanceDataThread")
        self.binance_data_thread.start()
        logger.warning(f"Bot: Binance thread started. Thread alive: {self.binance_data_thread.is_alive()}")

        # Wait for initial market data to be available
        logger.warning("Bot: Waiting for initial market data...")
        if binance_data_service.wait_for_initial_data(timeout_seconds=30):
            logger.warning("Bot: Initial market data received successfully")
        else:
            logger.warning("Bot: Proceeding without initial market data (timeout)")

        logger.info("Bot: Initializing Alpaca REST API client.")
        try:
            self.api_client = REST(key_id=config.ALPACA_API_KEY_ID, secret_key=config.ALPACA_SECRET_KEY, base_url=config.ALPACA_BASE_URL, api_version='v2')
            acct = alpaca_service.get_account_info(self.api_client)
            pos = alpaca_service.get_positions(self.api_client, symbol=config.PRIMARY_TRADING_SYMBOL.replace("/", ""))
            
            if acct is None:
                raise ConnectionError("Failed to fetch account info from Alpaca on startup.")
            
            equity = Decimal(str(acct.get("equity", "0.0")))
            logger.info(f"Alpaca API connected. Current Equity: ${equity:.2f}")

            from datetime import datetime, timezone
            self.equity_day_open = Decimal(str(acct["equity"])).quantize(Decimal("0.01"))
            self.day_open_date   = datetime.now(timezone.utc).date()
            
            if self.session_stats.get("equity_start") is None:
                self.session_stats["equity_start"] = float(equity)
                self.session_stats["equity_max"] = float(equity)
                self.session_stats["equity_min"] = float(equity)
            self.session_stats["last_equity"] = float(equity)
            
            today = datetime.today().date()
            if self.session_stats.get("daily_start_date") is None or self.session_stats["daily_start_date"] < today:
                self.session_stats["daily_start_equity"] = equity
                self.session_stats["daily_start_date"] = today
            
            weekly_start_date = self.session_stats.get("weekly_start_date")
            if weekly_start_date is None or weekly_start_date.isocalendar()[1] != today.isocalendar()[1]:
                self.session_stats["weekly_start_equity"] = equity
                self.session_stats["weekly_start_date"] = today

            load_bot_state()
            trade_logic.load_open_lots()
            order_tracker_service.load_active_orders()
            
            with self._retrain_file_lock:
                trade_logic.prune_lots_to_available(self.api_client, pos, {})
                with trade_logic.lots():
                    lots_copy = [lot.copy() for lot in trade_logic.open_lots]
                market_indicators_initial = market_analysis.analyze_market(account_info=acct, positions_info=pos, open_lots_list=lots_copy)

            # Ensure market_indicators_initial is a dictionary
            if not isinstance(market_indicators_initial, dict):
                logger.error(f"market_analysis.analyze_market returned non-dict: {type(market_indicators_initial)}")
                market_indicators_initial = {}

            if not market_indicators_initial or market_indicators_initial.get("rsi") is None:
                logger.warning("Bot: Skipping initial Gemini AI call due to insufficient market data.")
            else:
                logger.info("Bot: Getting trade history for dynamic parameters...")
                history = trade_logic.get_trade_history_for_dynamic_params()

                # Smart AI call with timeout and retry logic
                new_ai = self._get_initial_ai_decision_with_timeout(market_indicators_initial)

                if new_ai:
                    # Phase 4: Use live integration manager if available, fallback to original
                    logger.info("Bot: Getting dynamic parameters...")
                    if PHASE4_AVAILABLE:
                        new_params = live_get_dynamic_parameters(market_indicators_initial, new_ai, trade_history=history)
                    else:
                        new_params = dynamic_parameter_service.get_dynamic_parameters(market_indicators_initial, new_ai, trade_history=history)
                    logger.info("Bot: Dynamic parameters retrieved successfully.")

                    # Ensure new_params is a dictionary
                    if not isinstance(new_params, dict):
                        logger.error(f"Initial dynamic parameters returned non-dict: {type(new_params)}, using config defaults")
                        new_params = {
                            "risk_percent": float(config.RISK_PERCENT),
                            "stop_percent": float(config.STOP_PERCENT),
                            "profit_targets": [float(p) for p in config.TAKE_PROFITS],
                            "profit_shares": [float(s) for s in config.TAKE_PROFIT_SHARES]
                        }

                    self.last_ai_decision_cached = new_ai
                    self.last_dynamic_params_cached = new_params
                    logger.info("Bot: Initial Gemini AI decision and dynamic parameters cached.")
                else:
                    logger.warning("Bot: Failed to get initial AI decision, using safe defaults for startup.")
                    # Set safe default values to ensure bot can start
                    self.last_ai_decision_cached = {"decision": "HOLD", "confidence_level": "LOW"}
                    if PHASE4_AVAILABLE:
                        fallback_params = live_get_dynamic_parameters(market_indicators_initial, self.last_ai_decision_cached, trade_history=history)
                    else:
                        fallback_params = dynamic_parameter_service.get_dynamic_parameters(market_indicators_initial, self.last_ai_decision_cached, trade_history=history)

                    # Ensure fallback_params is a dictionary
                    if not isinstance(fallback_params, dict):
                        logger.error(f"Fallback dynamic parameters returned non-dict: {type(fallback_params)}, using config defaults")
                        fallback_params = {
                            "risk_percent": float(config.RISK_PERCENT),
                            "stop_percent": float(config.STOP_PERCENT),
                            "profit_targets": [float(p) for p in config.TAKE_PROFITS],
                            "profit_shares": [float(s) for s in config.TAKE_PROFIT_SHARES]
                        }

                    self.last_dynamic_params_cached = fallback_params
                    logger.info("Bot: Safe default parameters set, will get fresh AI decision in first trade cycle.")
            
            self._save_session_stats()
            return True
        except (APIError, RequestException, ConnectionError) as e:
            error = SystemError(
                f"Network or API error during bot initialization: {e}",
                ErrorSeverity.CRITICAL,
                {"operation": "bot_initialization", "error_type": "network_api"},
                e
            )
            handle_error(error)

            send_enhanced_alert(
                "Bot Initialization Failed - Network/API Error",
                f"Bot failed to initialize due to network or API error: {e}",
                AlertPriority.URGENT,
                AlertCategory.SYSTEM,
                {"error": str(e), "error_type": "network_api"}
            )

            self.shutdown_event.set()
            return False

        except Exception as e:
            error = SystemError(
                f"Critical unexpected error during bot initialization: {e}",
                ErrorSeverity.CRITICAL,
                {"operation": "bot_initialization", "error_type": "unexpected"},
                e
            )
            handle_error(error)

            send_enhanced_alert(
                "Bot Initialization Failed - Critical Error",
                f"Bot failed to initialize due to unexpected error: {e}",
                AlertPriority.URGENT,
                AlertCategory.SYSTEM,
                {"error": str(e), "error_type": "unexpected"}
            )

            self.shutdown_event.set()
            return False

    def _rollover_day_open(self, current_equity: Decimal):
        """Reset open-of-day equity at UTC 00:00 for daily performance tracking."""
        from datetime import datetime, timezone
        today = datetime.now(timezone.utc).date()
        if today != self.day_open_date:
            self.equity_day_open = current_equity
            self.day_open_date   = today
            logger.info("Day rollover: equity_day_open reset for daily performance tracking.")

    def _check_daily_performance(self):
        """Check daily performance for informational purposes only - does NOT restrict trading."""
        # Use session_stats daily_start_equity for consistency
        daily_start_equity = Decimal(str(self.session_stats.get("daily_start_equity", "0.0")))
        equity_now = Decimal(str(self.session_stats.get("equity_now", "0.0")))

        pnl_today = equity_now - daily_start_equity
        target_usd = (daily_start_equity * config.TARGET_DAILY_RETURN_PCT).quantize(Decimal("0.01"))

        self.debug_logger.debug(f"Daily Performance: equity_now={equity_now:.2f}, daily_start_equity={daily_start_equity:.2f}, pnl_today={pnl_today:.2f}, target_usd={target_usd:.2f}")

        # INFORMATIONAL ONLY - celebrate milestones but don't restrict trading
        if pnl_today >= target_usd:
            logger.info(f"[TARGET] Daily target achieved! P&L: +${pnl_today:.2f} (target: ${target_usd:.2f}). Bot continues trading freely.")
        elif pnl_today > Decimal("0"):
            logger.info(f"[PROFIT] Daily profit: +${pnl_today:.2f} (target: ${target_usd:.2f}). Bot trading freely.")
        else:
            logger.info(f"[P&L] Daily P&L: ${pnl_today:.2f} (target: ${target_usd:.2f}). Bot trading freely.")

    def robust_run_trade_cycle(self):
        try:
            acct_current = alpaca_service.get_account_info(self.api_client)
            if acct_current is None:
                logger.error("Failed to fetch account info. Skipping cycle.")
                return
            self.session_stats["equity_now"] = Decimal(str(acct_current.get("equity", "0.0")))
            self._rollover_day_open(self.session_stats["equity_now"])
            if self._retrain_file_lock.locked():
                logger.info("Trade cycle is paused while model retraining is in progress.")
                return

            if (pause_until := self.session_stats.get("pause_until")) and datetime.now(UTC) < pause_until:
                logger.warning(f"TRADING PAUSED due to 3-strike loss rule. Resumes in {str(pause_until - datetime.now(UTC)).split('.')[0]}.")
                return

            bot_state_data = load_bot_state()
            order_tracker_service.reconcile_open_orders(self.api_client)

            acct_current = alpaca_service.get_account_info(self.api_client)
            pos = alpaca_service.get_positions(self.api_client, symbol=config.PRIMARY_TRADING_SYMBOL.replace("/", ""))
            
            if acct_current is None:
                logger.error("Failed to fetch account info. Skipping cycle.")
                return
            
            equity_dec = Decimal(str(acct_current.get("equity", "0.0")))
            
            trade_logic.prune_lots_to_available(self.api_client, pos, {})
            
            with trade_logic.lots():
                current_open_lots_copy = [lot.copy() for lot in trade_logic.open_lots]
            
            ind = market_analysis.analyze_market(account_info=acct_current, positions_info=pos, open_lots_list=current_open_lots_copy)

            # Ensure ind is a dictionary
            if not isinstance(ind, dict):
                logger.error(f"market_analysis.analyze_market returned non-dict: {type(ind)}")
                ind = {"error": "Market analysis returned invalid format"}

            if ind.get("error"):
                logger.error(f"Cycle: Insufficient market data: {ind['error']}. Skipping logic.")
                return

            if self.session_stats.get("safety_triggered", False):
                logger.warning(f"Bot remains HALTED due to safety trigger: {self.session_stats.get('safety_reason')}. Skipping cycle.")
                return

            if self.session_stats.get("loss_streak", 0) >= config.LOSS_STREAK_LIMIT:
                pause_duration = timedelta(seconds=config.LOSS_PAUSE_SECONDS)
                self.session_stats["pause_until"] = datetime.now(UTC) + pause_duration
                self.session_stats["loss_streak"] = 0
                self._save_session_stats()
                pause_msg = f"3-STRIKE LOSS LIMIT REACHED. Pausing for {pause_duration}."
                logger.critical(pause_msg)
                send_alert(subject="AI Bot: Trading Paused (3-Strike Loss Rule)", message=pause_msg)
                return

            current_ai = self.last_ai_decision_cached
            current_params = self.last_dynamic_params_cached
            
            if self.gemini_call_counter % config.GEMINI_AI_CALL_INTERVAL_CYCLES == 0:
                logger.info(f"Gemini AI: Making fresh call (Cycle counter {self.gemini_call_counter}).")
                decision = ai_decision_service.get_ai_decision(ind, f"TRADING_{self.gemini_call_counter}")

                # Ensure decision is a dictionary
                if not isinstance(decision, dict):
                    logger.error(f"AI decision service returned non-dict: {type(decision)}")
                    decision = {
                        "decision": "HOLD",
                        "reasoning": "Error: AI service returned invalid format",
                        "confidence_level": "LOW",
                        "quantity_percentage": None
                    }

                if decision.get("quantity_percentage") is None and decision.get("decision") == "BUY":
                    decision["quantity_percentage"] = float(config.MIN_QTY_PCT_CONFIG)
                elif decision.get("quantity_percentage") == 0.0 and decision.get("decision") == "BUY":
                    decision["decision"] = "HOLD"
                current_ai = decision
                history = trade_logic.get_trade_history_for_dynamic_params()
                # Phase 4: Use live integration manager if available, fallback to original
                if PHASE4_AVAILABLE:
                    current_params = live_get_dynamic_parameters(ind, current_ai, trade_history=history)
                else:
                    current_params = dynamic_parameter_service.get_dynamic_parameters(ind, current_ai, trade_history=history)

                # Ensure current_params is a dictionary
                if not isinstance(current_params, dict):
                    logger.error(f"Dynamic parameters returned non-dict: {type(current_params)}, using cached defaults")
                    current_params = self.last_dynamic_params_cached

                self.last_ai_decision_cached = current_ai
                self.last_dynamic_params_cached = current_params
            else:
                logger.info(f"Gemini AI: Using cached decision (Cycle counter {self.gemini_call_counter}).")
                # Use cached values but ensure they are dictionaries
                current_ai = self.last_ai_decision_cached
                current_params = self.last_dynamic_params_cached

                # Ensure cached values are dictionaries
                if not isinstance(current_ai, dict):
                    logger.error(f"Cached AI decision is not a dict: {type(current_ai)}, using safe default")
                    current_ai = {"decision": "HOLD", "confidence_level": "LOW"}

                if not isinstance(current_params, dict):
                    logger.error(f"Cached params are not a dict: {type(current_params)}, using config defaults")
                    current_params = {
                        "risk_percent": float(config.RISK_PERCENT),
                        "stop_percent": float(config.STOP_PERCENT),
                        "profit_targets": [float(p) for p in config.TAKE_PROFITS],
                        "profit_shares": [float(s) for s in config.TAKE_PROFIT_SHARES]
                    }

            self.gemini_call_counter += 1

            # Recover any lots that might be stuck before processing trades
            trade_logic.recover_stuck_lots()

            # Update trailing stops before making a new decision
            current_price_for_stops = ind.get("avg_price")
            if current_price_for_stops:
                # Ensure current_params is a dictionary before calling .get()
                if isinstance(current_params, dict):
                    stop_percent = current_params.get("stop_percent", config.STOP_PERCENT)
                else:
                    logger.error(f"current_params is not a dict: {type(current_params)}, using default stop_percent")
                    stop_percent = config.STOP_PERCENT
                update_trailing_stops(Decimal(str(current_price_for_stops)), Decimal(str(stop_percent)))
            
            # Process the main AI decision and execute trades
            returned_context, b_count, s_count = trade_logic.process_ai_decision_and_trade(
                self.api_client, current_ai, acct_current, pos, ind, self.buy_count, self.sell_count, current_params, bot_state_data, self.session_stats
            )
            
            self.buy_count, self.sell_count = b_count, s_count
            self.session_stats["cycle_count"] += 1
            equity_after = returned_context.get("equity", float(equity_dec))
            if self.session_stats.get("equity_max") is None or equity_after > self.session_stats.get("equity_max", float('-inf')): self.session_stats["equity_max"] = equity_after
            if self.session_stats.get("equity_min") is None or equity_after < self.session_stats.get("equity_min", float('inf')): self.session_stats["equity_min"] = equity_after
            if (max_equity := self.session_stats.get("equity_max", 0.0)) > 0:
                drawdown = (Decimal(str(max_equity)) - Decimal(str(equity_after))) / Decimal(str(max_equity))
                if drawdown > self.session_stats.get("max_drawdown", Decimal("0.0")):
                    self.session_stats["max_drawdown"] = drawdown
            self.session_stats["last_equity"] = equity_after

            # Ensure returned_context is a dict to prevent .get() errors
            if isinstance(returned_context, str):
                self.last_trade_action_summary = returned_context
            elif isinstance(returned_context, dict):
                self.last_trade_action_summary = returned_context.get("action", "No action")
            else:
                logger.error(f"Unexpected returned_context type: {type(returned_context)}")
                self.last_trade_action_summary = "Error: Invalid context type"

            self._check_daily_performance()

            self.print_trade_summary(returned_context, ind)

        except (APIError, RequestException) as e:
            context = {"operation": "trade_cycle", "cycle_count": getattr(self, 'cycle_count', 0)}
            error = SystemError(
                f"Network/API error in trade cycle: {e}",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
            handle_error(error)
            self.last_trade_action_summary = f"ERROR: API/Network Failed ({type(e).__name__})"

        except (ValueError, TypeError, KeyError, AttributeError) as e:
            context = {"operation": "trade_cycle", "cycle_count": getattr(self, 'cycle_count', 0)}
            error = TradingError(
                f"Data processing or logic error in trade cycle: {e}",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
            handle_error(error)
            self.last_trade_action_summary = f"ERROR: Data/Logic Failed ({type(e).__name__})"

        except Exception as e:
            context = {"operation": "trade_cycle", "cycle_count": getattr(self, 'cycle_count', 0)}
            error = SystemError(
                f"Unexpected unhandled error in trade cycle: {e}",
                ErrorSeverity.HIGH,
                context,
                e
            )
            handle_error(error)

            # Send alert for unexpected trade cycle errors
            send_enhanced_alert(
                "Trade Cycle Unexpected Error",
                f"Unexpected error occurred in trade cycle: {e}",
                AlertPriority.HIGH,
                AlertCategory.TRADING,
                {"error": str(e), "cycle_count": getattr(self, 'cycle_count', 0)}
            )

            self.last_trade_action_summary = f"ERROR: Unhandled Exception ({type(e).__name__})"
        
        self.session_stats["last_action"] = self.last_trade_action_summary
        self._save_session_stats()

    def _get_initial_ai_decision_with_timeout(self, market_indicators: dict, timeout_seconds: int = 90):
        """
        Get initial AI decision with smart timeout and retry logic.

        Args:
            market_indicators: Market analysis data
            timeout_seconds: Maximum time to wait for AI response

        Returns:
            AI decision dict or None if failed
        """
        import threading
        import time
        from datetime import datetime

        logger.info(f"Bot: Calling Gemini AI for initial decision (timeout: {timeout_seconds}s)...")
        start_time = time.time()

        # Use threading to implement timeout
        ai_result = [None]  # Use list to allow modification in thread
        ai_error = [None]

        def ai_call_thread():
            try:
                ai_result[0] = ai_decision_service.get_ai_decision(market_indicators, "INITIAL_STARTUP")
            except Exception as e:
                ai_error[0] = e

        # Start AI call in separate thread
        ai_thread = threading.Thread(target=ai_call_thread, daemon=True)
        ai_thread.start()

        # Monitor progress with periodic updates
        last_update = time.time()
        while ai_thread.is_alive():
            elapsed = time.time() - start_time

            # Show progress every 15 seconds
            if time.time() - last_update >= 15:
                logger.info(f"Bot: AI call in progress... ({elapsed:.1f}s elapsed)")
                last_update = time.time()

            # Check timeout
            if elapsed >= timeout_seconds:
                logger.warning(f"Bot: AI call timeout after {timeout_seconds}s - will retry in trade cycle")
                return None

            time.sleep(1)

        # Check results
        if ai_error[0]:
            logger.error(f"Bot: AI call failed: {ai_error[0]}")
            return None

        if ai_result[0]:
            elapsed = time.time() - start_time
            logger.info(f"Bot: Gemini AI call completed successfully in {elapsed:.1f}s")
            return ai_result[0]

        logger.warning("Bot: AI call completed but returned no result")
        return None

    def print_trade_summary(self, context, ind: dict):
        # Handle case where context might be a string
        if isinstance(context, str):
            action = context
            decision = "UNKNOWN"
            context = {"action": action, "decision": decision}  # Create minimal dict
        elif not isinstance(context, dict):
            logger.error(f"print_trade_summary received invalid context type: {type(context)}")
            context = {"action": "Error: Invalid context", "decision": "UNKNOWN"}

        # Use fresh equity from session_stats (updated from Alpaca API) instead of context
        eq = float(self.session_stats.get("equity_now", 0.0))
        action = context.get("action", "No action")
        decision = context.get("decision", "UNKNOWN")
        unrealized_pl = context.get("profit_loss", 0.0)
        unrealized_pl_text = f"\033[92m${unrealized_pl:.2f}\033[0m" if unrealized_pl > 0 else f"\033[91m-${abs(unrealized_pl):.2f}\033[0m" if unrealized_pl < 0 else f"${unrealized_pl:.2f}"
        net_pl = self.session_stats.get("net_pl", Decimal("0.0"))
        net_pl_text = f"\033[92m${net_pl:.2f}\033[0m" if net_pl > 0 else f"\033[91m-${abs(net_pl):.2f}\033[0m" if net_pl < 0 else f"${net_pl:.2f}"
        
        # Extracting new information from ind and session_stats
        available_cash = ind.get("available_cash_usd", 0.0)
        total_open_position_value = ind.get("total_open_position_value_usd", 0.0)
        
        daily_start_equity = self.session_stats.get("daily_start_equity", Decimal("0.0"))
        current_equity_dec = Decimal(str(eq)) # Convert float equity back to Decimal for precise calculation
        daily_pnl = current_equity_dec - daily_start_equity
        daily_pnl_text = f"\033[92m${daily_pnl:.2f}\033[0m" if daily_pnl > 0 else f"\033[91m-${abs(daily_pnl):.2f}\033[0m" if daily_pnl < 0 else f"${daily_pnl:.2f}"

        print("="*50)
        cycle_timestamp_str = context.get("timestamp", datetime.now(UTC).isoformat())
        try:
            cycle_dt_utc = datetime.fromisoformat(cycle_timestamp_str.replace('Z', '+00:00'))
            print(f"Trade Cycle Summary @ {cycle_dt_utc.astimezone(LONDON_TZ).isoformat()}")
        except ValueError:
            print(f"Trade Cycle Summary @ {cycle_timestamp_str}")
        print(f"Action Taken      : {action}\nAI Decision       : {decision}\n" + "-"*50)
        print(f"Current Equity    : ${eq:.2f}")
        print(f"  Cash Available  : ${available_cash:.2f}")
        print(f"  BTC Position Val: ${total_open_position_value:.2f}")
        print(f"Unrealized P/L    : {unrealized_pl_text}")
        print(f"Daily P/L         : {daily_pnl_text}")
        print(f"Open Lots         : {len(context.get('open_lots', []))}")

        if (pause_until := self.session_stats.get("pause_until")) and datetime.now(UTC) < pause_until:
            remaining_pause = pause_until - datetime.now(UTC)
            print(f"\033[93m!!! BOT PAUSED (3-Strike Loss) !!! Resumes in: {str(remaining_pause).split('.')[0]}\033[0m")

        if self.session_stats.get("safety_triggered"):
            print(f"\033[91m!!! BOT HALTED ({self.session_stats.get('safety_reason')}) !!!\033[0m")
        
        print("-" * 50)
        print("Session Stats (Current Run):")
        start_eq = self.session_stats.get('equity_start', 0.0)
        print(f"  Start Equity    : ${start_eq:.2f}")
        if start_eq != 0:
            session_return = (Decimal(str(eq)) - Decimal(str(start_eq))) / Decimal(str(start_eq)) * 100
            ret_color = "\033[92m" if session_return > 0 else "\033[91m" if session_return < 0 else ""
            print(f"  Session Return  : {ret_color}{session_return:.2f}%\033[0m")
        
        if (daily_start := self.session_stats.get("daily_start_equity")) and daily_start > 0:
            dd_pct = (daily_start - Decimal(str(eq))) / daily_start * 100
            dd_color = "\033[91m" if dd_pct > 0 else "\033[92m"
            print(f"  Daily Drawdown  : {dd_color}{dd_pct:.2f}%\033[0m (from ${daily_start:.2f})")

        if (weekly_start := self.session_stats.get("weekly_start_equity")) and weekly_start > 0:
            wd_pct = (weekly_start - Decimal(str(eq))) / weekly_start * 100
            wd_color = "\033[91m" if wd_pct > 0 else "\033[92m"
            print(f"  Weekly Drawdown : {wd_color}{wd_pct:.2f}%\033[0m (from ${weekly_start:.2f})")

        print(f"  Max/Min Equity  : ${self.session_stats.get('equity_max', 0.0):.2f} / ${self.session_stats.get('equity_min', 0.0):.2f}")
        print(f"  Max Drawdown    : {self.session_stats.get('max_drawdown', Decimal('0.0')) * 100:.2f}%")
        print("-" * 50)
        print(f"Cumulative Stats (All Runs):\n  Realized P/L    : {net_pl_text}")
        print(f"  Win/Loss Count  : {self.session_stats.get('win_count', 0)} / {self.session_stats.get('loss_count', 0)}")
        print(f"  Win/Loss Streak : {self.session_stats.get('win_streak', 0)} / {self.session_stats.get('loss_streak', 0)}")
        print("="*50)

    def start_performance_analyzer(self):
        """Start the AI Performance Analyzer in background."""
        if not PERFORMANCE_ANALYZER_AVAILABLE:
            logger.info("AI Performance Analyzer not available, skipping...")
            return

        try:
            logger.info("Starting AI Performance Analyzer...")
            self.performance_analyzer = AIPerformanceAnalyzer()

            def analyzer_worker():
                try:
                    # Run continuous monitoring every 24 hours
                    self.performance_analyzer.run_continuous_monitoring(24)
                except Exception as e:
                    logger.error(f"Performance Analyzer error: {e}")

            self.analyzer_thread = threading.Thread(
                target=analyzer_worker,
                daemon=True,
                name="PerformanceAnalyzerThread"
            )
            self.analyzer_thread.start()
            logger.info("AI Performance Analyzer started successfully")

        except Exception as e:
            logger.error(f"Failed to start Performance Analyzer: {e}")

    def run(self):
        logger.warning("Bot: Starting AI Trading Bot...")
        if not self.initialize_services():
            logger.critical("Bot: Initialization failed. Exiting.")
            return

        # Start background threads
        retrain_thread = threading.Thread(target=self.retrain_daily_thread, daemon=True, name="DailyRetrainThread")
        retrain_thread.start()

        # Start AI Performance Analyzer
        self.start_performance_analyzer()

        schedule.every(config.TRADE_CYCLE_INTERVAL_MINUTES).minutes.do(self.robust_run_trade_cycle)
        
        logger.warning("Bot: Performing initial trade cycle run...")
        self.robust_run_trade_cycle()
        
        logger.warning("Bot: Main loop running. Last Action: %s. Press Ctrl+C to stop.", self.last_trade_action_summary)

        try:
            while not self.shutdown_event.is_set():
                schedule.run_pending()
                # Use shorter wait time for more responsive Ctrl+C handling
                self.shutdown_event.wait(0.5)
        except KeyboardInterrupt:
            logger.warning("Ctrl+C detected in main loop. Initiating graceful shutdown.")
            self.shutdown()

        logger.warning("Bot: Main loop exited.")

    def shutdown(self):
        if not self.shutdown_event.is_set():
            logger.warning("Bot: Shutdown sequence initiated.")
            self.shutdown_event.set()

            # Save session stats first
            try:
                self._save_session_stats()
                logger.info("Bot: Session stats saved successfully.")
            except Exception as e:
                logger.error(f"Bot: Error saving session stats during shutdown: {e}")

            # Shutdown AI Performance Analyzer
            try:
                if self.analyzer_thread and self.analyzer_thread.is_alive():
                    logger.info("Bot: Shutting down AI Performance Analyzer...")
                    # The analyzer thread is daemon, so it will stop automatically
                    logger.info("Bot: AI Performance Analyzer shutdown complete.")
            except Exception as e:
                logger.error(f"Bot: Error shutting down Performance Analyzer: {e}")

            # Shutdown Binance data thread
            try:
                if self.binance_data_thread and self.binance_data_thread.is_alive():
                    logger.info("Bot: Shutting down Binance data thread...")
                    binance_data_service.shutdown_event.set()
                    # Give thread time to shutdown gracefully
                    self.binance_data_thread.join(timeout=3.0)
                    if self.binance_data_thread.is_alive():
                        logger.warning("Bot: Binance data thread did not shutdown gracefully within timeout.")
                    else:
                        logger.info("Bot: Binance data thread shutdown successfully.")
            except Exception as e:
                logger.error(f"Bot: Error shutting down Binance data thread: {e}")

            logger.warning("Bot: Shutdown sequence complete.")

def main():
    # Security: Require authentication before starting live trading
    print("🔐 Bitcoin AI Trading Bot - Security Check")
    print("=" * 50)

    # Use direct authentication with default credentials
    from security.access_control import AccessController
    controller = AccessController()

    if not controller.authenticate("trader", "SecureTrading2025!"):
        print("❌ Authentication failed. Exiting for security.")
        sys.exit(1)

    print("✅ Authentication successful. Loading secure credentials...")

    # Validate API credentials after authentication
    try:
        import config
        config.validate_api_credentials()
        print("✅ All API credentials validated successfully.")
    except Exception as e:
        print(f"❌ Credential validation failed: {e}")
        sys.exit(1)

    print("✅ Starting bot with secure configuration...")
    print("=" * 50)

    bot = TradingBot()
    shutdown_count = 0

    def signal_handler(signum, frame):
        nonlocal shutdown_count
        shutdown_count += 1

        if shutdown_count == 1:
            logger.warning(f"Main: Signal {signal.Signals(signum).name} received. Initiating graceful shutdown...")
            logger.warning("Main: Press Ctrl+C again within 5 seconds to force quit.")
            bot.shutdown()
        elif shutdown_count >= 2:
            logger.error("Main: Force quit requested. Exiting immediately!")
            import sys
            sys.exit(1)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        bot.run()
    except KeyboardInterrupt:
        # This should rarely be reached due to signal handler, but just in case
        logger.warning("Main: KeyboardInterrupt caught. Shutting down...")
        bot.shutdown()
    except Exception as e:
        logger.error(f"Main: Unexpected error: {e}")
        bot.shutdown()
        raise

if __name__ == "__main__":
    main()