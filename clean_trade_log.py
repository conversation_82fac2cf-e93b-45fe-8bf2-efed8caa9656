import csv
import os

INPUT_FILE = "trade_cycle_log.csv"
OUTPUT_FILE = "trade_cycle_log_clean.csv"

with open(INPUT_FILE, newline="") as fin:
    reader = csv.reader(fin)
    rows = list(reader)

# Use the first row as header and column count reference
header = rows[0]
clean_rows = [header]
col_count = len(header)

# Keep only rows matching the header’s column count
for row in rows[1:]:
    if len(row) == col_count:
        clean_rows.append(row)

# Write the cleaned data to a new file
with open(OUTPUT_FILE, "w", newline="") as fout:
    writer = csv.writer(fout)
    writer.writerows(clean_rows)

print(f"Done! Kept {len(clean_rows)-1} valid rows (plus header).")
