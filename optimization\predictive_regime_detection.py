#!/usr/bin/env python3
"""
Predictive Regime Detection System for Bitcoin AI Trading Bot

This system enhances the existing regime detection with machine learning-based
prediction capabilities to anticipate regime changes before they occur.

Key Features:
- ML-based regime prediction using historical patterns
- Early warning system for regime transitions
- Confidence scoring for predictions
- Integration with existing Phase 3 regime system
- Real-time prediction updates
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque
import joblib
import os
from threading import Lock
import time

# ML imports
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, accuracy_score
import xgboost as xgb

# Import existing regime system
from optimization.market_regime_analyzer import MarketRegime, MarketRegimeAnalyzer, RegimeAnalysis

logger = logging.getLogger("TradingBotApp.PredictiveRegimeDetection")

@dataclass
class RegimePrediction:
    """Prediction for future market regime"""
    predicted_regime: MarketRegime
    confidence: float
    prediction_horizon_minutes: int
    prediction_timestamp: datetime
    supporting_features: Dict[str, float]
    transition_probability: float
    current_regime: MarketRegime

@dataclass
class RegimeTransitionSignal:
    """Signal indicating potential regime transition"""
    from_regime: MarketRegime
    to_regime: MarketRegime
    transition_probability: float
    expected_transition_time: datetime
    confidence: float
    trigger_features: List[str]

@dataclass
class PredictiveFeatures:
    """Features used for regime prediction"""
    timestamp: datetime
    
    # Price momentum features
    price_momentum_1m: float
    price_momentum_5m: float
    price_momentum_15m: float
    price_acceleration: float
    
    # Volume features
    volume_momentum: float
    volume_acceleration: float
    volume_profile_change: float
    
    # Volatility features
    volatility_momentum: float
    volatility_clustering: float
    volatility_regime_stability: float
    
    # Technical indicator momentum
    rsi_momentum: float
    macd_momentum: float
    atr_momentum: float
    
    # Market microstructure
    bid_ask_spread_momentum: float
    order_flow_imbalance: float
    liquidity_stress: float
    
    # Regime stability features
    current_regime_duration: float
    regime_confidence_trend: float
    regime_transition_frequency: float
    
    # Cross-market features
    correlation_breakdown: float
    sentiment_divergence: float
    macro_stress_indicator: float

class PredictiveRegimeDetector:
    """
    Machine learning-based regime prediction system
    
    This system learns from historical regime patterns and market conditions
    to predict future regime changes before they occur.
    """
    
    def __init__(self, 
                 prediction_horizons: List[int] = [5, 15, 30, 60],  # minutes
                 feature_history_length: int = 500,
                 model_retrain_interval_hours: int = 24):
        """
        Initialize the predictive regime detector
        
        Args:
            prediction_horizons: Time horizons for predictions (in minutes)
            feature_history_length: Number of historical feature sets to maintain
            model_retrain_interval_hours: How often to retrain models
        """
        self.prediction_horizons = prediction_horizons
        self.feature_history_length = feature_history_length
        self.model_retrain_interval = timedelta(hours=model_retrain_interval_hours)
        
        # Data storage
        self.feature_history: deque = deque(maxlen=feature_history_length)
        self.regime_history: deque = deque(maxlen=feature_history_length)
        self.prediction_history: deque = deque(maxlen=100)
        
        # Models for different prediction horizons
        self.models: Dict[int, Any] = {}
        self.scalers: Dict[int, StandardScaler] = {}
        self.label_encoders: Dict[int, LabelEncoder] = {}
        
        # Model performance tracking
        self.model_performance: Dict[int, Dict[str, float]] = {}
        self.last_retrain_time = datetime.now(timezone.utc)
        
        # Thread safety
        self.data_lock = Lock()
        
        # Integration with existing system
        self.base_regime_analyzer = MarketRegimeAnalyzer()
        
        # Configuration
        self.config = {
            'min_training_samples': 100,
            'confidence_threshold': 0.7,
            'transition_probability_threshold': 0.6,
            'feature_importance_threshold': 0.05,
            'model_accuracy_threshold': 0.6
        }
        
        logger.info("Predictive Regime Detector initialized")
    
    def update_market_data(self, market_data: Dict[str, Any], 
                          current_regime_analysis: RegimeAnalysis) -> None:
        """
        Update the system with new market data and current regime
        
        Args:
            market_data: Current market data
            current_regime_analysis: Current regime analysis from base system
        """
        try:
            # Extract predictive features
            features = self._extract_predictive_features(market_data, current_regime_analysis)
            
            with self.data_lock:
                # Store features and regime
                self.feature_history.append(features)
                self.regime_history.append(current_regime_analysis)
                
                # Check if we need to retrain models
                if self._should_retrain_models():
                    self._retrain_models()
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
    
    def predict_regime_changes(self, market_data: Dict[str, Any]) -> List[RegimePrediction]:
        """
        Predict regime changes for all configured time horizons
        
        Args:
            market_data: Current market data
            
        Returns:
            List of regime predictions for different time horizons
        """
        try:
            predictions = []
            
            # Get current regime analysis
            current_regime_analysis = self.base_regime_analyzer.analyze_current_regime(market_data)
            
            # Extract current features
            current_features = self._extract_predictive_features(market_data, current_regime_analysis)
            
            # Make predictions for each horizon
            for horizon in self.prediction_horizons:
                if horizon in self.models:
                    prediction = self._predict_for_horizon(current_features, horizon, current_regime_analysis)
                    if prediction:
                        predictions.append(prediction)
            
            # Store predictions for performance tracking
            with self.data_lock:
                self.prediction_history.append({
                    'timestamp': datetime.now(timezone.utc),
                    'predictions': predictions,
                    'actual_regime': current_regime_analysis.regime
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting regime changes: {e}")
            return []
    
    def detect_transition_signals(self, market_data: Dict[str, Any]) -> List[RegimeTransitionSignal]:
        """
        Detect early warning signals for regime transitions
        
        Args:
            market_data: Current market data
            
        Returns:
            List of transition signals
        """
        try:
            signals = []
            predictions = self.predict_regime_changes(market_data)
            
            if not predictions:
                return signals
            
            current_regime = predictions[0].current_regime
            
            # Analyze predictions for transition signals
            for prediction in predictions:
                if (prediction.predicted_regime != current_regime and 
                    prediction.confidence > self.config['confidence_threshold']):
                    
                    # Calculate transition probability
                    transition_prob = self._calculate_transition_probability(prediction)
                    
                    if transition_prob > self.config['transition_probability_threshold']:
                        # Identify trigger features
                        trigger_features = self._identify_trigger_features(prediction)
                        
                        # Estimate transition time
                        expected_time = datetime.now(timezone.utc) + timedelta(
                            minutes=prediction.prediction_horizon_minutes
                        )
                        
                        signal = RegimeTransitionSignal(
                            from_regime=current_regime,
                            to_regime=prediction.predicted_regime,
                            transition_probability=transition_prob,
                            expected_transition_time=expected_time,
                            confidence=prediction.confidence,
                            trigger_features=trigger_features
                        )
                        
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error detecting transition signals: {e}")
            return []
    
    def get_prediction_performance(self) -> Dict[str, Any]:
        """Get performance metrics for the prediction models"""
        try:
            performance = {
                'model_performance': dict(self.model_performance),
                'total_predictions': len(self.prediction_history),
                'last_retrain_time': self.last_retrain_time.isoformat(),
                'feature_history_length': len(self.feature_history),
                'regime_history_length': len(self.regime_history)
            }
            
            # Calculate recent accuracy
            if len(self.prediction_history) > 10:
                recent_predictions = list(self.prediction_history)[-10:]
                # This would require actual validation logic
                performance['recent_accuracy_estimate'] = 0.75  # Placeholder
            
            return performance
            
        except Exception as e:
            logger.error(f"Error getting prediction performance: {e}")
            return {}

    def _extract_predictive_features(self, market_data: Dict[str, Any],
                                   regime_analysis: RegimeAnalysis) -> PredictiveFeatures:
        """Extract features for regime prediction"""
        try:
            current_time = datetime.now(timezone.utc)

            # Get basic market data
            current_price = market_data.get('avg_price', 0)
            volume = market_data.get('volume', 0)
            volatility = market_data.get('volatility', 0)
            rsi = market_data.get('rsi', 50)
            macd_hist = market_data.get('macd_hist', 0)
            atr = market_data.get('atr', 0)

            # Calculate momentum features from history
            price_momentum_1m = self._calculate_momentum(self.feature_history, 'price', 1)
            price_momentum_5m = self._calculate_momentum(self.feature_history, 'price', 5)
            price_momentum_15m = self._calculate_momentum(self.feature_history, 'price', 15)
            price_acceleration = self._calculate_acceleration(self.feature_history, 'price')

            # Volume features
            volume_momentum = self._calculate_momentum(self.feature_history, 'volume', 5)
            volume_acceleration = self._calculate_acceleration(self.feature_history, 'volume')
            volume_profile_change = self._calculate_profile_change(self.feature_history, 'volume')

            # Volatility features
            volatility_momentum = self._calculate_momentum(self.feature_history, 'volatility', 10)
            volatility_clustering = self._calculate_volatility_clustering()
            volatility_regime_stability = self._calculate_regime_stability()

            # Technical indicator momentum
            rsi_momentum = self._calculate_momentum(self.feature_history, 'rsi', 5)
            macd_momentum = self._calculate_momentum(self.feature_history, 'macd_hist', 5)
            atr_momentum = self._calculate_momentum(self.feature_history, 'atr', 10)

            # Market microstructure (simplified for now)
            bid_ask_spread_momentum = 0.0  # Would need order book data
            order_flow_imbalance = 0.0     # Would need order flow data
            liquidity_stress = volatility * 2  # Simplified proxy

            # Regime stability features
            current_regime_duration = self._calculate_regime_duration(regime_analysis.regime)
            regime_confidence_trend = self._calculate_confidence_trend()
            regime_transition_frequency = self._calculate_transition_frequency()

            # Cross-market features (simplified)
            correlation_breakdown = 0.0    # Would need cross-asset data
            sentiment_divergence = 0.0     # Would need sentiment data
            macro_stress_indicator = volatility * 1.5  # Simplified proxy

            return PredictiveFeatures(
                timestamp=current_time,
                price_momentum_1m=price_momentum_1m,
                price_momentum_5m=price_momentum_5m,
                price_momentum_15m=price_momentum_15m,
                price_acceleration=price_acceleration,
                volume_momentum=volume_momentum,
                volume_acceleration=volume_acceleration,
                volume_profile_change=volume_profile_change,
                volatility_momentum=volatility_momentum,
                volatility_clustering=volatility_clustering,
                volatility_regime_stability=volatility_regime_stability,
                rsi_momentum=rsi_momentum,
                macd_momentum=macd_momentum,
                atr_momentum=atr_momentum,
                bid_ask_spread_momentum=bid_ask_spread_momentum,
                order_flow_imbalance=order_flow_imbalance,
                liquidity_stress=liquidity_stress,
                current_regime_duration=current_regime_duration,
                regime_confidence_trend=regime_confidence_trend,
                regime_transition_frequency=regime_transition_frequency,
                correlation_breakdown=correlation_breakdown,
                sentiment_divergence=sentiment_divergence,
                macro_stress_indicator=macro_stress_indicator
            )

        except Exception as e:
            logger.error(f"Error extracting predictive features: {e}")
            return self._get_default_features()

    def _calculate_momentum(self, history: deque, feature_name: str, lookback: int) -> float:
        """Calculate momentum for a specific feature"""
        try:
            if len(history) < lookback + 1:
                return 0.0

            # Get recent values
            recent_values = []
            for i in range(min(lookback + 1, len(history))):
                item = history[-(i+1)]
                if hasattr(item, feature_name):
                    value = getattr(item, feature_name, 0)
                elif isinstance(item, dict):
                    value = item.get(feature_name, 0)
                else:
                    value = 0
                recent_values.append(float(value))

            if len(recent_values) < 2:
                return 0.0

            # Calculate momentum as rate of change
            current_value = recent_values[0]
            past_value = recent_values[-1]

            if past_value != 0:
                momentum = (current_value - past_value) / abs(past_value)
            else:
                momentum = 0.0

            return np.clip(momentum, -1.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating momentum for {feature_name}: {e}")
            return 0.0

    def _calculate_acceleration(self, history: deque, feature_name: str) -> float:
        """Calculate acceleration (second derivative) for a feature"""
        try:
            if len(history) < 3:
                return 0.0

            # Get last 3 values
            values = []
            for i in range(3):
                item = history[-(i+1)]
                if hasattr(item, feature_name):
                    value = getattr(item, feature_name, 0)
                elif isinstance(item, dict):
                    value = item.get(feature_name, 0)
                else:
                    value = 0
                values.append(float(value))

            # Calculate acceleration
            v1 = values[1] - values[2]  # First derivative
            v2 = values[0] - values[1]  # Second derivative
            acceleration = v2 - v1

            # Normalize
            avg_value = np.mean(values)
            if avg_value != 0:
                acceleration = acceleration / abs(avg_value)

            return np.clip(acceleration, -1.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating acceleration for {feature_name}: {e}")
            return 0.0

    def _calculate_profile_change(self, history: deque, feature_name: str) -> float:
        """Calculate profile change for a feature"""
        try:
            if len(history) < 10:
                return 0.0

            # Get recent values
            recent_values = []
            for i in range(min(10, len(history))):
                item = history[-(i+1)]
                if hasattr(item, feature_name):
                    value = getattr(item, feature_name, 0)
                elif isinstance(item, dict):
                    value = item.get(feature_name, 0)
                else:
                    value = 0
                recent_values.append(float(value))

            if len(recent_values) < 5:
                return 0.0

            # Calculate standard deviation change
            recent_std = np.std(recent_values[:5])
            past_std = np.std(recent_values[5:])

            if past_std != 0:
                profile_change = (recent_std - past_std) / past_std
            else:
                profile_change = 0.0

            return np.clip(profile_change, -1.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating profile change for {feature_name}: {e}")
            return 0.0

    def _calculate_volatility_clustering(self) -> float:
        """Calculate volatility clustering indicator"""
        try:
            if len(self.feature_history) < 20:
                return 0.0

            # Get recent volatility values
            volatilities = []
            for i in range(min(20, len(self.feature_history))):
                item = self.feature_history[-(i+1)]
                if hasattr(item, 'volatility'):
                    volatilities.append(item.volatility)
                else:
                    volatilities.append(0.0)

            if len(volatilities) < 10:
                return 0.0

            # Calculate clustering using autocorrelation
            recent_vol = volatilities[:10]
            past_vol = volatilities[10:]

            correlation = np.corrcoef(recent_vol, past_vol)[0, 1] if len(past_vol) == len(recent_vol) else 0.0
            return np.clip(correlation, -1.0, 1.0) if not np.isnan(correlation) else 0.0

        except Exception as e:
            logger.error(f"Error calculating volatility clustering: {e}")
            return 0.0

    def _calculate_regime_stability(self) -> float:
        """Calculate regime stability indicator"""
        try:
            if len(self.regime_history) < 10:
                return 0.0

            # Count regime changes in recent history
            recent_regimes = [r.regime for r in list(self.regime_history)[-10:]]
            unique_regimes = len(set(recent_regimes))

            # Stability is inverse of regime diversity
            stability = 1.0 - (unique_regimes - 1) / 9.0  # Normalize to 0-1
            return max(0.0, stability)

        except Exception as e:
            logger.error(f"Error calculating regime stability: {e}")
            return 0.0

    def _calculate_regime_duration(self, current_regime: MarketRegime) -> float:
        """Calculate how long current regime has been active"""
        try:
            if len(self.regime_history) < 2:
                return 0.0

            # Count consecutive periods of current regime
            duration = 0
            for regime_analysis in reversed(list(self.regime_history)):
                if regime_analysis.regime == current_regime:
                    duration += 1
                else:
                    break

            # Normalize to 0-1 scale (assuming max duration of 100 periods)
            return min(1.0, duration / 100.0)

        except Exception as e:
            logger.error(f"Error calculating regime duration: {e}")
            return 0.0

    def _calculate_confidence_trend(self) -> float:
        """Calculate trend in regime confidence"""
        try:
            if len(self.regime_history) < 5:
                return 0.0

            # Get recent confidence values
            confidences = [r.confidence for r in list(self.regime_history)[-5:]]

            # Calculate trend using linear regression slope
            x = np.arange(len(confidences))
            slope = np.polyfit(x, confidences, 1)[0] if len(confidences) > 1 else 0.0

            return np.clip(slope, -1.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating confidence trend: {e}")
            return 0.0

    def _calculate_transition_frequency(self) -> float:
        """Calculate frequency of regime transitions"""
        try:
            if len(self.regime_history) < 20:
                return 0.0

            # Count transitions in recent history
            recent_regimes = [r.regime for r in list(self.regime_history)[-20:]]
            transitions = 0

            for i in range(1, len(recent_regimes)):
                if recent_regimes[i] != recent_regimes[i-1]:
                    transitions += 1

            # Normalize to 0-1 scale
            frequency = transitions / 19.0  # 19 possible transitions in 20 periods
            return min(1.0, frequency)

        except Exception as e:
            logger.error(f"Error calculating transition frequency: {e}")
            return 0.0

    def _get_default_features(self) -> PredictiveFeatures:
        """Get default features when extraction fails"""
        return PredictiveFeatures(
            timestamp=datetime.now(timezone.utc),
            price_momentum_1m=0.0,
            price_momentum_5m=0.0,
            price_momentum_15m=0.0,
            price_acceleration=0.0,
            volume_momentum=0.0,
            volume_acceleration=0.0,
            volume_profile_change=0.0,
            volatility_momentum=0.0,
            volatility_clustering=0.0,
            volatility_regime_stability=0.0,
            rsi_momentum=0.0,
            macd_momentum=0.0,
            atr_momentum=0.0,
            bid_ask_spread_momentum=0.0,
            order_flow_imbalance=0.0,
            liquidity_stress=0.0,
            current_regime_duration=0.0,
            regime_confidence_trend=0.0,
            regime_transition_frequency=0.0,
            correlation_breakdown=0.0,
            sentiment_divergence=0.0,
            macro_stress_indicator=0.0
        )

    def _should_retrain_models(self) -> bool:
        """Check if models should be retrained"""
        try:
            # Check time since last retrain
            time_since_retrain = datetime.now(timezone.utc) - self.last_retrain_time
            if time_since_retrain > self.model_retrain_interval:
                return True

            # Check if we have enough new data
            if len(self.feature_history) >= self.config['min_training_samples']:
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking retrain condition: {e}")
            return False

    def _retrain_models(self) -> None:
        """Retrain prediction models with latest data"""
        try:
            logger.info("Starting model retraining...")

            if len(self.feature_history) < self.config['min_training_samples']:
                logger.warning("Insufficient data for model retraining")
                return

            # Prepare training data
            X, y = self._prepare_training_data()

            if len(X) < self.config['min_training_samples']:
                logger.warning("Insufficient prepared data for training")
                return

            # Train models for each prediction horizon
            for horizon in self.prediction_horizons:
                self._train_horizon_model(X, y, horizon)

            self.last_retrain_time = datetime.now(timezone.utc)
            logger.info("Model retraining completed successfully")

        except Exception as e:
            logger.error(f"Error retraining models: {e}")

    def _prepare_training_data(self) -> Tuple[np.ndarray, Dict[int, np.ndarray]]:
        """Prepare training data from historical features and regimes"""
        try:
            # Convert features to numpy array
            feature_list = []
            regime_list = []

            for i, (features, regime_analysis) in enumerate(zip(self.feature_history, self.regime_history)):
                # Convert features to array
                feature_dict = asdict(features)
                feature_values = [v for k, v in feature_dict.items() if k != 'timestamp']
                feature_list.append(feature_values)
                regime_list.append(regime_analysis.regime.value)

            X = np.array(feature_list)

            # Create target variables for different horizons
            y_dict = {}
            for horizon in self.prediction_horizons:
                y_horizon = []
                # Only create targets where we have future data
                max_index = len(regime_list) - horizon
                for i in range(max_index):
                    y_horizon.append(regime_list[i + horizon])

                y_dict[horizon] = np.array(y_horizon)

            # Trim X to match shortest y (accounting for prediction horizons)
            if y_dict:
                min_length = min(len(y) for y in y_dict.values())
                X = X[:min_length]
            else:
                X = np.array([])  # No valid targets

            return X, y_dict

        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return np.array([]), {}

    def _train_horizon_model(self, X: np.ndarray, y_dict: Dict[int, np.ndarray], horizon: int) -> None:
        """Train model for specific prediction horizon"""
        try:
            if horizon not in y_dict or len(y_dict[horizon]) == 0:
                logger.warning(f"No target data for horizon {horizon}")
                return

            y = y_dict[horizon]

            # Ensure X and y have same length
            min_length = min(len(X), len(y))
            X_train = X[:min_length]
            y_train = y[:min_length]

            if len(X_train) < 10:
                logger.warning(f"Insufficient data for horizon {horizon}")
                return

            # Initialize and fit scaler
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_train)

            # Initialize and fit label encoder
            label_encoder = LabelEncoder()
            y_encoded = label_encoder.fit_transform(y_train)

            # Train XGBoost model
            model = xgb.XGBClassifier(
                objective='multi:softprob',
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='mlogloss'
            )

            model.fit(X_scaled, y_encoded)

            # Store model components
            self.models[horizon] = model
            self.scalers[horizon] = scaler
            self.label_encoders[horizon] = label_encoder

            # Evaluate model performance
            y_pred = model.predict(X_scaled)
            accuracy = accuracy_score(y_encoded, y_pred)

            self.model_performance[horizon] = {
                'accuracy': accuracy,
                'training_samples': len(X_train),
                'last_trained': datetime.now(timezone.utc).isoformat()
            }

            logger.info(f"Trained model for horizon {horizon}min: accuracy={accuracy:.3f}")

        except Exception as e:
            logger.error(f"Error training model for horizon {horizon}: {e}")

    def _predict_for_horizon(self, features: PredictiveFeatures, horizon: int,
                           current_regime_analysis: RegimeAnalysis) -> Optional[RegimePrediction]:
        """Make prediction for specific time horizon"""
        try:
            if horizon not in self.models:
                return None

            model = self.models[horizon]
            scaler = self.scalers[horizon]
            label_encoder = self.label_encoders[horizon]

            # Prepare feature vector
            feature_dict = asdict(features)
            feature_values = [v for k, v in feature_dict.items() if k != 'timestamp']
            X = np.array([feature_values])

            # Scale features
            X_scaled = scaler.transform(X)

            # Make prediction
            prediction_proba = model.predict_proba(X_scaled)[0]
            predicted_class = model.predict(X_scaled)[0]

            # Convert back to regime
            predicted_regime_str = label_encoder.inverse_transform([predicted_class])[0]
            predicted_regime = MarketRegime(predicted_regime_str)

            # Get confidence (max probability)
            confidence = float(np.max(prediction_proba))

            # Calculate transition probability
            current_regime_encoded = label_encoder.transform([current_regime_analysis.regime.value])[0]
            current_regime_prob = prediction_proba[current_regime_encoded]
            transition_probability = 1.0 - current_regime_prob

            # Get feature importance as supporting features
            feature_importance = model.feature_importances_
            feature_names = [k for k in feature_dict.keys() if k != 'timestamp']
            supporting_features = dict(zip(feature_names, feature_importance))

            return RegimePrediction(
                predicted_regime=predicted_regime,
                confidence=confidence,
                prediction_horizon_minutes=horizon,
                prediction_timestamp=datetime.now(timezone.utc),
                supporting_features=supporting_features,
                transition_probability=transition_probability,
                current_regime=current_regime_analysis.regime
            )

        except Exception as e:
            logger.error(f"Error making prediction for horizon {horizon}: {e}")
            return None

    def _calculate_transition_probability(self, prediction: RegimePrediction) -> float:
        """Calculate probability of regime transition"""
        try:
            # Base probability from model
            base_prob = prediction.transition_probability

            # Adjust based on confidence
            confidence_factor = prediction.confidence

            # Adjust based on supporting features
            feature_strength = np.mean(list(prediction.supporting_features.values()))

            # Combined probability
            combined_prob = base_prob * confidence_factor * (1 + feature_strength)

            return np.clip(combined_prob, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Error calculating transition probability: {e}")
            return 0.0

    def _identify_trigger_features(self, prediction: RegimePrediction) -> List[str]:
        """Identify key features triggering the prediction"""
        try:
            # Sort features by importance
            sorted_features = sorted(
                prediction.supporting_features.items(),
                key=lambda x: x[1],
                reverse=True
            )

            # Return top features above threshold
            trigger_features = [
                feature for feature, importance in sorted_features
                if importance > self.config['feature_importance_threshold']
            ]

            return trigger_features[:5]  # Top 5 features

        except Exception as e:
            logger.error(f"Error identifying trigger features: {e}")
            return []


# Global interface functions for integration
_predictive_detector: Optional[PredictiveRegimeDetector] = None

def initialize_predictive_regime_detection(prediction_horizons: List[int] = [5, 15, 30, 60]) -> bool:
    """Initialize the predictive regime detection system"""
    global _predictive_detector
    try:
        _predictive_detector = PredictiveRegimeDetector(prediction_horizons=prediction_horizons)
        logger.info("Predictive regime detection system initialized")
        return True
    except Exception as e:
        logger.error(f"Error initializing predictive regime detection: {e}")
        return False

def update_predictive_system(market_data: Dict[str, Any], current_regime_analysis: Any) -> None:
    """Update the predictive system with new market data"""
    global _predictive_detector
    if _predictive_detector:
        _predictive_detector.update_market_data(market_data, current_regime_analysis)

def get_regime_predictions(market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get regime predictions for all time horizons"""
    global _predictive_detector
    if not _predictive_detector:
        return []

    try:
        predictions = _predictive_detector.predict_regime_changes(market_data)
        return [asdict(pred) for pred in predictions]
    except Exception as e:
        logger.error(f"Error getting regime predictions: {e}")
        return []

def get_transition_signals(market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get early warning signals for regime transitions"""
    global _predictive_detector
    if not _predictive_detector:
        return []

    try:
        signals = _predictive_detector.detect_transition_signals(market_data)
        return [asdict(signal) for signal in signals]
    except Exception as e:
        logger.error(f"Error getting transition signals: {e}")
        return []

def get_predictive_performance() -> Dict[str, Any]:
    """Get performance metrics for the predictive system"""
    global _predictive_detector
    if not _predictive_detector:
        return {}

    return _predictive_detector.get_prediction_performance()

def stop_predictive_regime_detection() -> None:
    """Stop the predictive regime detection system"""
    global _predictive_detector
    if _predictive_detector:
        logger.info("Stopping predictive regime detection system")
        _predictive_detector = None
