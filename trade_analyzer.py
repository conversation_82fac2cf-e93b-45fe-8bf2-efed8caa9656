# START OF FILE trade_analyzer.py

"""
Trade Analy<PERSON> (The "Post-Mortem" Engine)

This script analyzes historical trade data to identify the market conditions
that correlate with winning and losing trades. It reads from two data sources:
1. decision_log_full.csv: A log of market indicators for every cycle.
2. trade_history.csv: A record of all filled buy and sell orders.

The script matches sell trades to the market state at that time, separates
them into wins and losses, and calculates the average indicator values for each
group. The output is a JSON report that can be used by other scripts, like
a weekly tuner, to provide deeper context to the AI for strategy optimization.
"""

import csv
import json
import os
from datetime import datetime, timezone
from collections import defaultdict
from statistics import mean, stdev

# Define file paths relative to this script's location
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DECISION_LOG_FILE = os.path.join(BASE_DIR, "decision_log_full.csv")
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
REPORT_OUTPUT_FILE = os.path.join(BASE_DIR, "trade_analysis_report.json")

# The key indicators we want to analyze
INDICATORS_TO_ANALYZE = [
    'rsi', 'rsi_slope', 'macd_hist', 'volume_spike', 'atr'
]

def parse_timestamp(ts_string: str) -> datetime:
    """
    Parses different timestamp formats and returns a timezone-aware datetime object,
    truncated to the minute for matching.
    """
    # Handle the format with 'T' (from trade_history.csv)
    if 'T' in ts_string:
        dt_obj = datetime.fromisoformat(ts_string)
    # Handle the format with a space (from decision_log_full.csv)
    else:
        # We need to manually add timezone info if it's not already there
        if '+' not in ts_string:
            ts_string += "+00:00"
        dt_obj = datetime.strptime(ts_string, '%Y-%m-%d %H:%M:%S%z')

    # Ensure the datetime is in UTC and truncate to the minute
    return dt_obj.astimezone(timezone.utc).replace(second=0, microsecond=0)

def safe_float(value, default=0.0):
    """Converts a value to float, returning a default if conversion fails."""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def analyze_trades():
    """
    Main analysis function. Loads data, correlates trades with indicators,
    and returns a structured report.
    """
    print("Starting trade analysis...")

    # 1. Load decision log into a lookup dictionary for fast access
    print(f"Loading decision log from {DECISION_LOG_FILE}...")
    decision_data = {}
    try:
        with open(DECISION_LOG_FILE, 'r', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    ts = parse_timestamp(row['timestamp'])
                    decision_data[ts] = {k: row[k] for k in INDICATORS_TO_ANALYZE if k in row}
                except Exception as e:
                    # Silently skip rows with bad timestamps in the log
                    continue
        print(f"Loaded {len(decision_data)} records from decision log.")
    except FileNotFoundError:
        print(f"ERROR: Decision log file not found at {DECISION_LOG_FILE}. Cannot proceed.")
        return None

    # 2. Process trade history and correlate with decision data
    print(f"Processing trade history from {TRADE_HISTORY_FILE}...")
    wins_indicators = defaultdict(list)
    losses_indicators = defaultdict(list)
    win_count = 0
    loss_count = 0

    try:
        with open(TRADE_HISTORY_FILE, 'r', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # We only care about SELL trades as they realize profit/loss
                if row.get('action', '').upper() != 'SELL':
                    continue

                pnl = safe_float(row.get('realized_pl'))
                
                # Ignore break-even trades
                if -0.01 < pnl < 0.01:
                    continue

                try:
                    trade_ts = parse_timestamp(row['timestamp'])
                except Exception:
                    print(f"Warning: Could not parse timestamp for order {row.get('order_id')}. Skipping.")
                    continue

                # Find the matching indicator snapshot from the decision log
                if trade_ts in decision_data:
                    indicators = decision_data[trade_ts]
                    
                    if pnl > 0: # It's a WIN
                        win_count += 1
                        for key in INDICATORS_TO_ANALYZE:
                            wins_indicators[key].append(safe_float(indicators.get(key)))
                    else: # It's a LOSS
                        loss_count += 1
                        for key in INDICATORS_TO_ANALYZE:
                            losses_indicators[key].append(safe_float(indicators.get(key)))

        print(f"Processed trades. Found {win_count} wins and {loss_count} losses to analyze.")
    except FileNotFoundError:
        print(f"ERROR: Trade history file not found at {TRADE_HISTORY_FILE}. Cannot proceed.")
        return None

    # 3. Calculate statistics for each group
    def calculate_stats(indicator_data, trade_count):
        if trade_count == 0:
            return {
                "trade_count": 0,
                "average_indicators": {key: 0 for key in INDICATORS_TO_ANALYZE}
            }
        
        stats = {"trade_count": trade_count, "average_indicators": {}}
        for key, values in indicator_data.items():
            if values:
                stats["average_indicators"][key] = round(mean(values), 4)
            else:
                stats["average_indicators"][key] = 0
        return stats

    win_analysis = calculate_stats(wins_indicators, win_count)
    loss_analysis = calculate_stats(losses_indicators, loss_count)

    # 4. Assemble the final report
    final_report = {
        "report_generated_utc": datetime.now(timezone.utc).isoformat(),
        "win_analysis": win_analysis,
        "loss_analysis": loss_analysis
    }
    
    print("Analysis complete.")
    return final_report


if __name__ == "__main__":
    report = analyze_trades()

    if report:
        # Print the report to the console in a readable format
        print("\n--- Trade Analysis Report ---")
        print(json.dumps(report, indent=2))

        # Save the report to a file for other scripts to use
        try:
            with open(REPORT_OUTPUT_FILE, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\nReport successfully saved to {REPORT_OUTPUT_FILE}")
        except Exception as e:
            print(f"\nERROR: Could not save report to file. Reason: {e}")

# END OF FILE trade_analyzer.py