# ===================================================================
# CENTRALIZED CONFIGURATION - Bitcoin AI Trading Bot
# ===================================================================
# All bot configuration is centralized here for easy management
# Last updated: 2025-01-29
#
# STRUCTURE:
# 1. Environment & API Configuration
# 2. Trading Parameters
# 3. AI & ML Configuration
# 4. Risk Management
# 5. System & Logging
# 6. File Paths & Data
# ===================================================================

import os
from dotenv import load_dotenv
import logging
import json
from datetime import timedelta
from decimal import Decimal
import pytz

# Load environment variables from .env file (fallback only)
load_dotenv()

# Import secure credential system
try:
    from security.secure_config import get_secure_api_key
    SECURE_CREDENTIALS_AVAILABLE = True
except ImportError:
    SECURE_CREDENTIALS_AVAILABLE = False

# --- Logging for config warnings ---
_config_logger = logging.getLogger("config_logger")
if not _config_logger.handlers:
    _ch = logging.StreamHandler()
    _formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    _ch.setFormatter(_formatter)
    _config_logger.addHandler(_ch)
    _config_logger.setLevel(logging.WARNING)

# ===================================================================
# 1. ENVIRONMENT & API CONFIGURATION
# ===================================================================

# Trading Mode Configuration
IS_LIVE_TRADING = True  # ⚠️ CAUTION: True = Real Money, False = Paper Trading

# API Keys & Endpoints (lazy loading to allow authentication first)
def get_gemini_api_key():
    """Get Gemini API key securely"""
    if SECURE_CREDENTIALS_AVAILABLE:
        return get_secure_api_key("GEMINI_API_KEY") or os.getenv("GEMINI_API_KEY")
    else:
        return os.getenv("GEMINI_API_KEY")

def get_alpaca_credentials():
    """Get Alpaca credentials securely"""
    if IS_LIVE_TRADING:
        if SECURE_CREDENTIALS_AVAILABLE:
            api_key_id = get_secure_api_key("ALPACA_LIVE_API_KEY_ID") or os.getenv("ALPACA_LIVE_API_KEY_ID")
            secret_key = get_secure_api_key("ALPACA_LIVE_SECRET_KEY") or os.getenv("ALPACA_LIVE_SECRET_KEY")
        else:
            api_key_id = os.getenv("ALPACA_LIVE_API_KEY_ID")
            secret_key = os.getenv("ALPACA_LIVE_SECRET_KEY")
        base_url = "https://api.alpaca.markets"
    else:
        if SECURE_CREDENTIALS_AVAILABLE:
            api_key_id = get_secure_api_key("ALPACA_PAPER_API_KEY_ID") or os.getenv("ALPACA_PAPER_API_KEY_ID")
            secret_key = get_secure_api_key("ALPACA_PAPER_SECRET_KEY") or os.getenv("ALPACA_PAPER_SECRET_KEY")
        else:
            api_key_id = os.getenv("ALPACA_PAPER_API_KEY_ID")
            secret_key = os.getenv("ALPACA_PAPER_SECRET_KEY")
        base_url = "https://paper-api.alpaca.markets"

    return api_key_id, secret_key, base_url

# Initialize with fallback values for backward compatibility
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
ALPACA_API_KEY_ID = os.getenv("ALPACA_LIVE_API_KEY_ID" if IS_LIVE_TRADING else "ALPACA_PAPER_API_KEY_ID", "")
ALPACA_SECRET_KEY = os.getenv("ALPACA_LIVE_SECRET_KEY" if IS_LIVE_TRADING else "ALPACA_PAPER_SECRET_KEY", "")

if IS_LIVE_TRADING:
    ALPACA_BASE_URL = "https://api.alpaca.markets"
    _config_logger.warning("############################################################")
    _config_logger.warning("##           LIVE TRADING MODE IS ACTIVE!                 ##")
    _config_logger.warning("##          REAL MONEY WILL BE USED FOR TRADES.           ##")
    _config_logger.warning("##          PROCEED WITH EXTREME CAUTION.                 ##")
    _config_logger.warning("############################################################")
else:
    ALPACA_BASE_URL = "https://paper-api.alpaca.markets"
    _config_logger.warning("############################################################")
    _config_logger.warning("##           PAPER TRADING MODE IS ACTIVE!                ##")
    _config_logger.warning("##           NO REAL MONEY WILL BE USED.                  ##")
    _config_logger.warning("############################################################")


# API Key Validation (called after authentication)
def validate_api_credentials():
    """Validate that all required API credentials are available"""
    gemini_key = get_gemini_api_key()
    alpaca_key_id, alpaca_secret, _ = get_alpaca_credentials()

    if not alpaca_key_id:
        raise ValueError(f"Alpaca API Key ID not found for {'LIVE' if IS_LIVE_TRADING else 'PAPER'} mode.")
    if not alpaca_secret:
        raise ValueError(f"Alpaca Secret Key not found for {'LIVE' if IS_LIVE_TRADING else 'PAPER'} mode.")
    if not gemini_key:
        raise ValueError("Gemini API Key not found. Ensure GEMINI_API_KEY is set in your environment.")

    # Update global variables for backward compatibility
    global GEMINI_API_KEY, ALPACA_API_KEY_ID, ALPACA_SECRET_KEY
    GEMINI_API_KEY = gemini_key
    ALPACA_API_KEY_ID = alpaca_key_id
    ALPACA_SECRET_KEY = alpaca_secret

    return True

# ===================================================================
# 2. TRADING PARAMETERS
# ===================================================================

# Trading Symbol
PRIMARY_TRADING_SYMBOL = "BTC/USD"

# Order Sizing & Limits
MIN_ORDER_VALUE_USD_FLOOR = Decimal("12.00")    # Minimum order value
MAX_TRADE_VALUE_USD = Decimal("50.01")          # Maximum single trade value
MAX_SCALP_TRADE_VALUE_USD = Decimal("50.01")    # Maximum scalp trade value
MIN_TRADE_QTY_BTC = Decimal("0.00002")          # Minimum BTC quantity

# ===== ENHANCED CASH MANAGEMENT =====
# Dynamic cash reserve as percentage of equity (3% default)
CASH_RESERVE_PERCENT = Decimal("0.03")          # 3% of equity kept in reserve
CASH_RESERVE_MIN_USD = Decimal("25.00")         # Minimum absolute reserve
CASH_RESERVE_MAX_USD = Decimal("100.00")        # Maximum absolute reserve

# Portfolio allocation limits
MAX_PORTFOLIO_ALLOCATION_PERCENT = Decimal("0.92")  # Max 92% of equity in positions
POSITION_SIZE_LIMIT_PERCENT = Decimal("0.05")       # Max 5% per position

# Dynamic Order Sizing
MIN_ORDER_VALUE_USD_PERCENT = Decimal("0.001")  # Min order as % of equity
MAX_ORDER_VALUE_USD_CAP = Decimal("0.03")       # Max order as % of equity

# Trade Execution Thresholds
MIN_SELL_QTY_THRESHOLD = Decimal("0.00000001")  # Minimum sell quantity
TRADE_BUFFER_QTY = Decimal("0.0002")            # Buffer for quantity calculations
DUST_THRESHOLD_QTY = Decimal("0.0001")          # Dust threshold for cleanup

# Fee Configuration
PER_TRADE_FEE_PERCENTAGE = Decimal("0.0025")    # 0.25% trading fee
FEE_PERCENT = PER_TRADE_FEE_PERCENTAGE          # Alias for compatibility

# Profit Targets
MIN_PROFIT_THRESHOLD_USD = Decimal("0.001")     # Minimum profit after fees (lowered for small lots)
MIN_PROFIT_THRESHOLD_TIME_BASED_USD = Decimal("0.05")  # Higher threshold for time-based sales (5 cents)
PROFIT_TARGET_PCT = Decimal("0.03")             # Default profit target (3%)
DIP_BUY_PROFIT_TARGET_PCT = Decimal("0.02")     # Dip buy profit target (2%)
MOMENTUM_BUY_NET_PROFIT_TARGET_PCT = Decimal("0.01")  # Momentum buy target (1%)

# Stop Loss Configuration
STOP_LOSS_PCT = Decimal("0.02")                 # Default stop loss (2%)
STOP_PERCENT = STOP_LOSS_PCT                    # Alias for compatibility

# ===================================================================
# 3. TECHNICAL INDICATORS & MARKET ANALYSIS
# ===================================================================

# RSI Configuration
RSI_PERIOD = 7                                  # RSI calculation period

# Moving Averages
SHORT_SMA_PERIOD = 5                            # Short SMA period
LONG_SMA_PERIOD = 10                            # Long SMA period
HIGHER_SMA_PERIOD = 10                          # Higher timeframe SMA

# MACD Configuration
MACD_FAST_PERIOD = 12                           # MACD fast EMA period
MACD_SLOW_PERIOD = 26                           # MACD slow EMA period
MACD_SIGNAL_PERIOD = 9                          # MACD signal line period

# ATR Configuration
ATR_PERIOD = 14                                 # ATR calculation period
REFERENCE_ATR = Decimal("40.0")                 # Reference ATR value

# Higher Timeframe Analysis
HIGHER_TIMEFRAME = "5m"                         # Higher timeframe for trend filter

# Historical Data Configuration
HISTORICAL_BARS_TIMEFRAME = "Minute"            # Timeframe for historical bars
HISTORICAL_BARS_COUNT = max(RSI_PERIOD, SHORT_SMA_PERIOD, LONG_SMA_PERIOD,
                           MACD_SLOW_PERIOD + MACD_SIGNAL_PERIOD, ATR_PERIOD) + 50

# Market Regime Detection Thresholds
TREND_REL_THRESHOLD = 0.002                     # Trend detection threshold
VOLATILE_REL_THRESHOLD = 0.0015                 # Volatility threshold
ATR_REL_THRESHOLD = 0.0015                      # ATR threshold
QUIET_REL_THRESHOLD = 0.0005                    # Quiet market threshold

# ===================================================================
# 4. AI & ML CONFIGURATION
# ===================================================================

# AI Decision Timing
GEMINI_AI_CALL_INTERVAL_CYCLES = 1              # Fresh AI decision every N cycles
TRADE_CYCLE_INTERVAL_MINUTES = 3                # Minutes between trade cycles

# AI Quantity Limits
MIN_QTY_PCT_CONFIG = Decimal("0.05")            # Minimum AI suggested quantity %
MAX_QTY_PCT_CONFIG = Decimal("0.07")            # Maximum AI suggested quantity %

# ===================================================================
# 5. RISK MANAGEMENT
# ===================================================================

# Performance Targets - INFORMATIONAL ONLY (not trading restrictions)
TARGET_ANNUAL_RETURN_PCT = Decimal("0.50")      # 50% annual return target (realistic for crypto)
TARGET_DAILY_RETURN_PCT = Decimal("0.01")       # 1% daily target (informational milestone)

# Drawdown Protection
ENABLE_DAILY_DRAWDOWN_PROTECTION = True         # Enable daily drawdown limits
MAX_DAILY_LOSS_PCT = Decimal("0.02")            # Maximum daily loss (2%)
MAX_WEEKLY_LOSS_PCT = Decimal("0.05")           # Maximum weekly loss (5%)
MIN_ACCOUNT_EQUITY = Decimal("250.00")          # Minimum account equity

# Guardian Veto System
ENABLE_GUARDIAN_VETO = True                     # Enable guardian override system
GUARDIAN_RSI_OVERBOUGHT = Decimal("80.0")       # Veto BUY if RSI above this
GUARDIAN_RSI_OVERSOLD = Decimal("30.0")         # Allow SELL if RSI below this

# Loss Control
MAX_AI_LOSS_EXIT_PCT = Decimal("0.0")           # Never sell at a loss (0%)
LOSS_STREAK_LIMIT = 3                           # Max consecutive losses before pause
LOSS_PAUSE_SECONDS = 3600                       # Pause duration after loss streak (1 hour)

# Dynamic Risk Adjustment
MIN_DYNAMIC_RISK_PERCENT = Decimal("0.04")      # Minimum dynamic risk
MAX_DYNAMIC_RISK_PERCENT = Decimal("0.12")      # Maximum dynamic risk
RISK_PERCENT = Decimal("0.01")                  # Base risk percentage

# Profit Taking Configuration
MIN_PROFIT_TARGET_PCT = Decimal("0.01")         # Minimum profit target
MAX_PROFIT_TARGET_PCT = Decimal("0.06")         # Maximum profit target
TRAIL_PROFIT_BUFFER_PCT = Decimal("0.002")      # Trailing stop buffer
GRANULAR_TAKE_PROFIT_PCT = Decimal("0.01")      # Granular profit taking

# Take Profit Ladder
TAKE_PROFIT_SHARES = [Decimal("0.5"), Decimal("0.3"), Decimal("0.2")]  # Profit taking shares
TAKE_PROFITS = [Decimal("0.02"), Decimal("0.03"), Decimal("0.05")]     # Profit levels

# Stop Loss Configuration
MIN_STOP_LOSS_PCT = Decimal("0.005")            # Minimum stop loss
MAX_STOP_LOSS_PCT = Decimal("0.04")             # Maximum stop loss

# Trading Cooldowns
COOLDOWN_SELL_AFTER_BUY = timedelta(minutes=2)  # Cooldown after buy
COOLDOWN_BUY_AFTER_SELL = timedelta(minutes=5)  # Cooldown after sell
MIN_REVERSAL_PCT = Decimal("0.002")             # Minimum reversal percentage
MIN_HOLD_CYCLES = 2                             # Minimum hold cycles

# Emergency Controls
KILL_SWITCH_FILE = "STOP_TRADING.txt"           # Emergency stop file

# Time-Based Profit Taking
TIME_BASED_PROFIT_TAKING_THRESHOLD_HOURS = 24   # Hours before time-based profit taking
TIME_BASED_PROFIT_TAKING_QTY_PCT = Decimal("0.50")  # Percentage to sell (50% - increased for better profit capture)
MIN_LOT_VALUE_FOR_TIME_BASED_USD = Decimal("10.00")  # Skip time-based sales for lots smaller than $10
TIME_BASED_EMAIL_COOLDOWN_MINUTES = 60  # Minimum minutes between time-based profit taking emails

# ===================================================================
# 6. ORDER MANAGEMENT
# ===================================================================

# Stale Order Management
ENABLE_STALE_ORDER_CANCELLATION = True         # Enable stale order cancellation
STALE_ORDER_AGE_HOURS = 8                       # Cancel orders older than N hours
STALE_SELL_PRICE_DEVIATION_PCT = Decimal("0.005")  # Price deviation for stale sells
STALE_BUY_PRICE_DEVIATION_PCT = Decimal("0.005")   # Price deviation for stale buys

# Lot Management
CONSOLIDATION_WINDOW_MINUTES = 5                # Window for lot consolidation

# ===================================================================
# 7. SYSTEM & LOGGING CONFIGURATION
# ===================================================================

# File Paths (relative to project root)
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE_PATH = os.path.join(BASE_DIR, "trading_bot.log")
AI_LOG_PATH = os.path.join(BASE_DIR, "ai_responses.log")
DEBUG_LOG_FILE_PATH = os.path.join(BASE_DIR, "trading_bot_debug.log")
TRADE_CYCLE_LOG = os.path.join(BASE_DIR, "trade_cycle_log.csv")
SESSION_STATS_FILE = os.path.join(BASE_DIR, "session_stats.json")
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
LOTS_LEDGER_FILE = os.path.join(BASE_DIR, "open_lots.json")

# Data Files
HISTORICAL_DATA_FILE = "binance_btcusdt_1m.csv"
MAX_BINANCE_KLINES_TO_STORE = 200               # Max klines to store in memory

# Logging Configuration
MAX_LOG_SIZE = 5 * 1024 * 1024                  # 5MB max log file size
MAX_DEBUG_LOG_SIZE = 1 * 1024 * 1024            # 1MB max debug log size
LONDON_TZ = pytz.timezone('Europe/London')      # Timezone for logging

# ML Model Retraining
RETRAIN_MAX_RETRIES = 3                         # Max retrain attempts
RETRAIN_RETRY_DELAY_SECONDS = 60                # Delay between retrain attempts

# ===================================================================
# 8. COMPATIBILITY ALIASES
# ===================================================================
# These maintain compatibility with existing code

# Trading aliases
HARD_MAX_TRADE_VALUE = MAX_TRADE_VALUE_USD
MIN_TRADE_VALUE = MIN_ORDER_VALUE_USD_FLOOR
MAX_DAILY_DRAWDOWN_PERCENT = MAX_DAILY_LOSS_PCT

# ===================================================================
# 9. ALERT & NOTIFICATION CONFIGURATION
# ===================================================================

# Alert Thresholds - Only send emails for significant issues
ALERT_ON_PREVENTED_SALES_THRESHOLD = 20        # Send alert if more than N sales prevented in one cycle
ALERT_ON_ACTUAL_STOP_LOSS = True               # Always alert on actual stop-loss execution
ALERT_ON_SYSTEM_ERRORS = True                  # Always alert on system errors

# ===================================================================
# 10. CONFIGURATION VALIDATION
# ===================================================================

def validate_config():
    """Validate configuration values for consistency and safety."""
    errors = []

    # Validate profit targets
    if MIN_PROFIT_TARGET_PCT >= MAX_PROFIT_TARGET_PCT:
        errors.append("MIN_PROFIT_TARGET_PCT must be less than MAX_PROFIT_TARGET_PCT")

    # Validate stop loss
    if MIN_STOP_LOSS_PCT >= MAX_STOP_LOSS_PCT:
        errors.append("MIN_STOP_LOSS_PCT must be less than MAX_STOP_LOSS_PCT")

    # Validate order sizes
    if MIN_ORDER_VALUE_USD_FLOOR >= MAX_TRADE_VALUE_USD:
        errors.append("MIN_ORDER_VALUE_USD_FLOOR must be less than MAX_TRADE_VALUE_USD")

    # Validate risk percentages
    if MAX_DAILY_LOSS_PCT >= Decimal("1.0"):
        errors.append("MAX_DAILY_LOSS_PCT should be less than 100%")

    if errors:
        raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")

    _config_logger.info("Configuration validation passed")

# Run validation on import
validate_config()

# ===================================================================
# END OF CONFIGURATION
# ===================================================================