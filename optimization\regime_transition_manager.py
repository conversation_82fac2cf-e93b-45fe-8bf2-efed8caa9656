#!/usr/bin/env python3
"""
Regime Transition Manager for Phase 3

Handles smooth transitions between different market regimes and their associated
parameter sets. Prevents abrupt parameter changes that could cause trading instability.

Key Features:
- Smooth parameter transitions using weighted averaging
- Regime confidence-based transition timing
- Parameter change validation and safety checks
- Transition history tracking
- Emergency fallback mechanisms

Author: Bitcoin AI Trading Bot - Phase 3 Enhancement
Date: July 29, 2025
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from optimization.market_regime_analyzer import MarketRegime, RegimeAnalysis
from optimization.regime_specific_optimizer import RegimeSpecificOptimizer

logger = logging.getLogger("TradingBotApp.RegimeTransitionManager")

@dataclass
class TransitionState:
    """Current state of regime transition."""
    from_regime: MarketRegime
    to_regime: MarketRegime
    transition_progress: float  # 0.0 to 1.0
    start_time: datetime
    expected_duration: timedelta
    confidence_threshold: float

@dataclass
class ParameterTransition:
    """Individual parameter transition details."""
    parameter_name: str
    from_value: float
    to_value: float
    current_value: float
    transition_speed: float  # How fast this parameter should change

class RegimeTransitionManager:
    """
    Manages smooth transitions between market regimes and their parameter sets.
    
    This system ensures that parameter changes happen gradually and safely,
    preventing sudden trading behavior changes that could cause losses.
    """
    
    def __init__(self, 
                 transition_duration_minutes: int = 15,
                 confidence_threshold: float = 0.75,
                 max_parameter_change_per_step: float = 0.1):
        """
        Initialize the regime transition manager.
        
        Args:
            transition_duration_minutes: How long transitions should take
            confidence_threshold: Minimum confidence to start transition
            max_parameter_change_per_step: Maximum parameter change per update
        """
        self.transition_duration = timedelta(minutes=transition_duration_minutes)
        self.confidence_threshold = confidence_threshold
        self.max_change_per_step = max_parameter_change_per_step
        
        # Current state
        self.current_regime = MarketRegime.UNKNOWN
        self.current_parameters: Dict[str, float] = {}
        self.target_parameters: Dict[str, float] = {}
        
        # Transition state
        self.in_transition = False
        self.transition_state: Optional[TransitionState] = None
        self.parameter_transitions: Dict[str, ParameterTransition] = {}
        
        # History tracking
        self.transition_history: List[Tuple[datetime, MarketRegime, MarketRegime]] = []
        
        # Safety mechanisms
        self.emergency_fallback_params: Dict[str, float] = {}
        self.last_stable_params: Dict[str, float] = {}
        
        logger.info("Regime Transition Manager initialized")
    
    def update_regime(self, 
                     regime_analysis: RegimeAnalysis,
                     regime_parameters: Dict[str, float]) -> Dict[str, float]:
        """
        Update current regime and handle transitions if needed.
        
        Args:
            regime_analysis: Current regime analysis
            regime_parameters: Optimized parameters for the detected regime
            
        Returns:
            Current parameters to use (may be transitioning)
        """
        
        detected_regime = regime_analysis.regime
        confidence = regime_analysis.confidence
        
        # Check if we need to start a transition
        if self._should_start_transition(detected_regime, confidence):
            self._start_transition(detected_regime, regime_parameters)
        
        # Update ongoing transition
        if self.in_transition:
            self._update_transition()
        
        # Return current parameters (transitioning or stable)
        # Ensure we always return a dictionary
        if not isinstance(self.current_parameters, dict):
            logger.error(f"current_parameters is not a dict: {type(self.current_parameters)} = {self.current_parameters}")
            self.current_parameters = {}

        return self.current_parameters.copy()
    
    def _should_start_transition(self, new_regime: MarketRegime, confidence: float) -> bool:
        """Determine if we should start a regime transition."""
        
        # Don't transition if confidence is too low
        if confidence < self.confidence_threshold:
            return False
        
        # Don't transition if already in transition
        if self.in_transition:
            return False
        
        # Don't transition if regime hasn't changed
        if new_regime == self.current_regime:
            return False
        
        # Don't transition too frequently (minimum 5 minutes between transitions)
        if self.transition_history:
            last_transition_time = self.transition_history[-1][0]
            if datetime.now() - last_transition_time < timedelta(minutes=5):
                logger.info(f"Transition too soon, waiting (last: {last_transition_time})")
                return False
        
        return True
    
    def _start_transition(self, new_regime: MarketRegime, new_parameters: Dict[str, float]):
        """Start a transition to a new regime."""
        
        logger.info(f"Starting transition: {self.current_regime.value} → {new_regime.value}")
        
        # Save current stable parameters as fallback
        if self.current_parameters:
            self.last_stable_params = self.current_parameters.copy()
        
        # Create transition state
        self.transition_state = TransitionState(
            from_regime=self.current_regime,
            to_regime=new_regime,
            transition_progress=0.0,
            start_time=datetime.now(),
            expected_duration=self.transition_duration,
            confidence_threshold=self.confidence_threshold
        )
        
        # Set target parameters
        self.target_parameters = new_parameters.copy()
        
        # Create parameter transitions
        self._create_parameter_transitions()
        
        # Mark as in transition
        self.in_transition = True
        
        # Record transition
        self.transition_history.append((datetime.now(), self.current_regime, new_regime))
        
        # Update current regime
        self.current_regime = new_regime
    
    def _create_parameter_transitions(self):
        """Create individual parameter transition plans."""
        
        self.parameter_transitions = {}
        
        for param_name, target_value in self.target_parameters.items():
            current_value = self.current_parameters.get(param_name, target_value)
            
            # Calculate transition speed based on parameter type
            transition_speed = self._calculate_transition_speed(param_name, current_value, target_value)
            
            self.parameter_transitions[param_name] = ParameterTransition(
                parameter_name=param_name,
                from_value=current_value,
                to_value=target_value,
                current_value=current_value,
                transition_speed=transition_speed
            )
    
    def _calculate_transition_speed(self, param_name: str, from_value: float, to_value: float) -> float:
        """Calculate how fast a parameter should transition."""
        
        # Different parameters have different transition speeds
        speed_multipliers = {
            'stop_percent': 0.5,  # Slower for risk parameters
            'trail_profit_buffer_pct': 0.5,
            'max_trade_value_usd': 0.3,  # Very slow for position sizing
            'cash_reserve_usd': 0.3,
            'rsi_overbought': 0.8,  # Faster for technical indicators
            'rsi_oversold': 0.8,
            'trade_cycle_interval_minutes': 0.7,
            'cooldown_buy_after_sell_minutes': 0.7,
            'cooldown_sell_after_buy_minutes': 0.7
        }
        
        base_speed = 1.0 / (self.transition_duration.total_seconds() / 60)  # Base speed per minute
        multiplier = speed_multipliers.get(param_name, 1.0)
        
        return base_speed * multiplier
    
    def _update_transition(self):
        """Update ongoing parameter transition."""
        
        if not self.transition_state:
            return
        
        # Calculate transition progress
        elapsed = datetime.now() - self.transition_state.start_time
        progress = min(1.0, elapsed.total_seconds() / self.transition_state.expected_duration.total_seconds())
        
        self.transition_state.transition_progress = progress
        
        # Update each parameter
        for param_name, transition in self.parameter_transitions.items():
            # Calculate new value using smooth interpolation
            new_value = self._interpolate_parameter(transition, progress)
            
            # Apply safety limits
            new_value = self._apply_safety_limits(param_name, new_value, transition.current_value)
            
            # Update current value
            transition.current_value = new_value
            self.current_parameters[param_name] = new_value
        
        # Check if transition is complete
        if progress >= 1.0:
            self._complete_transition()
    
    def _interpolate_parameter(self, transition: ParameterTransition, progress: float) -> float:
        """Smoothly interpolate parameter value."""
        
        # Use smooth cubic interpolation for natural transitions
        smooth_progress = 3 * progress**2 - 2 * progress**3
        
        return transition.from_value + (transition.to_value - transition.from_value) * smooth_progress
    
    def _apply_safety_limits(self, param_name: str, new_value: float, current_value: float) -> float:
        """Apply safety limits to parameter changes."""
        
        # Limit maximum change per step
        max_change = abs(current_value * self.max_change_per_step)
        
        if abs(new_value - current_value) > max_change:
            if new_value > current_value:
                return current_value + max_change
            else:
                return current_value - max_change
        
        return new_value
    
    def _complete_transition(self):
        """Complete the current transition."""
        
        logger.info(f"Transition completed: {self.transition_state.from_regime.value} → {self.transition_state.to_regime.value}")
        
        # Finalize parameters
        self.current_parameters = self.target_parameters.copy()
        
        # Clear transition state
        self.in_transition = False
        self.transition_state = None
        self.parameter_transitions = {}
        
        # Update stable parameters
        self.last_stable_params = self.current_parameters.copy()
    
    def force_emergency_fallback(self):
        """Force fallback to last stable parameters in case of emergency."""
        
        logger.warning("Emergency fallback activated - reverting to last stable parameters")
        
        if self.last_stable_params:
            self.current_parameters = self.last_stable_params.copy()
        elif self.emergency_fallback_params:
            self.current_parameters = self.emergency_fallback_params.copy()
        
        # Cancel any ongoing transition
        self.in_transition = False
        self.transition_state = None
        self.parameter_transitions = {}
    
    def set_emergency_fallback_parameters(self, params: Dict[str, float]):
        """Set emergency fallback parameters."""
        self.emergency_fallback_params = params.copy()
        logger.info("Emergency fallback parameters updated")
    
    def get_transition_status(self) -> Dict[str, Any]:
        """Get current transition status for monitoring."""
        
        if not self.in_transition:
            return {
                "in_transition": False,
                "current_regime": self.current_regime.value,
                "stable_since": self.transition_history[-1][0] if self.transition_history else None
            }
        
        return {
            "in_transition": True,
            "from_regime": self.transition_state.from_regime.value,
            "to_regime": self.transition_state.to_regime.value,
            "progress": self.transition_state.transition_progress,
            "elapsed_minutes": (datetime.now() - self.transition_state.start_time).total_seconds() / 60,
            "expected_duration_minutes": self.transition_state.expected_duration.total_seconds() / 60,
            "parameters_transitioning": len(self.parameter_transitions)
        }
    
    def get_parameter_changes(self) -> Dict[str, Dict[str, float]]:
        """Get details of current parameter changes."""
        
        if not self.in_transition:
            return {}
        
        changes = {}
        for param_name, transition in self.parameter_transitions.items():
            changes[param_name] = {
                "from": transition.from_value,
                "to": transition.to_value,
                "current": transition.current_value,
                "progress": (transition.current_value - transition.from_value) / 
                           (transition.to_value - transition.from_value) if transition.to_value != transition.from_value else 1.0
            }
        
        return changes
