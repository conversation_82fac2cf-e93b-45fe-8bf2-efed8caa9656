{"collection_timestamp": "2025-08-12T20:44:54.927305", "trade_data": {"open_lots": [{"lot_id": "lot_e17ac73b-1b00-4ecf-801b-4d7d01dc9364_1754891408.387615", "buy_order_id": "e17ac73b-1b00-4ecf-801b-4d7d01dc9364", "buy_timestamp": "2025-08-11T05:50:03.198497+00:00", "original_qty": "0.00440591", "remaining_qty": "0.00440591", "buy_price": "121951.371826705", "buy_fee_usd": "1.34326692", "cost_basis_per_unit": "122256.2502559057780458520487", "initial_stop": "122073.323198531705", "current_stop": "122378.5065061616838238979007", "take_profit_price": "124390.3993", "type": "dip_accumulation", "strategy_type": "DIP_ACCUMULATION", "original_order_ids": ["e17ac73b-1b00-4ecf-801b-4d7d01dc9364"]}], "trade_summary": {"total_lots": 1, "total_value": 537.3067686449979, "lot_types": {"dip_accumulation": 1}, "average_holding_period_hours": 38.914362723055554, "oldest_lot_hours": 38.914362723055554, "newest_lot_hours": 38.914362723055554}, "position_analysis": {"total_unrealized_pnl": 0.0, "profitable_lots": 0, "losing_lots": 0, "breakeven_lots": 1}}, "bot_state": {"last_cycles": [], "last_trade_timestamp": null, "last_trade_side": null, "pause_trading_today": false}, "session_data": {"equity_start": 2228.98, "equity_max": 2269.89, "equity_min": 0.0, "net_pl": "91.44690648107517101726962576", "cycle_count": 928, "win_count": 682, "loss_count": 155, "max_drawdown": "1", "win_streak": 506, "loss_streak": 0, "last_equity": 2225.78, "last_action": "No action (AI decision: HOLD)", "daily_start_equity": "2196.6", "daily_start_date": "2025-08-10", "weekly_start_equity": "2136.47", "weekly_start_date": "2025-08-04", "safety_triggered": false, "safety_reason": "", "safety_day": null, "last_retrain_date": "2025-08-12", "pause_until": null, "last_action_was_loss": false, "equity_now": "2225.78"}, "log_analysis": {"ai_decisions": [], "trade_actions": [], "errors": [{"timestamp": "2025-08-12 06:11:52", "error": "2025-08-12 06:11:52,413 - ERROR    - Binance price fetch error: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/ticker/price?symbol=BTCUSDT (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001150CBF1220>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))"}, {"timestamp": "2025-08-12 06:12:04", "error": "2025-08-12 06:12:04,416 - ERROR    - Coinbase price fetch error: HTTPSConnectionPool(host='api.coinbase.com', port=443): Max retries exceeded with url: /v2/prices/BTC-USD/spot (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001150CBF3EC0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))"}, {"timestamp": "2025-08-12 06:12:04", "error": "2025-08-12 06:12:04,419 - ERROR    - Kraken price fetch error: HTTPSConnectionPool(host='api.kraken.com', port=443): Max retries exceeded with url: /0/public/Ticker?pair=XBTUSD (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001150CBF2CC0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))"}, {"timestamp": "2025-08-12 06:12:04", "error": "2025-08-12 06:12:04,419 - ERROR    - Insufficient data or no avg_price. 1m bars: 5000/45, 5m bars: 1003/15, Avg Price: None. Cannot perform full market analysis."}, {"timestamp": "2025-08-12 06:12:04", "error": "2025-08-12 06:12:04,419 - ERROR    - Cycle: Insufficient market data: Market data unavailable or insufficient for full analysis.. Skipping logic."}], "performance_metrics": {"total_decisions": 0, "total_actions": 0, "total_errors": 5, "decision_distribution": {}, "error_rate": 0}}, "summary": {"collection_time": "2025-08-12T20:44:54.927305", "data_sources": ["open_lots", "bot_state", "session_data"], "key_metrics": {"total_open_lots": 1, "total_decisions": 0, "total_errors": 0, "error_rate": 0}}}