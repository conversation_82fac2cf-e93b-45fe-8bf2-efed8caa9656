import os
from datetime import datetime, timedelta, date, UTC 
import time 
import logging 
import json 

from alpaca_trade_api.rest import REST
import config 

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_history_fetch_activities_robust():
    try:
        api = REST(
            key_id=config.ALPACA_API_KEY_ID,
            secret_key=config.ALPACA_SECRET_KEY,
            base_url=config.ALPACA_BASE_URL,
            api_version='v2'
        )
        
        symbol_to_test = "BTC/USD" # <-- THIS IS THE CRITICAL CHANGE
        
        history_start_date = datetime(2015, 1, 1, tzinfo=UTC) 
        history_end_date = datetime.now(UTC) 

        logger.info(f"Attempting to fetch 'FILL' activities (RAW) for {symbol_to_test} from {history_start_date.isoformat()} to {history_end_date.isoformat()}.")
        
        all_raw_activities_fetched = []
        current_page_token = None
        page_count = 0
        max_pages = 50 

        while page_count < max_pages:
            page_count += 1
            logger.info(f"  Fetching page {page_count} with token: {current_page_token}")
            
            activities_response = api.get_activities(
                activity_types=['FILL'], 
                date=None, 
                after=history_start_date.isoformat(), 
                until=history_end_date.isoformat(),
                page_token=current_page_token,
                direction="asc" 
            )
            
            if not activities_response: break
            all_raw_activities_fetched.extend(activities_response)
            logger.info(f"  Fetched {len(activities_response)} activities on this page (total raw: {len(all_raw_activities_fetched)}).")

            current_page_token = getattr(activities_response, 'next_page_token', None)
            if not current_page_token: break 
            
            time.sleep(0.5) 
            
        logger.info(f"\n--- RAW ACTIVITIES INSPECTION (First 10) ---")
        if all_raw_activities_fetched:
            for i, activity in enumerate(all_raw_activities_fetched[:10]):
                logger.info(f"  Activity {i+1} Type: {activity.activity_type}, ID: {activity.id}")
                try: logger.info(f"  Details: {activity.to_dict()}")
                except AttributeError: logger.info(f"  Details: {activity._raw}")
            
            logger.info(f"\n--- Symbol/Side Check for {symbol_to_test} ---") # Log actual symbol
            btc_fills = []
            for activity in all_raw_activities_fetched:
                if getattr(activity, 'symbol', None) == symbol_to_test and (getattr(activity, 'side', None) == 'buy' or getattr(activity, 'side', None) == 'sell'): # Explicit check for side
                    logger.info(f"  Found potential {symbol_to_test} fill: Type={activity.activity_type}, Side={getattr(activity, 'side', 'N/A')}, Qty={getattr(activity, 'qty', 'N/A')}, Price={getattr(activity, 'price', 'N/A')}, Time={getattr(activity, 'transaction_time', 'N/A')}")
                    btc_fills.append(activity)

            if btc_fills:
                logger.info(f"Total {symbol_to_test} activities found: {len(btc_fills)}")
            else:
                logger.warning(f"No activities found with symbol '{symbol_to_test}'.")

        else: logger.warning("No activities were retrieved at all for inspection.")

    except Exception as e: logger.exception(f"An error occurred during API test: {e}")

if __name__ == "__main__":
    test_history_fetch_activities_robust()