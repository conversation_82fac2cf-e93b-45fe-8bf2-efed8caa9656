import csv

LOG_FILE = "trade_cycle_log.csv"
NUM_CYCLES_TO_REVIEW = 20  # Window to check for streaks

# These could be parameters set in your config
DEFAULT_RISK_PERCENT = 1.0
MIN_RISK_PERCENT = 0.25
MAX_RISK_PERCENT = 2.0
AGGRESSION_LEVELS = ['defensive', 'normal', 'aggressive']

def try_float(x):
    try:
        return float(x)
    except:
        return 0.0

def simulate_adaptive_tuning():
    rows = []
    with open(LOG_FILE, newline="") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            rows.append(row)
    if not rows:
        print("No trades logged yet!")
        return

    recent = rows[-NUM_CYCLES_TO_REVIEW:]
    current_risk = DEFAULT_RISK_PERCENT
    current_aggression = 'normal'
    win_streak, loss_streak = 0, 0

    print("="*42)
    print(f"ADAPTIVE TUNING SIMULATION (last {NUM_CYCLES_TO_REVIEW} cycles)")
    print("="*42)

    for idx, row in enumerate(recent):
        action = row.get("trade_action", "").upper()
        pnl = try_float(row.get("session_pnl", 0))

        if "WIN" in row.get("trade_result", "").upper() or (pnl > 0.01 and action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            win_streak += 1
            loss_streak = 0
        elif "LOSS" in row.get("trade_result", "").upper() or (pnl < -0.01 and action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            loss_streak += 1
            win_streak = 0
        else:
            # Hold or no action resets both streaks
            win_streak = 0
            loss_streak = 0

        # Adaptive logic
        change = None
        if loss_streak >= 3:
            if current_risk > MIN_RISK_PERCENT:
                change = f"Reduce risk (from {current_risk:.2f}% to {max(current_risk - 0.25, MIN_RISK_PERCENT):.2f}%)"
                current_risk = max(current_risk - 0.25, MIN_RISK_PERCENT)
                current_aggression = 'defensive'
        elif win_streak >= 3:
            if current_risk < MAX_RISK_PERCENT:
                change = f"Increase risk (from {current_risk:.2f}% to {min(current_risk + 0.25, MAX_RISK_PERCENT):.2f}%)"
                current_risk = min(current_risk + 0.25, MAX_RISK_PERCENT)
                current_aggression = 'aggressive'
        else:
            change = "Keep risk/aggression unchanged"

        print(f"Cycle {idx+1}: Action={action}, PnL={pnl:.2f}, WinStreak={win_streak}, LossStreak={loss_streak}, Aggression={current_aggression}, Risk={current_risk:.2f}%")
        if change:
            print(f" -> {change}")

    print("="*42)
    print("This is a SIMULATION. No settings are changed.")
    print("="*42)
    print("If you like this logic, you can copy/adapt it to your main bot code.")

if __name__ == "__main__":
    simulate_adaptive_tuning()
