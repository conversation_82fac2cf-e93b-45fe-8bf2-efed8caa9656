2025-07-26 15:34:15,297 - INFO - Starting merge_data_files.py script.
2025-07-26 15:34:15,297 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-26 15:34:15,298 - INFO - --- Starting Data Merging and Finalization ---
2025-07-26 15:34:15,298 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-26 15:34:18,154 - INFO - Loaded 1856000 rows from backup file.
2025-07-26 15:34:18,154 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-26 15:37:02,519 - INFO - Loaded 102997688 rows from new data file.
2025-07-26 15:37:02,520 - INFO - Concatenating dataframes...
2025-07-26 15:37:03,934 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-26 15:37:19,975 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-26 15:37:45,762 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-07-26 15:37:45,762 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-07-26 15:37:45,762 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-07-26 15:37:46,055 - INFO - merge_data_files.py script completed successfully.
2025-07-26 15:52:22,329 - INFO - Starting merge_data_files.py script.
2025-07-26 15:52:22,330 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-26 15:52:22,330 - INFO - --- Starting Data Merging and Finalization ---
2025-07-26 15:52:22,330 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-26 15:52:25,645 - INFO - Loaded 1856000 rows from backup file.
2025-07-26 15:52:25,645 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-26 15:56:05,827 - INFO - Loaded 102997688 rows from new data file.
2025-07-26 15:56:05,827 - INFO - Concatenating dataframes...
2025-07-26 15:56:07,644 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-26 15:56:28,349 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-26 15:57:09,337 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-07-26 15:57:09,337 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-07-26 15:57:09,337 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-07-26 15:57:09,934 - INFO - merge_data_files.py script completed successfully.
2025-07-26 16:10:58,397 - INFO - Starting merge_data_files.py script.
2025-07-26 16:10:58,397 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-26 16:10:58,397 - INFO - --- Starting Data Merging and Finalization ---
2025-07-26 16:10:58,397 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-26 16:11:01,474 - INFO - Loaded 1856000 rows from backup file.
2025-07-26 16:11:01,474 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-26 16:13:56,689 - INFO - Loaded 102997688 rows from new data file.
2025-07-26 16:13:56,690 - INFO - Concatenating dataframes...
2025-07-26 16:13:57,930 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-26 16:14:13,973 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-26 16:14:39,984 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-07-26 16:14:39,985 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-07-26 16:14:39,985 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-07-26 16:14:40,351 - INFO - merge_data_files.py script completed successfully.
2025-07-27 08:35:16,805 - INFO - Starting merge_data_files.py script.
2025-07-27 08:35:16,806 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-27 08:35:16,806 - INFO - --- Starting Data Merging and Finalization ---
2025-07-27 08:35:16,806 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-27 08:35:19,772 - INFO - Loaded 1856000 rows from backup file.
2025-07-27 08:35:19,772 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-27 08:38:06,163 - INFO - Loaded 102997688 rows from new data file.
2025-07-27 08:38:06,163 - INFO - Concatenating dataframes...
2025-07-27 08:38:07,457 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-27 08:38:23,127 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-27 08:38:48,661 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-07-27 08:38:48,662 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-07-27 08:38:48,662 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-07-27 08:38:49,040 - INFO - merge_data_files.py script completed successfully.
2025-07-28 04:02:56,405 - INFO - Starting merge_data_files.py script.
2025-07-28 04:02:56,406 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-28 04:02:56,406 - INFO - --- Starting Data Merging and Finalization ---
2025-07-28 04:02:56,407 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-28 04:03:01,168 - INFO - Loaded 1856000 rows from backup file.
2025-07-28 04:03:01,169 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-28 04:07:14,588 - INFO - Loaded 102997688 rows from new data file.
2025-07-28 04:07:14,588 - INFO - Concatenating dataframes...
2025-07-28 04:07:16,059 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-28 04:07:33,460 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-29 13:34:52,987 - INFO - Starting merge_data_files.py script.
2025-07-29 13:34:52,988 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-07-29 13:34:52,988 - INFO - --- Starting Data Merging and Finalization ---
2025-07-29 13:34:52,988 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-07-29 13:34:56,177 - INFO - Loaded 1856000 rows from backup file.
2025-07-29 13:34:56,178 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-07-29 13:38:39,623 - INFO - Loaded 102997688 rows from new data file.
2025-07-29 13:38:39,623 - INFO - Concatenating dataframes...
2025-07-29 13:38:41,286 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-07-29 13:38:59,146 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-07-29 13:39:39,564 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-07-29 13:39:39,564 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-07-29 13:39:39,564 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-07-29 13:39:40,236 - INFO - merge_data_files.py script completed successfully.
2025-08-01 04:11:08,512 - INFO - Starting merge_data_files.py script.
2025-08-01 04:11:08,512 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-01 04:11:08,512 - INFO - --- Starting Data Merging and Finalization ---
2025-08-01 04:11:08,512 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-01 04:11:12,376 - INFO - Loaded 1856000 rows from backup file.
2025-08-01 04:11:12,377 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-01 04:14:43,191 - INFO - Loaded 102997688 rows from new data file.
2025-08-01 04:14:43,192 - INFO - Concatenating dataframes...
2025-08-01 04:14:44,783 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-01 04:15:01,800 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-01 04:15:34,474 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-01 04:15:34,475 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-01 04:15:34,475 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-01 04:15:34,946 - INFO - merge_data_files.py script completed successfully.
2025-08-02 04:22:29,434 - INFO - Starting merge_data_files.py script.
2025-08-02 04:22:29,434 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-02 04:22:29,434 - INFO - --- Starting Data Merging and Finalization ---
2025-08-02 04:22:29,434 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-02 04:22:32,919 - INFO - Loaded 1856000 rows from backup file.
2025-08-02 04:22:32,920 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-02 04:25:30,734 - INFO - Loaded 102997688 rows from new data file.
2025-08-02 04:25:30,734 - INFO - Concatenating dataframes...
2025-08-02 04:25:32,237 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-02 04:25:49,473 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-02 04:26:15,682 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-02 04:26:15,683 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-02 04:26:15,683 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-02 04:26:16,067 - INFO - merge_data_files.py script completed successfully.
2025-08-03 04:22:28,319 - INFO - Starting merge_data_files.py script.
2025-08-03 04:22:28,319 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-03 04:22:28,319 - INFO - --- Starting Data Merging and Finalization ---
2025-08-03 04:22:28,319 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-03 04:22:31,667 - INFO - Loaded 1856000 rows from backup file.
2025-08-03 04:22:31,667 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-03 04:25:19,422 - INFO - Loaded 102997688 rows from new data file.
2025-08-03 04:25:19,424 - INFO - Concatenating dataframes...
2025-08-03 04:25:20,792 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-03 04:25:35,961 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-03 04:26:02,471 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-03 04:26:02,471 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-03 04:26:02,471 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-03 04:26:02,847 - INFO - merge_data_files.py script completed successfully.
2025-08-04 04:01:46,750 - INFO - Starting merge_data_files.py script.
2025-08-04 04:01:46,750 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-04 04:01:46,750 - INFO - --- Starting Data Merging and Finalization ---
2025-08-04 04:01:46,750 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-04 04:01:49,795 - INFO - Loaded 1856000 rows from backup file.
2025-08-04 04:01:49,796 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-04 04:05:00,522 - INFO - Loaded 102997688 rows from new data file.
2025-08-04 04:05:00,522 - INFO - Concatenating dataframes...
2025-08-04 04:05:02,316 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-04 04:05:19,771 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-04 04:05:47,366 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-04 04:05:47,366 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-04 04:05:47,366 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-04 04:05:47,806 - INFO - merge_data_files.py script completed successfully.
2025-08-05 04:28:20,921 - INFO - Starting merge_data_files.py script.
2025-08-05 04:28:20,922 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-05 04:28:20,922 - INFO - --- Starting Data Merging and Finalization ---
2025-08-05 04:28:20,922 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-05 04:28:23,946 - INFO - Loaded 1856000 rows from backup file.
2025-08-05 04:28:23,946 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-05 04:31:24,900 - INFO - Loaded 102997688 rows from new data file.
2025-08-05 04:31:24,901 - INFO - Concatenating dataframes...
2025-08-05 04:31:26,501 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-05 04:31:43,173 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-05 04:32:09,213 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-05 04:32:09,213 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-05 04:32:09,214 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-05 04:32:09,676 - INFO - merge_data_files.py script completed successfully.
2025-08-06 04:28:01,869 - INFO - Starting merge_data_files.py script.
2025-08-06 04:28:01,870 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-06 04:28:01,871 - INFO - --- Starting Data Merging and Finalization ---
2025-08-06 04:28:01,871 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-06 04:28:04,721 - INFO - Loaded 1856000 rows from backup file.
2025-08-06 04:28:04,722 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-06 04:30:50,695 - INFO - Loaded 102997688 rows from new data file.
2025-08-06 04:30:50,695 - INFO - Concatenating dataframes...
2025-08-06 04:30:52,045 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-06 04:31:08,725 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-06 04:31:32,851 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-06 04:31:32,851 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-06 04:31:32,851 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-06 04:31:33,199 - INFO - merge_data_files.py script completed successfully.
2025-08-07 04:28:07,325 - INFO - Starting merge_data_files.py script.
2025-08-07 04:28:07,335 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-07 04:28:07,335 - INFO - --- Starting Data Merging and Finalization ---
2025-08-07 04:28:07,335 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-07 04:28:10,619 - INFO - Loaded 1856000 rows from backup file.
2025-08-07 04:28:10,620 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-07 04:31:08,498 - INFO - Loaded 102997688 rows from new data file.
2025-08-07 04:31:08,498 - INFO - Concatenating dataframes...
2025-08-07 04:31:10,108 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-07 04:31:26,368 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-07 04:31:51,766 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-07 04:31:51,767 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-07 04:31:51,767 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-07 04:31:52,211 - INFO - merge_data_files.py script completed successfully.
2025-08-08 04:12:29,436 - INFO - Starting merge_data_files.py script.
2025-08-08 04:12:29,437 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-08 04:12:29,437 - INFO - --- Starting Data Merging and Finalization ---
2025-08-08 04:12:29,438 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-08 04:12:32,692 - INFO - Loaded 1856000 rows from backup file.
2025-08-08 04:12:32,692 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-08 04:15:32,691 - INFO - Loaded 102997688 rows from new data file.
2025-08-08 04:15:32,691 - INFO - Concatenating dataframes...
2025-08-08 04:15:34,448 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-08 04:15:51,450 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-08 04:16:16,681 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-08 04:16:16,681 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-08 04:16:16,681 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-08 04:16:17,266 - INFO - merge_data_files.py script completed successfully.
2025-08-09 04:12:37,118 - INFO - Starting merge_data_files.py script.
2025-08-09 04:12:37,126 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-09 04:12:37,126 - INFO - --- Starting Data Merging and Finalization ---
2025-08-09 04:12:37,126 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-09 04:12:40,572 - INFO - Loaded 1856000 rows from backup file.
2025-08-09 04:12:40,572 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-09 04:15:44,090 - INFO - Loaded 102997688 rows from new data file.
2025-08-09 04:15:44,090 - INFO - Concatenating dataframes...
2025-08-09 04:15:45,770 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-09 04:15:52,439 - ERROR - Error during data merging and finalization: Unable to allocate 8.59 GiB for an array with shape (11, 104853688) and data type float64
2025-08-09 04:16:55,700 - INFO - Starting merge_data_files.py script.
2025-08-09 04:16:55,700 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-09 04:16:55,700 - INFO - --- Starting Data Merging and Finalization ---
2025-08-09 04:16:55,700 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-09 04:16:58,912 - INFO - Loaded 1856000 rows from backup file.
2025-08-09 04:16:58,912 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-09 04:20:00,255 - INFO - Loaded 102997688 rows from new data file.
2025-08-09 04:20:00,255 - INFO - Concatenating dataframes...
2025-08-09 04:20:01,387 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-09 04:20:17,884 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-09 04:20:46,154 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-09 04:20:46,154 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-09 04:20:46,154 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-09 04:20:46,539 - INFO - merge_data_files.py script completed successfully.
2025-08-10 04:24:34,758 - INFO - Starting merge_data_files.py script.
2025-08-10 04:24:34,758 - INFO - New data file binance_btcusdt_1m_NEW_DATA.csv found. Proceeding with merge.
2025-08-10 04:24:34,758 - INFO - --- Starting Data Merging and Finalization ---
2025-08-10 04:24:34,758 - INFO - Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
2025-08-10 04:24:38,156 - INFO - Loaded 1856000 rows from backup file.
2025-08-10 04:24:38,156 - INFO - Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
2025-08-10 04:27:35,822 - INFO - Loaded 102997688 rows from new data file.
2025-08-10 04:27:35,822 - INFO - Concatenating dataframes...
2025-08-10 04:27:37,350 - INFO - Sorting combined data by timestamp and removing duplicates...
2025-08-10 04:27:53,598 - INFO - Saving finalized merged data to binance_btcusdt_1m.csv...
2025-08-10 04:28:20,200 - INFO - Successfully merged and saved 4167805 rows to binance_btcusdt_1m.csv.
2025-08-10 04:28:20,201 - INFO - First timestamp in merged file: 2017-08-17 04:00:00+00:00
2025-08-10 04:28:20,201 - INFO - Last timestamp in merged file: 2025-07-26 11:16:00+00:00
2025-08-10 04:28:20,593 - INFO - merge_data_files.py script completed successfully.
