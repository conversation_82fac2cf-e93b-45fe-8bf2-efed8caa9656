"""
Configuration Safety Tests - Prevent Dangerous Settings

These tests ensure the trading bot:
- Validates all configuration parameters
- Prevents dangerous trading limits
- Enforces safety boundaries
- Handles missing/invalid config gracefully
- Maintains secure API credentials
"""

import unittest
from unittest.mock import patch, mock_open
from decimal import Decimal
import sys
import os
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config


class TestConfigurationSafety(unittest.TestCase):
    """Test configuration safety and validation"""
    
    def test_trading_limits_are_safe(self):
        """CRITICAL: Ensure trading limits prevent catastrophic losses"""
        # Test maximum trade value is reasonable (user set to $50 intentionally)
        max_trade = config.MAX_TRADE_VALUE_USD
        self.assertGreaterEqual(
            max_trade,
            10,  # At least $10 per trade
            f"MAX_TRADE_VALUE_USD of ${max_trade} is too small for meaningful trades"
        )
        self.assertLessEqual(
            max_trade,
            10000,  # Never more than $10k per trade
            f"MAX_TRADE_VALUE_USD of ${max_trade} is dangerously high"
        )
        
        # Test stop loss percentage is reasonable
        stop_percent = config.STOP_PERCENT
        self.assertGreaterEqual(
            stop_percent,
            0.01,  # At least 1% stop loss
            f"STOP_PERCENT of {stop_percent:.4f} is too small"
        )
        self.assertLessEqual(
            stop_percent,
            0.10,  # No more than 10% stop loss
            f"STOP_PERCENT of {stop_percent:.4f} is too large"
        )
        
        # Test daily loss limit is reasonable (0.0 means "never sell at a loss" - valid strategy)
        max_loss = config.MAX_AI_LOSS_EXIT_PCT
        self.assertGreaterEqual(
            max_loss,
            0.0,  # 0% is valid (never sell at a loss)
            f"MAX_AI_LOSS_EXIT_PCT of {max_loss:.4f} cannot be negative"
        )
        self.assertLessEqual(
            max_loss,
            0.20,  # No more than 20% daily loss
            f"MAX_AI_LOSS_EXIT_PCT of {max_loss:.4f} is too large"
        )
    
    def test_cash_reserve_is_adequate(self):
        """CRITICAL: Ensure cash reserve prevents account depletion"""
        cash_reserve = config.CASH_RESERVE_USD
        
        # Cash reserve should be meaningful
        self.assertGreaterEqual(
            cash_reserve,
            100,  # At least $100 reserve
            f"CASH_RESERVE_USD of ${cash_reserve} is too small"
        )
        
        # But not excessive
        self.assertLessEqual(
            cash_reserve,
            5000,  # No more than $5k reserve
            f"CASH_RESERVE_USD of ${cash_reserve} is excessive"
        )
    
    def test_minimum_order_values_prevent_dust(self):
        """CRITICAL: Ensure minimum order values prevent uneconomical trades"""
        min_order = config.MIN_ORDER_VALUE_USD_FLOOR
        
        # Should be above typical exchange minimums
        self.assertGreaterEqual(
            min_order,
            5,  # At least $5 minimum
            f"MIN_ORDER_VALUE_USD_FLOOR of ${min_order} is too small"
        )
        
        # But not so high it prevents small accounts from trading
        self.assertLessEqual(
            min_order,
            100,  # No more than $100 minimum
            f"MIN_ORDER_VALUE_USD_FLOOR of ${min_order} is too high"
        )
    
    def test_fee_percentage_is_realistic(self):
        """CRITICAL: Ensure fee percentage matches actual trading costs"""
        fee_percent = config.FEE_PERCENT
        
        # Should be realistic for crypto trading
        self.assertGreaterEqual(
            fee_percent,
            0.0001,  # At least 0.01% (very low)
            f"FEE_PERCENT of {fee_percent:.6f} is unrealistically low"
        )
        self.assertLessEqual(
            fee_percent,
            0.01,  # No more than 1% (very high)
            f"FEE_PERCENT of {fee_percent:.6f} is unrealistically high"
        )
    
    def test_rsi_thresholds_are_valid(self):
        """Test RSI thresholds are within valid ranges"""
        # Check if RSI period is reasonable
        rsi_period = config.RSI_PERIOD
        self.assertGreaterEqual(rsi_period, 5, "RSI period too short")
        self.assertLessEqual(rsi_period, 21, "RSI period too long")

        # Test guardian RSI thresholds if they exist
        if hasattr(config, 'GUARDIAN_RSI_OVERSOLD') and hasattr(config, 'GUARDIAN_RSI_OVERBOUGHT'):
            rsi_oversold = float(config.GUARDIAN_RSI_OVERSOLD)
            rsi_overbought = float(config.GUARDIAN_RSI_OVERBOUGHT)

            # RSI values must be between 0 and 100
            self.assertGreaterEqual(rsi_oversold, 0, "Guardian RSI oversold threshold below 0")
            self.assertLessEqual(rsi_oversold, 100, "Guardian RSI oversold threshold above 100")
            self.assertGreaterEqual(rsi_overbought, 0, "Guardian RSI overbought threshold below 0")
            self.assertLessEqual(rsi_overbought, 100, "Guardian RSI overbought threshold above 100")

            # Oversold should be less than overbought
            self.assertLess(
                rsi_oversold,
                rsi_overbought,
                "Guardian RSI oversold threshold should be less than overbought"
            )
    
    def test_guardian_veto_thresholds(self):
        """Test guardian veto RSI thresholds are safe"""
        if hasattr(config, 'GUARDIAN_RSI_OVERSOLD') and hasattr(config, 'GUARDIAN_RSI_OVERBOUGHT'):
            guardian_oversold = float(config.GUARDIAN_RSI_OVERSOLD)
            guardian_overbought = float(config.GUARDIAN_RSI_OVERBOUGHT)

            # Guardian thresholds should be reasonable
            self.assertLessEqual(guardian_oversold, 35, "Guardian oversold threshold too high")
            self.assertGreaterEqual(guardian_overbought, 65, "Guardian overbought threshold too low")

            # Should be properly separated
            self.assertLess(
                guardian_oversold,
                guardian_overbought,
                "Guardian oversold should be less than overbought"
            )
    
    def test_timeouts_are_reasonable(self):
        """Test API timeouts are reasonable"""
        # Check if timeout configurations exist and are reasonable
        if hasattr(config, 'LOSS_PAUSE_SECONDS'):
            pause_seconds = config.LOSS_PAUSE_SECONDS
            self.assertGreaterEqual(
                pause_seconds,
                300,  # At least 5 minutes
                f"Loss pause {pause_seconds}s is too short"
            )
            self.assertLessEqual(
                pause_seconds,
                86400,  # No more than 24 hours
                f"Loss pause {pause_seconds}s is too long"
            )

        # Check RSI period is reasonable
        if hasattr(config, 'RSI_PERIOD'):
            rsi_period = config.RSI_PERIOD
            self.assertGreaterEqual(rsi_period, 5, "RSI period too short")
            self.assertLessEqual(rsi_period, 21, "RSI period too long")
    
    def test_cooldown_periods_prevent_overtrading(self):
        """Test cooldown periods are adequate"""
        if hasattr(config, 'BUY_COOLDOWN_MINUTES'):
            buy_cooldown = config.BUY_COOLDOWN_MINUTES
            
            # Should prevent rapid-fire trading
            self.assertGreaterEqual(
                buy_cooldown,
                1,  # At least 1 minute
                f"BUY_COOLDOWN_MINUTES of {buy_cooldown} is too short"
            )
            
            # But not so long it misses opportunities
            self.assertLessEqual(
                buy_cooldown,
                60,  # No more than 1 hour
                f"BUY_COOLDOWN_MINUTES of {buy_cooldown} is too long"
            )
    
    def test_api_credentials_are_configured(self):
        """Test that API credentials are properly configured"""
        # Check Alpaca credentials exist
        self.assertTrue(
            hasattr(config, 'ALPACA_API_KEY_ID'),
            "ALPACA_API_KEY_ID not configured"
        )
        self.assertTrue(
            hasattr(config, 'ALPACA_SECRET_KEY'),
            "ALPACA_SECRET_KEY not configured"
        )
        self.assertTrue(
            hasattr(config, 'ALPACA_BASE_URL'),
            "ALPACA_BASE_URL not configured"
        )
        
        # Check Google AI credentials exist (using Gemini)
        self.assertTrue(
            hasattr(config, 'GEMINI_API_KEY'),
            "GEMINI_API_KEY not configured"
        )
        
        # Verify base URL is valid
        valid_urls = [
            "https://paper-api.alpaca.markets",  # Paper trading
            "https://api.alpaca.markets"         # Live trading
        ]
        self.assertIn(
            config.ALPACA_BASE_URL,
            valid_urls,
            f"ALPACA_BASE_URL '{config.ALPACA_BASE_URL}' is not a valid Alpaca URL"
        )
    
    def test_file_paths_are_valid(self):
        """Test that file paths are properly configured"""
        # Check critical file paths exist in config
        required_paths = [
            'LOG_FILE_PATH',
            'SESSION_STATS_FILE', 
            'TRADE_HISTORY_FILE',
            'LOTS_LEDGER_FILE'
        ]
        
        for path_name in required_paths:
            self.assertTrue(
                hasattr(config, path_name),
                f"{path_name} not configured"
            )
            
            # Path should be a string
            path_value = getattr(config, path_name)
            self.assertIsInstance(
                path_value,
                str,
                f"{path_name} should be a string"
            )
    
    def test_decimal_precision_is_adequate(self):
        """Test decimal precision settings are adequate for crypto"""
        # Check if precision constants exist
        if hasattr(config, 'PRICE_PRECISION'):
            price_precision = config.PRICE_PRECISION
            self.assertGreaterEqual(
                price_precision,
                2,  # At least 2 decimal places
                "PRICE_PRECISION too low for crypto prices"
            )
        
        if hasattr(config, 'QTY_PRECISION'):
            qty_precision = config.QTY_PRECISION
            self.assertGreaterEqual(
                qty_precision,
                6,  # At least 6 decimal places for BTC quantities
                "QTY_PRECISION too low for crypto quantities"
            )
    
    def test_dynamic_config_validation(self):
        """Test dynamic configuration validation"""
        # Test that dynamic config has reasonable bounds
        if hasattr(config, 'DYNAMIC_CONFIG_FILE'):
            # Mock dynamic config file
            mock_dynamic_config = {
                "risk_percent": 0.02,
                "profit_target_pct": 0.05,
                "stop_percent": 0.03,
                "energy_mode": "balanced"
            }
            
            # Validate risk percent
            risk_percent = mock_dynamic_config.get("risk_percent", 0.01)
            self.assertGreaterEqual(risk_percent, 0.001, "Risk percent too low")
            self.assertLessEqual(risk_percent, 0.10, "Risk percent too high")
            
            # Validate profit target
            profit_target = mock_dynamic_config.get("profit_target_pct", 0.03)
            self.assertGreaterEqual(profit_target, 0.01, "Profit target too low")
            self.assertLessEqual(profit_target, 0.50, "Profit target too high")
            
            # Validate energy mode
            energy_mode = mock_dynamic_config.get("energy_mode", "balanced")
            valid_modes = ["conservative", "balanced", "aggressive", "defensive"]
            self.assertIn(energy_mode, valid_modes, f"Invalid energy mode: {energy_mode}")
    
    def test_logging_configuration(self):
        """Test logging configuration is safe"""
        # Check log levels are reasonable
        if hasattr(config, 'LOG_LEVEL'):
            log_level = config.LOG_LEVEL
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            self.assertIn(log_level, valid_levels, f"Invalid log level: {log_level}")
        
        # Check log file rotation settings
        if hasattr(config, 'MAX_LOG_SIZE_MB'):
            max_size = config.MAX_LOG_SIZE_MB
            self.assertGreaterEqual(max_size, 1, "Log file size too small")
            self.assertLessEqual(max_size, 1000, "Log file size too large")


if __name__ == "__main__":
    unittest.main(verbosity=2)
