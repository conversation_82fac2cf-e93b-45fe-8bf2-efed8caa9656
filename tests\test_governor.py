from decimal import Decimal
import bot_state
import config

def test_daily_gate_triggers(monkeypatch):
    from main_bot import TradingBot
    bot = TradingBot()
    monkeypatch.setattr(bot, "equity_day_open", Decimal("1000"))
    bot_state._state_cache["pause_trading_today"] = False
    target = (Decimal("1000") * config.TARGET_DAILY_RETURN_PCT).quantize(Decimal("0.01"))

    # simulate hitting the target
    bot.session_stats = {"equity_now": Decimal("1000") + target + Decimal("0.01")}

    # call the internal check function
    bot._check_governor()

    assert bot_state._state_cache["pause_trading_today"] is True
