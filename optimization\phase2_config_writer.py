#!/usr/bin/env python3
"""
Phase 2 Configuration Writer

Creates temporary configuration files for passing expanded parameters to the backtester.
Since the backtesting suite is READ-ONLY, we create config files in the main project folder
that the backtester can read to get all 20 parameters.
"""

import json
import os
import tempfile
from typing import Dict, Any
from decimal import Decimal


class Phase2ConfigWriter:
    """
    Writes Phase 2 optimization parameters to temporary config files.
    """
    
    def __init__(self, base_dir: str = None):
        """
        Initialize the config writer.
        
        Args:
            base_dir: Base directory for config files (defaults to current directory)
        """
        self.base_dir = base_dir or os.getcwd()
        self.config_file_prefix = "phase2_optimization_config"
    
    def write_config(self, params: Dict[str, Any], config_id: str = None) -> str:
        """
        Write parameters to a temporary config file.
        
        Args:
            params: Dictionary of optimization parameters
            config_id: Optional unique identifier for the config file
            
        Returns:
            Path to the created config file
        """
        if config_id is None:
            config_id = f"temp_{os.getpid()}"
        
        config_filename = f"{self.config_file_prefix}_{config_id}.json"
        config_path = os.path.join(self.base_dir, config_filename)
        
        # Convert parameters to JSON-serializable format
        serializable_params = self._make_serializable(params)
        
        # Write config file
        with open(config_path, 'w') as f:
            json.dump({
                'phase2_optimization_parameters': serializable_params,
                'created_by': 'Phase2ConfigWriter',
                'config_id': config_id
            }, f, indent=2)
        
        return config_path
    
    def read_config(self, config_path: str) -> Dict[str, Any]:
        """
        Read parameters from a config file.
        
        Args:
            config_path: Path to the config file
            
        Returns:
            Dictionary of parameters
        """
        with open(config_path, 'r') as f:
            data = json.load(f)
        
        return data.get('phase2_optimization_parameters', {})
    
    def cleanup_config(self, config_path: str) -> bool:
        """
        Remove a temporary config file.
        
        Args:
            config_path: Path to the config file to remove
            
        Returns:
            True if file was removed, False otherwise
        """
        try:
            if os.path.exists(config_path):
                os.remove(config_path)
                return True
        except Exception:
            pass
        return False
    
    def _make_serializable(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert parameters to JSON-serializable format.
        
        Args:
            params: Dictionary of parameters
            
        Returns:
            JSON-serializable dictionary
        """
        serializable = {}
        
        for key, value in params.items():
            if isinstance(value, Decimal):
                serializable[key] = float(value)
            elif isinstance(value, (int, float, str, bool)):
                serializable[key] = value
            else:
                # Convert to string as fallback
                serializable[key] = str(value)
        
        return serializable
    
    def create_phase2_config_template(self) -> Dict[str, Any]:
        """
        Create a template with all Phase 2 parameters and their default values.
        
        Returns:
            Dictionary with all Phase 2 parameters
        """
        return {
            # === PHASE 1 PARAMETERS ===
            'stop_percent': 0.02,
            'trail_profit_buffer_pct': 0.005,
            
            # === TECHNICAL INDICATORS ===
            'rsi_period': 14,
            'rsi_overbought': 80.0,
            'rsi_oversold': 20.0,
            'short_sma_period': 10,
            'long_sma_period': 20,
            'macd_fast_period': 12,
            'macd_slow_period': 26,
            'atr_period': 14,
            
            # === RISK MANAGEMENT ===
            'max_trade_value_usd': 50.0,
            'cash_reserve_usd': 250.0,
            'min_qty_pct': 0.05,
            'max_qty_pct': 0.10,
            
            # === PROFIT TARGETS ===
            'dip_buy_profit_target_pct': 0.02,
            'momentum_buy_profit_target_pct': 0.015,
            'granular_take_profit_pct': 0.01,
            
            # === TIMING & COOLDOWNS ===
            'trade_cycle_interval_minutes': 3,
            'cooldown_buy_after_sell_minutes': 5,
            'cooldown_sell_after_buy_minutes': 2
        }
    
    def get_config_mapping(self) -> Dict[str, str]:
        """
        Get mapping of optimization parameters to config.py variable names.
        
        Returns:
            Dictionary mapping parameter names to config variables
        """
        return {
            # Technical indicators
            'rsi_period': 'RSI_PERIOD',
            'rsi_overbought': 'GUARDIAN_RSI_OVERBOUGHT',
            'rsi_oversold': 'GUARDIAN_RSI_OVERSOLD',
            'short_sma_period': 'SHORT_SMA_PERIOD',
            'long_sma_period': 'LONG_SMA_PERIOD',
            'macd_fast_period': 'MACD_FAST_PERIOD',
            'macd_slow_period': 'MACD_SLOW_PERIOD',
            'atr_period': 'ATR_PERIOD',
            
            # Risk management
            'max_trade_value_usd': 'MAX_TRADE_VALUE_USD',
            'cash_reserve_usd': 'CASH_RESERVE_USD',
            'min_qty_pct': 'MIN_QTY_PCT_CONFIG',
            'max_qty_pct': 'MAX_QTY_PCT_CONFIG',
            
            # Profit targets
            'dip_buy_profit_target_pct': 'DIP_BUY_PROFIT_TARGET_PCT',
            'momentum_buy_profit_target_pct': 'MOMENTUM_BUY_NET_PROFIT_TARGET_PCT',
            'granular_take_profit_pct': 'GRANULAR_TAKE_PROFIT_PCT',
            
            # Timing
            'trade_cycle_interval_minutes': 'TRADE_CYCLE_INTERVAL_MINUTES',
            'cooldown_buy_after_sell_minutes': 'COOLDOWN_BUY_AFTER_SELL',
            'cooldown_sell_after_buy_minutes': 'COOLDOWN_SELL_AFTER_BUY',
            
            # Stop loss and trailing
            'stop_percent': 'STOP_LOSS_PCT',
            'trail_profit_buffer_pct': 'TRAIL_PROFIT_BUFFER_PCT'
        }
    
    def validate_config_file(self, config_path: str) -> bool:
        """
        Validate that a config file contains all required Phase 2 parameters.
        
        Args:
            config_path: Path to the config file
            
        Returns:
            True if config is valid, False otherwise
        """
        try:
            params = self.read_config(config_path)
            template = self.create_phase2_config_template()
            
            # Check that all required parameters are present
            for required_param in template.keys():
                if required_param not in params:
                    return False
            
            return True
        except Exception:
            return False


def test_config_writer():
    """Test the Phase 2 config writer."""
    
    print("🧪 Testing Phase 2 Config Writer")
    print("="*50)
    
    writer = Phase2ConfigWriter()
    
    # Test 1: Create config template
    template = writer.create_phase2_config_template()
    print(f"✅ Created template with {len(template)} parameters")
    
    # Test 2: Write config file
    config_path = writer.write_config(template, "test")
    print(f"✅ Created config file: {config_path}")
    
    # Test 3: Read config file
    read_params = writer.read_config(config_path)
    print(f"✅ Read {len(read_params)} parameters from config")
    
    # Test 4: Validate config
    is_valid = writer.validate_config_file(config_path)
    print(f"✅ Config validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # Test 5: Cleanup
    cleaned = writer.cleanup_config(config_path)
    print(f"✅ Config cleanup: {'SUCCESS' if cleaned else 'FAILED'}")
    
    print(f"\n🎊 Config writer test completed!")


if __name__ == "__main__":
    test_config_writer()
