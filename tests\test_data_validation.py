"""
Data Validation Tests - Ensure Accurate Market Analysis

These tests ensure the trading bot:
- Validates market data integrity
- Calculates technical indicators correctly
- Processes ML model outputs safely
- Handles missing/corrupted data gracefully
- Maintains data quality standards
"""

import unittest
from unittest.mock import MagicMock, patch
from decimal import Decimal
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, UTC, timedelta

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
import market_analysis
import trade_logic


class TestMarketDataValidation(unittest.TestCase):
    """Test market data validation and processing"""
    
    def setUp(self):
        """Setup test data"""
        # Create sample market data
        dates = pd.date_range(start='2025-01-01', periods=100, freq='1min')
        self.sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(49000, 51000, 100),
            'high': np.random.uniform(50000, 52000, 100),
            'low': np.random.uniform(48000, 50000, 100),
            'close': np.random.uniform(49500, 50500, 100),
            'volume': np.random.uniform(100, 1000, 100)
        })
        
        # Ensure high >= low and other OHLC relationships
        for i in range(len(self.sample_data)):
            row = self.sample_data.iloc[i]
            high = max(row['open'], row['high'], row['low'], row['close'])
            low = min(row['open'], row['high'], row['low'], row['close'])
            self.sample_data.at[i, 'high'] = high
            self.sample_data.at[i, 'low'] = low
    
    def test_ohlc_data_integrity(self):
        """CRITICAL: Test OHLC data maintains proper relationships"""
        for i, row in self.sample_data.iterrows():
            # High should be >= all other prices
            self.assertGreaterEqual(
                row['high'], row['open'],
                f"Row {i}: High {row['high']} < Open {row['open']}"
            )
            self.assertGreaterEqual(
                row['high'], row['close'],
                f"Row {i}: High {row['high']} < Close {row['close']}"
            )
            self.assertGreaterEqual(
                row['high'], row['low'],
                f"Row {i}: High {row['high']} < Low {row['low']}"
            )
            
            # Low should be <= all other prices
            self.assertLessEqual(
                row['low'], row['open'],
                f"Row {i}: Low {row['low']} > Open {row['open']}"
            )
            self.assertLessEqual(
                row['low'], row['close'],
                f"Row {i}: Low {row['low']} > Close {row['close']}"
            )
            self.assertLessEqual(
                row['low'], row['high'],
                f"Row {i}: Low {row['low']} > High {row['high']}"
            )
            
            # Volume should be positive
            self.assertGreater(
                row['volume'], 0,
                f"Row {i}: Volume {row['volume']} should be positive"
            )
    
    def test_price_data_reasonableness(self):
        """Test that price data is within reasonable ranges"""
        # Bitcoin prices should be within reasonable bounds
        min_reasonable_price = 1000   # $1,000 (very low but possible)
        max_reasonable_price = 500000 # $500,000 (very high but possible)
        
        for col in ['open', 'high', 'low', 'close']:
            prices = self.sample_data[col]
            
            self.assertTrue(
                (prices >= min_reasonable_price).all(),
                f"{col} prices contain unreasonably low values"
            )
            self.assertTrue(
                (prices <= max_reasonable_price).all(),
                f"{col} prices contain unreasonably high values"
            )
    
    def test_missing_data_handling(self):
        """Test handling of missing market data"""
        # Create data with missing values
        corrupted_data = self.sample_data.copy()
        corrupted_data.loc[10:15, 'close'] = np.nan
        corrupted_data.loc[20:25, 'volume'] = np.nan
        
        # Test that missing data is detected
        has_missing = corrupted_data.isnull().any().any()
        self.assertTrue(has_missing, "Missing data not detected")
        
        # Test that missing data can be handled
        # (Implementation would depend on your data cleaning strategy)
        cleaned_data = corrupted_data.dropna()
        self.assertLess(
            len(cleaned_data), len(corrupted_data),
            "Data cleaning should remove rows with missing values"
        )
    
    def test_data_timestamp_continuity(self):
        """Test that timestamp data is continuous and ordered"""
        timestamps = pd.to_datetime(self.sample_data['timestamp'])
        
        # Timestamps should be in ascending order
        self.assertTrue(
            timestamps.is_monotonic_increasing,
            "Timestamps should be in ascending order"
        )
        
        # Check for reasonable time gaps (for 1-minute data)
        time_diffs = timestamps.diff().dropna()
        expected_diff = pd.Timedelta(minutes=1)
        
        # Most differences should be exactly 1 minute
        normal_diffs = (time_diffs == expected_diff).sum()
        total_diffs = len(time_diffs)
        
        self.assertGreater(
            normal_diffs / total_diffs, 0.8,  # At least 80% should be normal
            "Too many irregular time gaps in data"
        )


class TestTechnicalIndicators(unittest.TestCase):
    """Test technical indicator calculations"""
    
    def setUp(self):
        """Setup test data for indicators"""
        # Create predictable price data for testing
        self.prices = pd.Series([
            50000, 50100, 50200, 50150, 50300,
            50250, 50400, 50350, 50500, 50450,
            50600, 50550, 50700, 50650, 50800
        ])
    
    def test_rsi_calculation_bounds(self):
        """Test RSI calculation stays within 0-100 bounds"""
        # Mock RSI calculation (replace with actual function)
        with patch('market_analysis.calculate_rsi') as mock_rsi:
            mock_rsi.return_value = 65.5
            
            rsi_value = mock_rsi(self.prices, period=14)
            
            # RSI must be between 0 and 100
            self.assertGreaterEqual(rsi_value, 0, "RSI below 0")
            self.assertLessEqual(rsi_value, 100, "RSI above 100")
    
    def test_moving_average_calculation(self):
        """Test moving average calculations are correct"""
        # Simple test case
        test_prices = pd.Series([10, 20, 30, 40, 50])
        
        # 3-period SMA of last 3 values should be (30+40+50)/3 = 40
        expected_sma = (30 + 40 + 50) / 3
        
        # Mock SMA calculation
        with patch('market_analysis.calculate_sma') as mock_sma:
            mock_sma.return_value = expected_sma
            
            sma_value = mock_sma(test_prices, period=3)
            self.assertEqual(sma_value, expected_sma)
    
    def test_macd_calculation(self):
        """Test MACD calculation produces reasonable values"""
        with patch('market_analysis.calculate_macd') as mock_macd:
            # Mock MACD components
            mock_macd.return_value = {
                'macd_line': 150.5,
                'signal_line': 145.2,
                'histogram': 5.3
            }
            
            macd_data = mock_macd(self.prices)
            
            # MACD histogram should equal macd_line - signal_line
            expected_histogram = macd_data['macd_line'] - macd_data['signal_line']
            self.assertAlmostEqual(
                macd_data['histogram'], expected_histogram, places=1
            )
    
    def test_atr_calculation_positive(self):
        """Test ATR calculation produces positive values"""
        # Create OHLC data
        ohlc_data = pd.DataFrame({
            'high': [50100, 50200, 50300, 50400, 50500],
            'low': [49900, 50000, 50100, 50200, 50300],
            'close': [50000, 50100, 50200, 50300, 50400]
        })
        
        with patch('market_analysis.calculate_atr') as mock_atr:
            mock_atr.return_value = 125.5
            
            atr_value = mock_atr(ohlc_data, period=14)
            
            # ATR should always be positive
            self.assertGreater(atr_value, 0, "ATR should be positive")


class TestMLModelValidation(unittest.TestCase):
    """Test ML model output validation"""
    
    def test_model_prediction_format(self):
        """Test ML model predictions are in correct format"""
        # Mock model prediction
        mock_prediction = np.array([0, 1, 2])  # BUY, SELL, HOLD classes
        
        # Predictions should be valid class indices
        valid_classes = [0, 1, 2]  # Assuming 3-class classification
        
        for pred in mock_prediction:
            self.assertIn(pred, valid_classes, f"Invalid prediction class: {pred}")
    
    def test_model_probability_bounds(self):
        """Test model probability outputs are valid"""
        # Mock probability predictions
        mock_probabilities = np.array([
            [0.7, 0.2, 0.1],  # High confidence BUY
            [0.1, 0.8, 0.1],  # High confidence SELL  
            [0.3, 0.3, 0.4]   # Low confidence HOLD
        ])
        
        for prob_row in mock_probabilities:
            # Probabilities should sum to 1
            self.assertAlmostEqual(
                prob_row.sum(), 1.0, places=3,
                msg=f"Probabilities don't sum to 1: {prob_row}"
            )
            
            # Each probability should be between 0 and 1
            for prob in prob_row:
                self.assertGreaterEqual(prob, 0, "Probability below 0")
                self.assertLessEqual(prob, 1, "Probability above 1")
    
    def test_feature_data_completeness(self):
        """Test that feature data for ML model is complete"""
        # Mock feature vector
        mock_features = {
            'rsi': 65.5,
            'macd_histogram': 0.15,
            'sma_20': 50000.0,
            'ema_12': 50050.0,
            'atr': 1250.0,
            'volume': 1500.0
        }
        
        # All features should be numeric and not NaN
        for feature_name, feature_value in mock_features.items():
            self.assertIsInstance(
                feature_value, (int, float),
                f"Feature {feature_name} is not numeric"
            )
            self.assertFalse(
                np.isnan(feature_value),
                f"Feature {feature_name} is NaN"
            )


if __name__ == "__main__":
    unittest.main(verbosity=2)
