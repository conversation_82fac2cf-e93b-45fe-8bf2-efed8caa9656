"""
Core Trading Logic Tests - Verify Decision Making

These tests ensure the trading bot:
- Makes correct buy/sell decisions
- Calculates positions accurately
- Manages lots properly
- Handles cooldown periods
- Processes AI decisions correctly
"""

import unittest
from unittest.mock import MagicMock, patch, mock_open
from decimal import Decimal
import sys
import os
import json
from datetime import datetime, UTC, timedelta

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
import trade_logic
import bot_state
from trade_logic import process_ai_decision_and_trade


class TestCoreTradingLogic(unittest.TestCase):
    """Test core trading decision logic"""
    
    def setUp(self):
        """Reset state before each test"""
        bot_state._state_cache = {}
        trade_logic.open_lots = []
        
        # Mock the current time for consistent testing
        self.mock_time = datetime(2025, 1, 15, 12, 0, 0, tzinfo=UTC)
    
    def test_buy_decision_with_sufficient_cash(self):
        """Test BUY decision when account has sufficient cash"""
        # Setup test data
        api_mock = MagicMock()
        api_mock.submit_order.return_value = MagicMock(id="test_order_123")
        
        ai_decision = {
            "decision": "BUY",
            "confidence_level": "HIGH", 
            "quantity_percentage": 0.01,
            "reasoning": "Strong bullish signal"
        }
        
        account = {
            "cash": "5000",
            "buying_power": "10000",
            "equity": "5000"
        }
        
        position = {"qty": 0.0}  # No existing position
        
        indicators = {
            "current_price": Decimal("50000"),
            "rsi": 45.0,  # Neutral RSI
            "macd_histogram": 0.1,
            "atr": Decimal("1000")
        }
        
        # Test the decision process
        with patch('trade_logic.alpaca_service.submit_order') as mock_submit:
            mock_submit.return_value = {"id": "test_order_123", "status": "accepted"}
            
            result, buy_count, sell_count = process_ai_decision_and_trade(
                api_mock, ai_decision, account, position, indicators,
                buy_cnt=0, sell_cnt=0, dynamic_params={}, 
                bot_state_data={}, session_stats={}
            )
            
            # Should result in a buy action
            self.assertIn("BUY", result.get("action", ""))
            self.assertEqual(buy_count, 1)
            self.assertEqual(sell_count, 0)
    
    def test_sell_decision_with_existing_position(self):
        """Test SELL decision when holding BTC position"""
        # Setup existing position
        trade_logic.open_lots = [{
            "lot_id": "lot_001",
            "buy_price": Decimal("48000"),
            "remaining_qty": Decimal("0.001"),
            "timestamp": "2025-01-15T10:00:00Z"
        }]
        
        api_mock = MagicMock()
        api_mock.submit_order.return_value = MagicMock(id="sell_order_123")
        
        ai_decision = {
            "decision": "SELL",
            "confidence_level": "HIGH",
            "quantity_percentage": 1.0,  # Sell all
            "reasoning": "Take profit signal"
        }
        
        account = {
            "cash": "1000",
            "buying_power": "2000", 
            "equity": "1050"  # Slight profit
        }
        
        position = {"qty": 0.001}  # Existing BTC position
        
        indicators = {
            "current_price": Decimal("50000"),  # Profitable price
            "rsi": 75.0,  # Overbought
            "macd_histogram": -0.1,
            "atr": Decimal("1000")
        }
        
        # Test the sell decision
        with patch('trade_logic.alpaca_service.submit_order') as mock_submit:
            mock_submit.return_value = {"id": "sell_order_123", "status": "accepted"}
            
            result, buy_count, sell_count = process_ai_decision_and_trade(
                api_mock, ai_decision, account, position, indicators,
                buy_cnt=0, sell_cnt=0, dynamic_params={},
                bot_state_data={}, session_stats={}
            )
            
            # Should result in a sell action
            self.assertIn("SELL", result.get("action", ""))
            self.assertEqual(buy_count, 0)
            self.assertEqual(sell_count, 1)
    
    def test_hold_decision_during_uncertainty(self):
        """Test HOLD decision when conditions are uncertain"""
        ai_decision = {
            "decision": "HOLD",
            "confidence_level": "LOW",
            "quantity_percentage": 0.0,
            "reasoning": "Market uncertainty, waiting for clearer signal"
        }
        
        account = {"cash": "5000", "buying_power": "10000", "equity": "5000"}
        position = {"qty": 0.0}
        indicators = {
            "current_price": Decimal("50000"),
            "rsi": 50.0,  # Neutral
            "macd_histogram": 0.0,
            "atr": Decimal("1000")
        }
        
        api_mock = MagicMock()
        
        result, buy_count, sell_count = process_ai_decision_and_trade(
            api_mock, ai_decision, account, position, indicators,
            buy_cnt=0, sell_cnt=0, dynamic_params={},
            bot_state_data={}, session_stats={}
        )
        
        # Should result in no action
        self.assertIn("HOLD", result.get("action", ""))
        self.assertEqual(buy_count, 0)
        self.assertEqual(sell_count, 0)
    
    def test_cooldown_period_prevents_rapid_trading(self):
        """Test that cooldown periods prevent excessive trading"""
        # Simulate recent buy order
        recent_time = datetime.now(UTC) - timedelta(minutes=5)  # 5 minutes ago
        
        bot_state_data = {
            "last_buy_time": recent_time.isoformat(),
            "last_sell_time": None
        }
        
        ai_decision = {
            "decision": "BUY",
            "confidence_level": "HIGH",
            "quantity_percentage": 0.01,
            "reasoning": "Another buy signal"
        }
        
        account = {"cash": "5000", "buying_power": "10000", "equity": "5000"}
        position = {"qty": 0.0}
        indicators = {
            "current_price": Decimal("50000"),
            "rsi": 30.0,  # Oversold
            "macd_histogram": 0.2,
            "atr": Decimal("1000")
        }
        
        api_mock = MagicMock()
        
        # If cooldown is enforced, should not execute buy
        result, buy_count, sell_count = process_ai_decision_and_trade(
            api_mock, ai_decision, account, position, indicators,
            buy_cnt=1, sell_cnt=0, dynamic_params={},
            bot_state_data=bot_state_data, session_stats={}
        )
        
        # Should be blocked by cooldown
        self.assertIn("cooldown", result.get("action", "").lower())
        self.assertEqual(buy_count, 1)  # No new buy
    
    def test_insufficient_cash_prevents_buy(self):
        """Test that insufficient cash prevents buy orders"""
        ai_decision = {
            "decision": "BUY",
            "confidence_level": "HIGH",
            "quantity_percentage": 0.01,
            "reasoning": "Buy signal but insufficient funds"
        }
        
        # Account with very little cash
        account = {
            "cash": "10",  # Only $10
            "buying_power": "20",
            "equity": "10"
        }
        
        position = {"qty": 0.0}
        indicators = {
            "current_price": Decimal("50000"),  # Expensive BTC
            "rsi": 25.0,  # Very oversold
            "macd_histogram": 0.3,
            "atr": Decimal("1000")
        }
        
        api_mock = MagicMock()
        
        result, buy_count, sell_count = process_ai_decision_and_trade(
            api_mock, ai_decision, account, position, indicators,
            buy_cnt=0, sell_cnt=0, dynamic_params={},
            bot_state_data={}, session_stats={}
        )
        
        # Should be rejected due to insufficient funds
        self.assertIn("insufficient", result.get("action", "").lower())
        self.assertEqual(buy_count, 0)  # No buy executed
    
    def test_lot_management_fifo_order(self):
        """Test that lots are managed in FIFO (First In, First Out) order"""
        # Setup multiple lots
        trade_logic.open_lots = [
            {
                "lot_id": "lot_001",
                "buy_price": Decimal("48000"),
                "remaining_qty": Decimal("0.001"),
                "timestamp": "2025-01-15T09:00:00Z"  # Older lot
            },
            {
                "lot_id": "lot_002", 
                "buy_price": Decimal("49000"),
                "remaining_qty": Decimal("0.001"),
                "timestamp": "2025-01-15T10:00:00Z"  # Newer lot
            }
        ]
        
        # When selling, should use older lot first (FIFO)
        sell_qty = Decimal("0.0005")  # Partial sell
        
        # Simulate lot processing
        lots_before = len(trade_logic.open_lots)
        first_lot_qty_before = trade_logic.open_lots[0]["remaining_qty"]
        
        # The older lot should be reduced first
        expected_remaining = first_lot_qty_before - sell_qty
        
        self.assertGreater(
            first_lot_qty_before,
            sell_qty,
            "First lot should have enough quantity for partial sell"
        )
        
        self.assertEqual(
            expected_remaining,
            Decimal("0.0005"),
            "Remaining quantity calculation should be correct"
        )
    
    def test_guardian_veto_system(self):
        """Test that guardian RSI veto system works correctly"""
        if not config.ENABLE_GUARDIAN_VETO:
            self.skipTest("Guardian veto system disabled in config")
        
        # Test overbought condition blocks buy
        ai_decision = {"decision": "BUY", "confidence_level": "HIGH", "quantity_percentage": 0.01}
        
        indicators = {
            "current_price": Decimal("50000"),
            "rsi": config.GUARDIAN_RSI_OVERBOUGHT + 5,  # Above overbought threshold
            "macd_histogram": 0.1,
            "atr": Decimal("1000")
        }
        
        account = {"cash": "5000", "buying_power": "10000", "equity": "5000"}
        position = {"qty": 0.0}
        api_mock = MagicMock()
        
        result, buy_count, sell_count = process_ai_decision_and_trade(
            api_mock, ai_decision, account, position, indicators,
            buy_cnt=0, sell_cnt=0, dynamic_params={},
            bot_state_data={}, session_stats={}
        )
        
        # Guardian should veto the buy
        self.assertIn("veto", result.get("action", "").lower())
        self.assertEqual(buy_count, 0)
        
        # Test oversold condition blocks sell
        trade_logic.open_lots = [{
            "lot_id": "lot_001",
            "buy_price": Decimal("48000"),
            "remaining_qty": Decimal("0.001"),
            "timestamp": "2025-01-15T10:00:00Z"
        }]
        
        ai_decision = {"decision": "SELL", "confidence_level": "HIGH", "quantity_percentage": 1.0}
        
        indicators["rsi"] = config.GUARDIAN_RSI_OVERSOLD - 5  # Below oversold threshold
        position = {"qty": 0.001}
        
        result, buy_count, sell_count = process_ai_decision_and_trade(
            api_mock, ai_decision, account, position, indicators,
            buy_cnt=0, sell_cnt=0, dynamic_params={},
            bot_state_data={}, session_stats={}
        )
        
        # Guardian should veto the sell
        self.assertIn("veto", result.get("action", "").lower())
        self.assertEqual(sell_count, 0)


if __name__ == "__main__":
    unittest.main(verbosity=2)
