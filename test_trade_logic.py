import unittest
from unittest.mock import MagicMock, patch
from types import SimpleNamespace
import logging
from decimal import Decimal
import trade_logic # Import trade_logic directly
import importlib # Import importlib

# Mock the logger to prevent actual log output during tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("TradingBotApp.TradeLogic")
logger.propagate = False # Prevent logs from showing up in test output

def apply_breakout_override(snapshot, ai_decision):
    """
    Helper function to simulate the breakout override logic from trade_logic.py
    for testing purposes.
    """
    signal = snapshot.get('breakout_signal')
    if signal == 'breakout_buy':
        logger.info("Donchian breakout buy detected; overriding AI decision to BUY")
        decision = 'BUY'
    elif signal == 'breakout_sell':
        logger.info("Donchian breakout sell detected; overriding AI decision to SELL")
        decision = 'SELL'
    else:
        # fall back to the AI’s original decision
        decision = ai_decision.decision.upper()
    return decision

class TestTradeLogic(unittest.TestCase):

    def test_donchian_breakout_override(self):
        # Build a fake snapshot at breakout levels
        snap = {'donchian_high': 100.0, 'donchian_low':  90.0, 'breakout_signal': 'breakout_buy'}
        ai_decision = SimpleNamespace(decision='SELL')
        decision = apply_breakout_override(snap, ai_decision)
        self.assertEqual(decision, 'BUY')
        
        snap['breakout_signal'] = 'breakout_sell'
        ai_decision.decision = 'BUY'
        decision = apply_breakout_override(snap, ai_decision)
        self.assertEqual(decision, 'SELL')
        
        snap['breakout_signal'] = None
        ai_decision.decision = 'HOLD'
        decision = apply_breakout_override(snap, ai_decision)
        self.assertEqual(decision, 'HOLD')

    @patch('trade_logic.alpaca_service.wait_for_order_fill')
    @patch('trade_logic.alpaca_service.get_current_price')
    @patch('trade_logic.alpaca_service.get_account_info')
    @patch('trade_logic.alpaca_service.get_positions')
    @patch('trade_logic.calculate_fees')
    @patch('trade_logic.log_trade_to_csv')
    @patch('trade_logic.save_open_lots')
    @patch('trade_logic.logger')
    @patch('trade_logic.send_alert')
    def test_volatility_adjusted_take_profit(self, mock_send_alert, mock_logger, mock_save_open_lots, mock_log_trade_to_csv, mock_calculate_fees, mock_get_positions, mock_get_account_info, mock_get_current_price, mock_wait_for_order_fill):
        # Mock external dependencies
        mock_get_account_info.return_value = {"equity": "10000", "cash": "10000"}
        mock_get_positions.return_value = {}
        mock_get_current_price.return_value = Decimal("100")
        mock_calculate_fees.return_value = Decimal("0.0") # Simplify fee calculation for this test

        # Mock a filled order response
        mock_wait_for_order_fill.return_value = MagicMock(
            status='filled',
            filled_qty=Decimal("1"),
            filled_avg_price=Decimal("100"),
            id="test_order_id"
        )
        
        # Create a mock for the api object and its submit_order method
        mock_api = MagicMock()
        mock_api.submit_order.return_value = MagicMock(id="test_order_id")

        # Set up initial conditions
        acct = {"equity": "10000", "cash": "10000"}
        pos = {}
        buy_cnt = 0
        sell_cnt = 0
        dynamic_params = {}
        bot_state_data = {}
        session_stats = {"win_count": 0, "loss_count": 0}
        trade_cycle_log_buffer = []
        trade_history_buffer = []

        # Test Case: Medium Volatility Regime
        # ATR = 10, k_tp for medium = 3.0 (from config.py)
        # Expected TP: 100 + (10 * 3.0) = 130.0
        ind_medium_vol = {"avg_price": Decimal("100"), "atr": Decimal("10"), "regime": "medium"}
        ai_decision = {"decision": "BUY", "confidence_level": "MEDIUM", "quantity_percentage": 0.01}

        with patch.object(trade_logic.config, 'vol_tp_multipliers', {'low': Decimal("4.0"), 'medium': Decimal("3.0"), 'high': Decimal("2.0")}):
            with patch.object(trade_logic.config, 'FEE_PERCENT', Decimal("0.0025")):
                with patch.object(trade_logic.config, 'DIP_BUY_PROFIT_TARGET_PCT', Decimal("0.03")):
                    with patch.object(trade_logic.config, 'MOMENTUM_BUY_NET_PROFIT_TARGET_PCT', Decimal("0.01")):
                        importlib.reload(trade_logic)
                        trade_logic.open_lots.clear() # Clear any existing lots

                        _, new_buy_cnt, _ = trade_logic.process_ai_decision_and_trade(
                            mock_api, ai_decision, acct, pos, ind_medium_vol, buy_cnt, sell_cnt,
                            dynamic_params, bot_state_data, session_stats, trade_cycle_log_buffer, trade_history_buffer
                        )
                        # The take_profit_price is calculated based on filled_price + tp_offset
                        # filled_price is mocked to 100, atr is 10, k_tp is 3.0
                        # TP = 100 + (10 * 3.0) = 130.0
                        self.assertAlmostEqual(trade_logic.open_lots[0]['take_profit_price'], Decimal("130.0"), places=2)
                        self.assertEqual(new_buy_cnt, 1)

                        # Test Case: High Volatility Regime
                        # ATR = 10, k_tp for high = 2.0 (from config.py)
                        # Expected TP: 100 + (10 * 2.0) = 120.0
                        ind_high_vol = {"avg_price": Decimal("100"), "atr": Decimal("10"), "regime": "high"}
                        trade_logic.open_lots.clear() # Clear previous lots

                        _, new_buy_cnt, _ = trade_logic.process_ai_decision_and_trade(
                            mock_api, ai_decision, acct, pos, ind_high_vol, buy_cnt, sell_cnt,
                            dynamic_params, bot_state_data, session_stats, trade_cycle_log_buffer, trade_history_buffer
                        )
                        self.assertAlmostEqual(trade_logic.open_lots[0]['take_profit_price'], Decimal("120.0"), places=2)
                        self.assertEqual(new_buy_cnt, 1)

                        # Test Case: Low Volatility Regime
                        # ATR = 10, k_tp for low = 4.0 (from config.py)
                        # Expected TP: 100 + (10 * 4.0) = 140.0
                        ind_low_vol = {"avg_price": Decimal("100"), "atr": Decimal("10"), "regime": "low"}
                        trade_logic.open_lots.clear() # Clear previous lots

                        _, new_buy_cnt, _ = trade_logic.process_ai_decision_and_trade(
                            mock_api, ai_decision, acct, pos, ind_low_vol, buy_cnt, sell_cnt,
                            dynamic_params, bot_state_data, session_stats, trade_cycle_log_buffer, trade_history_buffer
                        )
                        self.assertAlmostEqual(trade_logic.open_lots[0]['take_profit_price'], Decimal("140.0"), places=2)
                        self.assertEqual(new_buy_cnt, 1)

if __name__ == '__main__':
    unittest.main()