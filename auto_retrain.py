import subprocess

def run_command(cmd):
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True)
    if result.returncode != 0:
        print(f"Command failed: {cmd}")
        exit(1)

def main():
    # Full path to python.exe inside your virtual environment
    python_exe = r'venv\Scripts\python.exe'

    # Step 1: Download new data
    run_command(f"{python_exe} multi_exchange_downloader.py")

    # Step 2: Prepare labeled data
    run_command(f"{python_exe} prepare_ml_data.py")

    # Step 3: Train model
    run_command(f"{python_exe} train_trade_model.py")

if __name__ == "__main__":
    main()
