#!/usr/bin/env python3
"""
Results comparison dashboard to analyze optimization performance.
Compares old grid search vs new Bayesian optimization results.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import os
from datetime import datetime
import json


class ResultsDashboard:
    """
    Dashboard for comparing and analyzing optimization results.
    """
    
    def __init__(self, results_dir: str = 'optimization/results'):
        """
        Initialize results dashboard.
        
        Args:
            results_dir: Directory containing optimization results
        """
        self.results_dir = results_dir
        self.old_results = None
        self.new_results = None
        
        # Create results directory if it doesn't exist
        os.makedirs(results_dir, exist_ok=True)
    
    def load_old_results(self, file_path: str) -> bool:
        """
        Load old grid search results from CSV file.
        
        Args:
            file_path: Path to old optimization results CSV
            
        Returns:
            True if loaded successfully
        """
        try:
            if os.path.exists(file_path):
                self.old_results = pd.read_csv(file_path)
                print(f"✅ Loaded old results: {len(self.old_results)} parameter combinations")
                return True
            else:
                print(f"⚠️  Old results file not found: {file_path}")
                return False
        except Exception as e:
            print(f"❌ Error loading old results: {e}")
            return False
    
    def load_new_results(self, file_path: str) -> bool:
        """
        Load new Bayesian optimization results from CSV file.
        
        Args:
            file_path: Path to new optimization results CSV
            
        Returns:
            True if loaded successfully
        """
        try:
            if os.path.exists(file_path):
                self.new_results = pd.read_csv(file_path)
                print(f"✅ Loaded new results: {len(self.new_results)} parameter combinations")
                return True
            else:
                print(f"⚠️  New results file not found: {file_path}")
                return False
        except Exception as e:
            print(f"❌ Error loading new results: {e}")
            return False
    
    def compare_performance(self) -> Dict[str, any]:
        """
        Compare performance between old and new optimization methods.
        
        Returns:
            Dictionary with comparison metrics
        """
        if self.old_results is None or self.new_results is None:
            print("❌ Need both old and new results to compare")
            return {}
        
        comparison = {}
        
        # Basic statistics
        old_best = self._get_best_result(self.old_results)
        new_best = self._get_best_result(self.new_results)
        
        comparison['old_best'] = old_best
        comparison['new_best'] = new_best
        
        # Performance metrics
        metrics_to_compare = ['Total_PL', 'Sharpe_Ratio', 'Total_Return', 'Win_Rate', 'Max_Drawdown']
        
        for metric in metrics_to_compare:
            if metric in self.old_results.columns and metric in self.new_results.columns:
                old_max = self.old_results[metric].max()
                new_max = self.new_results[metric].max()
                
                improvement = ((new_max - old_max) / abs(old_max) * 100) if old_max != 0 else 0
                
                comparison[f'{metric}_improvement'] = {
                    'old_best': old_max,
                    'new_best': new_max,
                    'improvement_pct': improvement
                }
        
        # Efficiency comparison
        comparison['efficiency'] = {
            'old_iterations': len(self.old_results),
            'new_iterations': len(self.new_results),
            'speedup': len(self.old_results) / len(self.new_results) if len(self.new_results) > 0 else 0
        }
        
        return comparison
    
    def _get_best_result(self, df: pd.DataFrame) -> Dict:
        """Get the best result from a DataFrame."""
        if 'Sharpe_Ratio' in df.columns:
            best_idx = df['Sharpe_Ratio'].idxmax()
        elif 'Total_Return' in df.columns:
            best_idx = df['Total_Return'].idxmax()
        elif 'Total_PL' in df.columns:
            best_idx = df['Total_PL'].idxmax()
        else:
            best_idx = 0
        
        return df.iloc[best_idx].to_dict()
    
    def print_comparison_summary(self) -> None:
        """Print a formatted comparison summary."""
        comparison = self.compare_performance()
        
        if not comparison:
            return
        
        print("\n" + "="*70)
        print("📊 OPTIMIZATION RESULTS COMPARISON")
        print("="*70)
        
        # Best results comparison
        print(f"\n🏆 BEST RESULTS:")
        old_best = comparison['old_best']
        new_best = comparison['new_best']
        
        print(f"   Old Grid Search Best:")
        if 'Sharpe_Ratio' in old_best:
            print(f"     Sharpe Ratio: {old_best.get('Sharpe_Ratio', 0):.4f}")
        if 'Total_Return' in old_best:
            print(f"     Total Return: {old_best.get('Total_Return', 0):.2f}%")
        if 'Win_Rate' in old_best:
            print(f"     Win Rate: {old_best.get('Win_Rate', 0):.1f}%")
        
        print(f"\n   New Bayesian Optimization Best:")
        if 'Sharpe_Ratio' in new_best:
            print(f"     Sharpe Ratio: {new_best.get('Sharpe_Ratio', 0):.4f}")
        if 'Total_Return' in new_best:
            print(f"     Total Return: {new_best.get('Total_Return', 0):.2f}%")
        if 'Win_Rate' in new_best:
            print(f"     Win Rate: {new_best.get('Win_Rate', 0):.1f}%")
        
        # Improvements
        print(f"\n📈 IMPROVEMENTS:")
        for metric, data in comparison.items():
            if metric.endswith('_improvement'):
                metric_name = metric.replace('_improvement', '').replace('_', ' ')
                improvement = data['improvement_pct']
                
                if improvement > 0:
                    print(f"   ✅ {metric_name}: +{improvement:.1f}% improvement")
                elif improvement < 0:
                    print(f"   ❌ {metric_name}: {improvement:.1f}% decline")
                else:
                    print(f"   ➖ {metric_name}: No change")
        
        # Efficiency
        efficiency = comparison['efficiency']
        print(f"\n⚡ EFFICIENCY:")
        print(f"   Old method: {efficiency['old_iterations']} iterations")
        print(f"   New method: {efficiency['new_iterations']} iterations")
        print(f"   Speedup: {efficiency['speedup']:.1f}x faster")
        
        # Time savings estimate
        if efficiency['speedup'] > 1:
            time_saved_pct = (1 - 1/efficiency['speedup']) * 100
            print(f"   Time saved: ~{time_saved_pct:.0f}%")
    
    def create_performance_plots(self, save_plots: bool = True) -> None:
        """
        Create visualization plots comparing old vs new results.
        
        Args:
            save_plots: Whether to save plots to files
        """
        if self.old_results is None or self.new_results is None:
            print("❌ Need both old and new results to create plots")
            return
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Optimization Results Comparison: Grid Search vs Bayesian Optimization', fontsize=16)
        
        # Plot 1: Performance Distribution
        if 'Sharpe_Ratio' in self.old_results.columns and 'Sharpe_Ratio' in self.new_results.columns:
            axes[0, 0].hist(self.old_results['Sharpe_Ratio'], alpha=0.7, label='Grid Search', bins=20)
            axes[0, 0].hist(self.new_results['Sharpe_Ratio'], alpha=0.7, label='Bayesian Opt', bins=20)
            axes[0, 0].set_title('Sharpe Ratio Distribution')
            axes[0, 0].set_xlabel('Sharpe Ratio')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].legend()
        
        # Plot 2: Return vs Risk
        if all(col in self.old_results.columns for col in ['Total_Return', 'Max_Drawdown']):
            axes[0, 1].scatter(abs(self.old_results['Max_Drawdown']), self.old_results['Total_Return'], 
                             alpha=0.6, label='Grid Search', s=30)
        if all(col in self.new_results.columns for col in ['Total_Return', 'Max_Drawdown']):
            axes[0, 1].scatter(abs(self.new_results['Max_Drawdown']), self.new_results['Total_Return'], 
                             alpha=0.6, label='Bayesian Opt', s=30)
        axes[0, 1].set_title('Return vs Risk')
        axes[0, 1].set_xlabel('Max Drawdown (%)')
        axes[0, 1].set_ylabel('Total Return (%)')
        axes[0, 1].legend()
        
        # Plot 3: Parameter Exploration (if available)
        if 'STOP_PERCENT' in self.old_results.columns:
            axes[1, 0].scatter(self.old_results['STOP_PERCENT'], self.old_results.get('Sharpe_Ratio', 0), 
                             alpha=0.6, label='Grid Search', s=30)
        if 'STOP_PERCENT' in self.new_results.columns:
            axes[1, 0].scatter(self.new_results['STOP_PERCENT'], self.new_results.get('Sharpe_Ratio', 0), 
                             alpha=0.6, label='Bayesian Opt', s=30)
        axes[1, 0].set_title('Stop Loss vs Performance')
        axes[1, 0].set_xlabel('Stop Loss Percentage')
        axes[1, 0].set_ylabel('Sharpe Ratio')
        axes[1, 0].legend()
        
        # Plot 4: Convergence (iterations vs best score found so far)
        if 'Sharpe_Ratio' in self.new_results.columns:
            new_cummax = self.new_results['Sharpe_Ratio'].cummax()
            axes[1, 1].plot(range(len(new_cummax)), new_cummax, 'o-', label='Bayesian Opt', linewidth=2)
        
        if 'Sharpe_Ratio' in self.old_results.columns:
            old_cummax = self.old_results['Sharpe_Ratio'].cummax()
            axes[1, 1].plot(range(len(old_cummax)), old_cummax, 's-', label='Grid Search', linewidth=2)
        
        axes[1, 1].set_title('Optimization Convergence')
        axes[1, 1].set_xlabel('Iteration')
        axes[1, 1].set_ylabel('Best Sharpe Ratio Found')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_plots:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_file = os.path.join(self.results_dir, f'optimization_comparison_{timestamp}.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"📊 Plots saved to: {plot_file}")
        
        plt.show()
    
    def export_comparison_report(self) -> str:
        """
        Export a detailed comparison report to file.
        
        Returns:
            Path to the exported report file
        """
        comparison = self.compare_performance()
        
        if not comparison:
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.results_dir, f'optimization_comparison_report_{timestamp}.json')
        
        # Add metadata
        report = {
            'timestamp': timestamp,
            'comparison_data': comparison,
            'summary': {
                'old_method': 'Grid Search',
                'new_method': 'Bayesian Optimization',
                'old_iterations': len(self.old_results) if self.old_results is not None else 0,
                'new_iterations': len(self.new_results) if self.new_results is not None else 0
            }
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Comparison report exported to: {report_file}")
        return report_file


def create_sample_comparison():
    """Create a sample comparison for testing."""
    dashboard = ResultsDashboard()
    
    # Check if we have actual results to compare
    old_results_path = 'backtesting_suite/optimization_results.csv'
    
    if os.path.exists(old_results_path):
        print("🔍 Found actual optimization results - creating real comparison")
        dashboard.load_old_results(old_results_path)
        
        # Look for new results
        new_results_files = [f for f in os.listdir('.') if f.startswith('intelligent_optimization_results_')]
        if new_results_files:
            newest_file = max(new_results_files)
            dashboard.load_new_results(newest_file)
            dashboard.print_comparison_summary()
        else:
            print("⚠️  No new optimization results found yet")
    else:
        print("⚠️  No old optimization results found")
        print("   Run the original parameter_optimizer.py first to generate baseline results")


if __name__ == "__main__":
    create_sample_comparison()
