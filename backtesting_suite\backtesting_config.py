# START OF FILE config.py

import os
from dotenv import load_dotenv
import logging
import json
from datetime import timedelta
from decimal import Decimal 

# Load environment variables from .env file
load_dotenv()

# --- Logging for config warnings ---
_config_logger = logging.getLogger("config_logger")
if not _config_logger.handlers:
    _ch = logging.StreamHandler()
    _formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    _ch.setFormatter(_formatter)
    _config_logger.addHandler(_ch)
    _config_logger.setLevel(logging.WARNING)

# --- LIVE OR PAPER TRADING ---
IS_LIVE_TRADING = True  # As per your instruction. PROCEED WITH EXTREME CAUTION.

# --- API KEYS / ENDPOINTS ---
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if IS_LIVE_TRADING:
    ALPACA_API_KEY_ID = os.getenv("ALPACA_LIVE_API_KEY_ID")
    ALPACA_SECRET_KEY = os.getenv("ALPACA_LIVE_SECRET_KEY")
    ALPACA_BASE_URL   = "https://api.alpaca.markets"
    _config_logger.warning("############################################################")
    _config_logger.warning("##           LIVE TRADING MODE IS ACTIVE!                 ##")
    _config_logger.warning("##          REAL MONEY WILL BE USED FOR TRADES.           ##")
    _config_logger.warning("##          PROCEED WITH EXTREME CAUTION.                 ##")
    _config_logger.warning("############################################################")
else:
    # These will not be used in live mode, but kept for completeness
    ALPACA_API_KEY_ID = os.getenv("ALPACA_PAPER_API_KEY_ID")
    ALPACA_SECRET_KEY = os.getenv("ALPACA_PAPER_SECRET_KEY")
    ALPACA_BASE_URL   = "https://paper-api.alpaca.markets"
    _config_logger.warning("############################################################")
    _config_logger.warning("##           PAPER TRADING MODE IS ACTIVE!                ##")
    _config_logger.warning("##           NO REAL MONEY WILL BE USED.                  ##")
    _config_logger.warning("############################################################")


if not ALPACA_API_KEY_ID:
    raise ValueError(f"Alpaca API Key ID not found for {'LIVE' if IS_LIVE_TRADING else 'PAPER'} mode.")
if not ALPACA_SECRET_KEY:
    raise ValueError(f"Alpaca Secret Key not found for {'LIVE' if IS_LIVE_TRADING else 'PAPER'} mode.")
if not GEMINI_API_KEY:
    raise ValueError("Gemini API Key not found. Ensure GEMINI_API_KEY is set in your environment.")

# --- SYMBOLS ---
PRIMARY_TRADING_SYMBOL = "BTC/USD"

# --- Higher-timeframe trend filter ---
HIGHER_TIMEFRAME   = "5m"
HIGHER_SMA_PERIOD  = 10

# --- INDICATOR PARAMETERS ---
RSI_PERIOD               = 7
SHORT_SMA_PERIOD         = 5
LONG_SMA_PERIOD          = 10
MACD_FAST_PERIOD         = 12
MACD_SLOW_PERIOD         = 26
MACD_SIGNAL_PERIOD       = 9
ATR_PERIOD               = 14 
# MODIFIED: Calibrated REFERENCE_ATR to a realistic value based on logs
REFERENCE_ATR            = Decimal("40.0") 

HISTORICAL_BARS_TIMEFRAME = "Minute"
HISTORICAL_BARS_COUNT    = max(RSI_PERIOD, SHORT_SMA_PERIOD, LONG_SMA_PERIOD, MACD_SLOW_PERIOD + MACD_SLOW_PERIOD, ATR_PERIOD) + 50
# Ensuring HISTORICAL_BARS_COUNT is large enough for all indicators.

# --- BINANCE DATA SERVICE PARAMETERS ---
MAX_BINANCE_KLINES_TO_STORE = 200

# --- SCHEDULING ---
TRADE_CYCLE_INTERVAL_MINUTES = 3
# --- FIX: Set to 1 to ensure a fresh AI decision on every trade cycle ---
GEMINI_AI_CALL_INTERVAL_CYCLES = 1 

# --- FEE AND PROFIT PARAMETERS ---
PER_TRADE_FEE_PERCENTAGE = Decimal("0.0025")
MIN_PROFIT_THRESHOLD_USD = Decimal("0.01") # Allowing $0.01 net profit after fees

# --- NEW: Profit Targets by Strategy Type ---
# This is for new lots initiated as "Dip Accumulation" buys.
DIP_BUY_PROFIT_TARGET_PCT = Decimal("0.03") # Explicitly 3% for dip buys as per user.
# This is for new lots initiated as "Momentum" buys.
MOMENTUM_BUY_NET_PROFIT_TARGET_PCT = Decimal("0.01") # 1% NET profit after fees.

# --- RISK MANAGEMENT ---
ENABLE_DAILY_DRAWDOWN_PROTECTION = True
MIN_ACCOUNT_EQUITY = Decimal("250.00")
MAX_DAILY_LOSS_PCT = Decimal("0.02") # MODIFIED: Tightened daily draw-down from 10% to 2%
MAX_WEEKLY_LOSS_PCT = Decimal("0.05") # MODIFIED: Tightened weekly loss limit from 15% to 5%
# NEW: The master control for portfolio risk. The bot will not open new positions if the
# total value of open positions exceeds this percentage of the total account equity.
MAX_PORTFOLIO_EXPOSURE_PCT = Decimal("0.85") # 85%

# --- NEW: Guardian Veto System Parameters ---
ENABLE_GUARDIAN_VETO = True                  # Master switch for the Guardian system.
GUARDIAN_RSI_OVERBOUGHT = Decimal("80.0")      # Veto any AI 'BUY' if RSI is above this.
GUARDIAN_RSI_OVERSOLD = Decimal("30.0")        # MODIFIED: Lifted from 20 to 30 to allow earlier exits

# This seems redundant with MAX_DAILY_LOSS_PCT, but keeping for reference if needed
MAX_DAILY_DRAWDOWN_PERCENT = MAX_DAILY_LOSS_PCT 

MAX_TRADE_VALUE_USD              = Decimal("50.01") # MODIFIED: Increased for larger trade amounts
MAX_SCALP_TRADE_VALUE_USD = Decimal("50.01") # MODIFIED: Increased for larger trade amounts

# --- AI-suggested QTY % limits ---
MIN_QTY_PCT_CONFIG = Decimal("0.05") 
MAX_QTY_PCT_CONFIG = Decimal("0.07") 

# --- MINIMUM ORDER QUANTITY & CASH RESERVE ---
CASH_RESERVE_USD    = Decimal("250.00")
MIN_TRADE_QTY_BTC   = Decimal("0.00002")

# --- New constants for dynamic order sizing ---
MIN_ORDER_VALUE_USD_PERCENT = Decimal("0.001")
MIN_ORDER_VALUE_USD_FLOOR   = Decimal("12.00")    # MODIFIED: Set to $12 as per user instruction
MAX_ORDER_VALUE_USD_CAP     = Decimal("0.03")

# --- TRADE EXECUTION THRESHOLDS (newly added/centralized) ---
MIN_SELL_QTY_THRESHOLD = Decimal("0.00000001") 
TRADE_BUFFER_QTY = Decimal("0.0002")

# --- EMERGENCY KILL SWITCH ---
KILL_SWITCH_FILE = "STOP_TRADING.txt"

# --- PROFIT / STOP / HOLD LOGIC ---
PROFIT_TARGET_PCT = Decimal("0.03") # This will now be superseded for new lots by strategy-specific targets
STOP_LOSS_PCT     = Decimal("0.02")
MIN_HOLD_CYCLES   = 2

# --- NEW: Dynamic threshold bounds ---
MIN_PROFIT_TARGET_PCT = Decimal("0.01")
MAX_PROFIT_TARGET_PCT = Decimal("0.06")
MIN_STOP_LOSS_PCT     = Decimal("0.005")
MAX_STOP_LOSS_PCT     = Decimal("0.04")
TRAIL_PROFIT_BUFFER_PCT = Decimal("0.01") # NEW: Centralized constant for trailing stop activation buffer
# NOTE: GRANULAR_TAKE_PROFIT_PCT is used by existing granular TP logic, not for setting new lot targets.
GRANULAR_TAKE_PROFIT_PCT = Decimal("0.01") 

# --- NEW: AI-driven controlled loss exit threshold ---
# MODIFIED: Set to 0.0 to enforce "never sell at a loss".
MAX_AI_LOSS_EXIT_PCT = Decimal("0.0") 

# --- NEW constants for dynamic profit calculation ---
MIN_PROFIT_PCT             = Decimal("0.002")
VOLATILITY_MULTIPLIER      = Decimal("1.0")
PROFIT_STAGGER_FACTORS     = [Decimal("1.0"), Decimal("1.5"), Decimal("2.0")]
MAX_PROFIT_PCT             = Decimal("0.02")

# --- NEW constants for hysteresis/cooldown ---
COOLDOWN_SELL_AFTER_BUY    = timedelta(minutes=2) # MODIFIED: Reduced from 5 to 2 minutes
COOLDOWN_BUY_AFTER_SELL    = timedelta(minutes=5)
MIN_REVERSAL_PCT           = Decimal("0.002")

# --- NEW constants for dust management ---
DUST_THRESHOLD_QTY         = Decimal("0.0001")

# --- NEW constants for lot consolidation ---
CONSOLIDATION_WINDOW_MINUTES = 5

# --- NEW: STALE ORDER MANAGEMENT ---
ENABLE_STALE_ORDER_CANCELLATION = True       # Master switch for this feature.
STALE_ORDER_AGE_HOURS           = 8          # Cancel any order older than this many hours.
STALE_SELL_PRICE_DEVIATION_PCT  = Decimal("0.005") 
STALE_BUY_PRICE_DEVIATION_PCT   = Decimal("0.005") 

# --- Consolidated Risk/Profit Parameters ---
STOP_PERCENT       = STOP_LOSS_PCT
TAKE_PROFIT_SHARES = [Decimal("0.5"), Decimal("0.3"), Decimal("0.2")]
FEE_PERCENT        = PER_TRADE_FEE_PERCENTAGE
RISK_PERCENT       = Decimal("0.01")

TAKE_PROFITS = [Decimal("0.02"), Decimal("0.03"), Decimal("0.05")] # MODIFIED: Raised profit ladder

# --- DYNAMIC RISK ADJUSTMENT BOUNDS ---
MIN_DYNAMIC_RISK_PERCENT = Decimal("0.04")
MAX_DYNAMIC_RISK_PERCENT = Decimal("0.12")

# --- THREE-STRIKE LOSS PAUSE (NEW) ---
LOSS_STREAK_LIMIT = 3
LOSS_PAUSE_SECONDS = 3600 # 1 hour

# ========== FIX: ADD THESE FOR TRADE LOGIC ==========
HARD_MAX_TRADE_VALUE = MAX_TRADE_VALUE_USD
MIN_TRADE_VALUE = MIN_ORDER_VALUE_USD_FLOOR
# =====================================================
