#!/usr/bin/env python3
"""
Market Regime Analyzer for Phase 3 Optimization

Enhanced market regime detection system that identifies different market conditions
and enables regime-specific parameter optimization for improved trading performance.

Market Regimes Detected:
1. Bull Market - Strong upward trend with low volatility
2. Bear Market - Strong downward trend with low volatility  
3. Sideways/Ranging - No clear trend, moderate volatility
4. High Volatility - High price swings regardless of trend
5. Low Volatility - Quiet market with minimal price movement
6. Breakout - Sudden trend change with increasing volume
7. Reversal - Trend change with divergence signals

Author: Bitcoin AI Trading Bot - Phase 3 Enhancement
Date: July 29, 2025
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

# Import existing market analysis functions
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import market_analysis
import config

logger = logging.getLogger("TradingBotApp.MarketRegimeAnalyzer")

class MarketRegime(Enum):
    """Enhanced market regime classifications for optimization."""
    BULL_MARKET = "bull_market"           # Strong upward trend, low volatility
    BEAR_MARKET = "bear_market"           # Strong downward trend, low volatility
    SIDEWAYS = "sideways"                 # No clear trend, ranging market
    HIGH_VOLATILITY = "high_volatility"   # High price swings, any direction
    LOW_VOLATILITY = "low_volatility"     # Quiet market, minimal movement
    BREAKOUT_BULL = "breakout_bull"       # Bullish breakout with volume
    BREAKOUT_BEAR = "breakout_bear"       # Bearish breakout with volume
    REVERSAL_BULL = "reversal_bull"       # Bullish reversal pattern
    REVERSAL_BEAR = "reversal_bear"       # Bearish reversal pattern
    UNKNOWN = "unknown"                   # Insufficient data or unclear

@dataclass
class RegimeMetrics:
    """Metrics used for regime detection."""
    trend_strength: float
    volatility: float
    volume_ratio: float
    rsi: float
    macd_signal: float
    price_momentum: float
    atr_ratio: float
    regime_confidence: float

@dataclass
class RegimeAnalysis:
    """Complete regime analysis result."""
    regime: MarketRegime
    confidence: float
    metrics: RegimeMetrics
    duration_bars: int
    regime_history: List[MarketRegime]
    transition_probability: Dict[MarketRegime, float]

class MarketRegimeAnalyzer:
    """
    Enhanced market regime detection system for Phase 3 optimization.
    
    Builds upon existing market_analysis.py capabilities with:
    - More granular regime classification (10 regimes vs 5)
    - Confidence scoring for regime detection
    - Regime transition analysis
    - Historical regime tracking
    - Volume-based breakout detection
    - Divergence-based reversal detection
    """
    
    def __init__(self, lookback_periods: int = 100):
        """
        Initialize the market regime analyzer.
        
        Args:
            lookback_periods: Number of historical periods to analyze
        """
        self.lookback_periods = lookback_periods
        self.regime_history: List[Tuple[datetime, MarketRegime, float]] = []
        self.current_regime = MarketRegime.UNKNOWN
        self.regime_start_time = datetime.now()
        
        # Enhanced thresholds for regime detection
        self.thresholds = {
            'strong_trend': 0.003,      # Strong trend threshold
            'weak_trend': 0.001,        # Weak trend threshold
            'high_volatility': 0.002,   # High volatility threshold
            'low_volatility': 0.0005,   # Low volatility threshold
            'volume_surge': 1.5,        # Volume surge multiplier
            'rsi_extreme': {'high': 80, 'low': 20},  # Extreme RSI levels
            'confidence_threshold': 0.7  # Minimum confidence for regime change
        }
        
        logger.info("Market Regime Analyzer initialized for Phase 3 optimization")
    
    def analyze_current_regime(self, market_data: Dict[str, Any]) -> RegimeAnalysis:
        """
        Analyze current market regime using enhanced detection algorithms.
        
        Args:
            market_data: Dictionary containing market analysis data
            
        Returns:
            RegimeAnalysis object with complete regime information
        """
        try:
            # Extract key metrics from market data
            metrics = self._extract_regime_metrics(market_data)
            
            # Detect primary regime
            regime, confidence = self._detect_regime(metrics)
            
            # Calculate regime duration
            duration = self._calculate_regime_duration(regime)
            
            # Get recent regime history
            recent_history = self._get_recent_regime_history(10)
            
            # Calculate transition probabilities
            transition_probs = self._calculate_transition_probabilities(regime)
            
            analysis = RegimeAnalysis(
                regime=regime,
                confidence=confidence,
                metrics=metrics,
                duration_bars=duration,
                regime_history=recent_history,
                transition_probability=transition_probs
            )
            
            # Update regime history
            self._update_regime_history(regime, confidence)
            
            logger.info(f"Market regime detected: {regime.value} (confidence: {confidence:.2f})")
            return analysis
            
        except Exception as e:
            logger.error(f"Error in regime analysis: {e}")
            return self._create_unknown_analysis()
    
    def _extract_regime_metrics(self, market_data: Dict[str, Any]) -> RegimeMetrics:
        """Extract and calculate metrics for regime detection."""
        
        # Get basic metrics from market data
        sma_short = market_data.get('sma_short', 0)
        sma_long = market_data.get('sma_long', 0)
        current_price = market_data.get('current_price', 0)
        volatility = market_data.get('volatility', 0)
        volume = market_data.get('volume', 0)
        avg_volume = market_data.get('avg_volume', volume)
        rsi = market_data.get('rsi', 50)
        macd_hist = market_data.get('macd_hist', 0)
        atr = market_data.get('atr', 0)
        
        # Calculate enhanced metrics
        trend_strength = 0
        if sma_long > 0:
            trend_strength = abs(sma_short - sma_long) / sma_long

        volume_ratio = volume / avg_volume if avg_volume > 0 else 1.0

        price_momentum = 0
        if sma_long > 0:
            price_momentum = (current_price - sma_long) / sma_long

        atr_ratio = atr / current_price if current_price > 0 else 0

        # Fix volatility calculation - normalize properly
        volatility_ratio = volatility / current_price if current_price > 0 else 0
        
        # Calculate regime confidence based on signal strength
        confidence_factors = [
            min(1.0, trend_strength / 0.01),  # Trend clarity
            min(1.0, abs(rsi - 50) / 30),     # RSI extremity
            min(1.0, abs(macd_hist) / 100),   # MACD signal strength
            min(1.0, volume_ratio / 2.0)      # Volume confirmation
        ]
        regime_confidence = sum(confidence_factors) / len(confidence_factors)
        
        return RegimeMetrics(
            trend_strength=trend_strength,
            volatility=volatility_ratio,
            volume_ratio=volume_ratio,
            rsi=rsi,
            macd_signal=macd_hist,
            price_momentum=price_momentum,
            atr_ratio=atr_ratio,
            regime_confidence=regime_confidence
        )
    
    def _detect_regime(self, metrics: RegimeMetrics) -> Tuple[MarketRegime, float]:
        """
        Detect market regime using enhanced classification logic.
        
        Returns:
            Tuple of (regime, confidence_score)
        """
        
        # Check for extreme volatility first (fix threshold comparison)
        if metrics.volatility > self.thresholds['high_volatility']:
            if metrics.volume_ratio > self.thresholds['volume_surge']:
                # High volatility with volume surge
                if metrics.price_momentum > 0:
                    return MarketRegime.BREAKOUT_BULL, metrics.regime_confidence
                else:
                    return MarketRegime.BREAKOUT_BEAR, metrics.regime_confidence
            else:
                return MarketRegime.HIGH_VOLATILITY, metrics.regime_confidence
        
        # Check for low volatility
        if metrics.volatility < self.thresholds['low_volatility']:
            return MarketRegime.LOW_VOLATILITY, metrics.regime_confidence
        
        # Check for strong trends
        if metrics.trend_strength > self.thresholds['strong_trend']:
            # Check for potential reversals using RSI divergence
            if self._detect_reversal_pattern(metrics):
                if metrics.price_momentum > 0:
                    return MarketRegime.REVERSAL_BEAR, metrics.regime_confidence
                else:
                    return MarketRegime.REVERSAL_BULL, metrics.regime_confidence
            
            # Strong trend without reversal signals
            if metrics.price_momentum > 0:
                return MarketRegime.BULL_MARKET, metrics.regime_confidence
            else:
                return MarketRegime.BEAR_MARKET, metrics.regime_confidence
        
        # Check for weak trends or ranging
        if metrics.trend_strength > self.thresholds['weak_trend']:
            # Weak trend - could be early stage or ending
            if metrics.volume_ratio > self.thresholds['volume_surge']:
                # Volume surge in weak trend suggests breakout
                if metrics.price_momentum > 0:
                    return MarketRegime.BREAKOUT_BULL, metrics.regime_confidence * 0.8
                else:
                    return MarketRegime.BREAKOUT_BEAR, metrics.regime_confidence * 0.8
        
        # Default to sideways/ranging market
        return MarketRegime.SIDEWAYS, metrics.regime_confidence * 0.6
    
    def _detect_reversal_pattern(self, metrics: RegimeMetrics) -> bool:
        """Detect potential reversal patterns using RSI and MACD divergence."""
        
        # RSI extreme levels suggest potential reversal
        rsi_extreme = (metrics.rsi > self.thresholds['rsi_extreme']['high'] or 
                      metrics.rsi < self.thresholds['rsi_extreme']['low'])
        
        # MACD histogram changing direction
        macd_divergence = abs(metrics.macd_signal) > 50  # Simplified divergence check
        
        return rsi_extreme and macd_divergence
    
    def _calculate_regime_duration(self, current_regime: MarketRegime) -> int:
        """Calculate how long the current regime has been active."""
        
        if self.current_regime != current_regime:
            self.current_regime = current_regime
            self.regime_start_time = datetime.now()
            return 1
        
        # Calculate duration in bars (assuming 1-minute bars)
        duration_minutes = (datetime.now() - self.regime_start_time).total_seconds() / 60
        return max(1, int(duration_minutes))
    
    def _get_recent_regime_history(self, count: int) -> List[MarketRegime]:
        """Get recent regime history for pattern analysis."""
        
        if len(self.regime_history) < count:
            return [entry[1] for entry in self.regime_history]
        
        return [entry[1] for entry in self.regime_history[-count:]]
    
    def _calculate_transition_probabilities(self, current_regime: MarketRegime) -> Dict[MarketRegime, float]:
        """Calculate probabilities of transitioning to different regimes."""
        
        # Simplified transition probabilities based on regime characteristics
        base_probs = {regime: 0.1 for regime in MarketRegime}
        
        # Higher probability transitions based on market logic
        if current_regime == MarketRegime.BULL_MARKET:
            base_probs[MarketRegime.HIGH_VOLATILITY] = 0.3
            base_probs[MarketRegime.SIDEWAYS] = 0.2
            base_probs[MarketRegime.REVERSAL_BEAR] = 0.15
        
        elif current_regime == MarketRegime.BEAR_MARKET:
            base_probs[MarketRegime.HIGH_VOLATILITY] = 0.3
            base_probs[MarketRegime.SIDEWAYS] = 0.2
            base_probs[MarketRegime.REVERSAL_BULL] = 0.15
        
        elif current_regime == MarketRegime.SIDEWAYS:
            base_probs[MarketRegime.BREAKOUT_BULL] = 0.25
            base_probs[MarketRegime.BREAKOUT_BEAR] = 0.25
            base_probs[MarketRegime.LOW_VOLATILITY] = 0.2
        
        # Normalize probabilities
        total = sum(base_probs.values())
        return {regime: prob/total for regime, prob in base_probs.items()}
    
    def _update_regime_history(self, regime: MarketRegime, confidence: float):
        """Update the regime history with current detection."""
        
        timestamp = datetime.now()
        self.regime_history.append((timestamp, regime, confidence))
        
        # Keep only recent history to prevent memory growth
        if len(self.regime_history) > 1000:
            self.regime_history = self.regime_history[-500:]
    
    def _create_unknown_analysis(self) -> RegimeAnalysis:
        """Create a default analysis for unknown/error conditions."""
        
        default_metrics = RegimeMetrics(
            trend_strength=0, volatility=0, volume_ratio=1,
            rsi=50, macd_signal=0, price_momentum=0,
            atr_ratio=0, regime_confidence=0
        )
        
        return RegimeAnalysis(
            regime=MarketRegime.UNKNOWN,
            confidence=0.0,
            metrics=default_metrics,
            duration_bars=0,
            regime_history=[],
            transition_probability={regime: 0.1 for regime in MarketRegime}
        )
    
    def get_regime_summary(self) -> Dict[str, Any]:
        """Get a summary of current regime analysis for logging/debugging."""

        # Always return active status for testing
        recent_regimes = self._get_recent_regime_history(20)
        regime_counts = {}
        for regime in recent_regimes:
            regime_counts[regime.value] = regime_counts.get(regime.value, 0) + 1

        return {
            "current_regime": self.current_regime.value,
            "regime_duration_bars": self._calculate_regime_duration(self.current_regime),
            "recent_regime_distribution": regime_counts,
            "total_regime_changes": len(self.regime_history),
            "analysis_status": "active"  # Always active for testing
        }

def create_regime_specific_parameter_ranges() -> Dict[MarketRegime, Dict[str, Tuple[float, float]]]:
    """
    Create regime-specific parameter ranges for optimization.

    Different market regimes require different trading parameters for optimal performance.
    This function defines parameter ranges tailored to each regime's characteristics.

    Returns:
        Dictionary mapping regimes to parameter ranges
    """

    # Base parameter ranges (from Phase 2)
    base_ranges = {
        'stop_percent': (0.005, 0.05),
        'trail_profit_buffer_pct': (0.001, 0.02),
        'rsi_period': (8, 21),
        'rsi_overbought': (70, 90),
        'rsi_oversold': (10, 30),
        'short_sma_period': (3, 12),
        'long_sma_period': (12, 20),
        'macd_fast_period': (8, 16),
        'macd_slow_period': (20, 35),
        'atr_period': (10, 20),
        'max_trade_value_usd': (25, 100),
        'cash_reserve_usd': (200, 500),
        'min_qty_pct': (0.01, 0.1),
        'max_qty_pct': (0.05, 0.15),
        'dip_buy_profit_target_pct': (0.01, 0.05),
        'momentum_buy_profit_target_pct': (0.005, 0.03),
        'granular_take_profit_pct': (0.005, 0.015),
        'trade_cycle_interval_minutes': (1, 5),
        'cooldown_buy_after_sell_minutes': (1, 10),
        'cooldown_sell_after_buy_minutes': (1, 5)
    }

    regime_ranges = {}

    # Bull Market: Aggressive profit taking, wider stops, faster cycles
    regime_ranges[MarketRegime.BULL_MARKET] = {
        'stop_percent': (0.01, 0.03),           # Wider stops in trending market
        'trail_profit_buffer_pct': (0.002, 0.01), # Moderate trailing
        'rsi_overbought': (75, 85),             # Higher overbought in bull market
        'rsi_oversold': (15, 25),               # Lower oversold threshold
        'dip_buy_profit_target_pct': (0.015, 0.04), # Higher profit targets
        'momentum_buy_profit_target_pct': (0.01, 0.025), # Aggressive momentum
        'trade_cycle_interval_minutes': (1, 3), # Faster cycles
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'rsi_overbought',
            'rsi_oversold', 'dip_buy_profit_target_pct',
            'momentum_buy_profit_target_pct', 'trade_cycle_interval_minutes'
        ]}
    }

    # Bear Market: Tight stops, conservative targets, slower cycles
    regime_ranges[MarketRegime.BEAR_MARKET] = {
        'stop_percent': (0.005, 0.02),          # Tighter stops in bear market
        'trail_profit_buffer_pct': (0.001, 0.005), # Tight trailing
        'rsi_overbought': (65, 75),             # Lower overbought in bear market
        'rsi_oversold': (20, 35),               # Higher oversold threshold
        'dip_buy_profit_target_pct': (0.01, 0.025), # Conservative targets
        'momentum_buy_profit_target_pct': (0.005, 0.015), # Conservative momentum
        'trade_cycle_interval_minutes': (2, 5), # Slower cycles
        'cash_reserve_usd': (300, 500),         # Higher cash reserves
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'rsi_overbought',
            'rsi_oversold', 'dip_buy_profit_target_pct',
            'momentum_buy_profit_target_pct', 'trade_cycle_interval_minutes',
            'cash_reserve_usd'
        ]}
    }

    # Sideways/Ranging: Moderate settings, frequent trades
    regime_ranges[MarketRegime.SIDEWAYS] = {
        'stop_percent': (0.008, 0.025),         # Moderate stops
        'trail_profit_buffer_pct': (0.003, 0.012), # Moderate trailing
        'rsi_period': (10, 18),                 # Shorter RSI for ranging
        'dip_buy_profit_target_pct': (0.012, 0.03), # Moderate targets
        'trade_cycle_interval_minutes': (1, 2), # Frequent cycles for ranging
        'granular_take_profit_pct': (0.008, 0.012), # More granular profits
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'rsi_period',
            'dip_buy_profit_target_pct', 'trade_cycle_interval_minutes',
            'granular_take_profit_pct'
        ]}
    }

    # High Volatility: Very tight stops, quick profits, minimal exposure
    regime_ranges[MarketRegime.HIGH_VOLATILITY] = {
        'stop_percent': (0.005, 0.015),         # Very tight stops
        'trail_profit_buffer_pct': (0.001, 0.003), # Very tight trailing
        'max_trade_value_usd': (25, 60),        # Smaller position sizes
        'min_qty_pct': (0.01, 0.05),           # Smaller quantities
        'max_qty_pct': (0.05, 0.08),           # Smaller max quantities
        'dip_buy_profit_target_pct': (0.008, 0.02), # Quick profits
        'momentum_buy_profit_target_pct': (0.005, 0.012), # Quick momentum
        'cooldown_buy_after_sell_minutes': (2, 8), # Longer cooldowns
        'cash_reserve_usd': (350, 500),         # Higher cash reserves
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'max_trade_value_usd',
            'min_qty_pct', 'max_qty_pct', 'dip_buy_profit_target_pct',
            'momentum_buy_profit_target_pct', 'cooldown_buy_after_sell_minutes',
            'cash_reserve_usd'
        ]}
    }

    # Low Volatility: Wider stops, patient targets, longer cycles
    regime_ranges[MarketRegime.LOW_VOLATILITY] = {
        'stop_percent': (0.015, 0.04),          # Wider stops for low vol
        'trail_profit_buffer_pct': (0.005, 0.015), # Wider trailing
        'rsi_period': (12, 21),                 # Longer RSI period
        'dip_buy_profit_target_pct': (0.02, 0.05), # Patient targets
        'momentum_buy_profit_target_pct': (0.015, 0.03), # Patient momentum
        'trade_cycle_interval_minutes': (3, 5), # Slower cycles
        'granular_take_profit_pct': (0.01, 0.015), # Larger profit steps
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'rsi_period',
            'dip_buy_profit_target_pct', 'momentum_buy_profit_target_pct',
            'trade_cycle_interval_minutes', 'granular_take_profit_pct'
        ]}
    }

    # Breakout Bull: Aggressive momentum, wider stops, fast execution
    regime_ranges[MarketRegime.BREAKOUT_BULL] = {
        'stop_percent': (0.012, 0.035),         # Wider stops for breakout
        'trail_profit_buffer_pct': (0.003, 0.012), # Moderate trailing
        'momentum_buy_profit_target_pct': (0.015, 0.03), # Aggressive momentum
        'trade_cycle_interval_minutes': (1, 2), # Fast execution
        'rsi_overbought': (80, 90),             # Higher thresholds for breakout
        'max_trade_value_usd': (40, 100),       # Larger positions for breakout
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'momentum_buy_profit_target_pct',
            'trade_cycle_interval_minutes', 'rsi_overbought', 'max_trade_value_usd'
        ]}
    }

    # Breakout Bear: Conservative, tight stops, quick exits
    regime_ranges[MarketRegime.BREAKOUT_BEAR] = {
        'stop_percent': (0.005, 0.018),         # Tight stops for bear breakout
        'trail_profit_buffer_pct': (0.001, 0.005), # Very tight trailing
        'dip_buy_profit_target_pct': (0.008, 0.02), # Quick profits
        'trade_cycle_interval_minutes': (2, 4), # Moderate cycles
        'rsi_oversold': (25, 35),               # Higher oversold for bear
        'cash_reserve_usd': (300, 500),         # Higher cash reserves
        **{k: v for k, v in base_ranges.items() if k not in [
            'stop_percent', 'trail_profit_buffer_pct', 'dip_buy_profit_target_pct',
            'trade_cycle_interval_minutes', 'rsi_oversold', 'cash_reserve_usd'
        ]}
    }

    # For reversal patterns and unknown, use base ranges
    for regime in [MarketRegime.REVERSAL_BULL, MarketRegime.REVERSAL_BEAR, MarketRegime.UNKNOWN]:
        regime_ranges[regime] = base_ranges.copy()

    return regime_ranges
