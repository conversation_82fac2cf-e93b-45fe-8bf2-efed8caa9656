# check_data.py
import pandas as pd
import os
import sys

file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'rsi_labeled_data.csv')
print(f'Checking file: {file_path}')

try:
    df = pd.read_csv(file_path)
    print(f'Data loaded successfully. Shape: {df.shape}')
    print('First 5 rows:\n', df.head())
except FileNotFoundError:
    print(f'Error: File not found at {file_path}. Make sure generate_rsi_labels.py has created it.')
    sys.exit(1)
except pd.errors.ParserError as e:
    print(f'Error loading data (ParserError - potential corruption or malformation): {e}')
    sys.exit(1)
except Exception as e:
    print(f'An unexpected error occurred while loading data: {e}')
    sys.exit(1)