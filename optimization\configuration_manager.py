#!/usr/bin/env python3
"""
Configuration Manager - Phase 4 Implementation

Safely manages configuration updates for the live trading bot with comprehensive
backup, validation, and rollback capabilities.

Key Features:
- Safe configuration file updates with atomic operations
- Comprehensive backup and versioning system
- Parameter validation and safety checks
- Automatic rollback on errors
- Integration with existing dynamic_config.json system

Author: Bitcoin AI Trading Bot - Phase 4 Configuration Management
Date: July 29, 2025
"""

import os
import json
import shutil
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import tempfile
import hashlib

logger = logging.getLogger("TradingBotApp.ConfigManager")

class ConfigurationManager:
    """
    Safe configuration management system for live trading bot.
    
    Handles updates to both static config.py and dynamic_config.json
    with comprehensive safety measures.
    """
    
    def __init__(self, base_dir: str = None):
        """Initialize the configuration manager."""
        
        self.base_dir = base_dir or os.path.dirname(os.path.dirname(__file__))
        self.config_file = os.path.join(self.base_dir, "config.py")
        self.dynamic_config_file = os.path.join(self.base_dir, "dynamic_config.json")
        self.backup_dir = os.path.join(self.base_dir, "config_backups")
        
        # Safety settings
        self.max_backups = 50
        self.validation_enabled = True
        self.atomic_updates = True
        
        # Parameter safety limits
        self.safety_limits = {
            'risk_percent': (0.001, 0.1),
            'stop_percent': (0.005, 0.05),
            'profit_target_pct': (0.005, 0.1),
            'max_trade_value_usd': (10, 500),
            'cash_reserve_usd': (50, 1000),
            'rsi_period': (5, 30),
            'sma_period': (3, 50),
            'macd_fast_period': (5, 20),
            'macd_slow_period': (15, 50),
            'atr_period': (5, 30)
        }
        
        # Initialize backup directory
        self._ensure_backup_directory()
        
        logger.info("Configuration Manager initialized")
    
    def _ensure_backup_directory(self):
        """Ensure backup directory exists."""
        
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            logger.debug(f"Backup directory ready: {self.backup_dir}")
        except Exception as e:
            logger.error(f"Failed to create backup directory: {e}")
            raise
    
    def update_dynamic_config(self, new_parameters: Dict[str, Any], 
                            source: str = "Phase4", 
                            validate: bool = True) -> bool:
        """
        Safely update dynamic_config.json with new parameters.
        
        Args:
            new_parameters: Dictionary of parameters to update
            source: Source of the update for logging
            validate: Whether to validate parameters
            
        Returns:
            True if update successful, False otherwise
        """
        
        try:
            logger.info(f"Updating dynamic config from {source} with {len(new_parameters)} parameters")
            
            # Load current configuration
            current_config = self._load_dynamic_config()
            
            # Create backup
            backup_path = self._create_backup(self.dynamic_config_file, "dynamic_config")
            
            # Validate new parameters if enabled
            if validate:
                validated_params = self._validate_parameters(new_parameters)
                if not validated_params:
                    logger.error("Parameter validation failed, aborting update")
                    return False
                new_parameters = validated_params
            
            # Merge with current config
            updated_config = current_config.copy()
            updated_config.update(new_parameters)
            updated_config['last_update'] = datetime.now().isoformat()
            updated_config['update_source'] = source
            
            # Atomic update
            if self.atomic_updates:
                success = self._atomic_json_update(self.dynamic_config_file, updated_config)
            else:
                success = self._direct_json_update(self.dynamic_config_file, updated_config)
            
            if success:
                logger.info(f"✅ Dynamic config updated successfully from {source}")
                self._log_parameter_update(new_parameters, source, "dynamic_config")
                return True
            else:
                logger.error("❌ Dynamic config update failed")
                return False
                
        except Exception as e:
            logger.error(f"Error updating dynamic config: {e}")
            return False
    
    def update_static_config_parameters(self, parameter_updates: Dict[str, Any], 
                                      source: str = "Phase4") -> bool:
        """
        Update specific parameters in config.py file.
        
        This is more complex as it involves modifying Python source code.
        Only updates specific parameter values, not structure.
        
        Args:
            parameter_updates: Dictionary of parameter name -> value updates
            source: Source of the update for logging
            
        Returns:
            True if update successful, False otherwise
        """
        
        try:
            logger.info(f"Updating static config from {source} with {len(parameter_updates)} parameters")
            
            # Create backup
            backup_path = self._create_backup(self.config_file, "config_py")
            
            # Read current config file
            with open(self.config_file, 'r') as f:
                config_lines = f.readlines()
            
            # Update specific parameters
            updated_lines = self._update_config_lines(config_lines, parameter_updates)
            
            # Atomic update
            if self.atomic_updates:
                success = self._atomic_file_update(self.config_file, updated_lines)
            else:
                success = self._direct_file_update(self.config_file, updated_lines)
            
            if success:
                logger.info(f"✅ Static config updated successfully from {source}")
                self._log_parameter_update(parameter_updates, source, "config_py")
                return True
            else:
                logger.error("❌ Static config update failed")
                return False
                
        except Exception as e:
            logger.error(f"Error updating static config: {e}")
            return False
    
    def _load_dynamic_config(self) -> Dict[str, Any]:
        """Load current dynamic configuration."""
        
        try:
            if os.path.exists(self.dynamic_config_file):
                with open(self.dynamic_config_file, 'r') as f:
                    return json.load(f)
            else:
                logger.warning("Dynamic config file not found, creating new one")
                return {}
        except Exception as e:
            logger.error(f"Error loading dynamic config: {e}")
            return {}
    
    def _validate_parameters(self, parameters: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate parameters against safety limits."""
        
        try:
            validated = {}
            
            for param_name, value in parameters.items():
                # Convert to appropriate type
                if isinstance(value, str):
                    try:
                        value = float(value)
                    except ValueError:
                        # Keep as string if not numeric
                        validated[param_name] = value
                        continue
                
                # Check safety limits
                if param_name in self.safety_limits:
                    min_val, max_val = self.safety_limits[param_name]
                    if value < min_val or value > max_val:
                        logger.warning(f"Parameter {param_name}={value} outside safe range [{min_val}, {max_val}], clamping")
                        value = max(min_val, min(max_val, value))
                
                validated[param_name] = value
            
            return validated
            
        except Exception as e:
            logger.error(f"Error validating parameters: {e}")
            return None
    
    def _create_backup(self, file_path: str, backup_type: str) -> str:
        """Create a timestamped backup of a file."""
        
        try:
            if not os.path.exists(file_path):
                logger.warning(f"File {file_path} does not exist, skipping backup")
                return ""
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{backup_type}_{timestamp}.backup"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            shutil.copy2(file_path, backup_path)
            logger.debug(f"Created backup: {backup_path}")
            
            # Clean old backups
            self._cleanup_old_backups(backup_type)
            
            return backup_path
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return ""
    
    def _cleanup_old_backups(self, backup_type: str):
        """Remove old backup files to prevent disk space issues."""
        
        try:
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(backup_type) and filename.endswith('.backup'):
                    file_path = os.path.join(self.backup_dir, filename)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove excess backups
            if len(backup_files) > self.max_backups:
                for file_path, _ in backup_files[self.max_backups:]:
                    os.remove(file_path)
                    logger.debug(f"Removed old backup: {file_path}")
                    
        except Exception as e:
            logger.error(f"Error cleaning up backups: {e}")
    
    def _atomic_json_update(self, file_path: str, data: Dict[str, Any]) -> bool:
        """Atomically update a JSON file."""
        
        try:
            # Write to temporary file first
            temp_file = file_path + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            # Atomic move
            shutil.move(temp_file, file_path)
            return True
            
        except Exception as e:
            logger.error(f"Error in atomic JSON update: {e}")
            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                os.remove(temp_file)
            return False
    
    def _direct_json_update(self, file_path: str, data: Dict[str, Any]) -> bool:
        """Directly update a JSON file."""
        
        try:
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            return True
            
        except Exception as e:
            logger.error(f"Error in direct JSON update: {e}")
            return False
    
    def _update_config_lines(self, config_lines: List[str], 
                           parameter_updates: Dict[str, Any]) -> List[str]:
        """Update specific parameter lines in config.py content."""
        
        updated_lines = []
        
        for line in config_lines:
            updated_line = line
            
            # Check if this line contains a parameter we want to update
            for param_name, new_value in parameter_updates.items():
                if line.strip().startswith(f"{param_name} ="):
                    # Format the new value appropriately
                    if isinstance(new_value, str):
                        formatted_value = f'"{new_value}"'
                    elif isinstance(new_value, (int, float)):
                        formatted_value = f'Decimal("{new_value}")'
                    else:
                        formatted_value = str(new_value)
                    
                    # Create the new line
                    comment_part = ""
                    if "#" in line:
                        comment_part = " " + line.split("#", 1)[1]
                    
                    updated_line = f"{param_name} = {formatted_value}{comment_part}"
                    logger.debug(f"Updated parameter: {param_name} = {new_value}")
                    break
            
            updated_lines.append(updated_line)
        
        return updated_lines
    
    def _atomic_file_update(self, file_path: str, lines: List[str]) -> bool:
        """Atomically update a text file."""
        
        try:
            # Write to temporary file first
            temp_file = file_path + '.tmp'
            with open(temp_file, 'w') as f:
                f.writelines(lines)
            
            # Atomic move
            shutil.move(temp_file, file_path)
            return True
            
        except Exception as e:
            logger.error(f"Error in atomic file update: {e}")
            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                os.remove(temp_file)
            return False
    
    def _direct_file_update(self, file_path: str, lines: List[str]) -> bool:
        """Directly update a text file."""
        
        try:
            with open(file_path, 'w') as f:
                f.writelines(lines)
            return True
            
        except Exception as e:
            logger.error(f"Error in direct file update: {e}")
            return False
    
    def _log_parameter_update(self, parameters: Dict[str, Any], 
                            source: str, config_type: str):
        """Log parameter updates for audit trail."""
        
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'config_type': config_type,
                'parameters': parameters,
                'parameter_count': len(parameters)
            }
            
            logger.info(f"Parameter update logged: {source} -> {config_type} ({len(parameters)} params)")
            
        except Exception as e:
            logger.error(f"Error logging parameter update: {e}")
    
    def rollback_to_backup(self, backup_path: str) -> bool:
        """Rollback configuration to a specific backup."""
        
        try:
            if not os.path.exists(backup_path):
                logger.error(f"Backup file not found: {backup_path}")
                return False
            
            # Determine target file based on backup name
            backup_name = os.path.basename(backup_path)
            if backup_name.startswith("dynamic_config"):
                target_file = self.dynamic_config_file
            elif backup_name.startswith("config_py"):
                target_file = self.config_file
            else:
                logger.error(f"Unknown backup type: {backup_name}")
                return False
            
            # Create backup of current state before rollback
            self._create_backup(target_file, f"pre_rollback_{int(datetime.now().timestamp())}")
            
            # Perform rollback
            shutil.copy2(backup_path, target_file)
            logger.info(f"✅ Rollback successful: {backup_path} -> {target_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error during rollback: {e}")
            return False
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """Get list of available backups."""
        
        try:
            backups = []
            
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.backup'):
                    file_path = os.path.join(self.backup_dir, filename)
                    stat = os.stat(file_path)
                    
                    backups.append({
                        'filename': filename,
                        'path': file_path,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'type': filename.split('_')[0]
                    })
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created'], reverse=True)
            return backups
            
        except Exception as e:
            logger.error(f"Error getting backup list: {e}")
            return []
    
    def get_current_config_status(self) -> Dict[str, Any]:
        """Get current configuration status."""
        
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'config_file_exists': os.path.exists(self.config_file),
                'dynamic_config_exists': os.path.exists(self.dynamic_config_file),
                'backup_count': len([f for f in os.listdir(self.backup_dir) if f.endswith('.backup')]),
                'backup_dir': self.backup_dir
            }
            
            # Add dynamic config info
            if status['dynamic_config_exists']:
                dynamic_config = self._load_dynamic_config()
                status['dynamic_config'] = {
                    'last_update': dynamic_config.get('last_update', 'unknown'),
                    'update_source': dynamic_config.get('update_source', 'unknown'),
                    'parameter_count': len([k for k in dynamic_config.keys() if not k.startswith('last_') and k != 'update_source'])
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting config status: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
