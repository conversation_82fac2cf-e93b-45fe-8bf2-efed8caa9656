import requests
import config
from datetime import datetime, timezone, timedelta, date
import logging
import math
import pandas as pd
import os
import threading
import alpaca_service
from decimal import Decimal
from typing import Any, Dict

import binance_data_service

logger = logging.getLogger("TradingBotApp")

_historical_klines_df: pd.DataFrame | None = None
_historical_klines_lock = threading.Lock()
_INITIAL_HISTORICAL_LOAD_LIMIT = 5000

def _load_initial_historical_klines():
    global _historical_klines_df
    logger.debug("Attempting to load initial historical klines.")
    if _historical_klines_df is not None and not _historical_klines_df.empty:
        logger.debug("Initial historical klines already loaded.")
        return

    if not os.path.exists(config.HISTORICAL_DATA_FILE):
        logger.warning(f"Historical data file '{config.HISTORICAL_DATA_FILE}' not found. Market analysis will rely solely on live data after sufficient collection, which may take time.")
        _historical_klines_df = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']).set_index('timestamp')
        return

    logger.info(f"Loading initial historical klines from '{config.HISTORICAL_DATA_FILE}' (last {_INITIAL_HISTORICAL_LOAD_LIMIT} bars)...")
    try:
        temp_df = pd.read_csv(
            config.HISTORICAL_DATA_FILE,
            dtype={
                'timestamp': str, # Read as string to handle parsing manually
                'open': float, 'high': float, 'low': float, 'close': float, 'volume': float,
                'close_time': float, 'quote_asset_volume': float, 'num_trades': float,
                'taker_buy_base': float, 'taker_buy_quote': float
            },
            na_values=['', 'None', 'NaN']
        )
        # Convert timestamp column after reading, mimicking old date_parser behavior
        temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp'], utc=True, errors='coerce')
        temp_df.dropna(subset=['timestamp', 'open', 'high', 'low', 'close', 'volume'], inplace=True)
        temp_df.sort_values('timestamp', inplace=True)
        temp_df.drop_duplicates(subset=['timestamp'], keep='last', inplace=True)
        temp_df = temp_df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].set_index('timestamp').astype(float)

        required_min_bars = max(config.SHORT_SMA_PERIOD, config.LONG_SMA_PERIOD, config.ATR_PERIOD, config.RSI_PERIOD,
                                config.MACD_SLOW_PERIOD + config.MACD_SIGNAL_PERIOD) + 100
        num_to_load = max(_INITIAL_HISTORICAL_LOAD_LIMIT, required_min_bars)
        _historical_klines_df = temp_df.tail(num_to_load)

        logger.info(f"Loaded {len(_historical_klines_df)} initial historical klines from '{config.HISTORICAL_DATA_FILE}'.")
    except Exception as e:
        logger.error(f"Error loading initial historical klines from '{config.HISTORICAL_DATA_FILE}': {e}", exc_info=True)
        _historical_klines_df = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']).set_index('timestamp')

if _historical_klines_df is None:
    _load_initial_historical_klines()

def _update_historical_data_from_live():
    global _historical_klines_df
    logger.debug("Attempting to update historical data from live klines.")
    with _historical_klines_lock:
        if _historical_klines_df is None or _historical_klines_df.empty:
            logger.warning("Historical klines DataFrame is empty or None. Attempting initial load.")
            _load_initial_historical_klines()
            if _historical_klines_df.empty:
                logger.warning("Initial historical klines load failed or resulted in empty DataFrame. Cannot update from live.")
                return

        live_df = binance_data_service.get_latest_klines_df()

        if live_df.empty:
            logger.debug("Live klines DataFrame from binance_data_service is empty. No new data to append.")
            return

        last_ts_in_history = _historical_klines_df.index.max() if not _historical_klines_df.empty else pd.Timestamp.min.tz_localize(timezone.utc)

        if live_df.index.tz is None:
            live_df.index = live_df.index.tz_localize(timezone.utc)

        new_live_klines = live_df[live_df.index > last_ts_in_history]

        if not new_live_klines.empty:
            missing_cols = set(_historical_klines_df.columns) - set(new_live_klines.columns)
            for col in missing_cols:
                new_live_klines[col] = 0.0

            new_live_klines = new_live_klines[_historical_klines_df.columns]

            _historical_klines_df = pd.concat([_historical_klines_df, new_live_klines])
            _historical_klines_df.sort_index(inplace=True)
            _historical_klines_df = _historical_klines_df[~_historical_klines_df.index.duplicated(keep='last')] 

            max_keep_bars = max(_INITIAL_HISTORICAL_LOAD_LIMIT, len(binance_data_service.kline_data_deque) * 2, 1000)
            _historical_klines_df = _historical_klines_df.tail(max_keep_bars)

            logger.info(f"Appended {len(new_live_klines)} new live klines. Total historical klines: {len(_historical_klines_df)}")
        else:
            logger.debug("No new unique live klines to append to historical data.")

def _get_ohlcv_data(timeframe: str = '1m', limit: int | None = None) -> pd.DataFrame:
    logger.debug(f"Retrieving OHLCV data for timeframe: {timeframe}, limit: {limit}.")
    with _historical_klines_lock:
        if _historical_klines_df is None or _historical_klines_df.empty:
            logger.warning("Attempted to get OHLCV data before _historical_klines_df is populated. Returning empty DataFrame.")
            return pd.DataFrame()

        df = _historical_klines_df.copy()

        if timeframe == '5m':
            df_resampled = df.resample('5min').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            df = df_resampled
            logger.debug(f"Resampled to 5m timeframe. {len(df)} bars available.")
        elif timeframe != '1m':
            logger.warning(f"Unsupported timeframe '{timeframe}' requested. Returning 1m data.")

        if limit:
            logger.debug(f"Returning last {limit} bars for timeframe {timeframe}.")
            return df.tail(limit)
        logger.debug(f"Returning all {len(df)} bars for timeframe {timeframe}.")
        return df

def get_historical_data() -> tuple[list[float], list[float], list[float], list[float]]:
    logger.debug(f"Fetching 1-minute historical kline data (limit: {config.HISTORICAL_BARS_COUNT}).")
    df = _get_ohlcv_data(timeframe='1m', limit=config.HISTORICAL_BARS_COUNT)
    if df.empty:
        logger.warning("No 1-minute historical data available.")
        return [], [], [], []
    logger.debug(f"Successfully fetched {len(df)} 1-minute historical bars.")
    return df['high'].tolist(), df['low'].tolist(), df['close'].tolist(), df['volume'].tolist()

def get_historical_data_5m() -> tuple[list[float], list[float], list[float], list[float]]:
    logger.debug(f"Fetching 5-minute historical kline data (limit: {config.HIGHER_SMA_PERIOD + 5}).")
    df = _get_ohlcv_data(timeframe='5m', limit=config.HIGHER_SMA_PERIOD + 5)
    if df.empty:
        logger.warning("No 5-minute historical data available.")
        return [], [], [], []
    logger.debug(f"Successfully fetched {len(df)} 5-minute historical bars.")
    return df['high'].tolist(), df['low'].tolist(), df['close'].tolist(), df['volume'].tolist()

def get_binance_price() -> tuple[float | None, str | None]:
    logger.debug("Fetching spot price from Binance.")
    url = 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'
    try:
        response = requests.get(url)
        response.raise_for_status()
        price = float(response.json()['price'])
        logger.info(f"Binance price fetched: ${price:.2f}")
        return price, "Binance"
    except requests.exceptions.RequestException as e:
        logger.error(f"Binance price fetch error: {e}")
        return None, None
    except Exception as e:
        logger.error(f"An unexpected error occurred fetching Binance price: {e}", exc_info=True)
        return None, None

def get_coinbase_price() -> tuple[float | None, str | None]:
    logger.debug("Fetching spot price from Coinbase.")
    url = 'https://api.coinbase.com/v2/prices/BTC-USD/spot'
    try:
        response = requests.get(url)
        response.raise_for_status()
        price = float(response.json()['data']['amount'])
        logger.info(f"Coinbase price fetched: ${price:.2f}")
        return price, "Coinbase"
    except requests.exceptions.RequestException as e:
        logger.error(f"Coinbase price fetch error: {e}")
        return None, None
    except Exception as e:
        logger.error(f"An unexpected error occurred fetching Coinbase price: {e}", exc_info=True)
        return None, None

def get_kraken_price() -> tuple[float | None, str | None]:
    logger.debug("Fetching spot price from Kraken.")
    url = 'https://api.kraken.com/0/public/Ticker?pair=XBTUSD'
    try:
        response = requests.get(url)
        response.raise_for_status()
        result = response.json()['result']
        for key in result:
            price = float(result[key]['c'][0])
            logger.info(f"Kraken price fetched: ${price:.2f}")
            return price, "Kraken"
    except requests.exceptions.RequestException as e:
        logger.error(f"Kraken price fetch error: {e}")
        return None, None
    except Exception as e:
        logger.error(f"An unexpected error occurred fetching Kraken price: {e}", exc_info=True)
        return None, None

def get_average_price() -> tuple[float | None, list[str]]:
    logger.debug("Calculating average price from multiple exchanges.")
    sources = []
    prices = []
    for func in [get_binance_price, get_coinbase_price, get_kraken_price]:
        price, source = func()
        if price is not None:
            prices.append(price)
            sources.append(source)
    if prices:
        avg_price = sum(prices) / len(prices)
        logger.info(f"Average price calculated: ${avg_price:.2f} from {len(sources)} sources: {', '.join(sources)}")
        return avg_price, sources
    logger.warning("Could not fetch prices from any exchange. Returning None for average price.")
    return None, sources

def simple_moving_average(prices: list[float], period: int) -> float | None:
    if len(prices) < period:
        logger.debug(f"Not enough data ({len(prices)} bars) for SMA period {period}. Returning None.")
        return None
    sma = sum(prices[-period:]) / period
    logger.debug(f"Calculated SMA ({period}): {sma:.2f}")
    return sma

def exponential_moving_average(prices: list[float], period: int) -> float | None:
    if len(prices) < period:
        logger.debug(f"Not enough data ({len(prices)} bars) for EMA period {period}. Returning None.")
        return None
    ema = prices[-period] if len(prices) >= period else prices[0]
    k = 2 / (period + 1)
    for price in prices[-period+1:]:
        ema = price * k + ema * (1 - k)
    logger.debug(f"Calculated EMA ({period}): {ema:.2f}")
    return ema

def calculate_rsi(prices: list[float], period: int = 14) -> float | None:
    if len(prices) < period + 1:
        logger.debug(f"Not enough data ({len(prices)} bars) for RSI period {period}. Returning None.")
        return None

    changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
    relevant_changes = changes[-period:]

    gains = [c for c in relevant_changes if c > 0]
    losses = [abs(c) for c in relevant_changes if c < 0]

    avg_gain = sum(gains) / period if gains else 0
    avg_loss = sum(losses) / period if losses else 0

    if avg_loss == 0:
        rsi_val = 100.0 if avg_gain > 0 else 50.0
        logger.debug(f"Calculated RSI ({period}): {rsi_val:.2f} (no average loss).")
        return rsi_val

    rs = avg_gain / avg_loss
    rsi_val = 100 - (100 / (1 + rs))
    logger.debug(f"Calculated RSI ({period}): {rsi_val:.2f}")
    return rsi_val

def calculate_macd(prices: list[float], short_period: int = 12, long_period: int = 26, signal_period: int = 9) -> tuple[float | None, float | None, float | None]:
    if len(prices) < long_period + signal_period:
        logger.debug(f"Not enough data ({len(prices)} bars) for MACD (long_period={long_period}, signal_period={signal_period}). Returning None.")
        return None, None, None

    ema_short = exponential_moving_average(prices, short_period)
    ema_long = exponential_moving_average(prices, long_period)

    if ema_short is None or ema_long is None:
        logger.debug("EMA short or long is None for MACD calculation. Returning None.")
        return None, None, None

    macd_line = ema_short - ema_long

    macd_history = []
    for i in range(len(prices) - signal_period, len(prices)):
        current_prices_slice = prices[:i+1]
        if len(current_prices_slice) < long_period:
            continue
        slice_ema_short = exponential_moving_average(current_prices_slice, short_period)
        slice_ema_long = exponential_moving_average(current_prices_slice, long_period)
        if slice_ema_short is not None and slice_ema_long is not None:
            macd_history.append(slice_ema_short - slice_ema_long)

    if len(macd_history) < signal_period:
        logger.debug("Not enough MACD history for signal line calculation. Returning partial MACD.")
        return macd_line, None, None

    signal_line = exponential_moving_average(macd_history, signal_period)

    if signal_line is None:
        logger.debug("Signal line is None for MACD calculation. Returning partial MACD.")
        return macd_line, None, None

    macd_hist = macd_line - signal_line
    logger.debug(f"Calculated MACD: Line={macd_line:.2f}, Signal={signal_line:.2f}, Hist={macd_hist:.2f}")
    return macd_line, signal_line, macd_hist

def calculate_volatility(prices: list[float]) -> float | None:
    if len(prices) < 2:
        logger.debug("Not enough data (less than 2 bars) for volatility calculation. Returning None.")
        return None
    volatility = pd.Series(prices).std()
    logger.debug(f"Calculated Volatility (Std Dev): {volatility:.2f}")
    return volatility

def calculate_atr(highs: list[float], lows: list[float], closes: list[float], period: int = config.ATR_PERIOD) -> float | None:
    if len(highs) < period + 1:
        logger.debug(f"Not enough data ({len(highs)} bars) for ATR period {period}. Returning None.")
        return None
    trs = []
    for i in range(1, len(highs)):
        tr = max(
            highs[i] - lows[i],
            abs(highs[i] - closes[i-1]),
            abs(lows[i] - closes[i-1])
        )
        trs.append(tr)

    if len(trs) < period:
        logger.debug(f"Not enough True Ranges ({len(trs)}) for initial ATR period {period}. Returning None.")
        return None

    atr_val = sum(trs[:period]) / period

    for i in range(period, len(trs)):
        atr_val = (atr_val * (period - 1) + trs[i]) / period

    logger.debug(f"Calculated ATR ({period}): {atr_val:.2f}")
    return atr_val

def get_recent_atr(symbol: str = "BTCUSDT", periods: int = 14, timeframe: str = "1m") -> float | None:
    logger.debug(f"Fetching recent ATR for {symbol} (periods: {periods}, timeframe: {timeframe}).")
    df = _get_ohlcv_data(timeframe=timeframe, limit=periods + 5)
    if df.empty or len(df) < periods + 1:
        logger.warning(f"Insufficient historical data ({len(df)} bars) to calculate ATR for {periods} periods from internal store. Returning None.")
        return None

    highs, lows, closes = df['high'].tolist(), df['low'].tolist(), df['close'].tolist()

    atr = calculate_atr(highs, lows, closes, period=periods)

    if atr is None:
        logger.warning(f"calculate_atr returned None for ATR with periods={periods}. Check calculation logic or data validity.")
    else:
        logger.debug(f"Recent ATR for {symbol}: {atr:.2f}")

    return atr

def calculate_momentum(prices: list[float], period: int = 10) -> float | None:
    if len(prices) < period + 1:
        logger.debug(f"Not enough data ({len(prices)} bars) for Momentum period {period}. Returning None.")
        return None
    momentum = prices[-1] - prices[-period-1]
    logger.debug(f"Calculated Momentum ({period}): {momentum:.2f}")
    return momentum

def calculate_volume_spike(volumes: list[float], period: int = 20) -> float | None:
    if len(volumes) < period + 1:
        logger.debug(f"Not enough data ({len(volumes)} bars) for Volume Spike period {period}. Returning None.")
        return None
    avg_vol = sum(volumes[-period-1:-1]) / period
    if avg_vol == 0:
        logger.debug("Average volume is zero, cannot calculate volume spike. Returning None.")
        return None
    volume_spike = volumes[-1] / avg_vol
    logger.debug(f"Calculated Volume Spike ({period}): {volume_spike:.2f}")
    return volume_spike

def calculate_rsi_slope(prices: list[float], period: int = 14, slope_period: int = 5) -> float | None:
    if len(prices) < period + slope_period + 1:
        logger.debug(f"Not enough data ({len(prices)} bars) for RSI Slope (RSI period={period}, slope period={slope_period}). Returning None.")
        return None

    rsi_vals = []
    for i in range(len(prices) - slope_period - period, len(prices) - period + 1):
        window_prices = prices[i:i+period+1]
        rsi_val = calculate_rsi(window_prices, period)
        if rsi_val is not None:
            rsi_vals.append(rsi_val)

    if len(rsi_vals) < 2:
        logger.debug("Not enough RSI values to calculate RSI Slope. Returning None.")
        return None

    rsi_slope = (rsi_vals[-1] - rsi_vals[0]) / slope_period
    logger.debug(f"Calculated RSI Slope ({slope_period}): {rsi_slope:.4f}")
    return rsi_slope

def compute_trend_5m(closes5: list[float]) -> str | None:
    logger.debug("Computing 5-minute trend.")
    if len(closes5) < config.HIGHER_SMA_PERIOD:
        logger.debug(f"Not enough 5m close data ({len(closes5)} bars) for trend computation (period={config.HIGHER_SMA_PERIOD}). Returning None.")
        return None
    ema = exponential_moving_average(closes5, config.HIGHER_SMA_PERIOD)
    if ema is None:
        logger.debug("EMA is None for 5m trend computation. Returning None.")
        return None
    trend = "bullish" if closes5[-1] > ema else "bearish"
    logger.debug(f"5-minute trend computed: {trend}")
    return trend

def adaptive_rsi_thresholds(volatility: float | None, regime: str | None) -> tuple[float, float]:
    logger.debug(f"Calculating adaptive RSI thresholds (volatility: {volatility}, regime: {regime}).")
    overbought = 70.0
    oversold = 30.0

    if volatility is not None:
        adjustment = min(10.0, max(2.0, int(volatility / 50.0)))
        overbought += adjustment
        oversold   -= adjustment
        logger.debug(f"RSI thresholds adjusted by volatility: +/-{adjustment:.2f}")

    if regime in ("volatile", "trending_volatile"):
        overbought += 5.0
        oversold   -= 5.0
        logger.debug(f"RSI thresholds further adjusted for {regime} regime.")

    if overbought > 85.0:
        overbought = 85.0
    if oversold < 15.0:
        oversold = 15.0
    logger.info(f"Adaptive RSI thresholds: Overbought={overbought:.2f}, Oversold={oversold:.2f}")
    return overbought, oversold

def adaptive_trend_threshold(volatility: float | None, regime: str | None) -> float:
    logger.debug(f"Calculating adaptive trend threshold (volatility: {volatility}, regime: {regime}).")
    base = 0.002

    if volatility is not None:
        base += min(0.003, max(0.0001, volatility / 200000.0))
        logger.debug(f"Trend threshold adjusted by volatility: {base:.5f}")

    if regime in ("volatile", "trending_volatile"):
        base += 0.001
        logger.debug(f"Trend threshold further adjusted for {regime} regime.")
    elif regime == "quiet":
        base -= 0.0005
        logger.debug(f"Trend threshold adjusted for quiet regime.")

    trend_threshold = round(base, 5)
    logger.info(f"Adaptive trend threshold: {trend_threshold:.5f}")
    return trend_threshold

def detect_regime(sma_short: float | None, sma_long: float | None, volatility: float | None, atr: float | None) -> str:
    logger.debug(f"Detecting market regime (SMA Short: {sma_short}, SMA Long: {sma_long}, Volatility: {volatility}, ATR: {atr}).")
    if sma_short is None or sma_long is None or volatility is None or atr is None or sma_long == 0:
        logger.warning("Insufficient data for regime detection. Returning 'unknown'.")
        return "unknown"

    trend_strength = abs(sma_short - sma_long)
    rel_trend = trend_strength / sma_long
    rel_vol = volatility / sma_long
    rel_atr = atr / sma_long

    # Use centralized thresholds from config
    if rel_trend > config.TREND_REL_THRESHOLD and (rel_vol > config.VOLATILE_REL_THRESHOLD or rel_atr > config.ATR_REL_THRESHOLD):
        regime = "trending_volatile"
    elif rel_trend > config.TREND_REL_THRESHOLD:
        regime = "trending"
    elif rel_vol > config.VOLATILE_REL_THRESHOLD or rel_atr > config.ATR_REL_THRESHOLD:
        regime = "volatile"
    elif rel_vol < config.QUIET_REL_THRESHOLD and rel_atr < config.QUIET_REL_THRESHOLD:
        regime = "quiet"
    else:
        regime = "ranging"
    logger.info(f"Market regime detected: {regime}")
    return regime

def _json_default_serializer(obj):
    if isinstance(obj, Decimal):
        return str(obj)
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, date):
        return obj.isoformat()
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

def analyze_market(account_info: Dict[str, Any], positions_info: Dict[str, Any], open_lots_list: list[dict] | None = None):
    logger.info("Starting market analysis.")
    _update_historical_data_from_live()

    # Ensure account_info and positions_info are dictionaries
    if not isinstance(account_info, dict):
        logger.error(f"account_info is not a dict: {type(account_info)}, value: {account_info}")
        account_info = {"equity": "0.0", "cash": "0.0"}

    if not isinstance(positions_info, dict):
        logger.error(f"positions_info is not a dict: {type(positions_info)}, value: {positions_info}")
        positions_info = {}

    total_account_equity_dec = Decimal(str(account_info.get("equity", "0.0")))
    available_cash_usd_dec = Decimal(str(account_info.get("cash", "0.0")))
    total_open_position_value_usd_dec = max(Decimal("0.0"), total_account_equity_dec - available_cash_usd_dec)
    
    overall_exposure_percentage_val = 0.0
    if total_account_equity_dec > Decimal("0.0"):
        overall_exposure_percentage_val = float((total_open_position_value_usd_dec / total_account_equity_dec) * Decimal("100.0"))

    df_1m = _get_ohlcv_data(timeframe='1m')
    df_5m = _get_ohlcv_data(timeframe='5m')

    required_bars_1m = max(config.SHORT_SMA_PERIOD, config.LONG_SMA_PERIOD, config.ATR_PERIOD, config.RSI_PERIOD, config.MACD_SLOW_PERIOD + config.MACD_SIGNAL_PERIOD) + 10
    required_bars_5m = config.HIGHER_SMA_PERIOD + 5

    avg_price, exchanges_used = get_average_price()
    timestamp_utc = datetime.now(timezone.utc).isoformat()

    if df_1m.empty or len(df_1m) < required_bars_1m or \
       df_5m.empty or len(df_5m) < required_bars_5m or \
       avg_price is None:
        logger.error(f"Insufficient data or no avg_price. 1m bars: {len(df_1m)}/{required_bars_1m}, 5m bars: {len(df_5m)}/{required_bars_5m}, Avg Price: {avg_price}. Cannot perform full market analysis.")
        return {
            "error": "Market data unavailable or insufficient for full analysis.",
            "exchanges_used": exchanges_used,
            "timestamp_utc": timestamp_utc,
            "avg_price": round(avg_price, 2) if avg_price is not None else None,
            "open_lots_details": [],
            "total_unrealized_pl": 0.0,
            "total_open_qty": 0.0,
            "total_account_equity": float(total_account_equity_dec),
            "available_cash_usd": float(available_cash_usd_dec),
            "total_open_position_value_usd": float(total_open_position_value_usd_dec),
            "overall_exposure_percentage": round(overall_exposure_percentage_val, 2)
        }

    highs, lows, closes, volumes = df_1m['high'].tolist(), df_1m['low'].tolist(), df_1m['close'].tolist(), df_1m['volume'].tolist()
    closes5 = df_5m['close'].tolist()

    rsi = calculate_rsi(closes)
    sma_short = simple_moving_average(closes, config.SHORT_SMA_PERIOD)
    sma_long = simple_moving_average(closes, config.LONG_SMA_PERIOD)
    ema_short = exponential_moving_average(closes, config.SHORT_SMA_PERIOD)
    ema_long = exponential_moving_average(closes, config.LONG_SMA_PERIOD)
    macd, macd_signal, macd_hist = calculate_macd(closes)
    volume = volumes[-1] if volumes else None
    volatility = calculate_volatility(closes)
    atr = calculate_atr(highs, lows, closes)

    regime = detect_regime(sma_short, sma_long, volatility, atr)
    rsi_overbought, rsi_oversold = adaptive_rsi_thresholds(volatility, regime)
    trend_threshold = adaptive_trend_threshold(volatility, regime)

    trend = "unknown"
    if sma_short is not None and sma_long is not None and sma_long != 0:
        if (sma_short - sma_long) > (trend_threshold * sma_long):
            trend = "bullish"
        elif (sma_long - sma_short) > (trend_threshold * sma_long):
            trend = "bearish"
        else:
            trend = "neutral"
    logger.debug(f"Overall trend: {trend}")

    trend_5m = compute_trend_5m(closes5)

    momentum = calculate_momentum(closes)
    volume_spike = calculate_volume_spike(volumes)
    rsi_slope = calculate_rsi_slope(closes)

    open_lots_details_calc = []
    total_unrealized_pl_calc = Decimal("0.0")
    total_open_qty_calc = Decimal("0.0")
    current_market_price = Decimal(str(avg_price))

    if open_lots_list and current_market_price > Decimal("0"):
        logger.debug(f"Processing {len(open_lots_list)} open lots for market snapshot.")
        for lot_data in open_lots_list:
            remaining_qty = Decimal(str(lot_data.get("remaining_qty", "0")))
            cost_basis_unit_str = lot_data.get("cost_basis_per_unit", lot_data.get("buy_price", "0"))
            cost_basis_unit = Decimal(str(cost_basis_unit_str))

            lot_unrealized_pl = Decimal("0.0")
            if remaining_qty > Decimal("0") and cost_basis_unit > Decimal("0"):
                lot_unrealized_pl = (current_market_price - cost_basis_unit) * remaining_qty

            total_unrealized_pl_calc += lot_unrealized_pl
            total_open_qty_calc += remaining_qty

            open_lots_details_calc.append({
                "lot_id": lot_data.get("lot_id"),
                "remaining_qty": float(remaining_qty),
                "cost_basis_per_unit": float(cost_basis_unit),
                "current_stop": float(lot_data.get("current_stop", Decimal("0.0"))),
                "unrealized_pl_lot": float(lot_unrealized_pl)
            })
        logger.debug(f"Finished processing open lots. Total unrealized P/L: {total_unrealized_pl_calc:.2f}, Total open quantity: {total_open_qty_calc:.8f}.")

    market_snapshot = {
        "avg_price": round(avg_price, 2) if avg_price is not None else None,
        "rsi": round(rsi, 2) if rsi is not None else None,
        "sma_short": round(sma_short, 2) if sma_short is not None else None,
        "sma_long": round(sma_long, 2) if sma_long is not None else None,
        "ema_short": round(ema_short, 2) if ema_short is not None else None,
        "ema_long": round(ema_long, 2) if ema_long is not None else None,
        "macd": round(macd, 2) if macd is not None else None,
        "macd_signal": round(macd_signal, 2) if macd_signal is not None else None,
        "macd_hist": round(macd_hist, 2) if macd_hist is not None else None,
        "volume": round(volume, 4) if volume is not None else None,
        "volatility": round(volatility, 2) if volatility is not None else None,
        "atr": round(atr, 2) if atr is not None else None,
        "trend": trend,
        "trend_5m": trend_5m,
        "regime": regime,
        "rsi_overbought": rsi_overbought,
        "rsi_oversold": rsi_oversold,
        "trend_threshold": trend_threshold,
        "momentum": round(momentum, 2) if momentum is not None else None,
        "volume_spike": round(volume_spike, 3) if volume_spike is not None else None,
        "rsi_slope": round(rsi_slope, 4) if rsi_slope is not None else None,
        "exchanges_used": exchanges_used,
        "timestamp_utc": timestamp_utc,
        "open_lots_details": open_lots_details_calc,
        "total_unrealized_pl": float(total_unrealized_pl_calc),
        "total_open_qty": float(total_open_qty_calc),
        "total_account_equity": float(total_account_equity_dec),
        "available_cash_usd": float(available_cash_usd_dec),
        "total_open_position_value_usd": float(total_open_position_value_usd_dec),
        "overall_exposure_percentage": round(overall_exposure_percentage_val, 2)
    }

    market_snapshot['unrealized_pl_alpaca'] = float(positions_info.get("unrealized_pl", 0.0)) if positions_info else 0.0
    logger.info("Market analysis completed. Snapshot generated.")
    return market_snapshot

def backtest_take_profits(highs: list[float], closes: list[float], tp_levels: list[float]) -> dict[float, float]:
    logger.debug("Starting backtest for take profits.")
    hits = {tp: 0 for tp in tp_levels}
    total = len(closes) - 1
    if total <= 0:
        logger.warning("Insufficient data for backtesting take profits. Returning zero hit rates.")
        return {tp: 0.0 for tp in tp_levels}
    for i in range(1, len(closes)):
        entry = closes[i-1]
        high  = highs[i]
        for tp in tp_levels:
            if high >= entry * (1 + tp):
                hits[tp] += 1
    results = {tp: hits[tp] / total for tp in tp_levels}
    logger.info(f"Backtest completed. Hit rates: {results}")
    return results

def get_latest_indicators(api=None, open_lots_list: list[dict] | None = None) -> dict:
    logger.warning("Using deprecated get_latest_indicators wrapper. Update direct calls to analyze_market.")
    return analyze_market(account_info={}, positions_info={}, open_lots_list=open_lots_list)
