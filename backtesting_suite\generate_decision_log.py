# generate_decision_log.py
import sys
import os
import pandas as pd
from decimal import Decimal

# Add the parent directory to the Python path to allow for absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backtesting_suite.data_handler import DataHandler
import backtesting_suite.backtest_config as config
import ai_decision_service

def resample_data(df, timeframe):
    """Resamples the dataframe to a larger timeframe."""
    print(f"Resampling data to {timeframe} timeframe...")
    
    resampling_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }
    
    resampled_df = df.resample(timeframe).apply(resampling_rules)
    resampled_df.dropna(inplace=True) # Drop periods with no data
    
    print(f"Resampling complete. New data size: {len(resampled_df)} rows.")
    return resampled_df

def generate_log():
    """
    Generates or resumes generating a CSV log file with cost and date controls,
    using timeframe aggregation.
    """
    print("--- Starting Decision Log Generation ---")
    
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    log_file_path = os.path.join(base_dir, "backtesting_suite", "decision_log_full.csv")

    # --- IMPORTANT: This section is commented out to prevent re-generating the decision log
    # and incurring additional API costs. The decision_log_full.csv is now copied from backup.
    # If you need to regenerate the log (e.g., with new AI models or data), uncomment this section.
    #
    # if os.path.exists(log_file_path):
    #     os.remove(log_file_path)
    #     print(f"Removed existing log file to start fresh: {log_file_path}")
    #
    # data_handler = DataHandler("binance_btcusdt_1m.csv", base_dir=base_dir)
    # data_handler.load_data()
    #
    # if data_handler.data is None or data_handler.data.empty:
    #     print("No source data loaded. Exiting.")
    #     return
    #
    # # --- Timeframe Aggregation and Date Filtering ---
    # resampled_df = resample_data(data_handler.data, config.TIMEFRAME)
    # data_handler.data = resampled_df # Replace original data with resampled data
    # data_handler.calculate_indicators()
    #
    # # Filter data to the last two years
    # start_date = '2023-07-04'
    # data_handler.data = data_handler.data[data_handler.data.index >= start_date]
    # print(f"Filtered data from {start_date} onwards. New data size: {len(data_handler.data)} rows.")
    #
    # data_to_process = data_handler.data.copy()
    #
    # if data_to_process.empty:
    #     print("No new data to process for the specified date range.")
    #     return
    #
    # total_rows = len(data_to_process)
    # calls_made = 0
    # print(f"Processing {total_rows} new rows of data...")
    #
    # # --- Process Data and Get AI Decisions ---
    # ai_decisions = []
    # for i, (timestamp, row) in enumerate(data_to_process.iterrows()):
    #     indicators = {
    #         "avg_price": row['close'], "rsi": row['rsi'], "rsi_slope": row['rsi_slope'],
    #         "macd_hist": row['macd_hist'], "volume_spike": row['volume_spike'],
    #         "trend_5m": "bullish", "atr": row['atr'], "total_unrealized_pl": "0.0",
    #         "regime": "ranging"
    #     }
    #
    #     ai_decision = ai_decision_service.get_ai_decision(indicators)
    #     ai_decisions.append(ai_decision['decision'])
    #     calls_made += 1
    #
    #     # Update and display estimated cost
    #     estimated_cost = calls_made * 0.0004  # $0.0004 per API call
    #     if (i + 1) % 10 == 0: # Display every 10 calls
    #         print(f"\033[92mEstimated Cost: ${estimated_cost:.2f}\033[0m")
    #
    #     if (i + 1) % 10 == 0:
    #         print(f"Processed {i + 1}/{total_rows} rows...")
    #
    # if not data_to_process.empty and len(ai_decisions) == len(data_to_process):
    #     data_to_process.loc[:, 'ai_decision'] = ai_decisions
    #
    # print(f"Shape of data_to_process before saving: {data_to_process.shape}")
    # print(f"Columns in data_to_process before saving: {data_to_process.columns.tolist()}")
    #
    # # --- Save to CSV ---
    # if not data_to_process.empty:
    #     # Define the columns to save to ensure consistency
    #     output_columns = [
    #         'open', 'high', 'low', 'close', 'volume',
    #         'rsi', 'macd', 'macd_hist', 'macd_signal', 'atr',
    #         'rsi_slope', 'volume_spike', 'ai_decision'
    #     ]
    #     # Filter the DataFrame to only include the desired columns
    #     output_df = data_to_process[[col for col in output_columns if col in data_to_process.columns]]
    #
    #     # Write new file with header
    #     output_df.to_csv(log_file_path, mode='w', header=True)
    #
    # print(f"\n--- Decision Log Generation Finished ---")
    # print(f"Log file saved to: {log_file_path}")

    # --- If the log file does not exist, print a message ---
    if not os.path.exists(log_file_path):
        print(f"Decision log file not found at {log_file_path}. Please ensure it's generated or copied from backup.")
    else:
        print(f"Using existing decision log file: {log_file_path}")

    print(f"\n--- Decision Log Generation Finished ---")
    print(f"Log file saved to: {log_file_path}")


if __name__ == "__main__":
    generate_log()
