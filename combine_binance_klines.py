# START OF FILE combine_binance_klines.py

import pandas as pd
import os
import glob
import sys

# --- Configuration ---
# FIX: Set this to the directory where you extracted all your downloaded Binance 1m CSVs.
# Example: 'C:\\Users\\<USER>\\Documents\\Bitcoin ai agent\\downloaded_klines_raw'
KLINES_DIR = "downloaded_klines_raw" 
OUTPUT_FILENAME = "binance_btcusdt_1m.csv"

# Columns from Binance's bulk download CSVs (typically 12 columns)
# Ensure this matches your actual downloaded CSVs.
BINANCE_KLINES_COLUMNS = [
    'open_time', 'open', 'high', 'low', 'close', 'volume',
    'close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base_asset_volume',
    'taker_buy_quote_asset_volume', 'ignore'
]

# Standardized columns for your bot (to match your existing code's expectations)
STANDARD_COLUMNS = [
    'timestamp', 'open', 'high', 'low', 'close', 'volume',
    'close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'ignore'
]

def combine_klines():
    """
    Combines multiple Binance kline CSV files into a single, sorted, deduplicated CSV.
    """
    print(f"Starting to combine klines from '{KLINES_DIR}' into '{OUTPUT_FILENAME}'...")

    if not os.path.exists(KLINES_DIR):
        print(f"Error: Klines directory '{KLINES_DIR}' not found. Please create it and extract your downloaded CSVs there.")
        sys.exit(1)

    all_files = glob.glob(os.path.join(KLINES_DIR, "*.csv"))
    if not all_files:
        print(f"Error: No CSV files found in '{KLINES_DIR}'. Please ensure you have extracted the downloaded klines.")
        sys.exit(1)

    all_dataframes = []
    print(f"Found {len(all_files)} CSV files to combine.")

    # Loop through each CSV file, read it, and add to list of DataFrames
    for i, file_path in enumerate(all_files):
        try:
            # Read CSV with a generic header=None, then assign names explicitly
            # This handles potential inconsistencies or missing headers in some downloaded files
            df = pd.read_csv(file_path, header=None, names=BINANCE_KLINES_COLUMNS)
            
            # Rename 'open_time' to 'timestamp' to match your bot's standard
            if 'open_time' in df.columns:
                df.rename(columns={'open_time': 'timestamp'}, inplace=True)
            
            # Convert timestamp to datetime and localize to UTC
            # Binance bulk data timestamps are Unix milliseconds
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            
            # Ensure other numeric columns are numeric and fill NaNs
            numeric_cols_to_check = [col for col in STANDARD_COLUMNS if col in df.columns and col != 'timestamp']
            for col in numeric_cols_to_check:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0) # Fill numeric NaNs with 0

            # For text columns like 'ignore', fill NaNs with empty string
            if 'ignore' in df.columns:
                df['ignore'] = df['ignore'].fillna('')
            
            all_dataframes.append(df)
            if (i + 1) % 100 == 0 or (i + 1) == len(all_files):
                print(f"  Processed {i + 1}/{len(all_files)} files. Last file: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"Warning: Could not process file '{file_path}': {e}. Skipping this file.")
            continue

    if not all_dataframes:
        print("Error: No valid dataframes were processed. Combination failed.")
        sys.exit(1)

    print("Concatenating all dataframes...")
    combined_df = pd.concat(all_dataframes, ignore_index=True)

    print("Removing duplicate timestamps and sorting...")
    combined_df.drop_duplicates(subset=['timestamp'], inplace=True)
    combined_df.sort_values('timestamp', inplace=True)

    # Reorder columns to ensure consistency with STANDARD_COLUMNS
    missing_cols = [col for col in STANDARD_COLUMNS if col not in combined_df.columns]
    for col in missing_cols:
        # Create missing columns with appropriate default values
        if col in ['close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote']:
            combined_df[col] = 0.0
        else: # For 'ignore'
            combined_df[col] = ''
            
    combined_df = combined_df[STANDARD_COLUMNS] # Ensure final column order

    print(f"Saving combined data to '{OUTPUT_FILENAME}'...")
    combined_df.to_csv(OUTPUT_FILENAME, index=False)

    print(f"Successfully combined {len(all_files)} kline files.")
    print(f"Final combined data shape: {combined_df.shape}")
    print(f"Data from {combined_df['timestamp'].min()} to {combined_df['timestamp'].max()}")
    print("Combination complete.")

if __name__ == "__main__":
    # Check for pandas dependency
    try:
        import pandas as pd
    except ImportError:
        print("\n'pandas' not found. Installing now...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas"])
            print("pandas installed successfully. Please re-run the script.")
            sys.exit(0) # Exit to allow a clean re-run after installation
        except Exception as e:
            print(f"Failed to install pandas: {e}")
            print("Please install it manually: pip install pandas")
            sys.exit(1)
            
    combine_klines()

# END OF FILE combine_binance_klines.py