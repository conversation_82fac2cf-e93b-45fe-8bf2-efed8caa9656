#!/usr/bin/env python3
"""
Real-Time Monitoring Dashboard - Phase 4 Implementation

Web-based dashboard for monitoring regime changes, parameter updates, and performance
of the Phase 4 live integration system.

Key Features:
- Real-time regime monitoring
- Parameter change tracking
- Performance visualization
- Safety system status
- System health monitoring
- Interactive controls

Author: Bitcoin AI Trading Bot - Phase 4 Monitoring Dashboard
Date: July 29, 2025
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
import threading
import time

# Import Phase 4 components
try:
    from live_integration_manager import get_system_status as get_integration_status
    from live_data_pipeline import get_pipeline_status
    from safety_validation_system import get_safety_status, clear_emergency_stop
    from configuration_manager import ConfigurationManager
    PHASE4_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Phase 4 components not available: {e}")
    PHASE4_AVAILABLE = False

logger = logging.getLogger("TradingBotApp.MonitoringDashboard")

class MonitoringDashboard:
    """
    Real-time monitoring dashboard for Phase 4 system.
    
    Provides web-based interface for monitoring all aspects of the
    live integration system.
    """
    
    def __init__(self, host: str = "127.0.0.1", port: int = 5000):
        """Initialize the monitoring dashboard."""
        
        self.host = host
        self.port = port
        self.app = Flask(__name__)
        self.config_manager = None
        
        # Dashboard state
        self.dashboard_active = False
        self.update_interval = 5  # Update every 5 seconds
        self.data_cache = {}
        self.last_update = None
        
        # Initialize components
        if PHASE4_AVAILABLE:
            self.config_manager = ConfigurationManager()
        
        # Setup Flask routes
        self._setup_routes()
        
        logger.info("Monitoring Dashboard initialized")
    
    def _setup_routes(self):
        """Setup Flask routes for the dashboard."""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page."""
            return self._render_dashboard()
        
        @self.app.route('/api/status')
        def api_status():
            """API endpoint for system status."""
            return jsonify(self._get_system_status())
        
        @self.app.route('/api/regime')
        def api_regime():
            """API endpoint for current regime information."""
            return jsonify(self._get_regime_status())
        
        @self.app.route('/api/safety')
        def api_safety():
            """API endpoint for safety system status."""
            return jsonify(self._get_safety_status())
        
        @self.app.route('/api/performance')
        def api_performance():
            """API endpoint for performance metrics."""
            return jsonify(self._get_performance_metrics())
        
        @self.app.route('/api/parameters')
        def api_parameters():
            """API endpoint for current parameters."""
            return jsonify(self._get_current_parameters())
        
        @self.app.route('/api/emergency_stop', methods=['POST'])
        def api_emergency_stop():
            """API endpoint to clear emergency stop."""
            try:
                if PHASE4_AVAILABLE:
                    success = clear_emergency_stop()
                    return jsonify({'success': success, 'message': 'Emergency stop cleared' if success else 'Failed to clear emergency stop'})
                else:
                    return jsonify({'success': False, 'message': 'Phase 4 components not available'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})
        
        @self.app.route('/api/config_status')
        def api_config_status():
            """API endpoint for configuration status."""
            return jsonify(self._get_config_status())
    
    def _render_dashboard(self) -> str:
        """Render the main dashboard HTML."""
        
        html_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin AI Trading Bot - Phase 4 Monitoring Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-card h3 { margin-top: 0; color: #333; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-green { background-color: #4CAF50; }
        .status-yellow { background-color: #FFC107; }
        .status-orange { background-color: #FF9800; }
        .status-red { background-color: #F44336; }
        .metric { margin: 10px 0; }
        .metric-label { font-weight: bold; color: #666; }
        .metric-value { color: #333; }
        .emergency-button { background-color: #F44336; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .emergency-button:hover { background-color: #D32F2F; }
        .refresh-info { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .error { color: #F44336; font-weight: bold; }
        .success { color: #4CAF50; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Bitcoin AI Trading Bot</h1>
            <h2>Phase 4 Live Integration Monitoring Dashboard</h2>
            <p>Real-time monitoring of regime-aware optimization system</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>🎯 System Status</h3>
                <div id="system-status">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>📊 Current Market Regime</h3>
                <div id="regime-status">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>🛡️ Safety System</h3>
                <div id="safety-status">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>📈 Performance Metrics</h3>
                <div id="performance-metrics">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>⚙️ Current Parameters</h3>
                <div id="current-parameters">Loading...</div>
            </div>
            
            <div class="status-card">
                <h3>🔧 Configuration Status</h3>
                <div id="config-status">Loading...</div>
            </div>
        </div>
        
        <div class="refresh-info">
            <p>Dashboard updates every 5 seconds | Last update: <span id="last-update">Never</span></p>
            <button class="emergency-button" onclick="clearEmergencyStop()">Clear Emergency Stop</button>
            <div id="action-result"></div>
        </div>
    </div>

    <script>
        function updateDashboard() {
            // Update system status
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('system-status').innerHTML = formatSystemStatus(data);
                })
                .catch(error => {
                    document.getElementById('system-status').innerHTML = '<div class="error">Error loading system status</div>';
                });
            
            // Update regime status
            fetch('/api/regime')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('regime-status').innerHTML = formatRegimeStatus(data);
                })
                .catch(error => {
                    document.getElementById('regime-status').innerHTML = '<div class="error">Error loading regime status</div>';
                });
            
            // Update safety status
            fetch('/api/safety')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('safety-status').innerHTML = formatSafetyStatus(data);
                })
                .catch(error => {
                    document.getElementById('safety-status').innerHTML = '<div class="error">Error loading safety status</div>';
                });
            
            // Update performance metrics
            fetch('/api/performance')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('performance-metrics').innerHTML = formatPerformanceMetrics(data);
                })
                .catch(error => {
                    document.getElementById('performance-metrics').innerHTML = '<div class="error">Error loading performance metrics</div>';
                });
            
            // Update current parameters
            fetch('/api/parameters')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('current-parameters').innerHTML = formatCurrentParameters(data);
                })
                .catch(error => {
                    document.getElementById('current-parameters').innerHTML = '<div class="error">Error loading parameters</div>';
                });
            
            // Update configuration status
            fetch('/api/config_status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('config-status').innerHTML = formatConfigStatus(data);
                })
                .catch(error => {
                    document.getElementById('config-status').innerHTML = '<div class="error">Error loading config status</div>';
                });
            
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }
        
        function formatSystemStatus(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let html = '';
            html += '<div class="metric"><span class="metric-label">Phase 3 Enabled:</span> <span class="metric-value">' + (data.phase3_enabled ? '✅ Yes' : '❌ No') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Fallback Active:</span> <span class="metric-value">' + (data.fallback_active ? '⚠️ Yes' : '✅ No') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Parameter Changes:</span> <span class="metric-value">' + (data.parameter_changes_logged || 0) + '</span></div>';
            if (data.current_regime) {
                html += '<div class="metric"><span class="metric-label">System Health:</span> <span class="metric-value">' + (data.system_health ? '✅ Healthy' : '❌ Issues') + '</span></div>';
            }
            return html;
        }
        
        function formatRegimeStatus(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let html = '';
            if (data.regime) {
                html += '<div class="metric"><span class="metric-label">Current Regime:</span> <span class="metric-value">' + data.regime + '</span></div>';
                html += '<div class="metric"><span class="metric-label">Confidence:</span> <span class="metric-value">' + (data.confidence * 100).toFixed(1) + '%</span></div>';
                if (data.metrics) {
                    html += '<div class="metric"><span class="metric-label">Trend Strength:</span> <span class="metric-value">' + data.metrics.trend_strength.toFixed(3) + '</span></div>';
                    html += '<div class="metric"><span class="metric-label">Volatility Ratio:</span> <span class="metric-value">' + data.metrics.volatility_ratio.toFixed(3) + '</span></div>';
                }
            } else {
                html += '<div class="metric">No regime data available</div>';
            }
            return html;
        }
        
        function formatSafetyStatus(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let statusClass = 'status-' + (data.safety_level || 'red');
            let html = '';
            html += '<div class="metric"><span class="status-indicator ' + statusClass + '"></span><span class="metric-label">Safety Level:</span> <span class="metric-value">' + (data.safety_level || 'unknown').toUpperCase() + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Emergency Stop:</span> <span class="metric-value">' + (data.emergency_stop_active ? '🚨 ACTIVE' : '✅ Clear') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Circuit Breaker:</span> <span class="metric-value">' + (data.circuit_breaker_active ? '⚡ ACTIVE' : '✅ Clear') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Errors (1h):</span> <span class="metric-value">' + (data.error_count_last_hour || 0) + '</span></div>';
            return html;
        }
        
        function formatPerformanceMetrics(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let html = '<div class="metric">Performance monitoring not yet implemented</div>';
            return html;
        }
        
        function formatCurrentParameters(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let html = '<div class="metric">Parameter display not yet implemented</div>';
            return html;
        }
        
        function formatConfigStatus(data) {
            if (data.error) return '<div class="error">' + data.error + '</div>';
            
            let html = '';
            html += '<div class="metric"><span class="metric-label">Config File:</span> <span class="metric-value">' + (data.config_file_exists ? '✅ Exists' : '❌ Missing') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Dynamic Config:</span> <span class="metric-value">' + (data.dynamic_config_exists ? '✅ Exists' : '❌ Missing') + '</span></div>';
            html += '<div class="metric"><span class="metric-label">Backups:</span> <span class="metric-value">' + (data.backup_count || 0) + '</span></div>';
            if (data.dynamic_config) {
                html += '<div class="metric"><span class="metric-label">Last Update:</span> <span class="metric-value">' + (data.dynamic_config.last_update || 'Unknown') + '</span></div>';
                html += '<div class="metric"><span class="metric-label">Update Source:</span> <span class="metric-value">' + (data.dynamic_config.update_source || 'Unknown') + '</span></div>';
            }
            return html;
        }
        
        function clearEmergencyStop() {
            fetch('/api/emergency_stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    let resultDiv = document.getElementById('action-result');
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="success">✅ ' + data.message + '</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                    setTimeout(() => { resultDiv.innerHTML = ''; }, 5000);
                })
                .catch(error => {
                    document.getElementById('action-result').innerHTML = '<div class="error">❌ Error: ' + error + '</div>';
                });
        }
        
        // Update dashboard every 5 seconds
        updateDashboard();
        setInterval(updateDashboard, 5000);
    </script>
</body>
</html>
        """
        
        return html_template
    
    def _get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        
        try:
            if PHASE4_AVAILABLE:
                return get_integration_status()
            else:
                return {'error': 'Phase 4 components not available', 'timestamp': datetime.now().isoformat()}
        except Exception as e:
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def _get_regime_status(self) -> Dict[str, Any]:
        """Get current regime status."""
        
        try:
            if PHASE4_AVAILABLE:
                pipeline_status = get_pipeline_status()
                # Extract regime information from pipeline status
                return pipeline_status.get('current_regime', {})
            else:
                return {'error': 'Phase 4 components not available'}
        except Exception as e:
            return {'error': str(e)}
    
    def _get_safety_status(self) -> Dict[str, Any]:
        """Get safety system status."""
        
        try:
            if PHASE4_AVAILABLE:
                return get_safety_status()
            else:
                return {'error': 'Phase 4 components not available'}
        except Exception as e:
            return {'error': str(e)}
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        
        try:
            # This would be implemented to show actual performance data
            return {'message': 'Performance metrics not yet implemented'}
        except Exception as e:
            return {'error': str(e)}
    
    def _get_current_parameters(self) -> Dict[str, Any]:
        """Get current trading parameters."""
        
        try:
            # This would be implemented to show current parameters
            return {'message': 'Parameter display not yet implemented'}
        except Exception as e:
            return {'error': str(e)}
    
    def _get_config_status(self) -> Dict[str, Any]:
        """Get configuration status."""
        
        try:
            if self.config_manager:
                return self.config_manager.get_current_config_status()
            else:
                return {'error': 'Configuration manager not available'}
        except Exception as e:
            return {'error': str(e)}
    
    def start_dashboard(self, debug: bool = False):
        """Start the monitoring dashboard."""
        
        try:
            logger.info(f"Starting monitoring dashboard on http://{self.host}:{self.port}")
            self.dashboard_active = True
            
            # Run Flask app
            self.app.run(host=self.host, port=self.port, debug=debug, threaded=True)
            
        except Exception as e:
            logger.error(f"Error starting dashboard: {e}")
            raise
    
    def stop_dashboard(self):
        """Stop the monitoring dashboard."""
        
        self.dashboard_active = False
        logger.info("Monitoring dashboard stopped")

# Global instance
_monitoring_dashboard = None

def start_monitoring_dashboard(host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
    """Start the monitoring dashboard."""
    
    global _monitoring_dashboard
    
    if _monitoring_dashboard is None:
        _monitoring_dashboard = MonitoringDashboard(host, port)
    
    _monitoring_dashboard.start_dashboard(debug)

def stop_monitoring_dashboard():
    """Stop the monitoring dashboard."""
    
    global _monitoring_dashboard
    
    if _monitoring_dashboard is not None:
        _monitoring_dashboard.stop_dashboard()
        _monitoring_dashboard = None

if __name__ == "__main__":
    # Run dashboard directly
    start_monitoring_dashboard(debug=True)
