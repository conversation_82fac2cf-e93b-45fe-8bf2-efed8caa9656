#!/usr/bin/env python3
"""
Live Data Pipeline - Phase 4 Implementation

Connects Phase 3 regime detection to live market data feeds and trading bot data sources.
Provides real-time market data processing and regime analysis for the live trading bot.

Key Features:
- Real-time market data integration
- Seamless connection to existing bot data sources
- Market regime detection with live data
- Data validation and error handling
- Performance monitoring and caching

Author: Bitcoin AI Trading Bot - Phase 4 Data Pipeline
Date: July 29, 2025
"""

import sys
import os
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import threading
import time

# Add optimization directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Import Phase 3 components
try:
    from market_regime_analyzer import MarketRegimeAnalyzer, MarketRegime
    PHASE3_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Phase 3 components not available: {e}")
    PHASE3_AVAILABLE = False

# Import bot components
try:
    import config
    import market_analysis
    import binance_data_service
except ImportError as e:
    logging.warning(f"Some bot components not available: {e}")

logger = logging.getLogger("TradingBotApp.LiveDataPipeline")

class LiveDataPipeline:
    """
    Live data pipeline for Phase 4 integration.
    
    Connects live market data to Phase 3 regime detection system
    and provides real-time market analysis.
    """
    
    def __init__(self):
        """Initialize the live data pipeline."""
        
        self.regime_analyzer = None
        self.data_cache = {}
        self.last_update = None
        self.update_interval = 60  # Update every 60 seconds
        self.data_sources_active = False
        
        # Data validation settings
        self.enable_data_validation = True
        self.required_fields = ['current_price', 'rsi', 'sma_short', 'sma_long', 'atr', 'volume']
        self.data_age_limit = timedelta(minutes=10)  # Max age for cached data
        
        # Performance monitoring
        self.update_count = 0
        self.error_count = 0
        self.last_error = None
        
        # Initialize components
        if PHASE3_AVAILABLE:
            self._initialize_regime_analyzer()
        
        logger.info("Live Data Pipeline initialized")
    
    def _initialize_regime_analyzer(self) -> bool:
        """Initialize the market regime analyzer."""
        
        try:
            self.regime_analyzer = MarketRegimeAnalyzer()
            logger.info("SUCCESS: Market regime analyzer initialized")
            return True
            
        except Exception as e:
            logger.error(f"ERROR: Failed to initialize regime analyzer: {e}")
            return False
    
    def get_live_market_data(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get live market data for regime analysis.
        
        Args:
            force_refresh: Force refresh of cached data
            
        Returns:
            Dictionary containing current market data
        """
        
        try:
            # Check if we need to refresh data
            if not force_refresh and self._is_cache_valid():
                logger.debug("Using cached market data")
                return self.data_cache.copy()
            
            # Get fresh market data
            market_data = self._fetch_fresh_market_data()
            
            if market_data and self._validate_market_data(market_data):
                # Update cache
                self.data_cache = market_data
                self.last_update = datetime.now()
                self.update_count += 1
                
                logger.debug(f"Market data updated: {len(market_data)} fields")
                return market_data.copy()
            else:
                logger.warning("Failed to get valid market data, using cache if available")
                return self.data_cache.copy() if self.data_cache else {}
                
        except Exception as e:
            logger.error(f"Error getting live market data: {e}")
            self.error_count += 1
            self.last_error = str(e)
            return self.data_cache.copy() if self.data_cache else {}
    
    def _is_cache_valid(self) -> bool:
        """Check if cached data is still valid."""
        
        if not self.data_cache or not self.last_update:
            return False
        
        age = datetime.now() - self.last_update
        return age < self.data_age_limit
    
    def _fetch_fresh_market_data(self) -> Optional[Dict[str, Any]]:
        """Fetch fresh market data from bot's data sources."""
        
        try:
            # Try to get data from market_analysis module (bot's existing system)
            if hasattr(market_analysis, 'get_market_indicators'):
                indicators = market_analysis.get_market_indicators()
                if indicators:
                    return self._convert_bot_indicators_to_phase3_format(indicators)
            
            # Fallback: try to get data from binance service directly
            if hasattr(binance_data_service, 'get_latest_data'):
                binance_data = binance_data_service.get_latest_data()
                if binance_data:
                    return self._convert_binance_data_to_phase3_format(binance_data)
            
            # Last resort: create synthetic data from config defaults
            logger.warning("No live data sources available, using synthetic data")
            return self._create_synthetic_market_data()
            
        except Exception as e:
            logger.error(f"Error fetching fresh market data: {e}")
            return None
    
    def _convert_bot_indicators_to_phase3_format(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Convert bot's market indicators to Phase 3 format."""
        
        try:
            # Map bot indicator fields to Phase 3 expected fields
            phase3_data = {
                'current_price': float(indicators.get('avg_price', indicators.get('close', 45000))),
                'sma_short': float(indicators.get('sma_short', indicators.get('sma_5', 45000))),
                'sma_long': float(indicators.get('sma_long', indicators.get('sma_10', 44000))),
                'rsi': float(indicators.get('rsi', 50)),
                'macd_hist': float(indicators.get('macd_hist', 0)),
                'atr': float(indicators.get('atr', 200)),
                'volume': float(indicators.get('volume', 1000)),
                'avg_volume': float(indicators.get('avg_volume', indicators.get('volume', 1000))),
                'volatility': float(indicators.get('volatility', 50))
            }
            
            # Calculate additional fields if not present
            if 'volatility' not in indicators:
                # Estimate volatility from ATR
                phase3_data['volatility'] = phase3_data['atr'] * 0.25
            
            # Add timestamp
            phase3_data['timestamp'] = datetime.now().isoformat()
            phase3_data['data_source'] = 'bot_indicators'
            
            return phase3_data
            
        except Exception as e:
            logger.error(f"Error converting bot indicators: {e}")
            return None
    
    def _convert_binance_data_to_phase3_format(self, binance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Binance data to Phase 3 format."""
        
        try:
            # Extract price data
            current_price = float(binance_data.get('close', 45000))
            
            # Calculate basic indicators if not present
            phase3_data = {
                'current_price': current_price,
                'sma_short': current_price * 0.999,  # Approximate
                'sma_long': current_price * 0.998,   # Approximate
                'rsi': 50,  # Default neutral
                'macd_hist': 0,  # Default neutral
                'atr': current_price * 0.005,  # Estimate 0.5% ATR
                'volume': float(binance_data.get('volume', 1000)),
                'avg_volume': float(binance_data.get('volume', 1000)),
                'volatility': current_price * 0.002  # Estimate 0.2% volatility
            }
            
            # Add timestamp
            phase3_data['timestamp'] = datetime.now().isoformat()
            phase3_data['data_source'] = 'binance_direct'
            
            return phase3_data
            
        except Exception as e:
            logger.error(f"Error converting Binance data: {e}")
            return None
    
    def _create_synthetic_market_data(self) -> Dict[str, Any]:
        """Create synthetic market data for testing/fallback."""
        
        try:
            # Use reasonable default values
            base_price = 45000
            
            synthetic_data = {
                'current_price': base_price,
                'sma_short': base_price * 0.999,
                'sma_long': base_price * 0.998,
                'rsi': 50,
                'macd_hist': 0,
                'atr': base_price * 0.005,
                'volume': 1000,
                'avg_volume': 1000,
                'volatility': base_price * 0.002,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'synthetic'
            }
            
            logger.warning("Using synthetic market data - this should only happen in testing")
            return synthetic_data
            
        except Exception as e:
            logger.error(f"Error creating synthetic data: {e}")
            return {}
    
    def _validate_market_data(self, data: Dict[str, Any]) -> bool:
        """Validate market data quality and completeness."""
        
        if not self.enable_data_validation:
            return True
        
        try:
            # Check required fields
            for field in self.required_fields:
                if field not in data:
                    logger.warning(f"Missing required field: {field}")
                    return False
            
            # Check data types and ranges
            price = data.get('current_price', 0)
            if not isinstance(price, (int, float)) or price <= 0:
                logger.warning(f"Invalid price data: {price}")
                return False
            
            rsi = data.get('rsi', 0)
            if not isinstance(rsi, (int, float)) or rsi < 0 or rsi > 100:
                logger.warning(f"Invalid RSI data: {rsi}")
                return False
            
            volume = data.get('volume', 0)
            if not isinstance(volume, (int, float)) or volume < 0:
                logger.warning(f"Invalid volume data: {volume}")
                return False
            
            # Check for reasonable price relationships
            sma_short = data.get('sma_short', price)
            sma_long = data.get('sma_long', price)
            
            # SMAs should be within reasonable range of current price
            if abs(sma_short - price) / price > 0.1:  # 10% deviation
                logger.warning(f"SMA short too far from price: {sma_short} vs {price}")
                return False
            
            if abs(sma_long - price) / price > 0.2:  # 20% deviation
                logger.warning(f"SMA long too far from price: {sma_long} vs {price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating market data: {e}")
            return False
    
    def get_current_regime(self, market_data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Get current market regime analysis.
        
        Args:
            market_data: Optional market data, will fetch if not provided
            
        Returns:
            Dictionary containing regime analysis
        """
        
        try:
            if not self.regime_analyzer:
                logger.warning("Regime analyzer not available")
                return None
            
            # Get market data if not provided
            if market_data is None:
                market_data = self.get_live_market_data()
            
            if not market_data:
                logger.warning("No market data available for regime analysis")
                return None
            
            # Perform regime analysis
            regime_analysis = self.regime_analyzer.analyze_current_regime(market_data)
            
            if regime_analysis:
                regime_info = {
                    'regime': regime_analysis.regime.value,
                    'confidence': regime_analysis.confidence,
                    'metrics': {
                        'trend_strength': regime_analysis.metrics.trend_strength,
                        'volatility_ratio': regime_analysis.metrics.volatility_ratio,
                        'volume_ratio': regime_analysis.metrics.volume_ratio,
                        'momentum_score': regime_analysis.metrics.momentum_score
                    },
                    'timestamp': datetime.now().isoformat(),
                    'data_source': market_data.get('data_source', 'unknown')
                }
                
                logger.debug(f"Regime detected: {regime_info['regime']} (confidence: {regime_info['confidence']:.2f})")
                return regime_info
            else:
                logger.warning("Regime analysis failed")
                return None
                
        except Exception as e:
            logger.error(f"Error getting current regime: {e}")
            return None
    
    def start_background_monitoring(self, update_interval: int = 300):
        """
        Start background monitoring of market data and regime changes.
        
        Args:
            update_interval: Update interval in seconds (default: 5 minutes)
        """
        
        try:
            self.update_interval = update_interval
            
            def monitoring_loop():
                logger.info(f"Starting background monitoring (interval: {update_interval}s)")
                
                while self.data_sources_active:
                    try:
                        # Update market data
                        market_data = self.get_live_market_data(force_refresh=True)
                        
                        # Get regime analysis
                        regime_info = self.get_current_regime(market_data)
                        
                        if regime_info:
                            logger.debug(f"Background update: {regime_info['regime']} regime detected")
                        
                        # Wait for next update
                        time.sleep(update_interval)
                        
                    except Exception as e:
                        logger.error(f"Error in background monitoring: {e}")
                        time.sleep(60)  # Wait 1 minute before retrying
                
                logger.info("Background monitoring stopped")
            
            # Start monitoring thread
            self.data_sources_active = True
            monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
            monitoring_thread.start()
            
            logger.info("SUCCESS: Background monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting background monitoring: {e}")
    
    def stop_background_monitoring(self):
        """Stop background monitoring."""
        
        self.data_sources_active = False
        logger.info("Background monitoring stopped")
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status for monitoring."""
        
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'regime_analyzer_available': self.regime_analyzer is not None,
                'data_cache_valid': self._is_cache_valid(),
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'update_count': self.update_count,
                'error_count': self.error_count,
                'last_error': self.last_error,
                'background_monitoring': self.data_sources_active,
                'update_interval': self.update_interval
            }
            
            # Add cache info
            if self.data_cache:
                status['cached_data'] = {
                    'fields': list(self.data_cache.keys()),
                    'data_source': self.data_cache.get('data_source', 'unknown'),
                    'current_price': self.data_cache.get('current_price', 0)
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting pipeline status: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def shutdown(self):
        """Shutdown the data pipeline."""
        
        try:
            self.stop_background_monitoring()
            self.data_cache.clear()
            logger.info("Live Data Pipeline shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during pipeline shutdown: {e}")

# Global instance
_live_data_pipeline = None

def get_live_market_data(force_refresh: bool = False) -> Dict[str, Any]:
    """Get live market data for regime analysis."""
    
    global _live_data_pipeline
    
    if _live_data_pipeline is None:
        _live_data_pipeline = LiveDataPipeline()
    
    return _live_data_pipeline.get_live_market_data(force_refresh)

def get_current_regime(market_data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
    """Get current market regime analysis."""
    
    global _live_data_pipeline
    
    if _live_data_pipeline is None:
        _live_data_pipeline = LiveDataPipeline()
    
    return _live_data_pipeline.get_current_regime(market_data)

def get_pipeline_status() -> Dict[str, Any]:
    """Get current pipeline status."""
    
    global _live_data_pipeline
    
    if _live_data_pipeline is None:
        return {'status': 'not_initialized', 'timestamp': datetime.now().isoformat()}
    
    return _live_data_pipeline.get_pipeline_status()

def shutdown_pipeline():
    """Shutdown the data pipeline."""
    
    global _live_data_pipeline
    
    if _live_data_pipeline is not None:
        _live_data_pipeline.shutdown()
        _live_data_pipeline = None
