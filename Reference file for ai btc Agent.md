# BTC Trading Bot Project: Chat Digest & Reference

## Overview

This file summarizes the full ChatGPT-guided development and troubleshooting of the BTC/USD AI trading bot project (Alpaca, Gemini, Binance). It covers architecture, major code improvements, persistent configuration, bug fixes, safety settings, and process management.

---

## 1. Major Project Milestones (Chronological Order)

- **Initial Setup**: Configured for BTC/USD trading on live Alpaca account. All safety notes and risk warnings highlighted at each startup.
    
- **Trading Logic**: Confirmed working main loop, trade cycle summary, AI prompt integration, session equity tracking.
    
- **Risk Management**: Added hard-coded limits for trade value, stop loss, daily loss, minimum equity, and quantity percentage.
    
- **Log File Management**: Implemented log rotation to prevent log files from growing beyond 1MB/5MB with backup count.
    
- **Open Lot Ledger Issues**: Debugged issues with 'dust' BTC amounts and open lot mismatch; added syncing logic to always match available BTC and resolve “trailing sell failure” errors.
    
- **Persistent Dynamic Config**: Introduced `dynamic_config.json` for dynamic parameters (e.g., risk_percent, profit_target_pct), with automatic overrides in `config.py` and reloads at every trade cycle.
    
- **Tick List System**: Used and maintained a “tick list” (feature/completion checklist) for major bot improvements and ongoing tasks, color-coding completed steps in green.
    
- **HTF Trend Mismatch Handling**: Explained and confirmed “HTF trends mismatch (blocked)” means bot is correctly not trading when higher timeframes disagree.
    
- **Continuous Feedback/Learning**: Implemented detailed trade cycle logs (`trade_cycle_log.csv`) and P&L charts for iterative review.
    
- **Safe Dynamic Updates**: Allowed config value overrides by dynamic_config.json but enforced safety limits at code level, preventing accidental runaway risk/aggression.
    

---

## 2. Main Tick List (Current)

**(Use this as a reference for completed/in-progress features.)**

1. **[✔]** Regime Detection Module: Market regime detection & output
    
2. **[✔]** AI Prompt Upgrade: Context-aware prompts (regime, volatility, context, aggression)
    
3. **[✔]** Dynamic Risk & Position Sizing: Trade size adapts to volatility, regime, AI confidence
    
4. **[✔]** Multi-Factor Signal Integration: Added extra signals (volume, volatility, momentum, order book)
    
5. **[✔]** Adaptive Thresholds: Dynamic adaptation of strategy thresholds (e.g., RSI)
    
6. **[✔]** Aggression/Safety Logic (Energy Switch): Bot adjusts “energy” (risk) based on context
    
7. **[✔]** Continuous Learning & Feedback: Log outcomes, review, tune
    
8. **[✔]** Portfolio Safety Net: Hard daily/session loss caps, min equity
    
9. **[✔]** Persistent Dynamic Config: `dynamic_config.json` is loaded at every cycle
    
10. **[✔]** Log Rotation: Trading/log/AI logs all rotate and will not grow indefinitely
    
11. **[✔]** Open Lot Ledger Sync: Always sync to available BTC, resolve dust issues
    
12. **[ ]** _[Add your next features below as needed!]_
    

---

## 3. Common Issues & Solutions

- **HTF Trends Mismatch (Blocked)**: Bot correctly prevents trades when high/low timeframe trends disagree. Not a bug unless you want to relax the strategy.
    
- **Trailing Sell Failure / Dust**: If you see “trailing sell failure…insufficient balance,” it means the open lot ledger disagreed with the actual available BTC. This is fixed by syncing open lots with available BTC at every cycle, clearing dust.
    
- **Log Files Growing Too Large**: Confirmed all log files use rotation. No log will ever grow beyond its maxBytes setting, so no disk fill risk.
    
- **Dynamic Config Confusion**: Only edit `dynamic_config.json` if you want to change risk, stop, profit target, etc., on the fly. The bot will not allow unsafe overrides beyond coded limits.
    
- **No Action/No Buys or Sells**: If the trade cycle shows “No action” repeatedly, it is due to trend regime filter, loss streak, or other safety criteria.
    

---

## 4. Files Used In Project

- **main_bot.py**: Main loop, trade cycle, service init, safety logic, logging.
    
- **trade_logic.py**: Trading logic, lot management, AI integration, syncing to available BTC.
    
- **config.py**: Core config, safety limits, dynamic config override.
    
- **dynamic_config.json**: Holds live-editable dynamic settings.
    
- **ai_decision_service.py**: AI trade decision logic.
    
- **market_analysis.py**: Market indicators and regime detection.
    
- **alpaca_service.py**: Alpaca API interface.
    
- **binance_data_service.py**: Binance market data fetch/streaming.
    
- **bot_state.py**: Saving and loading session/cycle states.
    

---

## 5. Persistent Settings Example (dynamic_config.json)

```json
{
  "risk_percent": 0.01,
  "profit_target_pct": 0.03,
  "stop_percent": 0.02,
  "energy_mode": "defensive",
  "last_update": "2025-05-25T12:34:56Z"
}
```

---

## 6. Safety Net Summary

- **MAX_TRADE_VALUE_USD**: $20 per trade
    
- **MIN_PROFIT_THRESHOLD_USD**: $0.10
    
- **MAX_DAILY_LOSS_PCT**: 5%
    
- **MIN_ACCOUNT_EQUITY**: $100
    
- **Aggression Mode Hard Limits**: Cannot be overridden by dynamic config
    

---

## 7. Lessons Learned

- Always sync open lots to _available_ BTC to prevent sell failures and dust.
    
- Never let log files grow indefinitely; always set maxBytes and backups.
    
- Use persistent dynamic config for live tweaking but enforce hardcoded limits for safety.
    
- Always keep a living tick list for reference and troubleshooting.
    

---

## 8. If Starting A New Chat

1. **Upload this file** to ChatGPT at the start of your session.
    
2. **Upload latest config, dynamic_config, and trade_logic files** if troubleshooting code.
    
3. **Reference the tick list** and say which milestone or bug you’re working on.
    
4. **State any new issues since last session.**
    

---

## 9. Conversation Etiquette (for future reference)

- Always ask for or provide the _entire_ file/block of code when making changes.
    
- Never summarize changes without showing full affected code for copy-paste.
    
- Always keep code in sync across all files (no snippets!).
    
- Add to the tick list after every completed step.
    

---

## 10. Addenda

You may copy-paste this file into future conversations, or upload it as a text/markdown file for ChatGPT to ingest as reference.

---

**End of Digest.**