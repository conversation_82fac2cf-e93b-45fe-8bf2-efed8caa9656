# START OF FILE generate_rsi_labels.py

import pandas as pd
import numpy as np
import os
import sys
import time # Added for retry mechanism

# --- Configuration ---
INPUT_FILENAME = "binance_btcusdt_1m.csv"
OUTPUT_FILENAME = "rsi_labeled_data.csv"
RSI_PERIOD = 7 

# RSI thresholds for labeling (ADJUSTED FOR MORE SELECTIVE TRADES)
RSI_BUY_THRESHOLD = 25 
RSI_SELL_THRESHOLD = 75

# Periods for additional indicators
SMA_PERIODS = [7, 10, 14, 50] # FIXED: Added 10 to SMA_PERIODS
EMA_PERIODS = [7, 14, 50]
ATR_PERIOD = 14
MOMENTUM_PERIOD = 10
MACD_FAST_PERIOD = 12
MACD_SLOW_PERIOD = 26
MACD_SIGNAL_PERIOD = 9

# Higher timeframe for trend
HIGHER_TIMEFRAME_PERIOD_MINUTES = 5 
HIGHER_SMA_PERIOD = 10 # For 5m trend

# Regime detection thresholds (consistent with market_analysis.py)
TREND_REL_THRESHOLD = 0.002 # 0.2% difference in SMAs
VOLATILE_REL_THRESHOLD = 0.0015 # 0.15% relative volatility (std dev)
ATR_REL_THRESHOLD = 0.0015 # 0.15% relative ATR
QUIET_REL_THRESHOLD = 0.0005 # 0.05% for quiet (low volatility, low ATR)

# --- File Save Retry Configuration ---
MAX_SAVE_RETRIES = 5
SAVE_RETRY_DELAY_SECONDS = 5


def calculate_rsi(df, window):
    """Calculate Relative Strength Index (RSI)."""
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).fillna(0)
    loss = (-delta.where(delta < 0, 0)).fillna(0)

    avg_gain = gain.ewm(com=window - 1, min_periods=window).mean()
    avg_loss = loss.ewm(com=window - 1, min_periods=window).mean()

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_sma(df, column, window):
    """Calculate Simple Moving Average (SMA)."""
    return df[column].rolling(window=window).mean()

def calculate_ema(df, column, window):
    """Calculate Exponential Moving Average (EMA)."""
    return df[column].ewm(span=window, adjust=False).mean()

def calculate_atr(df, window):
    """Calculate Average True Range (ATR)."""
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = np.max(ranges, axis=1)
    atr = true_range.ewm(span=window, adjust=False).mean()
    return atr

def calculate_momentum(df, column, window):
    """Calculate Momentum."""
    return df[column].diff(window)

def calculate_macd(df, column, fast_period, slow_period, signal_period):
    """Calculate MACD, MACD Signal, and MACD Histogram."""
    exp1 = df[column].ewm(span=fast_period, adjust=False).mean()
    exp2 = df[column].ewm(span=slow_period, adjust=False).mean()
    macd = exp1 - exp2
    signal = macd.ewm(span=signal_period, adjust=False).mean()
    hist = macd - signal
    return macd, signal, hist

def calculate_volatility(df, column):
    """Calculates standard deviation of price as a measure of volatility."""
    return df[column].rolling(window=20).std() # Use a 20-period standard deviation for volatility

def detect_regime(sma_short: float | None, sma_long: float | None, volatility: float | None, atr: float | None) -> str:
    """
    Detects current market regime (e.g., trending, ranging, volatile, quiet)
    based on SMAs, volatility, and ATR.
    (Copied from market_analysis.py logic)
    """
    if sma_short is None or sma_long is None or volatility is None or atr is None or sma_long == 0:
        return "unknown"
    
    # Relativize metrics to price for consistent comparison
    trend_strength = abs(sma_short - sma_long)
    rel_trend = trend_strength / sma_long
    rel_vol = volatility / sma_long
    rel_atr = atr / sma_long 

    if rel_trend > TREND_REL_THRESHOLD and (rel_vol > VOLATILE_REL_THRESHOLD or rel_atr > ATR_REL_THRESHOLD):
        return "trending_volatile"
    elif rel_trend > TREND_REL_THRESHOLD:
        return "trending"
    elif rel_vol > VOLATILE_REL_THRESHOLD or rel_atr > ATR_REL_THRESHOLD:
        return "volatile"
    elif rel_vol < QUIET_REL_THRESHOLD and rel_atr < QUIET_REL_THRESHOLD:
        return "quiet"
    else:
        return "ranging"

def compute_trend(sma_short: float | None, sma_long: float | None, current_price: float | None, trend_threshold: float) -> str:
    """
    Determines trend based on SMA crossover and price relation to SMAs.
    (Adapted from market_analysis.py logic, using a fixed threshold here).
    """
    if sma_short is None or sma_long is None or current_price is None or sma_long == 0:
        return "unknown"

    if (sma_short - sma_long) > (trend_threshold * sma_long):
        return "bullish"
    elif (sma_long - sma_short) > (trend_threshold * sma_long):
        return "bearish"
    else:
        return "neutral"


def generate_features_and_labels():
    """
    Reads the main historical data file, calculates various technical indicators (features),
    and generates 'BUY', 'SELL', or 'HOLD' labels based on RSI thresholds.
    Saves the result to a new CSV file.
    """
    print(f"Loading data from {INPUT_FILENAME} for feature generation and labeling...")
    try:
        df = pd.read_csv(INPUT_FILENAME)
    except FileNotFoundError:
        print(f"Error: '{INPUT_FILENAME}' not found. Please ensure merge_data_files.py has been run successfully.")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading '{INPUT_FILENAME}': {e}")
        sys.exit(1)

    print(f"Initial data shape: {df.shape}")
    
    # Ensure 'timestamp' is datetime and data is sorted
    df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce', utc=True)
    df.dropna(subset=['timestamp'], inplace=True)
    df.sort_values('timestamp', inplace=True)
    
    if df.empty:
        print("Error: DataFrame is empty after timestamp processing. Cannot generate features/labels.")
        sys.exit(1)

    # Ensure core OHLCV columns are numeric
    ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
    for col in ohlcv_cols:
        if col not in df.columns:
            print(f"Error: Required column '{col}' not found in input data. Cannot generate features.")
            sys.exit(1)
        # Use .loc for explicit assignment to avoid SettingWithCopyWarning
        df.loc[:, col] = pd.to_numeric(df[col], errors='coerce').fillna(method='ffill').fillna(df[col].mean())


    # --- Calculate Features ---
    print(f"Calculating RSI with period {RSI_PERIOD}...")
    df['rsi'] = calculate_rsi(df, RSI_PERIOD)
    df['rsi'] = df['rsi'].fillna(50) # Fill NaN RSI values (e.g., at the beginning of the series)

    print("Calculating SMAs...")
    for period in SMA_PERIODS:
        df[f'sma_{period}'] = calculate_sma(df, 'close', period)
        df.loc[:, f'sma_{period}'] = df[f'sma_{period}'].fillna(df['close']) # Fill NaNs with close price

    print("Calculating EMAs...")
    for period in EMA_PERIODS:
        df[f'ema_{period}'] = calculate_ema(df, 'close', period)
        df.loc[:, f'ema_{period}'] = df[f'ema_{period}'].fillna(df['close']) # Fill NaNs with close price

    print(f"Calculating ATR with period {ATR_PERIOD}...")
    df['atr'] = calculate_atr(df, ATR_PERIOD)
    df.loc[:, 'atr'] = df['atr'].fillna(df['atr'].mean()) # Fill NaNs with mean ATR

    print(f"Calculating Volatility (Std Dev) with period 20...")
    df['volatility'] = calculate_volatility(df, 'close')
    df.loc[:, 'volatility'] = df['volatility'].fillna(df['volatility'].mean()) # Fill NaNs with mean volatility

    print(f"Calculating Momentum with period {MOMENTUM_PERIOD}...")
    df['momentum_10'] = calculate_momentum(df, 'close', MOMENTUM_PERIOD)
    df.loc[:, 'momentum_10'] = df['momentum_10'].fillna(0) # Fill NaNs with 0

    print("Calculating MACD...")
    df['macd'], df['macd_signal'], df['macd_hist'] = calculate_macd(df, 'close', MACD_FAST_PERIOD, MACD_SLOW_PERIOD, MACD_SIGNAL_PERIOD)
    df.loc[:, 'macd'] = df['macd'].fillna(0)
    df.loc[:, 'macd_signal'] = df['macd_signal'].fillna(0)
    df.loc[:, 'macd_hist'] = df['macd_hist'].fillna(0)

    # --- Calculate Trend and Regime (NEW) ---
    print("Calculating Trend and Regime features...")
    # Trend based on 1m data
    _fixed_trend_threshold_for_labeling = 0.001 
    df['trend'] = df.apply(
        lambda row: compute_trend(row[f'sma_{SMA_PERIODS[0]}'], row[f'sma_{SMA_PERIODS[1]}'], row['close'], _fixed_trend_threshold_for_labeling),
        axis=1
    )
    # Regime based on 1m data
    df['regime'] = df.apply(
        lambda row: detect_regime(row[f'sma_{SMA_PERIODS[0]}'], row[f'sma_{SMA_PERIODS[1]}'], row['volatility'], row['atr']),
        axis=1
    )

    # Calculate 5m trend (NEW)
    print(f"Calculating 5-minute trend (trend_5m) feature...")
    df_5m = df.resample(f'{HIGHER_TIMEFRAME_PERIOD_MINUTES}min', on='timestamp').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        f'sma_{HIGHER_SMA_PERIOD}': 'last' # Need a SMA for 5m trend calculation
    }).dropna()
    
    # Ensure 5m EMA for trend_5m
    df_5m['ema_5m_trend'] = calculate_ema(df_5m, 'close', HIGHER_SMA_PERIOD)
    df_5m.loc[:, 'ema_5m_trend'] = df_5m['ema_5m_trend'].fillna(df_5m['close'])

    df_5m['trend_5m_calculated'] = df_5m.apply(
        lambda row: "bullish" if row['close'] > row['ema_5m_trend'] else "bearish" if row['close'] < row['ema_5m_trend'] else "neutral",
        axis=1
    )
    
    # Merge 5m trend back to 1m DataFrame (forward fill to align 5m trend to all 1m bars within that 5m period)
    # Reset index to make 'timestamp' a column for merging, then set back
    df_5m_reset = df_5m[['trend_5m_calculated']].reset_index()
    df_5m_reset.rename(columns={'timestamp': 'timestamp_5m_resampled'}, inplace=True)
    
    # Use merge_asof to forward fill the 5m trend onto the 1m data
    # Need to sort both DFs by timestamp before merge_asof
    df.sort_values('timestamp', inplace=True)
    df_5m_reset.sort_values('timestamp_5m_resampled', inplace=True)

    df = pd.merge_asof(
        df, 
        df_5m_reset, 
        left_on='timestamp', 
        right_on='timestamp_5m_resampled', 
        direction='backward', 
        tolerance=pd.Timedelta(f'{HIGHER_TIMEFRAME_PERIOD_MINUTES-1}min') # Fill for bars within the 5-min window
    )
    df.rename(columns={'trend_5m_calculated': 'trend_5m'}, inplace=True)
    df.drop(columns=['timestamp_5m_resampled'], inplace=True, errors='ignore')
    
    # Fill any remaining NaNs in trend_5m (e.g., very beginning of data)
    df['trend_5m'] = df['trend_5m'].fillna('unknown')


    # Handle other columns from previous script if they exist and are needed
    placeholder_cols = [
        'num_trades', 'quote_asset_volume', 'taker_buy_base', 'taker_buy_quote',
        'close_time', 'ignore'
    ]
    for col in placeholder_cols:
        if col not in df.columns:
            if col in ['num_trades', 'quote_asset_volume', 'taker_buy_base', 'taker_buy_quote']:
                df[col] = 0.0
            elif col == 'close_time':
                df[col] = (df['timestamp'] + pd.Timedelta(minutes=1)).astype(np.int64) // 10**6 # Unix ms
            else: # 'ignore' or any other arbitrary
                df[col] = ''
        elif pd.api.types.is_numeric_dtype(df[col]): # Ensure numeric placeholder cols are indeed numeric
             df.loc[:, col] = pd.to_numeric(df[col], errors='coerce').fillna(0)


    # --- Generate Labels ---
    print("Generating 'BUY', 'SELL', 'HOLD' labels based on RSI thresholds (ADJUSTED)...")
    df['label'] = 'HOLD' # Default label
    df.loc[df['rsi'] < RSI_BUY_THRESHOLD, 'label'] = 'BUY'
    df.loc[df['rsi'] > RSI_SELL_THRESHOLD, 'label'] = 'SELL'

    print(f"Generated {len(df)} features and labels. Label distribution:")
    print(df['label'].value_counts())

    # Save the labeled dataset with retry mechanism
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), OUTPUT_FILENAME)
    for i in range(MAX_SAVE_RETRIES):
        try:
            df.to_csv(output_path, index=False)
            print(f"Labeled data saved successfully to '{output_path}'.")
            break # Exit loop if save is successful
        except OSError as e:
            print(f"Warning: Could not save to '{output_path}' (attempt {i+1}/{MAX_SAVE_RETRIES}): {e}")
            if i < MAX_SAVE_RETRIES - 1:
                print(f"Retrying in {SAVE_RETRY_DELAY_SECONDS} seconds...")
                time.sleep(SAVE_RETRY_DELAY_SECONDS)
            else:
                print(f"Error: Failed to save labeled data to '{output_path}' after {MAX_SAVE_RETRIES} attempts. Please ensure the file is not locked or in use by another program.")
                sys.exit(1)
        except Exception as e:
            print(f"An unexpected error occurred while saving to '{output_path}': {e}")
            sys.exit(1)
            

    print(f"Data range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    print(f"First few rows with new 'rsi' and 'label' columns:\n{df[['timestamp', 'close', 'rsi', 'label']].head()}")
    print(f"Example rows with new technical indicators and market context:\n{df[['timestamp', 'close', 'rsi', 'sma_7', 'ema_7', 'atr', 'macd', 'trend', 'trend_5m', 'regime', 'label']].head()}")

if __name__ == "__main__":
    # Check for pandas and numpy dependencies
    try:
        import pandas as pd
        import numpy as np
    except ImportError:
        print("\n'pandas' and/or 'numpy' not found. Installing now...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "numpy"])
            print("Dependencies installed successfully. Please re-run the script.")
            sys.exit(0)
        except Exception as e:
            print(f"Failed to install dependencies: {e}")
            print("Please install them manually: pip install pandas numpy")
            sys.exit(1)
    
    generate_features_and_labels()