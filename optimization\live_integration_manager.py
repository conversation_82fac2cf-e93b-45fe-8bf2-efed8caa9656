#!/usr/bin/env python3
"""
Live Integration Manager - Phase 4 Implementation

Seamlessly integrates Phase 3 regime-aware optimization with the live trading bot.
This manager acts as a bridge between the existing dynamic parameter system and 
the new regime-specific optimization system.

Key Features:
- Drop-in replacement for dynamic_parameter_service
- Automatic regime detection and parameter switching
- Safety validation and emergency fallbacks
- Smooth integration with existing bot architecture

Author: Bitcoin AI Trading Bot - Phase 4 Live Integration
Date: July 29, 2025
"""

import sys
import os
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal

# Add optimization directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Import Phase 3 components
try:
    from phase3_integration_system import Phase3IntegrationSystem
    from market_regime_analyzer import MarketRegime
    PHASE3_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Phase 3 components not available: {e}")
    PHASE3_AVAILABLE = False

# Import existing bot components
try:
    import config
    import dynamic_parameter_service
except ImportError as e:
    logging.error(f"Bot components not available: {e}")
    raise

logger = logging.getLogger("TradingBotApp.LiveIntegration")

class LiveIntegrationManager:
    """
    Live Integration Manager for Phase 4.
    
    This class seamlessly integrates Phase 3 regime-aware optimization
    with the existing trading bot architecture.
    """
    
    def __init__(self):
        """Initialize the live integration manager."""
        
        self.phase3_system = None
        self.phase3_enabled = False
        self.fallback_to_original = False
        self.last_regime_check = None
        self.last_parameters = {}
        self.parameter_change_log = []
        
        # Configuration
        self.enable_phase3 = True  # Can be disabled for testing
        self.safety_validation = True
        self.max_parameter_change_rate = 0.5  # Max 50% change per update
        
        # Initialize Phase 3 system if available
        if PHASE3_AVAILABLE and self.enable_phase3:
            self._initialize_phase3_system()
        else:
            logger.warning("Phase 3 system disabled or unavailable, using original dynamic parameters")
            self.fallback_to_original = True
    
    def _initialize_phase3_system(self) -> bool:
        """Initialize the Phase 3 integration system."""
        
        try:
            logger.info("Initializing Phase 3 regime-aware optimization system...")
            
            # Create Phase 3 system with production settings
            self.phase3_system = Phase3IntegrationSystem(
                optimization_interval_hours=24,  # Daily optimization
                regime_check_interval_minutes=5,  # Check regime every 5 minutes
                enable_auto_optimization=True    # Enable automatic optimization
            )
            
            # Initialize with dummy market data (will be updated on first call)
            dummy_market_data = {
                'sma_short': 45000, 'sma_long': 44000, 'current_price': 45000,
                'volatility': 50, 'volume': 1000, 'avg_volume': 1000,
                'rsi': 50, 'macd_hist': 0, 'atr': 200
            }
            
            if self.phase3_system.initialize_system(dummy_market_data):
                self.phase3_enabled = True
                logger.info("SUCCESS: Phase 3 system initialized successfully")
                return True
            else:
                logger.error("ERROR: Phase 3 system initialization failed")
                self.fallback_to_original = True
                return False
                
        except Exception as e:
            logger.error(f"Error initializing Phase 3 system: {e}")
            self.fallback_to_original = True
            return False
    
    def get_dynamic_parameters(self, market_data: dict, ai_data: dict, trade_history: list = None) -> dict:
        """
        Get dynamic trading parameters with Phase 3 regime awareness.
        
        This method is a drop-in replacement for dynamic_parameter_service.get_dynamic_parameters()
        that adds regime-aware optimization while maintaining full compatibility.
        
        Args:
            market_data: Current market analysis data
            ai_data: AI decision context
            trade_history: Recent trade history
            
        Returns:
            Optimized trading parameters
        """
        
        try:
            # If Phase 3 is enabled and working, use regime-aware parameters
            if self.phase3_enabled and not self.fallback_to_original:
                return self._get_phase3_parameters(market_data, ai_data, trade_history)
            else:
                # Fallback to original dynamic parameter system
                return self._get_original_parameters(market_data, ai_data, trade_history)
                
        except Exception as e:
            logger.error(f"Error in get_dynamic_parameters: {e}")
            # Emergency fallback to original system
            return self._get_original_parameters(market_data, ai_data, trade_history)
    
    def _get_phase3_parameters(self, market_data: dict, ai_data: dict, trade_history: list) -> dict:
        """Get parameters using Phase 3 regime-aware optimization."""
        
        try:
            # Convert market data to Phase 3 format
            phase3_market_data = self._convert_market_data_format(market_data)
            
            # Get regime-aware parameters from Phase 3 system
            regime_params = self.phase3_system.get_current_parameters(phase3_market_data)
            
            # Convert Phase 3 parameters to bot format
            bot_params = self._convert_phase3_to_bot_format(regime_params)
            
            # Apply safety validation
            if self.safety_validation:
                bot_params = self._validate_parameter_safety(bot_params)
            
            # Log parameter changes
            self._log_parameter_changes(bot_params, "Phase3")
            
            # Store for comparison
            self.last_parameters = bot_params.copy()
            
            logger.info(f"Phase 3 parameters applied: {len(bot_params)} parameters")
            return bot_params
            
        except Exception as e:
            logger.error(f"Error getting Phase 3 parameters: {e}")
            # Fallback to original system
            return self._get_original_parameters(market_data, ai_data, trade_history)
    
    def _get_original_parameters(self, market_data: dict, ai_data: dict, trade_history: list) -> dict:
        """Get parameters using original dynamic parameter system."""
        
        try:
            # Use original dynamic parameter service
            original_params = dynamic_parameter_service.get_dynamic_parameters(
                market_data, ai_data, trade_history
            )
            
            # Log parameter source
            self._log_parameter_changes(original_params, "Original")
            
            return original_params
            
        except Exception as e:
            logger.error(f"Error getting original parameters: {e}")
            # Ultimate fallback to config defaults
            return self._get_config_defaults()
    
    def _convert_market_data_format(self, market_data: dict) -> dict:
        """Convert bot market data format to Phase 3 format."""
        
        try:
            # Map bot market data fields to Phase 3 expected fields
            phase3_data = {
                'current_price': market_data.get('avg_price', market_data.get('close', 45000)),
                'sma_short': market_data.get('sma_short', market_data.get('sma_5', 45000)),
                'sma_long': market_data.get('sma_long', market_data.get('sma_10', 44000)),
                'rsi': market_data.get('rsi', 50),
                'macd_hist': market_data.get('macd_hist', 0),
                'atr': market_data.get('atr', 200),
                'volume': market_data.get('volume', 1000),
                'avg_volume': market_data.get('avg_volume', 1000),
                'volatility': market_data.get('volatility', 50)
            }
            
            return phase3_data
            
        except Exception as e:
            logger.error(f"Error converting market data format: {e}")
            # Return safe defaults
            return {
                'current_price': 45000, 'sma_short': 45000, 'sma_long': 44000,
                'rsi': 50, 'macd_hist': 0, 'atr': 200,
                'volume': 1000, 'avg_volume': 1000, 'volatility': 50
            }
    
    def _convert_phase3_to_bot_format(self, phase3_params) -> dict:
        """Convert Phase 3 parameters to bot format."""

        try:
            # Ensure phase3_params is a dictionary
            if not isinstance(phase3_params, dict):
                logger.error(f"Expected dict for phase3_params, got {type(phase3_params)}: {phase3_params}")
                return self._get_config_defaults()

            # Map Phase 3 parameters to bot expected format
            bot_params = {
                'risk_percent': phase3_params.get('min_qty_pct', 0.02),
                'stop_percent': phase3_params.get('stop_percent', 0.02),
                'profit_targets': [
                    phase3_params.get('dip_buy_profit_target_pct', 0.02),
                    phase3_params.get('momentum_buy_profit_target_pct', 0.015),
                    phase3_params.get('granular_take_profit_pct', 0.01)
                ],
                'profit_shares': [0.5, 0.3, 0.2]  # Standard profit taking shares
            }
            
            # Add additional parameters that might be used
            bot_params.update({
                'trail_profit_buffer_pct': phase3_params.get('trail_profit_buffer_pct', 0.005),
                'max_trade_value_usd': phase3_params.get('max_trade_value_usd', 50),
                'cash_reserve_usd': phase3_params.get('cash_reserve_usd', 250)
            })
            
            return bot_params
            
        except Exception as e:
            logger.error(f"Error converting Phase 3 parameters: {e}")
            return self._get_config_defaults()
    
    def _validate_parameter_safety(self, params: dict) -> dict:
        """Validate parameter safety and apply limits."""
        
        try:
            validated_params = params.copy()
            
            # Apply safety limits
            validated_params['risk_percent'] = max(0.005, min(0.1, params.get('risk_percent', 0.02)))
            validated_params['stop_percent'] = max(0.005, min(0.05, params.get('stop_percent', 0.02)))
            
            # Validate profit targets
            profit_targets = params.get('profit_targets', [0.02, 0.015, 0.01])
            validated_targets = [max(0.005, min(0.1, target)) for target in profit_targets]
            validated_params['profit_targets'] = validated_targets
            
            # Check for excessive parameter changes
            if self.last_parameters:
                for key in ['risk_percent', 'stop_percent']:
                    if key in self.last_parameters and key in validated_params:
                        old_val = self.last_parameters[key]
                        new_val = validated_params[key]
                        change_ratio = abs(new_val - old_val) / old_val if old_val > 0 else 0
                        
                        if change_ratio > self.max_parameter_change_rate:
                            # Limit the change rate
                            direction = 1 if new_val > old_val else -1
                            limited_val = old_val * (1 + direction * self.max_parameter_change_rate)
                            validated_params[key] = limited_val
                            logger.warning(f"Limited {key} change: {new_val:.4f} → {limited_val:.4f}")
            
            return validated_params
            
        except Exception as e:
            logger.error(f"Error validating parameter safety: {e}")
            return self._get_config_defaults()
    
    def _get_config_defaults(self) -> dict:
        """Get safe default parameters from config."""
        
        return {
            'risk_percent': float(config.RISK_PERCENT),
            'stop_percent': float(config.STOP_PERCENT),
            'profit_targets': [float(p) for p in config.TAKE_PROFITS],
            'profit_shares': [float(s) for s in config.TAKE_PROFIT_SHARES]
        }
    
    def _log_parameter_changes(self, params: dict, source: str):
        """Log parameter changes for monitoring."""
        
        try:
            # Get regime info safely
            regime_info = 'N/A'
            if self.phase3_system and hasattr(self.phase3_system, 'current_regime_analysis'):
                regime_analysis = self.phase3_system.current_regime_analysis
                if hasattr(regime_analysis, 'regime'):
                    regime_info = regime_analysis.regime.value if hasattr(regime_analysis.regime, 'value') else str(regime_analysis.regime)
                elif isinstance(regime_analysis, dict):
                    regime_info = regime_analysis.get('regime', 'unknown')

            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'source': source,
                'parameters': params.copy(),
                'regime': regime_info
            }
            
            self.parameter_change_log.append(log_entry)
            
            # Keep only last 100 entries
            if len(self.parameter_change_log) > 100:
                self.parameter_change_log = self.parameter_change_log[-100:]
            
            # Log key changes
            if source == "Phase3" and hasattr(self.phase3_system, 'current_regime_analysis'):
                regime_analysis = self.phase3_system.current_regime_analysis
                if hasattr(regime_analysis, 'regime'):
                    regime = regime_analysis.regime.value if hasattr(regime_analysis.regime, 'value') else str(regime_analysis.regime)
                    logger.info(f"Parameters from {source} (Regime: {regime}): "
                               f"Risk={params.get('risk_percent', 0):.3f}, "
                               f"Stop={params.get('stop_percent', 0):.3f}")
                else:
                    logger.info(f"Parameters from {source}: "
                               f"Risk={params.get('risk_percent', 0):.3f}, "
                               f"Stop={params.get('stop_percent', 0):.3f}")
            
        except Exception as e:
            logger.error(f"Error logging parameter changes: {e}")
    
    def get_system_status(self) -> dict:
        """Get current system status for monitoring."""
        
        try:
            status = {
                'phase3_enabled': self.phase3_enabled,
                'fallback_active': self.fallback_to_original,
                'last_update': datetime.now().isoformat(),
                'parameter_changes_logged': len(self.parameter_change_log)
            }
            
            if self.phase3_system:
                phase3_status = self.phase3_system.get_system_status()
                status.update({
                    'current_regime': phase3_status.get('current_regime', {}),
                    'system_health': phase3_status.get('system_active', False)
                })
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def shutdown(self):
        """Shutdown the integration manager."""
        
        try:
            if self.phase3_system:
                self.phase3_system.shutdown_system()
            logger.info("Live Integration Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

# Global instance for the bot to use
_live_integration_manager = None

def get_dynamic_parameters(market_data: dict, ai_data: dict, trade_history: list = None) -> dict:
    """
    Drop-in replacement for dynamic_parameter_service.get_dynamic_parameters()
    
    This function provides the same interface as the original but adds
    Phase 3 regime-aware optimization capabilities.
    """
    
    global _live_integration_manager
    
    # Initialize manager if not already done
    if _live_integration_manager is None:
        _live_integration_manager = LiveIntegrationManager()
    
    # Get parameters using the integration manager
    return _live_integration_manager.get_dynamic_parameters(market_data, ai_data, trade_history)

def get_system_status() -> dict:
    """Get current system status for monitoring."""
    
    global _live_integration_manager
    
    if _live_integration_manager is None:
        return {'status': 'not_initialized', 'timestamp': datetime.now().isoformat()}
    
    return _live_integration_manager.get_system_status()

def shutdown_integration():
    """Shutdown the integration system."""
    
    global _live_integration_manager
    
    if _live_integration_manager is not None:
        _live_integration_manager.shutdown()
        _live_integration_manager = None
