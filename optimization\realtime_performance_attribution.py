"""
Real-Time Performance Attribution System

This module provides comprehensive performance tracking that attributes P&L to specific factors
including regime, parameters, market conditions, and trading decisions. It offers real-time
analytics and reporting capabilities for enhanced trading insights.

Key Features:
- Factor-based P&L attribution (regime, parameters, market conditions)
- Real-time performance analytics and reporting
- Trade-level attribution analysis
- Performance decomposition by time periods
- Risk-adjusted performance metrics
- Comparative analysis across different factors

Author: Augment Agent
Date: 2025-07-30
"""

import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import threading
import time
import os

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class TradeAttribution:
    """Attribution data for a single trade."""
    trade_id: str
    timestamp: datetime
    action: str  # BUY/SELL
    quantity: float
    price: float
    pnl: float
    fees: float
    
    # Market regime attribution
    regime: str
    regime_confidence: float
    
    # Parameter attribution
    parameters: Dict[str, Any]
    parameter_source: str  # 'static', 'regime_optimized', 'dynamic'
    
    # Market condition attribution
    market_conditions: Dict[str, float]
    volatility_regime: str
    trend_strength: float
    
    # Technical indicator attribution
    rsi_value: float
    macd_signal: float
    volume_spike: float
    
    # AI decision attribution
    ai_confidence: float
    ai_features: Dict[str, float]
    
    # Risk attribution
    position_size_factor: float
    risk_level: str
    drawdown_protection: bool

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    total_pnl: float
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration: float
    total_trades: int
    winning_trades: int
    losing_trades: int

@dataclass
class AttributionBreakdown:
    """Performance attribution breakdown by factor."""
    factor_name: str
    factor_value: str
    pnl_contribution: float
    trade_count: int
    win_rate: float
    avg_pnl_per_trade: float
    total_exposure_time: float
    risk_adjusted_return: float

@dataclass
class RealTimeReport:
    """Real-time performance report."""
    timestamp: datetime
    overall_metrics: PerformanceMetrics
    regime_attribution: List[AttributionBreakdown]
    parameter_attribution: List[AttributionBreakdown]
    market_condition_attribution: List[AttributionBreakdown]
    time_period_attribution: List[AttributionBreakdown]
    top_performing_factors: List[Tuple[str, float]]
    risk_metrics: Dict[str, float]

class RealTimePerformanceAttribution:
    """
    Real-time performance attribution system for comprehensive trading analytics.
    
    This system tracks and attributes trading performance to various factors including
    market regimes, trading parameters, market conditions, and AI decisions.
    """
    
    def __init__(self, 
                 max_history_days: int = 30,
                 attribution_window_hours: int = 24,
                 update_interval_seconds: int = 60):
        """
        Initialize the real-time performance attribution system.
        
        Args:
            max_history_days: Maximum days of trade history to maintain
            attribution_window_hours: Time window for attribution analysis
            update_interval_seconds: Interval for real-time updates
        """
        self.max_history_days = max_history_days
        self.attribution_window_hours = attribution_window_hours
        self.update_interval_seconds = update_interval_seconds
        
        # Data storage
        self.trade_attributions: List[TradeAttribution] = []
        self.performance_history: deque = deque(maxlen=1000)
        self.attribution_cache: Dict[str, Any] = {}
        
        # Real-time tracking
        self.current_session_start = datetime.now(timezone.utc)
        self.last_update_time = datetime.now(timezone.utc)
        self.is_running = False
        self.update_thread: Optional[threading.Thread] = None
        
        # Performance tracking
        self.session_metrics = PerformanceMetrics(
            total_pnl=0.0, win_rate=0.0, profit_factor=0.0, sharpe_ratio=0.0,
            max_drawdown=0.0, avg_trade_duration=0.0, total_trades=0,
            winning_trades=0, losing_trades=0
        )
        
        # Attribution tracking
        self.regime_performance: Dict[str, List[float]] = defaultdict(list)
        self.parameter_performance: Dict[str, List[float]] = defaultdict(list)
        self.market_condition_performance: Dict[str, List[float]] = defaultdict(list)
        
        # File paths
        self.attribution_file = "performance_attribution.json"
        self.report_file = "realtime_performance_report.json"
        
        logger.info("Real-time performance attribution system initialized")
    
    def add_trade_attribution(self, trade_data: Dict[str, Any], 
                            market_data: Dict[str, Any],
                            ai_data: Dict[str, Any],
                            parameters: Dict[str, Any]) -> None:
        """
        Add a new trade with full attribution data.
        
        Args:
            trade_data: Trade execution data
            market_data: Market conditions at trade time
            ai_data: AI decision data
            parameters: Trading parameters used
        """
        try:
            # Create trade attribution
            attribution = TradeAttribution(
                trade_id=trade_data.get('order_id', ''),
                timestamp=datetime.now(timezone.utc),
                action=trade_data.get('action', ''),
                quantity=float(trade_data.get('quantity', 0)),
                price=float(trade_data.get('price', 0)),
                pnl=float(trade_data.get('realized_pl', 0)),
                fees=float(trade_data.get('fee', 0)),
                
                # Market regime attribution
                regime=market_data.get('current_regime', 'unknown'),
                regime_confidence=float(market_data.get('regime_confidence', 0)),
                
                # Parameter attribution
                parameters=parameters.copy(),
                parameter_source=parameters.get('source', 'static'),
                
                # Market condition attribution
                market_conditions={
                    'volatility': float(market_data.get('volatility', 0)),
                    'trend_strength': float(market_data.get('trend_strength', 0)),
                    'volume_ratio': float(market_data.get('volume_ratio', 1)),
                    'spread': float(market_data.get('spread', 0))
                },
                volatility_regime=market_data.get('volatility_regime', 'normal'),
                trend_strength=float(market_data.get('trend_strength', 0)),
                
                # Technical indicator attribution
                rsi_value=float(market_data.get('rsi', 50)),
                macd_signal=float(market_data.get('macd_signal', 0)),
                volume_spike=float(market_data.get('volume_spike', 0)),
                
                # AI decision attribution
                ai_confidence=float(ai_data.get('confidence', 0)),
                ai_features=ai_data.get('features', {}),
                
                # Risk attribution
                position_size_factor=float(parameters.get('position_size_multiplier', 1)),
                risk_level=parameters.get('risk_level', 'normal'),
                drawdown_protection=bool(parameters.get('drawdown_protection', False))
            )
            
            # Add to storage
            self.trade_attributions.append(attribution)
            
            # Update performance tracking
            self._update_performance_metrics(attribution)
            self._update_attribution_tracking(attribution)
            
            # Clean old data
            self._cleanup_old_data()
            
            # Save to file
            self._save_attribution_data()
            
            logger.info(f"Trade attribution added: {attribution.trade_id} "
                       f"PnL: {attribution.pnl:.2f} Regime: {attribution.regime}")
            
        except Exception as e:
            logger.error(f"Error adding trade attribution: {e}")
    
    def _update_performance_metrics(self, attribution: TradeAttribution) -> None:
        """Update overall performance metrics."""
        try:
            # Update session metrics
            self.session_metrics.total_pnl += attribution.pnl
            self.session_metrics.total_trades += 1
            
            if attribution.pnl > 0:
                self.session_metrics.winning_trades += 1
            else:
                self.session_metrics.losing_trades += 1
            
            # Calculate win rate
            if self.session_metrics.total_trades > 0:
                self.session_metrics.win_rate = (
                    self.session_metrics.winning_trades / self.session_metrics.total_trades * 100
                )
            
            # Calculate profit factor
            winning_pnl = sum(t.pnl for t in self.trade_attributions if t.pnl > 0)
            losing_pnl = abs(sum(t.pnl for t in self.trade_attributions if t.pnl < 0))
            
            if losing_pnl > 0:
                self.session_metrics.profit_factor = winning_pnl / losing_pnl
            else:
                self.session_metrics.profit_factor = float('inf') if winning_pnl > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            if len(self.trade_attributions) > 1:
                returns = [t.pnl for t in self.trade_attributions]
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                
                if std_return > 0:
                    self.session_metrics.sharpe_ratio = mean_return / std_return
            
            # Calculate max drawdown
            cumulative_pnl = 0
            peak = 0
            max_dd = 0
            
            for trade in self.trade_attributions:
                cumulative_pnl += trade.pnl
                if cumulative_pnl > peak:
                    peak = cumulative_pnl
                drawdown = peak - cumulative_pnl
                if drawdown > max_dd:
                    max_dd = drawdown
            
            self.session_metrics.max_drawdown = max_dd
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    def _update_attribution_tracking(self, attribution: TradeAttribution) -> None:
        """Update attribution tracking by factor."""
        try:
            # Regime attribution
            self.regime_performance[attribution.regime].append(attribution.pnl)

            # Parameter attribution
            param_key = f"{attribution.parameter_source}_{attribution.parameters.get('strategy_type', 'default')}"
            self.parameter_performance[param_key].append(attribution.pnl)

            # Market condition attribution
            volatility_key = f"volatility_{attribution.volatility_regime}"
            self.market_condition_performance[volatility_key].append(attribution.pnl)

            trend_key = f"trend_{self._categorize_trend_strength(attribution.trend_strength)}"
            self.market_condition_performance[trend_key].append(attribution.pnl)

        except Exception as e:
            logger.error(f"Error updating attribution tracking: {e}")

    def _categorize_trend_strength(self, trend_strength: float) -> str:
        """Categorize trend strength into buckets."""
        if abs(trend_strength) < 0.001:
            return "sideways"
        elif abs(trend_strength) < 0.005:
            return "weak_trend"
        elif abs(trend_strength) < 0.01:
            return "moderate_trend"
        else:
            return "strong_trend"

    def generate_attribution_breakdown(self, factor_type: str) -> List[AttributionBreakdown]:
        """Generate attribution breakdown for a specific factor type."""
        try:
            breakdowns = []

            # Get performance data based on factor type
            if factor_type == "regime":
                # Group trades by regime
                factor_groups = {}
                for trade in self.trade_attributions:
                    regime = trade.regime
                    if regime not in factor_groups:
                        factor_groups[regime] = []
                    factor_groups[regime].append(trade.pnl)

            elif factor_type == "parameter":
                # Group trades by parameter source and strategy type
                factor_groups = {}
                for trade in self.trade_attributions:
                    param_key = f"{trade.parameter_source}_{trade.parameters.get('strategy_type', 'default')}"
                    if param_key not in factor_groups:
                        factor_groups[param_key] = []
                    factor_groups[param_key].append(trade.pnl)

            elif factor_type == "market_condition":
                # Group trades by market conditions
                factor_groups = {}
                for trade in self.trade_attributions:
                    # Volatility regime grouping
                    vol_key = f"volatility_{trade.volatility_regime}"
                    if vol_key not in factor_groups:
                        factor_groups[vol_key] = []
                    factor_groups[vol_key].append(trade.pnl)

                    # Trend strength grouping
                    trend_key = f"trend_{self._categorize_trend_strength(trade.trend_strength)}"
                    if trend_key not in factor_groups:
                        factor_groups[trend_key] = []
                    factor_groups[trend_key].append(trade.pnl)
            else:
                return []

            # Generate breakdowns for each factor group
            for factor_value, pnl_list in factor_groups.items():
                if not pnl_list:
                    continue

                total_pnl = sum(pnl_list)
                trade_count = len(pnl_list)
                winning_trades = len([p for p in pnl_list if p > 0])
                win_rate = (winning_trades / trade_count * 100) if trade_count > 0 else 0
                avg_pnl = total_pnl / trade_count if trade_count > 0 else 0

                # Calculate risk-adjusted return (simplified Sharpe)
                if len(pnl_list) > 1:
                    std_pnl = np.std(pnl_list)
                    risk_adjusted = avg_pnl / std_pnl if std_pnl > 0 else 0
                else:
                    risk_adjusted = avg_pnl if avg_pnl != 0 else 0

                breakdown = AttributionBreakdown(
                    factor_name=factor_type,
                    factor_value=factor_value,
                    pnl_contribution=total_pnl,
                    trade_count=trade_count,
                    win_rate=win_rate,
                    avg_pnl_per_trade=avg_pnl,
                    total_exposure_time=trade_count * 15.0,  # Assume 15min avg trade duration
                    risk_adjusted_return=risk_adjusted
                )

                breakdowns.append(breakdown)

            # Sort by PnL contribution
            breakdowns.sort(key=lambda x: x.pnl_contribution, reverse=True)
            return breakdowns

        except Exception as e:
            logger.error(f"Error generating attribution breakdown for {factor_type}: {e}")
            return []

    def generate_realtime_report(self) -> RealTimeReport:
        """Generate comprehensive real-time performance report."""
        try:
            # Generate attribution breakdowns
            regime_attribution = self.generate_attribution_breakdown("regime")
            parameter_attribution = self.generate_attribution_breakdown("parameter")
            market_condition_attribution = self.generate_attribution_breakdown("market_condition")

            # Generate time period attribution
            time_attribution = self._generate_time_period_attribution()

            # Identify top performing factors
            top_factors = self._identify_top_performing_factors()

            # Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics()

            report = RealTimeReport(
                timestamp=datetime.now(timezone.utc),
                overall_metrics=self.session_metrics,
                regime_attribution=regime_attribution,
                parameter_attribution=parameter_attribution,
                market_condition_attribution=market_condition_attribution,
                time_period_attribution=time_attribution,
                top_performing_factors=top_factors,
                risk_metrics=risk_metrics
            )

            # Save report
            self._save_report(report)

            return report

        except Exception as e:
            logger.error(f"Error generating real-time report: {e}")
            return self._empty_report()

    def _generate_time_period_attribution(self) -> List[AttributionBreakdown]:
        """Generate attribution breakdown by time periods."""
        try:
            breakdowns = []
            now = datetime.now(timezone.utc)

            # Define time periods
            periods = [
                ("last_hour", timedelta(hours=1)),
                ("last_4_hours", timedelta(hours=4)),
                ("last_24_hours", timedelta(hours=24)),
                ("last_week", timedelta(days=7))
            ]

            for period_name, period_delta in periods:
                start_time = now - period_delta
                period_trades = [
                    t for t in self.trade_attributions
                    if t.timestamp >= start_time
                ]

                if not period_trades:
                    continue

                pnl_list = [t.pnl for t in period_trades]
                total_pnl = sum(pnl_list)
                trade_count = len(period_trades)
                winning_trades = len([p for p in pnl_list if p > 0])
                win_rate = (winning_trades / trade_count * 100) if trade_count > 0 else 0
                avg_pnl = total_pnl / trade_count if trade_count > 0 else 0

                # Risk-adjusted return
                if len(pnl_list) > 1:
                    std_pnl = np.std(pnl_list)
                    risk_adjusted = avg_pnl / std_pnl if std_pnl > 0 else 0
                else:
                    risk_adjusted = 0

                breakdown = AttributionBreakdown(
                    factor_name="time_period",
                    factor_value=period_name,
                    pnl_contribution=total_pnl,
                    trade_count=trade_count,
                    win_rate=win_rate,
                    avg_pnl_per_trade=avg_pnl,
                    total_exposure_time=period_delta.total_seconds() / 3600,  # hours
                    risk_adjusted_return=risk_adjusted
                )

                breakdowns.append(breakdown)

            return breakdowns

        except Exception as e:
            logger.error(f"Error generating time period attribution: {e}")
            return []

    def _identify_top_performing_factors(self) -> List[Tuple[str, float]]:
        """Identify top performing factors across all categories."""
        try:
            all_factors = []

            # Collect all factors with their performance
            for factor_type in ["regime", "parameter", "market_condition"]:
                breakdowns = self.generate_attribution_breakdown(factor_type)
                for breakdown in breakdowns:
                    if breakdown.trade_count >= 3:  # Minimum trades for significance
                        factor_key = f"{breakdown.factor_name}:{breakdown.factor_value}"
                        all_factors.append((factor_key, breakdown.risk_adjusted_return))

            # Sort by risk-adjusted return
            all_factors.sort(key=lambda x: x[1], reverse=True)

            # Return top 10
            return all_factors[:10]

        except Exception as e:
            logger.error(f"Error identifying top performing factors: {e}")
            return []

    def _calculate_risk_metrics(self) -> Dict[str, float]:
        """Calculate comprehensive risk metrics."""
        try:
            if not self.trade_attributions:
                return {}

            pnl_series = [t.pnl for t in self.trade_attributions]

            # Basic risk metrics
            total_pnl = sum(pnl_series)
            volatility = np.std(pnl_series) if len(pnl_series) > 1 else 0

            # Value at Risk (95%)
            var_95 = np.percentile(pnl_series, 5) if pnl_series else 0

            # Maximum consecutive losses
            max_consecutive_losses = 0
            current_losses = 0
            for pnl in pnl_series:
                if pnl < 0:
                    current_losses += 1
                    max_consecutive_losses = max(max_consecutive_losses, current_losses)
                else:
                    current_losses = 0

            # Drawdown metrics
            cumulative = np.cumsum(pnl_series)
            running_max = np.maximum.accumulate(cumulative)
            drawdowns = running_max - cumulative
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

            # Risk-reward ratio
            winning_trades = [p for p in pnl_series if p > 0]
            losing_trades = [p for p in pnl_series if p < 0]

            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = abs(np.mean(losing_trades)) if losing_trades else 0
            risk_reward_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            return {
                'total_pnl': total_pnl,
                'volatility': volatility,
                'var_95': var_95,
                'max_consecutive_losses': max_consecutive_losses,
                'max_drawdown': max_drawdown,
                'risk_reward_ratio': risk_reward_ratio,
                'total_trades': len(pnl_series),
                'avg_trade_pnl': np.mean(pnl_series) if pnl_series else 0
            }

        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {}

    def _cleanup_old_data(self) -> None:
        """Clean up old trade attribution data."""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.max_history_days)

            # Remove old trade attributions
            self.trade_attributions = [
                t for t in self.trade_attributions
                if t.timestamp >= cutoff_date
            ]

            # Clean attribution tracking
            for performance_dict in [self.regime_performance,
                                   self.parameter_performance,
                                   self.market_condition_performance]:
                for key in list(performance_dict.keys()):
                    # Keep only recent data (simplified - could be more sophisticated)
                    if len(performance_dict[key]) > 1000:
                        performance_dict[key] = performance_dict[key][-500:]

        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")

    def _save_attribution_data(self) -> None:
        """Save attribution data to file."""
        try:
            data = {
                'trade_attributions': [asdict(t) for t in self.trade_attributions[-100:]],  # Last 100 trades
                'session_metrics': asdict(self.session_metrics),
                'last_update': datetime.now(timezone.utc).isoformat()
            }

            with open(self.attribution_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error saving attribution data: {e}")

    def _save_report(self, report: RealTimeReport) -> None:
        """Save real-time report to file."""
        try:
            # Convert report to dictionary
            report_dict = asdict(report)

            with open(self.report_file, 'w') as f:
                json.dump(report_dict, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error saving report: {e}")

    def _empty_report(self) -> RealTimeReport:
        """Return empty report for error cases."""
        return RealTimeReport(
            timestamp=datetime.now(timezone.utc),
            overall_metrics=self.session_metrics,
            regime_attribution=[],
            parameter_attribution=[],
            market_condition_attribution=[],
            time_period_attribution=[],
            top_performing_factors=[],
            risk_metrics={}
        )

    def start_realtime_updates(self) -> None:
        """Start real-time update thread."""
        try:
            if self.is_running:
                logger.warning("Real-time updates already running")
                return

            self.is_running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()

            logger.info("Real-time performance attribution updates started")

        except Exception as e:
            logger.error(f"Error starting real-time updates: {e}")

    def stop_realtime_updates(self) -> None:
        """Stop real-time update thread."""
        try:
            self.is_running = False
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=5)

            logger.info("Real-time performance attribution updates stopped")

        except Exception as e:
            logger.error(f"Error stopping real-time updates: {e}")

    def _update_loop(self) -> None:
        """Main update loop for real-time processing."""
        while self.is_running:
            try:
                # Generate and save updated report
                self.generate_realtime_report()

                # Update timestamp
                self.last_update_time = datetime.now(timezone.utc)

                # Sleep until next update
                time.sleep(self.update_interval_seconds)

            except Exception as e:
                logger.error(f"Error in update loop: {e}")
                time.sleep(self.update_interval_seconds)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get current performance summary."""
        try:
            return {
                'session_metrics': asdict(self.session_metrics),
                'total_attributions': len(self.trade_attributions),
                'last_update': self.last_update_time.isoformat(),
                'session_start': self.current_session_start.isoformat(),
                'is_running': self.is_running
            }

        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}


# Global performance attribution system instance
_performance_attribution_system: Optional[RealTimePerformanceAttribution] = None


def initialize_performance_attribution(max_history_days: int = 30,
                                     attribution_window_hours: int = 24,
                                     update_interval_seconds: int = 60) -> bool:
    """
    Initialize the real-time performance attribution system.

    Args:
        max_history_days: Maximum days of trade history to maintain
        attribution_window_hours: Time window for attribution analysis
        update_interval_seconds: Interval for real-time updates

    Returns:
        bool: True if initialization successful
    """
    global _performance_attribution_system

    try:
        _performance_attribution_system = RealTimePerformanceAttribution(
            max_history_days=max_history_days,
            attribution_window_hours=attribution_window_hours,
            update_interval_seconds=update_interval_seconds
        )

        # Start real-time updates
        _performance_attribution_system.start_realtime_updates()

        logger.info("Performance attribution system initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing performance attribution system: {e}")
        return False


def add_trade_attribution(trade_data: Dict[str, Any],
                        market_data: Dict[str, Any],
                        ai_data: Dict[str, Any],
                        parameters: Dict[str, Any]) -> bool:
    """
    Add a new trade with full attribution data.

    Args:
        trade_data: Trade execution data
        market_data: Market conditions at trade time
        ai_data: AI decision data
        parameters: Trading parameters used

    Returns:
        bool: True if attribution added successfully
    """
    global _performance_attribution_system

    try:
        if _performance_attribution_system is None:
            logger.warning("Performance attribution system not initialized")
            return False

        _performance_attribution_system.add_trade_attribution(
            trade_data, market_data, ai_data, parameters
        )
        return True

    except Exception as e:
        logger.error(f"Error adding trade attribution: {e}")
        return False


def get_realtime_performance_report() -> Dict[str, Any]:
    """
    Get the current real-time performance report.

    Returns:
        dict: Real-time performance report data
    """
    global _performance_attribution_system

    try:
        if _performance_attribution_system is None:
            logger.warning("Performance attribution system not initialized")
            return {}

        report = _performance_attribution_system.generate_realtime_report()
        return asdict(report)

    except Exception as e:
        logger.error(f"Error getting real-time performance report: {e}")
        return {}


def get_attribution_breakdown(factor_type: str) -> List[Dict[str, Any]]:
    """
    Get attribution breakdown for a specific factor type.

    Args:
        factor_type: Type of factor ('regime', 'parameter', 'market_condition')

    Returns:
        list: Attribution breakdown data
    """
    global _performance_attribution_system

    try:
        if _performance_attribution_system is None:
            logger.warning("Performance attribution system not initialized")
            return []

        breakdowns = _performance_attribution_system.generate_attribution_breakdown(factor_type)
        return [asdict(b) for b in breakdowns]

    except Exception as e:
        logger.error(f"Error getting attribution breakdown: {e}")
        return []


def get_performance_summary() -> Dict[str, Any]:
    """
    Get current performance summary.

    Returns:
        dict: Performance summary data
    """
    global _performance_attribution_system

    try:
        if _performance_attribution_system is None:
            logger.warning("Performance attribution system not initialized")
            return {}

        return _performance_attribution_system.get_performance_summary()

    except Exception as e:
        logger.error(f"Error getting performance summary: {e}")
        return {}


def stop_performance_attribution() -> bool:
    """
    Stop the performance attribution system.

    Returns:
        bool: True if stopped successfully
    """
    global _performance_attribution_system

    try:
        if _performance_attribution_system is None:
            return True

        _performance_attribution_system.stop_realtime_updates()
        _performance_attribution_system = None

        logger.info("Performance attribution system stopped")
        return True

    except Exception as e:
        logger.error(f"Error stopping performance attribution system: {e}")
        return False
