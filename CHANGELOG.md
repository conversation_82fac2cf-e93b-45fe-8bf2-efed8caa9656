# CHANGELOG

## [Unreleased]
- Added daily ISA-style governor that pauses buying once P&L ≥ 0.013% of opening equity.
- Implemented Step 2: Volatility-Adjusted Position Sizing.
  - Added `risk_per_trade` parameter to `config.py`.
  - Implemented volatility-adjusted sizing logic in `trade_logic.py`.
  - Added unit tests for volatility-adjusted sizing.
- Implemented Step 1: Dynamic Regime Classification by Rolling Volatility.
  - Replaced fixed regime thresholds with dynamic buckets based on rolling realized volatility percentiles.
  - Added unit tests for regime classification.
  - Verified AI prompt integration with new regime classification.