import os
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
import pandas as pd
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import APIError
import csv

# ==============================================================================
# --- FORENSIC AUDIT AND STATE RECONSTRUCTION SCRIPT ---
# ==============================================================================
# This script is a powerful, one-time utility designed to fix severe data
# corruption in the bot's state files. It treats the Alpaca exchange as the
# single source of truth and rebuilds all local records from scratch.
#
# WHAT IT DOES:
# 1. Fetches the COMPLETE order history from Alpaca.
# 2. Processes every single trade in chronological order.
# 3. For each sell, it correctly matches it to the oldest available buy (FIFO).
# 4. It RECALCULATES the profit or loss for every single sell trade.
# 5. It generates a brand new, perfectly accurate trade history CSV.
# 6. It determines the true current open positions (lots).
# 7. It identifies any genuinely active (unfilled) orders.
# 8. It recalculates all performance statistics (P/L, Win/Loss, etc.).
# 9. It saves all of this corrected data into new '_rebuilt' files,
#    leaving the original corrupted files untouched for safety.
#
# ==============================================================================

# --- CONFIGURATION ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    import config
except ImportError:
    logging.error("CRITICAL: config.py not found. Make sure this script is in the same directory as your bot.")
    exit()

# --- FILE PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# We will write to new files to avoid touching the corrupted originals until we are sure.
LOTS_REBUILT_FILE = os.path.join(BASE_DIR, "open_lots_rebuilt.json")
ACTIVE_ORDERS_REBUILT_FILE = os.path.join(BASE_DIR, "active_orders_rebuilt.json")
TRADE_HISTORY_REBUILT_FILE = os.path.join(BASE_DIR, "trade_history_rebuilt.csv")
SESSION_STATS_REBUILT_FILE = os.path.join(BASE_DIR, "session_stats_rebuilt.json")

def fetch_all_historical_orders(api):
    """
    Fetches all historical orders from Alpaca, handling pagination correctly.
    """
    logging.info("Connecting to Alpaca to fetch complete order history...")
    all_orders = []
    try:
        orders = api.list_orders(status='all', limit=500, nested=True, direction='asc')
        all_orders.extend(orders)
        logging.info(f"Fetched initial {len(orders)} orders.")
        
        while len(orders) == 500:
            last_order_time = orders[-1].submitted_at
            formatted_time = last_order_time.isoformat().replace('+00:00', 'Z')
            logging.info(f"Paginating... fetching orders submitted after {formatted_time}")
            # To get the next page, we ask for orders submitted *after* the last one we received.
            orders = api.list_orders(status='all', limit=500, after=formatted_time, nested=True, direction='asc')
            all_orders.extend(orders)
            logging.info(f"Fetched another {len(orders)} orders.")

    except APIError as e:
        logging.error(f"Alpaca API error while fetching orders: {e}")
        return None
        
    logging.info(f"Total historical orders fetched: {len(all_orders)}")
    return all_orders

def forensic_rebuild():
    """
    Performs the full audit and rebuild of all state and history files.
    """
    api = tradeapi.REST(config.ALPACA_API_KEY_ID, config.ALPACA_SECRET_KEY, base_url=config.ALPACA_BASE_URL)
    
    orders = fetch_all_historical_orders(api)
    if not orders:
        logging.error("Could not fetch order history. Aborting audit.")
        return

    logging.info("Beginning forensic audit and reconstruction...")

    # Sort orders chronologically by when they were filled to ensure correct processing.
    # Use submitted_at as a fallback for non-filled orders.
    orders.sort(key=lambda o: o.filled_at or o.submitted_at)

    # --- In-memory state during the rebuild ---
    live_lots = []
    rebuilt_history = []
    genuinely_active_orders = []

    for order in orders:
        status = order.status

        # 1. Identify genuinely active (unfilled) orders
        if status not in ['filled', 'canceled', 'expired', 'rejected']:
            genuinely_active_orders.append(order._raw)
            continue # Skip to the next order

        # 2. Process only filled orders for P/L and lot calculations
        if status != 'filled':
            continue # Skip cancelled, expired etc.

        order_id = str(order.id)
        qty = Decimal(order.filled_qty)
        price = Decimal(order.filled_avg_price)
        fee = (qty * price * config.FEE_PERCENT).quantize(Decimal("0.00000001"))
        
        if order.side == 'buy':
            cost_basis = ((price * qty) + fee) / qty
            new_lot = {
                "lot_id": order_id,
                "buy_timestamp": order.filled_at.isoformat(),
                "original_qty": qty,
                "remaining_qty": qty, # Start with the full amount
                "buy_price": price,
                "cost_basis_per_unit": cost_basis,
            }
            live_lots.append(new_lot)
            logging.info(f"Reconstructed BUY Lot: {order_id} | Qty: {qty} @ ${price}")

            # Add to rebuilt history
            rebuilt_history.append({
                "timestamp": order.filled_at.isoformat(),
                "action": "BUY", "quantity": qty, "price": price,
                "order_id": order_id, "status": "filled",
                "realized_pl": Decimal("0.0"), # No P/L on a buy
                "fee": fee, "entry_price": price, "lot_id": order_id
            })

        elif order.side == 'sell':
            sell_qty_to_match = qty
            total_cost_of_sold_btc = Decimal("0.0")
            
            # Match this sell against the oldest available lots (FIFO)
            for lot in live_lots:
                if sell_qty_to_match <= 0: break
                
                lot_qty_available = lot["remaining_qty"]
                if lot_qty_available > 0:
                    qty_from_this_lot = min(lot_qty_available, sell_qty_to_match)
                    
                    # Track the cost of the portion being sold
                    total_cost_of_sold_btc += qty_from_this_lot * lot["cost_basis_per_unit"]
                    
                    # Reduce the quantity in the live lot and the sell order
                    lot["remaining_qty"] -= qty_from_this_lot
                    sell_qty_to_match -= qty_from_this_lot
                    
                    logging.info(f"Matched {qty_from_this_lot} of SELL {order_id} to BUY Lot {lot['lot_id']}. "
                                 f"Lot remaining: {lot['remaining_qty']}")

            # Now calculate the true P/L for this sell
            proceeds = price * qty
            cost_basis_total = total_cost_of_sold_btc
            realized_pl = proceeds - cost_basis_total - fee
            
            rebuilt_history.append({
                "timestamp": order.filled_at.isoformat(),
                "action": "SELL", "quantity": qty, "price": price,
                "order_id": order_id, "status": "filled",
                "realized_pl": realized_pl,
                "fee": fee, "entry_price": None, "lot_id": None
            })
            logging.info(f"Recalculated P/L for SELL {order_id}: ${realized_pl:.2f}")

    # --- Post-Processing and Saving ---

    # 1. Finalize the list of open lots
    final_open_lots = [
        {k: str(v) for k, v in lot.items()}
        for lot in live_lots if lot["remaining_qty"] > 0
    ]
    
    # 2. Save the new, clean files
    logging.info(f"Saving {len(final_open_lots)} correctly calculated open lots to {LOTS_REBUILT_FILE}")
    with open(LOTS_REBUILT_FILE, 'w') as f:
        json.dump(final_open_lots, f, indent=4)

    logging.info(f"Saving {len(genuinely_active_orders)} genuinely active orders to {ACTIVE_ORDERS_REBUILT_FILE}")
    with open(ACTIVE_ORDERS_REBUILT_FILE, 'w') as f:
        json.dump(genuinely_active_orders, f, indent=4)

    # 3. Save the new, clean trade history
    logging.info(f"Saving {len(rebuilt_history)} records to new history file {TRADE_HISTORY_REBUILT_FILE}")
    history_df = pd.DataFrame(rebuilt_history)
    history_df.to_csv(TRADE_HISTORY_REBUILT_FILE, index=False)

    # 4. Recalculate final stats from the *new* history file
    total_pl = history_df[history_df['action'] == 'SELL']['realized_pl'].sum()
    win_count = (history_df[history_df['action'] == 'SELL']['realized_pl'] > 0).sum()
    loss_count = (history_df[history_df['action'] == 'SELL']['realized_pl'] < 0).sum()
    
    final_stats = {
        "net_pl": float(total_pl),
        "win_count": int(win_count),
        "loss_count": int(loss_count),
    }

    logging.info(f"Saving new, accurate statistics to {SESSION_STATS_REBUILT_FILE}")
    logging.info(f"  -> Correct Realized P/L: ${total_pl:,.2f}")
    logging.info(f"  -> Correct Win/Loss Count: {win_count} / {loss_count}")
    with open(SESSION_STATS_REBUILT_FILE, 'w') as f:
        json.dump(final_stats, f, indent=4)
        
    print("\n" + "="*70)
    print("           ✅ FORENSIC AUDIT AND RECONSTRUCTION COMPLETE ✅")
    print("="*70)
    print("New, accurate files have been created with the '_rebuilt' suffix.")
    print("\n--- NEXT STEPS ---")
    print("1. Please review the new '_rebuilt' files to confirm they look correct.")
    print("2. If they are correct, you will need to:")
    print("   a. Delete the old, corrupted files:")
    print("      - open_lots.json")
    print("      - active_orders.json")
    print("      - trade_history.csv")
    print("      - session_stats.json")
    print("   b. Rename the new '_rebuilt' files to their original names.")
    print("3. Once the files are replaced, you can safely restart the main bot.")
    print("="*70)

if __name__ == "__main__":
    input("CRITICAL: This script will rebuild your state files from your Alpaca history. "
          "Please ensure the main trading bot is STOPPED before proceeding. Press Enter to continue...")
    forensic_rebuild()
