"""
API Integration Tests - Verify External Service Interactions

These tests ensure the trading bot:
- Handles Alpaca API responses correctly
- Processes Binance data feeds properly  
- Manages AI service interactions safely
- Recovers from API failures gracefully
- Validates data formats and responses
"""

import unittest
from unittest.mock import MagicMock, patch, Mock
from decimal import Decimal
import sys
import os
import json
from datetime import datetime, UTC
import requests

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
import alpaca_service
import ai_decision_service
import binance_data_service
from error_handler import APIError, NetworkError


class TestAlpacaIntegration(unittest.TestCase):
    """Test Alpaca API integration"""
    
    def setUp(self):
        """Setup mock API for testing"""
        self.mock_api = MagicMock()
    
    def test_get_account_info_success(self):
        """Test successful account info retrieval"""
        # Mock successful API response
        mock_account = MagicMock()
        mock_account.cash = "5000.00"
        mock_account.buying_power = "10000.00"
        mock_account.equity = "5000.00"
        mock_account.portfolio_value = "5000.00"
        
        self.mock_api.get_account.return_value = mock_account
        
        # Test the function
        result = alpaca_service.get_account_info(self.mock_api)
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertEqual(result["cash"], "5000.00")
        self.assertEqual(result["buying_power"], "10000.00")
        self.assertEqual(result["equity"], "5000.00")
        
        # Verify API was called
        self.mock_api.get_account.assert_called_once()
    
    def test_get_account_info_api_error(self):
        """Test account info retrieval with API error"""
        # Mock API error
        from alpaca_trade_api.rest import APIError
        self.mock_api.get_account.side_effect = APIError("Rate limit exceeded")
        
        # Should handle error gracefully
        with self.assertRaises(APIError):
            alpaca_service.get_account_info(self.mock_api)
    
    def test_get_positions_success(self):
        """Test successful position retrieval"""
        # Mock position data
        mock_position = MagicMock()
        mock_position.symbol = "BTC/USD"
        mock_position.qty = "0.001"
        mock_position.market_value = "50.00"
        mock_position.avg_entry_price = "50000.00"
        
        self.mock_api.list_positions.return_value = [mock_position]
        
        # Test the function
        result = alpaca_service.get_positions(self.mock_api)
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertIn("BTC/USD", result)
        self.assertEqual(result["BTC/USD"]["qty"], 0.001)
        self.assertEqual(result["BTC/USD"]["market_value"], "50.00")
    
    def test_get_positions_empty(self):
        """Test position retrieval with no positions"""
        self.mock_api.list_positions.return_value = []
        
        result = alpaca_service.get_positions(self.mock_api)
        
        # Should return empty dict
        self.assertEqual(result, {})
    
    def test_submit_order_success(self):
        """Test successful order submission"""
        # Mock successful order
        mock_order = MagicMock()
        mock_order.id = "order_123"
        mock_order.status = "accepted"
        mock_order.symbol = "BTC/USD"
        mock_order.qty = "0.001"
        
        self.mock_api.submit_order.return_value = mock_order
        
        # Test order submission
        with patch('alpaca_service.submit_order') as mock_submit:
            mock_submit.return_value = {
                "id": "order_123",
                "status": "accepted",
                "symbol": "BTC/USD",
                "qty": "0.001"
            }
            
            result = mock_submit(
                symbol="BTC/USD",
                qty=0.001,
                side="buy",
                type="market",
                time_in_force="gtc"
            )
            
            self.assertEqual(result["id"], "order_123")
            self.assertEqual(result["status"], "accepted")
    
    def test_historical_data_retrieval(self):
        """Test historical crypto data retrieval"""
        # Mock historical data
        mock_bars = MagicMock()
        mock_bars.df = MagicMock()
        
        # Create mock dataframe data
        import pandas as pd
        mock_df = pd.DataFrame({
            'timestamp': [datetime.now(UTC)],
            'open': [50000.0],
            'high': [50100.0], 
            'low': [49900.0],
            'close': [50050.0],
            'volume': [100.0]
        })
        mock_bars.df = mock_df
        
        self.mock_api.get_crypto_bars.return_value = mock_bars
        
        # Test data retrieval
        result = alpaca_service.get_historical_crypto_data(
            self.mock_api, "BTC/USD", "1Min", limit=100
        )
        
        # Verify data structure
        self.assertIsNotNone(result)
        self.assertIn('close', result.columns)
        self.assertIn('volume', result.columns)


class TestAIServiceIntegration(unittest.TestCase):
    """Test AI decision service integration"""
    
    @patch('ai_decision_service._MODEL')
    def test_ai_decision_success(self, mock_model):
        """Test successful AI decision generation"""
        # Mock AI response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "decision": "BUY",
            "confidence_level": "HIGH",
            "quantity_percentage": 0.01,
            "reasoning": "Strong bullish signal detected"
        })
        
        mock_model.generate_content.return_value = mock_response
        
        # Test AI decision
        market_data = {
            "current_price": 50000,
            "rsi": 45.0,
            "macd_histogram": 0.1,
            "volume": 1000
        }
        
        result = ai_decision_service.get_ai_decision(market_data, {}, {})
        
        # Verify decision structure
        self.assertIsNotNone(result)
        self.assertEqual(result.decision, "BUY")
        self.assertEqual(result.confidence_level, "HIGH")
        self.assertEqual(result.quantity_percentage, 0.01)
    
    @patch('ai_decision_service._MODEL')
    def test_ai_decision_invalid_json(self, mock_model):
        """Test AI decision with invalid JSON response"""
        # Mock invalid JSON response
        mock_response = MagicMock()
        mock_response.text = "Invalid JSON response from AI"
        
        mock_model.generate_content.return_value = mock_response
        
        # Should handle invalid JSON gracefully
        market_data = {"current_price": 50000, "rsi": 50.0}
        
        result = ai_decision_service.get_ai_decision(market_data, {}, {})
        
        # Should fallback to HOLD
        self.assertEqual(result.decision, "HOLD")
        self.assertEqual(result.confidence_level, "LOW")
    
    @patch('ai_decision_service._MODEL')
    def test_ai_decision_api_timeout(self, mock_model):
        """Test AI decision with API timeout"""
        # Mock timeout error
        import google.generativeai.types.generation_types as g_exc
        mock_model.generate_content.side_effect = TimeoutError("Request timeout")
        
        market_data = {"current_price": 50000, "rsi": 50.0}
        
        # Should handle timeout gracefully
        result = ai_decision_service.get_ai_decision(market_data, {}, {})
        
        # Should fallback to HOLD
        self.assertEqual(result.decision, "HOLD")
        self.assertEqual(result.confidence_level, "LOW")


class TestBinanceIntegration(unittest.TestCase):
    """Test Binance data service integration"""
    
    def test_websocket_message_processing(self):
        """Test Binance WebSocket message processing"""
        # Mock WebSocket message
        mock_message = json.dumps({
            "s": "BTCUSDT",
            "c": "50000.00",  # Close price
            "v": "100.50",    # Volume
            "h": "50100.00",  # High
            "l": "49900.00",  # Low
            "o": "49950.00"   # Open
        })
        
        # Test message processing
        with patch('binance_data_service.process_ticker_data') as mock_process:
            # Simulate message processing
            data = json.loads(mock_message)
            
            # Verify data structure
            self.assertEqual(data["s"], "BTCUSDT")
            self.assertEqual(float(data["c"]), 50000.00)
            self.assertEqual(float(data["v"]), 100.50)
    
    def test_websocket_error_handling(self):
        """Test WebSocket error handling"""
        # Mock WebSocket error
        mock_ws = MagicMock()
        error_message = "Connection lost"
        
        # Test error handler
        with patch('binance_data_service.logger') as mock_logger:
            binance_data_service.on_error(mock_ws, error_message)
            
            # Should log the error
            mock_logger.error.assert_called()
    
    def test_websocket_connection_recovery(self):
        """Test WebSocket connection recovery"""
        # Mock connection close
        mock_ws = MagicMock()
        
        # Test close handler
        with patch('binance_data_service.logger') as mock_logger:
            binance_data_service.on_close(mock_ws, 1000, "Normal closure")
            
            # Should log the closure
            mock_logger.warning.assert_called()


class TestAPIErrorRecovery(unittest.TestCase):
    """Test API error recovery mechanisms"""
    
    def test_retry_mechanism(self):
        """Test API retry mechanism"""
        # Mock API that fails twice then succeeds
        mock_api = MagicMock()
        call_count = 0
        
        def side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise requests.exceptions.RequestException("Temporary error")
            return MagicMock(cash="5000.00")
        
        mock_api.get_account.side_effect = side_effect
        
        # Test with retry decorator
        with patch('time.sleep'):  # Speed up test
            try:
                result = alpaca_service.get_account_info(mock_api)
                # Should eventually succeed
                self.assertIsNotNone(result)
            except Exception:
                # If it still fails, verify retry attempts were made
                self.assertGreaterEqual(call_count, 2)
    
    def test_circuit_breaker_activation(self):
        """Test circuit breaker activation on repeated failures"""
        # This would test the circuit breaker pattern
        # Implementation depends on your circuit breaker logic
        pass
    
    def test_fallback_data_sources(self):
        """Test fallback to alternative data sources"""
        # Test that system can fall back to cached data
        # when primary data source fails
        pass


if __name__ == "__main__":
    unittest.main(verbosity=2)
