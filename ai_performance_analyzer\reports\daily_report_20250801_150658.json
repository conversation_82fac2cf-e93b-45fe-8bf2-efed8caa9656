{"report_metadata": {"type": "daily", "generated_at": "2025-08-01T15:06:58.660798", "analysis_timestamp": "2025-08-01T15:06:58.651293", "version": "1.0"}, "analysis_results": {"analysis_timestamp": "2025-08-01T15:06:58.651293", "trade_analysis": {"position_analysis": {"total_lots": 54, "total_value": 2208.6393702083637, "average_lot_value": 40.90072907793266, "value_distribution": "uniform"}, "lot_performance": {"total_lots": 54, "performance_summary": "Requires current market data for P&L calculation"}, "strategy_effectiveness": {"strategy_distribution": {"DIP_ACCUMULATION_MACD_Hist_Initial": 15, "DIP_ACCUMULATION_RSI_Slope_Initial": 19, "MOMENTUM_BUY": 11, "DIP_ACCUMULATION_Volume_Spike_Initial": 4, "synthesized_excess_recovery": 2, "dip_accumulation": 3}, "strategy_values": {"DIP_ACCUMULATION_MACD_Hist_Initial": 654.5534998119899, "DIP_ACCUMULATION_RSI_Slope_Initial": 742.1616147768101, "MOMENTUM_BUY": 474.04717966595996, "DIP_ACCUMULATION_Volume_Spike_Initial": 184.61732240675, "synthesized_excess_recovery": 75.49021157659381, "dip_accumulation": 77.76954197026001}, "most_used_strategy": "DIP_ACCUMULATION_RSI_Slope_Initial"}, "holding_period_analysis": {"average_holding_period": 105.53323789299897, "median_holding_period": 112.43201613430556, "max_holding_period": 116.490282225, "min_holding_period": 15.494715759999998, "total_lots_analyzed": 54}}, "decision_analysis": {"decision_distribution": {"total_decisions": 0, "decision_counts": {}, "decision_percentages": {}}, "decision_timing": {"total_decisions": 0, "analysis_note": "Timing analysis requires time series implementation"}, "decision_quality": {"total_decisions": 0, "total_actions": 0, "decision_to_action_ratio": 0}, "error_patterns": {"total_errors": 0, "error_analysis": "Error pattern analysis requires error categorization"}}, "performance_metrics": {"profitability": {"total_lots": 54, "total_position_value": 2208.6393702083637, "average_lot_size": 40.90072907793266, "unrealized_pnl": 0}, "risk_metrics": {}, "efficiency_metrics": {}, "benchmark_comparison": {}}, "pattern_recognition": {"temporal_patterns": {"hourly_patterns": "Temporal analysis requires time-series data", "daily_patterns": "Daily pattern analysis not yet implemented", "weekly_patterns": "Weekly pattern analysis not yet implemented", "seasonal_patterns": "Seasonal analysis requires longer data history"}, "strategy_patterns": {}, "market_condition_patterns": {}, "decision_patterns": {}}, "risk_analysis": {"exposure_analysis": {"total_exposure": 2208.6393702083637, "average_lot_exposure": 40.90072907793266, "largest_lot_exposure": 55.877360768771226}, "concentration_risk": {"strategy_concentration": {"DIP_ACCUMULATION_MACD_Hist_Initial": 654.5534998119899, "DIP_ACCUMULATION_RSI_Slope_Initial": 742.1616147768101, "MOMENTUM_BUY": 474.04717966595996, "DIP_ACCUMULATION_Volume_Spike_Initial": 184.61732240675, "synthesized_excess_recovery": 75.49021157659381, "dip_accumulation": 77.76954197026001}, "max_strategy_exposure": 742.1616147768101, "strategy_count": 6}, "strategy_risk": {}}, "efficiency_analysis": {"decision_efficiency": {"decision_to_action_ratio": 0, "total_decisions": 0, "total_actions": 0}, "execution_efficiency": {}, "error_efficiency": {"error_rate": 0, "total_errors": 0, "errors_per_hour": 0}}, "insights": {"key_findings": ["Currently managing 54 open positions", "Average holding period: 105.5 hours"], "recommendations": ["Continue monitoring performance patterns", "Consider implementing automated alerts for significant changes"], "performance_alerts": ["High number of open positions detected", "Low cash availability for new trades"], "optimization_opportunities": []}}, "report_summary": {"key_metrics": {"total_positions": 54, "portfolio_value": 2208.6393702083637, "average_position_size": 40.90072907793266}, "performance_highlights": ["Currently managing 54 open positions", "Average holding period: 105.5 hours"], "risk_indicators": ["High number of open positions detected", "Low cash availability for new trades"], "recommendations": ["Continue monitoring performance patterns", "Consider implementing automated alerts for significant changes"]}}