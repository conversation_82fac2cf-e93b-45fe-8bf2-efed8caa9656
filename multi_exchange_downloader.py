# START OF FILE multi_exchange_downloader.py

import ccxt
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
import os
import sys
import logging

# Configure logging for multi_exchange_downloader.py
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # Set to DEBUG to capture all messages

# Create handlers (e.g., console handler, file handler)
# For now, let's just use a StreamHandler to print to console (which main_bot.py will capture)
if not logger.handlers:
    ch = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    logger.addHandler(ch)

exchanges = {
    'binance': {
        'symbol': 'BTC/USDT',
        'start_date': '2017-01-01T00:00:00Z', 
        # FIX: Changed filename to a temporary one for new data
        'filename': 'binance_btcusdt_1m_NEW_DATA.csv', 
        'FORCE_REDOWNLOAD_FROM_START_DATE': False, 
        'DOWNLOAD_YEARS_ONE_BY_ONE': False, 
        # FIX: We will set RESUME_FROM_DATE to where your backup ends
        'FORCE_RESUME_DATE': False, 
        'RESUME_FROM_DATE': None, # No longer forcing a resume date, rely on get_last_timestamp
    }
}

timeframe = '1m'
limit = 1000 
sleep_time = 0.1 
REPORT_BATCH_INTERVAL = 500 # Keep this higher for less verbose output

MAX_NO_DATA_RETRIES = 5 
RETRY_SLEEP_MULTIPLIER = 5 

def get_last_timestamp(filename: str) -> pd.Timestamp | None:
    logger.debug(f"Attempting to get last timestamp from {filename}")
    if not os.path.exists(filename):
        logger.debug(f"File {filename} does not exist. Returning None.")
        return None
    try:
        logger.debug(f"Opening file {filename} to read last line.")
        with open(filename, 'r', newline='') as f:
            f.seek(0, os.SEEK_END)
            position = f.tell()
            line = ''
            while position >= 0 and line == '':
                position -= 1
                f.seek(position)
                char = f.read(1)
                if char == '\n' and position < f.tell() - 1:
                    line = f.readline()
                elif position == 0:
                    line = f.readline()
            
            if not line or line.strip() == '':
                logger.warning(f"File {filename} is empty or contains only header. Returning None.")
                return None
            
            if line.strip().split(',')[0] == 'timestamp': 
                 logger.warning(f"File {filename} contains only header. Returning None.")
                 return None 

            val_str = line.strip().split(',')[0]
            
            if val_str.isdigit():
                val = int(val_str)
            else:
                val = val_str

            if isinstance(val, (int, float)) and val > 1e12:
                last = pd.to_datetime(val, unit='ms', utc=True)
            else:
                last = pd.to_datetime(val, utc=True)
            logger.debug(f"Successfully read last timestamp: {last}")
            return last
        
    except Exception as e:
        logger.error(f"Error reading last timestamp from {filename}: {e}. Defaulting to start_date from config.")
        return None

    logger.info(f"Starting download for {exchange_id} {config['symbol']}")

    exchange_class = getattr(ccxt, exchange_id)
    exchange = exchange_class()

    start_new_file_flag = False 

    # Prioritize FORCE_RESUME_DATE
    if config.get('FORCE_RESUME_DATE', False) and config.get('RESUME_FROM_DATE'):
        since_dt = pd.to_datetime(config['RESUME_FROM_DATE'], utc=True)
        logger.info(f"{exchange_id}: FORCING RESUME from explicitly set date {since_dt}.")
        if not os.path.exists(config['filename']):
            start_new_file_flag = True 
            logger.info(f"File {config['filename']} not found, will start a new file from RESUME_FROM_DATE.")
        else:
            # If file exists and we are forcing resume, we are appending
            # We assume the existing file (the NEW_DATA file) is okay to append to.
            # If it's the first time running for NEW_DATA, get_last_timestamp will return None for it,
            # and start_new_file_flag will be set correctly later.
            pass
    elif config.get('FORCE_REDOWNLOAD_FROM_START_DATE', False): # This should be False for this operation
        since_dt = pd.to_datetime(config['start_date'], utc=True)
        logger.info(f"{exchange_id}: FORCE REDOWNLOAD from {since_dt}. (This will delete {config['filename']}).")
        if os.path.exists(config['filename']):
            os.remove(config['filename'])
            logger.info(f"Deleted existing {config['filename']}.")
        start_new_file_flag = True
    else: # Normal resume logic (will use this if RESUME_FROM_DATE is not set for NEW_DATA file)
        logger.debug(f"Attempting normal resume logic for {config['filename']}.")
        last_ts = get_last_timestamp(config['filename'])
        if last_ts is not None:
            since_dt = last_ts + pd.Timedelta(minutes=1)
            logger.info(f"{exchange_id}: Resuming from {since_dt}.")
        else:
            since_dt = pd.to_datetime(config['start_date'], utc=True) # Fallback to original start if all else fails
            logger.info(f"{exchange_id}: Starting fresh from {since_dt} (file not found or empty).")
            start_new_file_flag = True
            
    # If starting a new file because it's new or didn't exist, mark to write header
    if not os.path.exists(config['filename']) or start_new_file_flag:
        start_new_file_flag = True
        logger.debug(f"start_new_file_flag set to {start_new_file_flag} for {config['filename']}.")


    current_fetch_end_dt = until_dt if until_dt is not None else pd.to_datetime(datetime.now(timezone.utc))
    
    batch_count = 0
    total_rows_fetched_current_run = 0
    no_data_retries = 0
    
    full_columns_set = ['timestamp', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_asset_volume', 'num_trades',
                        'taker_buy_base', 'taker_buy_quote', 'ignore']
    
    logger.debug(f"Initial since_dt: {since_dt}")
    logger.debug(f"Initial current_fetch_end_dt: {current_fetch_end_dt}")
    
    logger.info(f"Entering data fetching loop. Fetching from {since_dt} to {current_fetch_end_dt}.")
    while since_dt < current_fetch_end_dt:
        since_ms = int(since_dt.timestamp() * 1000)
        logger.debug(f"Fetching OHLCV for {config['symbol']} from {since_dt} (ms: {since_ms}).")
        try:
            ohlcv = exchange.fetch_ohlcv(config['symbol'], timeframe, since_ms, limit)
        except ccxt.RateLimitExceeded as e:
            logger.warning(f"Rate limit exceeded for {exchange_id}. Waiting {exchange.rateLimit / 1000 + 1}s and retrying... {e}")
            time.sleep(exchange.rateLimit / 1000 + 1)
            continue
        except Exception as e:
            logger.error(f"Error fetching data from {exchange_id} at {since_dt}: {e}. Breaking download loop.")
            break

        if not ohlcv:
            if no_data_retries < MAX_NO_DATA_RETRIES:
                no_data_retries += 1
                sleep_duration = sleep_time * RETRY_SLEEP_MULTIPLIER * no_data_retries
                logger.warning(f"No new data from {exchange_id} at {since_dt}. Retrying (attempt {no_data_retries}/{MAX_NO_DATA_RETRIES}) after {sleep_duration:.1f}s.")
                time.sleep(sleep_duration)
                continue
            else:
                logger.info(f"No new data from {exchange_id} at {since_dt} after {MAX_NO_DATA_RETRIES} retries. Assuming end of available history or current fetch window. Breaking download loop.")
                break

        no_data_retries = 0

        last_ts_ms = ohlcv[-1][0]
        since_dt = pd.to_datetime(last_ts_ms, unit='ms', utc=True) + pd.Timedelta(minutes=1)

        batch_count += 1
        total_rows_fetched_current_run += len(ohlcv)

        if batch_count % REPORT_BATCH_INTERVAL == 0:
            from_time = pd.to_datetime(ohlcv[0][0], unit='ms', utc=True)
            to_time = pd.to_datetime(last_ts_ms, unit='ms', utc=True)
            logger.info(f"{exchange_id}: Fetched {len(ohlcv)} rows. Total batches: {batch_count}. Total rows this run: {total_rows_fetched_current_run}. Current range: {from_time} to {to_time}. Up to {since_dt}.")

        df_new = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df_new['timestamp'] = pd.to_datetime(df_new['timestamp'], unit='ms', utc=True)
        
        for col in full_columns_set:
            if col not in df_new.columns:
                if col in ['close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote']:
                    df_new[col] = 0.0
                else:
                    df_new[col] = ''
        
        df_new = df_new[full_columns_set]

        # Write header only if start_new_file_flag is True and it's the first batch
        write_header = start_new_file_flag and batch_count == 1
        logger.debug(f"Writing {len(df_new)} rows to {config['filename']}. Append mode. Write header: {write_header}.")
        df_new.to_csv(config['filename'], mode='a', header=write_header, index=False)
        
        if write_header:
            start_new_file_flag = False # Reset flag after writing header once

        time.sleep(sleep_time)

    logger.info(f"{exchange_id}: Finished data collection for this range. Total rows fetched: {total_rows_fetched_current_run}.")
    

def main():
    cfg = exchanges['binance']

    # The year-by-year logic is disabled by 'DOWNLOAD_YEARS_ONE_BY_ONE': False in config
    # The script will now use the FORCE_RESUME_DATE and RESUME_FROM_DATE
    # to fetch data into the new temporary file.

    try:
        fetch_and_save('binance', cfg)
        # FIX: Removed the final re-processing block.
        # The goal is to just get the new data into binance_btcusdt_1m_NEW_DATA.csv
        if os.path.exists(cfg['filename']):
            logger.info(f"\n--- Download run complete for {cfg['filename']} ---")
            # Optional: Print last few lines to verify data
            try:
                df_check = pd.read_csv(cfg['filename'])
                if not df_check.empty:
                    logger.info(f"Current file '{cfg['filename']}' has {len(df_check)} rows.")
                    logger.info(f"Last timestamp in file: {pd.to_datetime(df_check['timestamp'].iloc[-1], utc=True)}")
            except Exception as e:
                logger.error(f"Could not read final file '{cfg['filename']}' for verification: {e}")


    except Exception as e:
        logger.error(f"Error fetching data from binance: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\nmulti_exchange_downloader.py: Detected KeyboardInterrupt. Exiting gracefully.")
        sys.exit(1)
