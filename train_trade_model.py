# START OF FILE train_trade_model.py

import pandas as pd
from xgboost import XGBClassifier
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import joblib
import os
import numpy as np
import sys
import pytz 

# Silence specific Pandas future warnings in VS Code
pd.set_option('future.no_silent_downcasting', True)


# --- Configuration ---
INPUT_DATA_PATH = "prepared_training_data.csv"
MODEL_OUTPUT_PATH = "trade_action_model.joblib"
ENCODER_DIR = "./" # Directory to save LabelEncoder objects (current_directory)
TRAIN_TEST_SPLIT_DATE = '2024-01-01 00:00:00' 

# List of features intended for the ML model.
model_features = [
    'open', 'high', 'low', 'close', 'volume', 'num_trades',
    'taker_buy_base', 'taker_buy_quote', 'rsi',
    'sma_7', 'sma_10', 'sma_14', 'sma_50', 
    'ema_7', 'ema_14', 'ema_50',
    'atr', 'volatility', 
    'momentum_10', 'macd', 'macd_signal', 'macd_hist',
    'trend', 'trend_5m', 'regime' 
]

def train_model():
    """
    Loads prepared data, performs chronological train-test split,
    trains the ML model, evaluates it, and saves the model and encoders.
    """
    print(f"Loading data from {INPUT_DATA_PATH} for training...")
    try:
        df = pd.read_csv(INPUT_DATA_PATH)
    except FileNotFoundError:
        print(f"Error: Input data file not found at {INPUT_DATA_PATH}.")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading input data: {e}")
        sys.exit(1)

    if df.empty:
        print("Error: Input data is empty. Cannot train model.")
        sys.exit(1)

    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
        df.dropna(subset=['timestamp'], inplace=True)
        df.sort_values('timestamp', inplace=True)
        print("Data verified and sorted chronologically by 'timestamp'.")
    else:
        print("Warning: 'timestamp' column not found. Assuming data is pre-sorted.")


    actual_features_in_df_for_model = [] 

    numeric_model_features = [
        'open', 'high', 'low', 'close', 'volume', 'num_trades',
        'taker_buy_base', 'taker_buy_quote', 'rsi',
        'sma_7', 'sma_10', 'sma_14', 'sma_50', 
        'ema_7', 'ema_14', 'ema_50',
        'atr', 'volatility', 
        'momentum_10', 'macd', 'macd_signal', 'macd_hist'
    ]
    for col in numeric_model_features:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').ffill().fillna(0) # Direct assignment
            actual_features_in_df_for_model.append(col)
        else:
            print(f"Warning: Numeric feature '{col}' not in DataFrame. Skipping.")

    categorical_cols_to_encode = ['trend', 'trend_5m', 'regime'] 
    for col in categorical_cols_to_encode:
        if col in df.columns:
            df[col] = df[col].astype(str).fillna('unknown') # Ensure string type and fill NaNs
            le = LabelEncoder()
            # Fit and transform directly, then assign back to the DataFrame column
            df[col] = le.fit_transform(df[col]) # This directly modifies df[col]
            
            # Verify dtype immediately after transformation
            if not pd.api.types.is_numeric_dtype(df[col]):
                print(f"CRITICAL ERROR: Column '{col}' DTYPE IS STILL {df[col].dtype} AFTER LabelEncoder.fit_transform().")
                # Attempt to force conversion if it's still object but looks like numbers
                try:
                    df[col] = pd.to_numeric(df[col], errors='raise') # Raise error if not convertible
                    print(f"INFO: Successfully forced '{col}' to numeric. New Dtype: {df[col].dtype}")
                except ValueError as ve:
                    print(f"CRITICAL ERROR: Could not force '{col}' to numeric after LabelEncoding. Error: {ve}")
                    print(f"Sample values for '{col}' after encoding attempt: {df[col].head().tolist()}")
                    sys.exit(1)

            os.makedirs(ENCODER_DIR, exist_ok=True)
            joblib.dump(le, os.path.join(ENCODER_DIR, f"{col}_encoder.joblib"))
            print(f"Saved LabelEncoder for '{col}' to {ENCODER_DIR}. Column '{col}' Dtype: {df[col].dtype}.")
            actual_features_in_df_for_model.append(col)
        else:
            print(f"Warning: Categorical feature '{col}' not in DataFrame. Skipping.")
            
    # Final verification loop for all features intended for the model
    print("\n--- Verifying Dtypes of Final Model Features ---")
    all_features_numeric = True
    for col in actual_features_in_df_for_model:
        if not pd.api.types.is_numeric_dtype(df[col]):
            print(f"ERROR: Feature '{col}' is not numeric. Dtype: {df[col].dtype}")
            all_features_numeric = False
        else:
            print(f"Feature '{col}' is numeric. Dtype: {df[col].dtype}")
    
    if not all_features_numeric:
        print("ERROR: Not all features intended for the model are numeric. Exiting.")
        sys.exit(1)
    print("------------------------------------------------\n")
    
    if not actual_features_in_df_for_model:
        print("Error: No valid feature columns available after processing.")
        sys.exit(1)

    print(f"Final features for model: {actual_features_in_df_for_model}")

    # --- Chronological Train-Test Split ---
    try:
        utc_timezone = pytz.utc
        split_date_aware = pd.to_datetime(TRAIN_TEST_SPLIT_DATE).tz_localize(utc_timezone)
        
        if df['timestamp'].dt.tz is None:
            df['timestamp'] = df['timestamp'].dt.tz_localize(utc_timezone)
        
        train_df = df[df['timestamp'] < split_date_aware].copy()
        test_df = df[df['timestamp'] >= split_date_aware].copy()
            
        X_train = train_df[actual_features_in_df_for_model]
        y_train = train_df['label']
        X_test = test_df[actual_features_in_df_for_model]
        y_test = test_df['label']
            
        print(f"Chronological split performed. Train data up to {TRAIN_TEST_SPLIT_DATE}.")

    except Exception as e:
        print(f"Error during chronological split: {e}")
        sys.exit(1)

    if X_train.empty or y_train.empty:
        print("Error: Training set is empty after split.")
        sys.exit(1)
    if X_test.empty or y_test.empty:
        print("Error: Test set is empty after split.")
        sys.exit(1)

    print(f"Training data shape: {X_train.shape}, Test data shape: {X_test.shape}")
    print(f"Training label distribution:\n{y_train.value_counts(dropna=False)}")
    print(f"Test label distribution:\n{y_test.value_counts(dropna=False)}")

    # --- Train the Model ---
    print("Training XGBClassifier model...")
    
    model = XGBClassifier(
        objective='multi:softprob', 
        num_class=3,               
        n_estimators=300,          
        learning_rate=0.1,         
        max_depth=7,               
        subsample=0.8,             
        colsample_bytree=0.8,      
        random_state=42,
        use_label_encoder=False,   
        eval_metric='mlogloss'     
    )
    
    label_mapping = {-1: 0, 0: 1, 1: 2} 
    y_train_mapped = y_train.map(label_mapping)
    
    if y_train_mapped.isnull().any():
        print("ERROR: y_train_mapped contains NaN values. Original labels might not be -1, 0, or 1.")
        print("Unique y_train values before mapping:", y_train.unique())
        sys.exit(1)

    model.fit(X_train, y_train_mapped) 
    print("Model training complete.")

    # --- Evaluate the Model ---
    print("\n--- Model Evaluation ---")
    y_pred_mapped = model.predict(X_test) 
    
    reverse_label_mapping = {v: k for k, v in label_mapping.items()}
    y_pred_original_scale = pd.Series(y_pred_mapped).map(reverse_label_mapping)
    
    print(f"Accuracy on test set: {accuracy_score(y_test, y_pred_original_scale):.4f}") 
    
    print("\nClassification Report (Test Set):")
    print(classification_report(y_test, y_pred_original_scale, zero_division=0, labels=[-1, 0, 1]))

    print("\nConfusion Matrix (Test Set - original scale):")
    print(confusion_matrix(y_test, y_pred_original_scale, labels=[-1, 0, 1]))

    # --- Save the Trained Model ---
    try:
        os.makedirs(os.path.dirname(MODEL_OUTPUT_PATH) or '.', exist_ok=True)
        joblib.dump(model, MODEL_OUTPUT_PATH)
        print(f"Model saved successfully as '{MODEL_OUTPUT_PATH}'")
    except Exception as e:
        print(f"Error saving model: {e}")

if __name__ == "__main__":
    try:
        import xgboost
    except ImportError:
        print("\n'xgboost' not found. It should have been installed in the previous step.")
        sys.exit(1)
    
    try:
        import sklearn
    except ImportError:
        print("\n'scikit-learn' (sklearn) not found. Please ensure it's installed.")
        sys.exit(1)

    train_model()