# Main backtesting engine
import sys
import os
from decimal import Decimal

# Add the parent directory to the Python path to allow for absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backtesting_suite.data_handler import DataHandler
from backtesting_suite.simulated_exchange import SimulatedExchange
import backtesting_suite.backtest_config as backtest_config
import csv
import json

# Import your bot's logic
import backtesting_suite.backtesting_trade_logic as trade_logic
import dynamic_parameter_service
import backtesting_suite.backtesting_config as live_config # Import the live config to get fee_percent
from bot_state import save_bot_state, load_bot_state

def run_backtest():
    """
    Main function to run the backtesting simulation.
    """
    print("--- Starting Backtest ---")

    # Get dynamic parameters from command-line arguments
    if len(sys.argv) > 2:
        stop_percent_arg = Decimal(sys.argv[1])
        trail_profit_buffer_pct_arg = Decimal(sys.argv[2])
        print(f"Received stop_percent_arg: {stop_percent_arg}, trail_profit_buffer_pct_arg: {trail_profit_buffer_pct_arg}")
    else:
        # Default values if not provided (e.g., for direct backtester.py run)
        # These defaults should match the initial values in parameter_optimizer.py
        stop_percent_arg = Decimal("0.02")
        trail_profit_buffer_pct_arg = Decimal("0.01")
        print(f"Using default stop_percent_arg: {stop_percent_arg}, trail_profit_buffer_pct_arg: {trail_profit_buffer_pct_arg}")

    # 1. Initialize components and data
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    # The data handler will now load the decision_log.csv which includes indicators
    data_handler = DataHandler(backtest_config.DATA_FILE_PATH, base_dir=base_dir)
    data_handler.load_data()

    simulated_exchange = SimulatedExchange(
        initial_capital=backtest_config.STARTING_CAPITAL,
        fee_percent=live_config.FEE_PERCENT
    )

    # Initialize bot state and other variables
    bot_state_data = load_bot_state()
    trade_logic.load_open_lots()
    trade_logic.open_lots.clear()
    
    buy_count = 0
    sell_count = 0
    session_stats = {
        "net_pl": Decimal("0.0"), "win_count": 0, "loss_count": 0,
        "win_streak": 0, "loss_streak": 0
    }

    # Buffers for in-memory logging
    trade_cycle_log_buffer = []
    trade_history_buffer = []
    
    # 2. Main simulation loop
    timestamp, tick_data = data_handler.get_next_tick()
    cycle_count = 0
    while timestamp is not None:
        cycle_count += 1
        current_price = Decimal(str(tick_data['close']))

        acct = simulated_exchange.get_account()
        pos = simulated_exchange.get_positions(symbol="BTCUSD")

        # Create the 'ind' dictionary from the pre-calculated data
        indicators = {
            "avg_price": current_price,
            "rsi": tick_data['rsi'],
            "rsi_slope": tick_data['rsi_slope'],
            "macd_hist": tick_data['macd_hist'],
            "volume_spike": tick_data['volume_spike'],
            "trend_5m": "bullish", # Placeholder
            "atr": tick_data['atr'],
            "total_unrealized_pl": "0.0", # This will be calculated by trade_logic
            "regime": "ranging" # Placeholder
        }

        # Get the pre-calculated AI decision from the data
        ai_decision = {
            "decision": tick_data['ai_decision'],
            "reasoning": "From pre-generated log",
            "confidence_level": "HIGH", # Assume high confidence for logged decisions
            "is_dip_buy": False, # This logic is handled by the rules in trade_logic
            "quantity_percentage": float(live_config.MIN_QTY_PCT_CONFIG)
        }

        # Get dynamic parameters
        dynamic_params = {
            "risk_percent": float(live_config.RISK_PERCENT),
            "stop_percent": stop_percent_arg,
            "trail_profit_buffer_pct": trail_profit_buffer_pct_arg,
        }

        print(f"\n--- Cycle {cycle_count} | {timestamp} | Price: ${current_price:.2f} | AI: {ai_decision['decision']} ---")

        # 3. Execute the trade logic using the simulated exchange
        original_submit_order = simulated_exchange.submit_order
        simulated_exchange.submit_order = lambda symbol, qty, side, type, time_in_force: \
            original_submit_order(symbol, qty, side, type, time_in_force, current_price=current_price)

        context, buy_count, sell_count = trade_logic.run_trade_cycle(
            api=simulated_exchange,
            acct=acct,
            pos=pos,
            ind=indicators,
            ai=ai_decision,
            buy_cnt=buy_count,
            sell_cnt=sell_count,
            dynamic_params=dynamic_params,
            session_stats=session_stats,
            bot_state_data=bot_state_data,
            trade_cycle_log_buffer=trade_cycle_log_buffer,
            trade_history_buffer=trade_history_buffer
        )

        simulated_exchange.submit_order = original_submit_order
        
        timestamp, tick_data = data_handler.get_next_tick()

    print("\n--- Backtest Finished ---")
    final_account_info = simulated_exchange.get_account()
    print(f"Final Equity: ${final_account_info.equity:.2f}")
    print(f"Total P/L: ${final_account_info.equity - backtest_config.STARTING_CAPITAL:.2f}")
    print(f"Wins: {session_stats['win_count']} | Losses: {session_stats['loss_count']}")

    # --- Save collected logs and final state ---
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    trade_cycle_log_path = os.path.join(base_dir, "trade_cycle_log.csv")
    trade_history_path = os.path.join(base_dir, "trade_history.csv")

    # Save trade cycle log
    if trade_cycle_log_buffer:
        with open(trade_cycle_log_path, 'w', newline='') as f:
            fieldnames = trade_cycle_log_buffer[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(trade_cycle_log_buffer)
        print(f"Trade cycle log saved to: {trade_cycle_log_path}")

    # Save trade history
    if trade_history_buffer:
        with open(trade_history_path, 'w', newline='') as f:
            fieldnames = trade_history_buffer[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(trade_history_buffer)
        print(f"Trade history saved to: {trade_history_path}")

    # Save final open lots state
    trade_logic.save_open_lots()
    print("Final open lots state saved.")


if __name__ == "__main__":
    run_backtest()

