"""
Advanced Risk Management System for Bitcoin AI Trading Bot

This system provides sophisticated risk controls including portfolio heat maps,
correlation monitoring, drawdown protection, and dynamic risk limits based on
market conditions. It integrates with existing risk systems to provide
institutional-grade risk management capabilities.

Author: Augment Agent
Date: 2025-07-30
"""

import logging
import json
import os
import threading
import time
import numpy as np
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Tuple
from collections import deque, defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics for portfolio monitoring."""
    timestamp: datetime
    portfolio_value: float
    total_exposure: float
    leverage_ratio: float
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    expected_shortfall: float
    max_drawdown: float
    current_drawdown: float
    volatility_1d: float
    volatility_7d: float
    volatility_30d: float
    sharpe_ratio: float
    sortino_ratio: float
    correlation_btc_usd: float
    concentration_risk: float
    liquidity_risk: float

@dataclass
class PositionRisk:
    """Risk metrics for individual positions."""
    symbol: str
    position_size: float
    market_value: float
    unrealized_pnl: float
    position_var: float
    position_beta: float
    concentration_pct: float
    liquidity_score: float
    time_in_position: timedelta
    stop_loss_distance: float
    risk_reward_ratio: float

@dataclass
class RiskAlert:
    """Risk alert notification."""
    timestamp: datetime
    severity: str  # 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    category: str  # 'DRAWDOWN', 'CONCENTRATION', 'VOLATILITY', 'CORRELATION', 'LIQUIDITY'
    message: str
    current_value: float
    threshold_value: float
    recommended_action: str

@dataclass
class RiskLimits:
    """Dynamic risk limits configuration."""
    max_portfolio_var: float = 0.05  # 5% portfolio VaR
    max_position_size: float = 0.25  # 25% max position size
    max_concentration: float = 0.30  # 30% max concentration
    max_drawdown: float = 0.15  # 15% max drawdown
    max_leverage: float = 1.0  # No leverage
    min_liquidity_score: float = 0.7  # Minimum liquidity requirement
    correlation_threshold: float = 0.8  # High correlation warning
    volatility_multiplier: float = 2.0  # Volatility-based scaling
    stop_loss_buffer: float = 0.02  # 2% stop loss buffer

@dataclass
class MarketRegimeRisk:
    """Risk adjustments based on market regime."""
    regime: str
    risk_multiplier: float
    max_position_adjustment: float
    volatility_adjustment: float
    correlation_adjustment: float
    liquidity_adjustment: float

class AdvancedRiskManagement:
    """
    Advanced risk management system with institutional-grade controls.
    
    Features:
    - Real-time portfolio risk monitoring
    - Dynamic risk limits based on market conditions
    - Multi-asset correlation analysis
    - Liquidity risk assessment
    - Automated position sizing controls
    - Risk-based alerts and notifications
    """
    
    def __init__(self, 
                 max_history_days: int = 90,
                 update_interval_seconds: int = 30,
                 risk_data_file: str = "advanced_risk_data.json"):
        """Initialize the advanced risk management system."""
        
        self.max_history_days = max_history_days
        self.update_interval_seconds = update_interval_seconds
        self.risk_data_file = risk_data_file
        
        # Risk tracking data
        self.risk_metrics_history: deque = deque(maxlen=max_history_days * 24 * 2)  # 30-second intervals
        self.position_risks: Dict[str, PositionRisk] = {}
        self.risk_alerts: deque = deque(maxlen=1000)
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        
        # Risk limits and configuration
        self.risk_limits = RiskLimits()
        self.regime_risk_adjustments: Dict[str, MarketRegimeRisk] = {
            'bull_market': MarketRegimeRisk('bull_market', 1.0, 1.0, 1.0, 1.0, 1.0),
            'bear_market': MarketRegimeRisk('bear_market', 0.7, 0.8, 1.2, 1.1, 0.9),
            'sideways': MarketRegimeRisk('sideways', 0.9, 0.9, 1.0, 1.0, 1.0),
            'high_volatility': MarketRegimeRisk('high_volatility', 0.6, 0.7, 1.5, 1.2, 0.8),
            'low_volatility': MarketRegimeRisk('low_volatility', 1.1, 1.1, 0.8, 0.9, 1.1),
            'breakout_bull': MarketRegimeRisk('breakout_bull', 1.2, 1.2, 1.1, 1.0, 1.0),
            'breakout_bear': MarketRegimeRisk('breakout_bear', 0.5, 0.6, 1.3, 1.2, 0.8),
            'reversal_bull': MarketRegimeRisk('reversal_bull', 0.8, 0.9, 1.1, 1.0, 0.9),
            'reversal_bear': MarketRegimeRisk('reversal_bear', 0.6, 0.7, 1.2, 1.1, 0.8),
            'unknown': MarketRegimeRisk('unknown', 0.7, 0.8, 1.1, 1.0, 0.9)
        }
        
        # Current state
        self.current_regime = 'unknown'
        self.current_volatility = 0.02
        self.portfolio_value = 0.0
        self.last_update_time = datetime.now(timezone.utc)
        
        # Threading for real-time updates
        self.is_running = False
        self.update_thread: Optional[threading.Thread] = None
        self.data_lock = threading.Lock()
        
        # Performance tracking
        self.stats = {
            'risk_calculations': 0,
            'alerts_generated': 0,
            'position_adjustments': 0,
            'regime_adjustments': 0,
            'correlation_updates': 0
        }
        
        # Load existing data
        self._load_risk_data()
        
        logger.info("Advanced Risk Management System initialized")
    
    def update_market_conditions(self, 
                               regime: str,
                               volatility: float,
                               portfolio_value: float,
                               positions: Dict[str, Any] = None) -> None:
        """Update current market conditions and portfolio state."""
        try:
            with self.data_lock:
                self.current_regime = regime
                self.current_volatility = volatility
                self.portfolio_value = portfolio_value
                self.last_update_time = datetime.now(timezone.utc)
                
                # Update position risks if provided
                if positions:
                    self._update_position_risks(positions)
                
                # Calculate current risk metrics
                risk_metrics = self._calculate_portfolio_risk_metrics()
                self.risk_metrics_history.append(risk_metrics)
                
                # Check for risk alerts
                self._check_risk_alerts(risk_metrics)
                
                self.stats['risk_calculations'] += 1
                
                logger.debug(f"Updated market conditions: regime={regime}, "
                           f"volatility={volatility:.3f}, portfolio=${portfolio_value:.2f}")
                
        except Exception as e:
            logger.error(f"Error updating market conditions: {e}")
    
    def calculate_position_size_limit(self, 
                                    symbol: str,
                                    base_position_size: float,
                                    confidence_level: float = 0.8) -> Tuple[float, str]:
        """
        Calculate position size limit based on advanced risk controls.
        
        Args:
            symbol: Trading symbol
            base_position_size: Base position size from other systems
            confidence_level: Confidence in the trade setup
            
        Returns:
            Tuple of (adjusted_position_size, adjustment_reason)
        """
        try:
            with self.data_lock:
                # Get regime-specific risk adjustments
                regime_risk = self.regime_risk_adjustments.get(self.current_regime, 
                                                             self.regime_risk_adjustments['unknown'])
                
                # Start with base position size
                adjusted_size = base_position_size
                adjustment_reasons = []
                
                # Apply regime-based position adjustment
                regime_adjustment = regime_risk.max_position_adjustment
                adjusted_size *= regime_adjustment
                if regime_adjustment != 1.0:
                    adjustment_reasons.append(f"regime_{self.current_regime}({regime_adjustment:.2f})")
                
                # Apply volatility-based adjustment
                volatility_adjustment = self._calculate_volatility_position_adjustment()
                adjusted_size *= volatility_adjustment
                if volatility_adjustment != 1.0:
                    adjustment_reasons.append(f"volatility({volatility_adjustment:.2f})")
                
                # Apply concentration limits
                concentration_adjustment = self._calculate_concentration_adjustment(symbol, adjusted_size)
                adjusted_size *= concentration_adjustment
                if concentration_adjustment != 1.0:
                    adjustment_reasons.append(f"concentration({concentration_adjustment:.2f})")
                
                # Apply portfolio risk limits
                portfolio_risk_adjustment = self._calculate_portfolio_risk_adjustment()
                adjusted_size *= portfolio_risk_adjustment
                if portfolio_risk_adjustment != 1.0:
                    adjustment_reasons.append(f"portfolio_risk({portfolio_risk_adjustment:.2f})")
                
                # Apply absolute limits
                max_position = self.risk_limits.max_position_size * regime_risk.max_position_adjustment
                if adjusted_size > max_position:
                    adjusted_size = max_position
                    adjustment_reasons.append(f"max_limit({max_position:.3f})")
                
                # Ensure minimum position size
                min_position = 0.001  # 0.1% minimum
                adjusted_size = max(min_position, adjusted_size)
                
                # Create adjustment reason string
                reason = "risk_mgmt:" + ",".join(adjustment_reasons) if adjustment_reasons else "no_adjustment"
                
                self.stats['position_adjustments'] += 1
                
                return adjusted_size, reason
                
        except Exception as e:
            logger.error(f"Error calculating position size limit: {e}")
            return base_position_size, "error_fallback"
    
    def check_trade_approval(self, 
                           symbol: str,
                           action: str,
                           quantity: float,
                           price: float) -> Tuple[bool, str]:
        """
        Check if a trade should be approved based on risk controls.
        
        Args:
            symbol: Trading symbol
            action: Trade action ('BUY' or 'SELL')
            quantity: Trade quantity
            price: Trade price
            
        Returns:
            Tuple of (approved, reason)
        """
        try:
            with self.data_lock:
                trade_value = quantity * price
                
                # Check portfolio risk limits
                if action == 'BUY':
                    # Check if trade would exceed portfolio limits
                    new_exposure = self._calculate_new_exposure(symbol, trade_value)
                    
                    if new_exposure > self.risk_limits.max_portfolio_var:
                        return False, f"Portfolio VaR limit exceeded: {new_exposure:.2%} > {self.risk_limits.max_portfolio_var:.2%}"
                    
                    # Check concentration limits
                    new_concentration = self._calculate_new_concentration(symbol, trade_value)
                    if new_concentration > self.risk_limits.max_concentration:
                        return False, f"Concentration limit exceeded: {new_concentration:.2%} > {self.risk_limits.max_concentration:.2%}"
                    
                    # Check regime-specific restrictions
                    regime_risk = self.regime_risk_adjustments.get(self.current_regime)
                    if regime_risk and regime_risk.risk_multiplier < 0.7:
                        if trade_value > self.portfolio_value * 0.05:  # 5% limit in high-risk regimes
                            return False, f"High-risk regime {self.current_regime}: trade size limited to 5% of portfolio"
                
                # Check liquidity requirements
                liquidity_score = self._calculate_liquidity_score(symbol)
                if liquidity_score < self.risk_limits.min_liquidity_score:
                    return False, f"Insufficient liquidity: {liquidity_score:.2f} < {self.risk_limits.min_liquidity_score:.2f}"
                
                # Check drawdown limits
                current_drawdown = self._get_current_drawdown()
                if current_drawdown > self.risk_limits.max_drawdown:
                    if action == 'BUY':
                        return False, f"Drawdown limit exceeded: {current_drawdown:.2%} > {self.risk_limits.max_drawdown:.2%}"
                
                return True, "approved"
                
        except Exception as e:
            logger.error(f"Error checking trade approval: {e}")
            return False, f"risk_check_error: {str(e)}"

    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive risk dashboard data."""
        try:
            with self.data_lock:
                current_metrics = self.risk_metrics_history[-1] if self.risk_metrics_history else None

                # Calculate portfolio heat map
                heat_map = self._generate_portfolio_heat_map()

                # Get recent alerts
                recent_alerts = list(self.risk_alerts)[-10:] if self.risk_alerts else []

                # Calculate risk-adjusted performance
                performance_metrics = self._calculate_risk_adjusted_performance()

                dashboard = {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'current_metrics': asdict(current_metrics) if current_metrics else {},
                    'portfolio_heat_map': heat_map,
                    'position_risks': {k: asdict(v) for k, v in self.position_risks.items()},
                    'recent_alerts': [asdict(alert) for alert in recent_alerts],
                    'risk_limits': asdict(self.risk_limits),
                    'regime_adjustments': {k: asdict(v) for k, v in self.regime_risk_adjustments.items()},
                    'performance_metrics': performance_metrics,
                    'correlation_matrix': self.correlation_matrix,
                    'system_stats': self.stats.copy()
                }

                return dashboard

        except Exception as e:
            logger.error(f"Error generating risk dashboard: {e}")
            return {}

    def _calculate_portfolio_risk_metrics(self) -> RiskMetrics:
        """Calculate comprehensive portfolio risk metrics."""
        try:
            timestamp = datetime.now(timezone.utc)

            # Get historical returns for calculations
            returns = self._get_portfolio_returns()

            # Calculate VaR and Expected Shortfall
            var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0
            var_99 = np.percentile(returns, 1) if len(returns) > 0 else 0

            # Expected Shortfall (Conditional VaR)
            tail_returns = [r for r in returns if r <= var_95]
            expected_shortfall = np.mean(tail_returns) if tail_returns else 0

            # Calculate drawdown metrics
            max_drawdown, current_drawdown = self._calculate_drawdown_metrics()

            # Calculate volatility metrics
            vol_1d = np.std(returns[-24:]) if len(returns) >= 24 else 0  # Last 24 periods
            vol_7d = np.std(returns[-168:]) if len(returns) >= 168 else 0  # Last 7 days
            vol_30d = np.std(returns) if len(returns) > 0 else 0

            # Calculate Sharpe and Sortino ratios
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            sortino_ratio = self._calculate_sortino_ratio(returns)

            # Calculate exposure and leverage
            total_exposure = sum(abs(pos.market_value) for pos in self.position_risks.values())
            leverage_ratio = total_exposure / self.portfolio_value if self.portfolio_value > 0 else 0

            # Calculate concentration and liquidity risks
            concentration_risk = self._calculate_concentration_risk()
            liquidity_risk = self._calculate_liquidity_risk()

            # BTC/USD correlation (simplified)
            correlation_btc_usd = self._calculate_btc_correlation()

            return RiskMetrics(
                timestamp=timestamp,
                portfolio_value=self.portfolio_value,
                total_exposure=total_exposure,
                leverage_ratio=leverage_ratio,
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                volatility_1d=vol_1d,
                volatility_7d=vol_7d,
                volatility_30d=vol_30d,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                correlation_btc_usd=correlation_btc_usd,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk
            )

        except Exception as e:
            logger.error(f"Error calculating portfolio risk metrics: {e}")
            return RiskMetrics(
                timestamp=datetime.now(timezone.utc),
                portfolio_value=self.portfolio_value,
                total_exposure=0, leverage_ratio=0, var_95=0, var_99=0,
                expected_shortfall=0, max_drawdown=0, current_drawdown=0,
                volatility_1d=0, volatility_7d=0, volatility_30d=0,
                sharpe_ratio=0, sortino_ratio=0, correlation_btc_usd=0,
                concentration_risk=0, liquidity_risk=0
            )

    def _update_position_risks(self, positions: Dict[str, Any]) -> None:
        """Update position-specific risk metrics."""
        try:
            for symbol, position_data in positions.items():
                # Calculate position risk metrics
                position_size = float(position_data.get('quantity', 0))
                market_value = float(position_data.get('market_value', 0))
                unrealized_pnl = float(position_data.get('unrealized_pl', 0))

                # Calculate position VaR (simplified)
                position_var = abs(market_value) * self.current_volatility * 2.33  # 99% confidence

                # Calculate concentration percentage
                concentration_pct = abs(market_value) / self.portfolio_value if self.portfolio_value > 0 else 0

                # Calculate liquidity score
                liquidity_score = self._calculate_liquidity_score(symbol)

                # Calculate time in position
                entry_time = position_data.get('entry_time')
                if entry_time:
                    if isinstance(entry_time, str):
                        entry_time = datetime.fromisoformat(entry_time.replace('Z', '+00:00'))
                    time_in_position = datetime.now(timezone.utc) - entry_time
                else:
                    time_in_position = timedelta(0)

                # Calculate stop loss distance
                current_price = float(position_data.get('current_price', 0))
                stop_loss = float(position_data.get('stop_loss', 0))
                stop_loss_distance = abs(current_price - stop_loss) / current_price if current_price > 0 else 0

                # Calculate risk-reward ratio
                target_price = float(position_data.get('target_price', 0))
                if target_price > 0 and stop_loss > 0 and current_price > 0:
                    potential_reward = abs(target_price - current_price) / current_price
                    potential_risk = abs(current_price - stop_loss) / current_price
                    risk_reward_ratio = potential_reward / potential_risk if potential_risk > 0 else 0
                else:
                    risk_reward_ratio = 0

                # Create position risk object
                position_risk = PositionRisk(
                    symbol=symbol,
                    position_size=position_size,
                    market_value=market_value,
                    unrealized_pnl=unrealized_pnl,
                    position_var=position_var,
                    position_beta=1.0,  # Simplified - could be calculated from correlation
                    concentration_pct=concentration_pct,
                    liquidity_score=liquidity_score,
                    time_in_position=time_in_position,
                    stop_loss_distance=stop_loss_distance,
                    risk_reward_ratio=risk_reward_ratio
                )

                self.position_risks[symbol] = position_risk

        except Exception as e:
            logger.error(f"Error updating position risks: {e}")

    def _check_risk_alerts(self, risk_metrics: RiskMetrics) -> None:
        """Check for risk threshold breaches and generate alerts."""
        try:
            alerts = []

            # Check drawdown alerts
            if risk_metrics.current_drawdown > self.risk_limits.max_drawdown * 0.8:  # 80% of limit
                severity = 'HIGH' if risk_metrics.current_drawdown > self.risk_limits.max_drawdown else 'MEDIUM'
                alerts.append(RiskAlert(
                    timestamp=datetime.now(timezone.utc),
                    severity=severity,
                    category='DRAWDOWN',
                    message=f"Portfolio drawdown approaching limit: {risk_metrics.current_drawdown:.2%}",
                    current_value=risk_metrics.current_drawdown,
                    threshold_value=self.risk_limits.max_drawdown,
                    recommended_action="Reduce position sizes or close losing positions"
                ))

            # Check concentration alerts
            if risk_metrics.concentration_risk > self.risk_limits.max_concentration * 0.9:  # 90% of limit
                severity = 'HIGH' if risk_metrics.concentration_risk > self.risk_limits.max_concentration else 'MEDIUM'
                alerts.append(RiskAlert(
                    timestamp=datetime.now(timezone.utc),
                    severity=severity,
                    category='CONCENTRATION',
                    message=f"Portfolio concentration risk high: {risk_metrics.concentration_risk:.2%}",
                    current_value=risk_metrics.concentration_risk,
                    threshold_value=self.risk_limits.max_concentration,
                    recommended_action="Diversify positions or reduce largest position"
                ))

            # Check volatility alerts
            if risk_metrics.volatility_1d > self.current_volatility * self.risk_limits.volatility_multiplier:
                alerts.append(RiskAlert(
                    timestamp=datetime.now(timezone.utc),
                    severity='MEDIUM',
                    category='VOLATILITY',
                    message=f"High volatility detected: {risk_metrics.volatility_1d:.3f}",
                    current_value=risk_metrics.volatility_1d,
                    threshold_value=self.current_volatility * self.risk_limits.volatility_multiplier,
                    recommended_action="Consider reducing position sizes"
                ))

            # Check VaR alerts
            if abs(risk_metrics.var_95) > self.risk_limits.max_portfolio_var:
                alerts.append(RiskAlert(
                    timestamp=datetime.now(timezone.utc),
                    severity='HIGH',
                    category='VAR',
                    message=f"Portfolio VaR exceeds limit: {abs(risk_metrics.var_95):.2%}",
                    current_value=abs(risk_metrics.var_95),
                    threshold_value=self.risk_limits.max_portfolio_var,
                    recommended_action="Reduce overall portfolio risk"
                ))

            # Check liquidity alerts
            if risk_metrics.liquidity_risk > 0.7:  # High liquidity risk
                alerts.append(RiskAlert(
                    timestamp=datetime.now(timezone.utc),
                    severity='MEDIUM',
                    category='LIQUIDITY',
                    message=f"High liquidity risk detected: {risk_metrics.liquidity_risk:.2f}",
                    current_value=risk_metrics.liquidity_risk,
                    threshold_value=0.7,
                    recommended_action="Review position sizes in illiquid assets"
                ))

            # Add alerts to history
            for alert in alerts:
                self.risk_alerts.append(alert)
                self.stats['alerts_generated'] += 1

                # Log critical alerts
                if alert.severity in ['HIGH', 'CRITICAL']:
                    logger.warning(f"Risk Alert [{alert.severity}] {alert.category}: {alert.message}")

        except Exception as e:
            logger.error(f"Error checking risk alerts: {e}")

    def _calculate_volatility_position_adjustment(self) -> float:
        """Calculate position size adjustment based on volatility."""
        try:
            # Base volatility assumption (2% daily)
            base_volatility = 0.02

            # Scale position size inversely with volatility
            if self.current_volatility > 0:
                volatility_ratio = base_volatility / self.current_volatility
                # Cap the adjustment between 0.5x and 2.0x
                return max(0.5, min(2.0, volatility_ratio))

            return 1.0

        except Exception as e:
            logger.error(f"Error calculating volatility adjustment: {e}")
            return 1.0

    def _calculate_concentration_adjustment(self, symbol: str, position_size: float) -> float:
        """Calculate position size adjustment based on concentration limits."""
        try:
            if self.portfolio_value <= 0:
                return 1.0

            # Calculate current concentration for this symbol
            current_exposure = self.position_risks.get(symbol, PositionRisk(
                symbol=symbol, position_size=0, market_value=0, unrealized_pnl=0,
                position_var=0, position_beta=0, concentration_pct=0, liquidity_score=1.0,
                time_in_position=timedelta(0), stop_loss_distance=0, risk_reward_ratio=0
            )).market_value

            # Calculate new concentration if this position were added
            new_exposure = abs(current_exposure) + abs(position_size * self.portfolio_value)
            new_concentration = new_exposure / self.portfolio_value

            # If new concentration would exceed limits, scale down
            if new_concentration > self.risk_limits.max_concentration:
                max_additional = self.risk_limits.max_concentration * self.portfolio_value - abs(current_exposure)
                if max_additional > 0:
                    adjustment = max_additional / (position_size * self.portfolio_value)
                    return max(0.1, adjustment)  # Minimum 10% of requested size
                else:
                    return 0.1  # Very small position if already at limit

            return 1.0

        except Exception as e:
            logger.error(f"Error calculating concentration adjustment: {e}")
            return 1.0

    def _calculate_portfolio_risk_adjustment(self) -> float:
        """Calculate position size adjustment based on overall portfolio risk."""
        try:
            if not self.risk_metrics_history:
                return 1.0

            current_metrics = self.risk_metrics_history[-1]

            # Reduce position sizes if portfolio risk is high
            risk_factors = []

            # VaR-based adjustment
            if abs(current_metrics.var_95) > self.risk_limits.max_portfolio_var * 0.8:
                var_adjustment = 1 - (abs(current_metrics.var_95) / self.risk_limits.max_portfolio_var)
                risk_factors.append(max(0.5, var_adjustment))

            # Drawdown-based adjustment
            if current_metrics.current_drawdown > self.risk_limits.max_drawdown * 0.7:
                dd_adjustment = 1 - (current_metrics.current_drawdown / self.risk_limits.max_drawdown)
                risk_factors.append(max(0.3, dd_adjustment))

            # Volatility-based adjustment
            if current_metrics.volatility_1d > self.current_volatility * 1.5:
                vol_adjustment = self.current_volatility * 1.5 / current_metrics.volatility_1d
                risk_factors.append(max(0.6, vol_adjustment))

            # Return the most conservative adjustment
            if risk_factors:
                return min(risk_factors)

            return 1.0

        except Exception as e:
            logger.error(f"Error calculating portfolio risk adjustment: {e}")
            return 1.0

    def _calculate_new_exposure(self, symbol: str, trade_value: float) -> float:
        """Calculate new portfolio exposure if trade were executed."""
        try:
            current_exposure = sum(abs(pos.market_value) for pos in self.position_risks.values())
            new_exposure = current_exposure + abs(trade_value)
            return new_exposure / self.portfolio_value if self.portfolio_value > 0 else 0

        except Exception as e:
            logger.error(f"Error calculating new exposure: {e}")
            return 0

    def _calculate_new_concentration(self, symbol: str, trade_value: float) -> float:
        """Calculate new concentration for symbol if trade were executed."""
        try:
            current_value = self.position_risks.get(symbol, PositionRisk(
                symbol=symbol, position_size=0, market_value=0, unrealized_pnl=0,
                position_var=0, position_beta=0, concentration_pct=0, liquidity_score=1.0,
                time_in_position=timedelta(0), stop_loss_distance=0, risk_reward_ratio=0
            )).market_value

            new_value = abs(current_value) + abs(trade_value)
            return new_value / self.portfolio_value if self.portfolio_value > 0 else 0

        except Exception as e:
            logger.error(f"Error calculating new concentration: {e}")
            return 0

    def _calculate_liquidity_score(self, symbol: str) -> float:
        """Calculate liquidity score for a symbol."""
        try:
            # Simplified liquidity scoring
            # In a real implementation, this would consider:
            # - Average daily volume
            # - Bid-ask spreads
            # - Market depth
            # - Time to execute typical position sizes

            if symbol.upper() in ['BTC', 'BTCUSD', 'BTC/USD']:
                return 0.95  # Very high liquidity for BTC
            elif symbol.upper() in ['ETH', 'ETHUSD', 'ETH/USD']:
                return 0.90  # High liquidity for ETH
            else:
                return 0.75  # Default moderate liquidity

        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.5

    def _get_current_drawdown(self) -> float:
        """Get current portfolio drawdown."""
        try:
            if not self.risk_metrics_history:
                return 0.0

            return self.risk_metrics_history[-1].current_drawdown

        except Exception as e:
            logger.error(f"Error getting current drawdown: {e}")
            return 0.0

    def _get_portfolio_returns(self) -> List[float]:
        """Get historical portfolio returns."""
        try:
            if len(self.risk_metrics_history) < 2:
                return []

            returns = []
            for i in range(1, len(self.risk_metrics_history)):
                prev_value = self.risk_metrics_history[i-1].portfolio_value
                curr_value = self.risk_metrics_history[i].portfolio_value

                if prev_value > 0:
                    return_pct = (curr_value - prev_value) / prev_value
                    returns.append(return_pct)

            return returns

        except Exception as e:
            logger.error(f"Error getting portfolio returns: {e}")
            return []

    def _calculate_drawdown_metrics(self) -> Tuple[float, float]:
        """Calculate maximum and current drawdown."""
        try:
            if len(self.risk_metrics_history) < 2:
                return 0.0, 0.0

            values = [m.portfolio_value for m in self.risk_metrics_history]

            # Calculate running maximum
            running_max = np.maximum.accumulate(values)

            # Calculate drawdowns
            drawdowns = (running_max - values) / running_max

            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0.0
            current_drawdown = drawdowns[-1] if len(drawdowns) > 0 else 0.0

            return max_drawdown, current_drawdown

        except Exception as e:
            logger.error(f"Error calculating drawdown metrics: {e}")
            return 0.0, 0.0

    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calculate Sharpe ratio."""
        try:
            if len(returns) < 2:
                return 0.0

            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return > 0:
                # Annualized Sharpe ratio (assuming 30-second intervals)
                periods_per_year = 365 * 24 * 2  # 30-second intervals
                sharpe = (mean_return * np.sqrt(periods_per_year)) / std_return
                return sharpe

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_sortino_ratio(self, returns: List[float]) -> float:
        """Calculate Sortino ratio (downside deviation)."""
        try:
            if len(returns) < 2:
                return 0.0

            mean_return = np.mean(returns)
            negative_returns = [r for r in returns if r < 0]

            if len(negative_returns) > 0:
                downside_std = np.std(negative_returns)
                if downside_std > 0:
                    # Annualized Sortino ratio
                    periods_per_year = 365 * 24 * 2
                    sortino = (mean_return * np.sqrt(periods_per_year)) / downside_std
                    return sortino

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0

    def _calculate_concentration_risk(self) -> float:
        """Calculate portfolio concentration risk."""
        try:
            if not self.position_risks or self.portfolio_value <= 0:
                return 0.0

            # Calculate Herfindahl-Hirschman Index for concentration
            concentrations = [pos.concentration_pct for pos in self.position_risks.values()]
            hhi = sum(c**2 for c in concentrations)

            return hhi

        except Exception as e:
            logger.error(f"Error calculating concentration risk: {e}")
            return 0.0

    def _calculate_liquidity_risk(self) -> float:
        """Calculate portfolio liquidity risk."""
        try:
            if not self.position_risks:
                return 0.0

            # Weighted average of liquidity scores
            total_value = sum(abs(pos.market_value) for pos in self.position_risks.values())
            if total_value <= 0:
                return 0.0

            weighted_liquidity = sum(
                abs(pos.market_value) * (1 - pos.liquidity_score)
                for pos in self.position_risks.values()
            ) / total_value

            return weighted_liquidity

        except Exception as e:
            logger.error(f"Error calculating liquidity risk: {e}")
            return 0.0

    def _calculate_btc_correlation(self) -> float:
        """Calculate correlation with BTC/USD."""
        try:
            # Simplified correlation calculation
            # In a real implementation, this would calculate correlation
            # between portfolio returns and BTC returns

            if 'BTC' in self.position_risks or 'BTCUSD' in self.position_risks:
                return 0.95  # High correlation if holding BTC
            else:
                return 0.3   # Moderate correlation for other crypto assets

        except Exception as e:
            logger.error(f"Error calculating BTC correlation: {e}")
            return 0.0

    def _generate_portfolio_heat_map(self) -> Dict[str, Any]:
        """Generate portfolio risk heat map."""
        try:
            heat_map = {
                'positions': {},
                'risk_levels': {
                    'low': [],
                    'medium': [],
                    'high': [],
                    'critical': []
                },
                'total_risk_score': 0.0
            }

            total_risk = 0.0

            for symbol, pos_risk in self.position_risks.items():
                # Calculate composite risk score
                risk_score = (
                    pos_risk.concentration_pct * 0.3 +
                    pos_risk.position_var / self.portfolio_value * 0.3 +
                    (1 - pos_risk.liquidity_score) * 0.2 +
                    max(0, -pos_risk.unrealized_pnl / abs(pos_risk.market_value)) * 0.2
                ) if self.portfolio_value > 0 and pos_risk.market_value != 0 else 0

                # Categorize risk level
                if risk_score < 0.1:
                    risk_level = 'low'
                elif risk_score < 0.25:
                    risk_level = 'medium'
                elif risk_score < 0.5:
                    risk_level = 'high'
                else:
                    risk_level = 'critical'

                position_data = {
                    'symbol': symbol,
                    'risk_score': risk_score,
                    'concentration': pos_risk.concentration_pct,
                    'var': pos_risk.position_var,
                    'liquidity': pos_risk.liquidity_score,
                    'unrealized_pnl': pos_risk.unrealized_pnl,
                    'market_value': pos_risk.market_value
                }

                heat_map['positions'][symbol] = position_data
                heat_map['risk_levels'][risk_level].append(symbol)
                total_risk += risk_score

            heat_map['total_risk_score'] = total_risk

            return heat_map

        except Exception as e:
            logger.error(f"Error generating portfolio heat map: {e}")
            return {}

    def _calculate_risk_adjusted_performance(self) -> Dict[str, float]:
        """Calculate risk-adjusted performance metrics."""
        try:
            if not self.risk_metrics_history:
                return {}

            current_metrics = self.risk_metrics_history[-1]

            return {
                'sharpe_ratio': current_metrics.sharpe_ratio,
                'sortino_ratio': current_metrics.sortino_ratio,
                'calmar_ratio': current_metrics.sharpe_ratio / max(current_metrics.max_drawdown, 0.01),
                'risk_adjusted_return': current_metrics.sharpe_ratio * current_metrics.volatility_30d,
                'volatility_adjusted_return': current_metrics.sharpe_ratio / max(current_metrics.volatility_30d, 0.01)
            }

        except Exception as e:
            logger.error(f"Error calculating risk-adjusted performance: {e}")
            return {}

    def _load_risk_data(self) -> None:
        """Load existing risk data from file."""
        try:
            if os.path.exists(self.risk_data_file):
                with open(self.risk_data_file, 'r') as f:
                    data = json.load(f)

                # Load risk metrics history
                if 'risk_metrics_history' in data:
                    for metrics_data in data['risk_metrics_history'][-100:]:  # Last 100 entries
                        metrics_data['timestamp'] = datetime.fromisoformat(metrics_data['timestamp'])
                        self.risk_metrics_history.append(RiskMetrics(**metrics_data))

                # Load risk limits
                if 'risk_limits' in data:
                    self.risk_limits = RiskLimits(**data['risk_limits'])

                logger.info(f"Loaded {len(self.risk_metrics_history)} risk metrics from {self.risk_data_file}")

        except Exception as e:
            logger.warning(f"Could not load risk data: {e}")

    def _save_risk_data(self) -> None:
        """Save risk data to file."""
        try:
            data = {
                'risk_metrics_history': [asdict(m) for m in list(self.risk_metrics_history)[-100:]],
                'risk_limits': asdict(self.risk_limits),
                'last_update': datetime.now(timezone.utc).isoformat(),
                'stats': self.stats
            }

            with open(self.risk_data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error saving risk data: {e}")

    def start_realtime_monitoring(self) -> None:
        """Start real-time risk monitoring."""
        try:
            if self.is_running:
                logger.warning("Risk monitoring already running")
                return

            self.is_running = True
            self.update_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.update_thread.start()

            logger.info("Advanced risk monitoring started")

        except Exception as e:
            logger.error(f"Error starting risk monitoring: {e}")

    def stop_realtime_monitoring(self) -> None:
        """Stop real-time risk monitoring."""
        try:
            self.is_running = False
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=5)

            # Save final data
            self._save_risk_data()

            logger.info("Advanced risk monitoring stopped")

        except Exception as e:
            logger.error(f"Error stopping risk monitoring: {e}")

    def _monitoring_loop(self) -> None:
        """Main monitoring loop for real-time risk assessment."""
        while self.is_running:
            try:
                # Save risk data periodically
                self._save_risk_data()

                # Sleep until next update
                time.sleep(self.update_interval_seconds)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.update_interval_seconds)


# Global risk management system instance
_risk_management_system: Optional[AdvancedRiskManagement] = None


def initialize_advanced_risk_management(max_history_days: int = 90,
                                      update_interval_seconds: int = 30) -> bool:
    """
    Initialize the advanced risk management system.

    Args:
        max_history_days: Maximum days of risk history to maintain
        update_interval_seconds: Interval for real-time monitoring

    Returns:
        bool: True if initialization successful
    """
    global _risk_management_system

    try:
        _risk_management_system = AdvancedRiskManagement(
            max_history_days=max_history_days,
            update_interval_seconds=update_interval_seconds
        )

        # Start real-time monitoring
        _risk_management_system.start_realtime_monitoring()

        logger.info("Advanced risk management system initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing advanced risk management system: {e}")
        return False


def update_risk_conditions(regime: str,
                         volatility: float,
                         portfolio_value: float,
                         positions: Dict[str, Any] = None) -> bool:
    """
    Update market conditions and portfolio state for risk assessment.

    Args:
        regime: Current market regime
        volatility: Current market volatility
        portfolio_value: Current portfolio value
        positions: Current positions data

    Returns:
        bool: True if update successful
    """
    global _risk_management_system

    try:
        if _risk_management_system is None:
            logger.warning("Advanced risk management system not initialized")
            return False

        _risk_management_system.update_market_conditions(
            regime, volatility, portfolio_value, positions
        )
        return True

    except Exception as e:
        logger.error(f"Error updating risk conditions: {e}")
        return False


def calculate_risk_adjusted_position_size(symbol: str,
                                        base_position_size: float,
                                        confidence_level: float = 0.8) -> Tuple[float, str]:
    """
    Calculate risk-adjusted position size.

    Args:
        symbol: Trading symbol
        base_position_size: Base position size from other systems
        confidence_level: Confidence in the trade setup

    Returns:
        Tuple of (adjusted_position_size, adjustment_reason)
    """
    global _risk_management_system

    try:
        if _risk_management_system is None:
            logger.warning("Advanced risk management system not initialized")
            return base_position_size, "risk_mgmt_not_initialized"

        return _risk_management_system.calculate_position_size_limit(
            symbol, base_position_size, confidence_level
        )

    except Exception as e:
        logger.error(f"Error calculating risk-adjusted position size: {e}")
        return base_position_size, f"risk_calculation_error: {str(e)}"


def check_trade_risk_approval(symbol: str,
                            action: str,
                            quantity: float,
                            price: float) -> Tuple[bool, str]:
    """
    Check if a trade should be approved based on risk controls.

    Args:
        symbol: Trading symbol
        action: Trade action ('BUY' or 'SELL')
        quantity: Trade quantity
        price: Trade price

    Returns:
        Tuple of (approved, reason)
    """
    global _risk_management_system

    try:
        if _risk_management_system is None:
            logger.warning("Advanced risk management system not initialized")
            return True, "risk_mgmt_not_initialized"

        return _risk_management_system.check_trade_approval(
            symbol, action, quantity, price
        )

    except Exception as e:
        logger.error(f"Error checking trade risk approval: {e}")
        return False, f"risk_check_error: {str(e)}"


def get_risk_dashboard_data() -> Dict[str, Any]:
    """
    Get comprehensive risk dashboard data.

    Returns:
        dict: Risk dashboard data
    """
    global _risk_management_system

    try:
        if _risk_management_system is None:
            logger.warning("Advanced risk management system not initialized")
            return {}

        return _risk_management_system.get_risk_dashboard()

    except Exception as e:
        logger.error(f"Error getting risk dashboard data: {e}")
        return {}


def stop_advanced_risk_management() -> bool:
    """
    Stop the advanced risk management system.

    Returns:
        bool: True if stopped successfully
    """
    global _risk_management_system

    try:
        if _risk_management_system is None:
            return True

        _risk_management_system.stop_realtime_monitoring()
        _risk_management_system = None

        logger.info("Advanced risk management system stopped")
        return True

    except Exception as e:
        logger.error(f"Error stopping advanced risk management system: {e}")
        return False
