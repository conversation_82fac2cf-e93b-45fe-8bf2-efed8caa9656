# START OF FILE weekly_tuner.py

import json
import os
import logging
from datetime import datetime
from decimal import Decimal

import google.generativeai as genai

# Import our custom analysis libraries
import performance_analyzer as pa
import trade_analyzer # <-- ADDED: Import our new post-mortem engine

# Import the main config file to get the Gemini API key
import config

# --- Configuration ---
# Configure logging for this script
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("WeeklyTuner")

# File paths
TRADE_HISTORY_FILE = "trade_history.csv"
DECISION_LOG_FILE = "decision_log_full.csv"
DYNAMIC_CONFIG_FILE = "dynamic_config.json"
ML_MODEL_FILE = "trade_action_model.joblib"
TUNING_REPORT_FILE = "tuning_reports.log"

# --- AI Configuration ---
# Configure the Gemini client
try:
    genai.configure(api_key=config.GEMINI_API_KEY)
    _MODEL = genai.GenerativeModel(config.GEMINI_MODEL)
    logger.info("Gemini AI model configured successfully.")
except Exception as e:
    logger.error(f"Failed to configure Gemini AI: {e}")
    _MODEL = None

# MODIFIED: The prompt is upgraded to include the new post-mortem analysis.
_ADVANCED_TUNING_PROMPT_TEMPLATE = """
You are a top-tier quantitative analyst responsible for managing and optimizing an autonomous trading bot.

Your task is to analyze the performance data provided and suggest optimized parameters for the Live LLM Model.

**1. Live LLM Model Performance (Based on Actual Executed Trades):**
{llm_summary}

**2. Benchmark ML Model Performance (Simulated on the same historical data):**
{ml_summary}

**3. Post-Mortem Analysis (Correlation of Indicators to Outcomes):**
{post_mortem_summary}

**Your Required Output:**
You MUST respond with a single, valid JSON object and nothing else. This JSON object must contain two top-level keys: "tuned_parameters" and "performance_review".

1.  **"tuned_parameters"**: An object containing the optimized parameters for the **Live LLM Model**. You must synthesize insights from ALL THREE data sources above. For example, if the post-mortem shows wins happen with a high RSI, and the performance summary confirms this, suggest an appropriate RSI threshold. The keys must be:
    - "RSI_BUY_THRESHOLD" (float)
    - "MACD_HIST_BUY_THRESHOLD" (float)
    - "VOLUME_SPIKE_BUY_THRESHOLD" (float)

2.  **"performance_review"**: A brief, one-paragraph string analyzing the results. Synthesize all data. What is the key takeaway from the post-mortem analysis? How should it influence the strategy for the week ahead?

**Example of a valid response:**
{{
  "tuned_parameters": {{
    "RSI_BUY_THRESHOLD": 68.0,
    "MACD_HIST_BUY_THRESHOLD": 75.0,
    "VOLUME_SPIKE_BUY_THRESHOLD": 1.8
  }},
  "performance_review": "The post-mortem analysis clearly shows that wins are strongly correlated with an RSI above 65. The Live LLM's performance was strong, but we can enhance its entry quality by raising the RSI threshold to align with this finding. The focus for the week is to capitalize on high-momentum entries only."
}}

Now, provide the complete JSON response based on the performance data above.
"""

def get_ai_analysis_and_tuning(llm_analysis: dict, ml_benchmark: dict, post_mortem_data: dict) -> dict:
    """
    Sends a comparative performance analysis to the Gemini AI and asks for
    optimized parameters and a written review.
    """
    if not _MODEL:
        logger.error("AI model is not available. Cannot get optimized parameters.")
        return None

    # Format the LLM analysis summary
    llm_summary = (
        f"- Total Trades Analyzed: {llm_analysis.get('total_trades', 'N/A')}\n"
        f"- Win Rate: {llm_analysis.get('win_rate_pct', 0):.2f}%\n"
        f"- Average Win P/L: ${llm_analysis.get('avg_win_pl', 0):.2f}\n"
        f"- Average Loss P/L: ${llm_analysis.get('avg_loss_pl', 0):.2f}\n"
        # The following details are now covered by the more advanced post-mortem, can be simplified
        f"- Indicator Averages on Wins (RSI): {llm_analysis.get('indicator_analysis', {}).get('rsi', {}).get('win_avg', 0):.2f}"
    )

    # Format the ML benchmark summary
    if ml_benchmark:
        ml_summary = (
            f"- Total Hypothetical Trades: {ml_benchmark.get('total_trades', 'N/A')}\n"
            f"- Simulated Win Rate: {ml_benchmark.get('win_rate_pct', 0):.2f}%\n"
            f"- Simulated Net P/L (as % of portfolio): {ml_benchmark.get('simulated_net_pl_pct', 0):.2%}"
        )
    else:
        ml_summary = "ML model simulation could not be run."

    # --- NEW: Format the post-mortem analysis summary ---
    post_mortem_summary = "Post-mortem analysis could not be generated."
    if post_mortem_data:
        win_analysis = post_mortem_data.get("win_analysis", {})
        loss_analysis = post_mortem_data.get("loss_analysis", {})
        win_count = win_analysis.get("trade_count", 0)
        loss_count = loss_analysis.get("trade_count", 0)

        win_indicators = "\n".join([f"    - Avg {key}: {val}" for key, val in win_analysis.get("average_indicators", {}).items()])
        loss_indicators = "\n".join([f"    - Avg {key}: {val}" for key, val in loss_analysis.get("average_indicators", {}).items()])

        post_mortem_summary = (
            f"- For {win_count} Winning Trades:\n{win_indicators}\n"
            f"- For {loss_count} Losing Trades:\n{loss_indicators}"
        )
    # --- END OF NEW BLOCK ---

    prompt = _ADVANCED_TUNING_PROMPT_TEMPLATE.format(
        llm_summary=llm_summary, 
        ml_summary=ml_summary,
        post_mortem_summary=post_mortem_summary # Pass the new data to the prompt
    )
    
    logger.info("Sending request to Gemini AI for advanced analysis and parameter optimization...")
    try:
        response = _MODEL.generate_content(prompt)
        json_text = response.text.strip().replace("```json", "").replace("```", "").strip()
        logger.info(f"Received raw response from AI: {response.text}")
        
        ai_response_data = json.loads(json_text)
        return ai_response_data
    except Exception as e:
        logger.error(f"Failed to get or parse complex analysis from AI: {e}", exc_info=True)
        return None

def update_dynamic_config(new_params: dict):
    """
    Safely updates the dynamic_config.json file with new parameters.
    """
    try:
        if os.path.exists(DYNAMIC_CONFIG_FILE):
            with open(DYNAMIC_CONFIG_FILE, 'r') as f:
                current_config = json.load(f)
        else:
            current_config = {}
        # Convert float values to strings for Decimal compatibility in the main bot
        string_params = {k: str(v) for k, v in new_params.items()}
        current_config.update(string_params)
        current_config["last_update"] = datetime.utcnow().isoformat()
        
        with open(DYNAMIC_CONFIG_FILE, 'w') as f:
            json.dump(current_config, f, indent=2)
            
        logger.warning("SUCCESS: dynamic_config.json has been updated with new AI-tuned parameters.")
        logger.info(f"New parameters: {json.dumps(string_params, indent=2)}")

    except Exception as e:
        logger.error(f"Failed to update {DYNAMIC_CONFIG_FILE}: {e}")

def log_performance_review(review_text: str):
    """Appends the AI's written review to a report file for historical tracking."""
    try:
        with open(TUNING_REPORT_FILE, 'a') as f:
            f.write(f"--- Tuning Report: {datetime.utcnow().isoformat()} ---\n")
            f.write(f"{review_text}\n\n")
        logger.info(f"Successfully saved AI performance review to {TUNING_REPORT_FILE}")
    except Exception as e:
        logger.error(f"Failed to write to report file: {e}")

def main():
    """
    The main execution function for the weekly tuning process.
    """
    logger.info("--- Starting Advanced Automated Weekly Tuning Process ---")

    # --- NEW: Step 0 - Run the post-mortem analysis first ---
    logger.info("Running the post-mortem trade analyzer...")
    post_mortem_report = trade_analyzer.analyze_trades()
    if not post_mortem_report:
        logger.error("Failed to generate post-mortem analysis report. Aborting tuning.")
        return
    # --- END OF NEW STEP ---
    
    # 1. Analyze the live LLM's historical performance (can be simplified now)
    llm_performance_data = pa.analyze_performance_from_files(
        trade_history_path=TRADE_HISTORY_FILE,
        decision_log_path=DECISION_LOG_FILE
    )
    if not llm_performance_data:
        logger.error("Failed to get LLM performance data. Aborting tuning process.")
        return

    # 2. Simulate the ML model's performance as a benchmark
    ml_benchmark_data = pa.simulate_ml_model_performance(
        decision_log_path=DECISION_LOG_FILE,
        model_path=ML_MODEL_FILE
    )
    
    # 3. Ask the AI for a comparative analysis and new parameters
    ai_response = get_ai_analysis_and_tuning(
        llm_performance_data, 
        ml_benchmark_data, 
        post_mortem_report # Pass the new report to the AI
    )
    if not ai_response:
        logger.error("Failed to get analysis and tuning from the AI. Aborting.")
        return
        
    # 4. Extract the two parts of the AI's response
    optimized_params = ai_response.get("tuned_parameters")
    performance_review = ai_response.get("performance_review")
    
    # 5. Validate and update the configuration file with new parameters
    if optimized_params and isinstance(optimized_params, dict):
        if not all(k in optimized_params for k in ["RSI_BUY_THRESHOLD", "MACD_HIST_BUY_THRESHOLD", "VOLUME_SPIKE_BUY_THRESHOLD"]):
            logger.error("AI response for 'tuned_parameters' was missing one or more required keys. Aborting update.")
        else:
            update_dynamic_config(optimized_params)
    else:
        logger.error("AI response did not contain valid 'tuned_parameters'.")

    # 6. Save the AI's written review to the report log
    if performance_review and isinstance(performance_review, str):
        log_performance_review(performance_review)
    else:
        logger.warning("AI response did not contain a 'performance_review' text.")
    
    logger.info("--- Advanced Automated Weekly Tuning Process Complete ---")

if __name__ == "__main__":
    main()