# Bitcoin AI Trading Bot - Easy Startup (No Password Prompt)
# This script sets the master password as an environment variable

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "   Bitcoin AI Trading Bot - Easy Startup" -ForegroundColor Yellow
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔐 Starting bot with automatic authentication..." -ForegroundColor Green
Write-Host "📁 Using virtual environment: venv\Scripts\python.exe" -ForegroundColor Blue
Write-Host ""

# Change to the script directory
Set-Location $PSScriptRoot

# Check if virtual environment exists
if (-not (Test-Path "venv\Scripts\python.exe")) {
    Write-Host "❌ Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please ensure the venv folder exists in the bot directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Set the master password as environment variable (avoids prompt)
$env:TRADING_BOT_MASTER_PASSWORD = "SecureBitcoinBot2025!"

# Run the bot using the virtual environment Python
try {
    & "venv\Scripts\python.exe" "main_bot.py"
}
catch {
    Write-Host "❌ Error starting bot: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "   Bot has stopped. Press any key to exit." -ForegroundColor Yellow
Write-Host "========================================================" -ForegroundColor Cyan
Read-Host
