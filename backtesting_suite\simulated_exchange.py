# Simulated exchange for backtesting
from decimal import Decimal, ROUND_DOWN
import uuid
from datetime import datetime, UTC

class SimulatedAccount:
    """
    A mock of the Alpaca account object.
    """
    def __init__(self, initial_capital: Decimal):
        self.equity = initial_capital
        self.buying_power = initial_capital
        self.cash = initial_capital # Added missing attribute

    def get(self, key, default=None):
        """
        Simulates dict-like access for compatibility with trade_logic.
        """
        return getattr(self, key, default)

    def to_dict(self):
        return {
            "equity": str(self.equity),
            "buying_power": str(self.buying_power),
            "cash": str(self.cash)
        }

class SimulatedPosition:
    """
    A mock of the Alpaca position object.
    """
    def __init__(self, qty: Decimal, avg_entry_price: Decimal):
        self.qty = qty
        self.avg_entry_price = avg_entry_price

    def to_dict(self):
        return {
            "qty": str(self.qty),
            "avg_entry_price": str(self.avg_entry_price)
        }

class SimulatedOrder:
    """
    A mock of the Alpaca order object.
    """
    def __init__(self, order_id: str, symbol: str, qty: Decimal, side: str, filled_qty: Decimal, filled_avg_price: Decimal, status: str):
        self.id = order_id
        self.symbol = symbol
        self.qty = qty
        self.side = side
        self.filled_qty = filled_qty
        self.filled_avg_price = filled_avg_price
        self.status = status
        self._raw = self.to_dict()

    def to_dict(self):
        return {
            "id": self.id,
            "symbol": self.symbol,
            "qty": str(self.qty),
            "side": self.side,
            "filled_qty": str(self.filled_qty),
            "filled_avg_price": str(self.filled_avg_price),
            "status": self.status
        }

class SimulatedExchange:
    """
    A simulated version of the Alpaca API client for backtesting.
    """
    def __init__(self, initial_capital: Decimal, fee_percent: Decimal):
        self.account = SimulatedAccount(initial_capital)
        self.position = None
        self.fee_percent = fee_percent
        self.orders = {}

    def get_account(self):
        """
        Returns the simulated account object, which behaves like the real Alpaca object.
        """
        return self.account

    def get_positions(self, symbol: str):
        """
        Returns the simulated position for a given symbol.
        """
        if self.position and self.position.qty > 0:
            return self.position.to_dict() # trade_logic expects a dict for positions
        return None

    def get_order(self, order_id: str):
        """
        Retrieves a simulated order by its ID.
        """
        return self.orders.get(order_id)

    def submit_order(self, symbol: str, qty: str, side: str, type: str, time_in_force: str, current_price: Decimal = Decimal("0")):
        """
        Simulates submitting an order. In our backtest, orders are filled instantly at the current price.
        """
        order_qty = Decimal(qty)
        order_id = str(uuid.uuid4())

        if side == "buy":
            cost = order_qty * current_price
            fee = cost * self.fee_percent
            total_cost = cost + fee

            if self.account.buying_power < total_cost:
                print("Error: Insufficient buying power to execute buy order.")
                order = SimulatedOrder(order_id, symbol, order_qty, side, Decimal("0"), Decimal("0"), "rejected")
                self.orders[order.id] = order
                return order
            
            self.account.buying_power -= total_cost
            self.account.cash = self.account.buying_power
            
            if self.position:
                new_total_qty = self.position.qty + order_qty
                new_total_cost = (self.position.avg_entry_price * self.position.qty) + cost
                self.position.avg_entry_price = new_total_cost / new_total_qty
                self.position.qty = new_total_qty
            else:
                self.position = SimulatedPosition(qty=order_qty, avg_entry_price=current_price)
            
            order = SimulatedOrder(order_id, symbol, order_qty, side, order_qty, current_price, "filled")
            self.orders[order.id] = order
            return order

        elif side == "sell":
            if not self.position or self.position.qty < order_qty:
                print("Error: Not enough position to execute sell order.")
                order = SimulatedOrder(order_id, symbol, order_qty, side, Decimal("0"), Decimal("0"), "rejected")
                self.orders[order.id] = order
                return order

            revenue = order_qty * current_price
            fee = revenue * self.fee_percent
            net_revenue = revenue - fee

            self.account.buying_power += net_revenue
            self.account.equity = self.account.buying_power 
            self.account.cash = self.account.buying_power
            
            self.position.qty -= order_qty
            if self.position.qty <= 0:
                self.position = None
            
            order = SimulatedOrder(order_id, symbol, order_qty, side, order_qty, current_price, "filled")
            self.orders[order.id] = order
            return order
        
        return None
