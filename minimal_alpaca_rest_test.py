import os
from dotenv import load_dotenv
from alpaca_trade_api.rest import REST, TimeFrame
from datetime import datetime, timezone
import logging

# Basic logging for the test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables (for API keys)
load_dotenv()

API_KEY_ID = os.getenv("ALPACA_LIVE_API_KEY_ID") # Use your live key
SECRET_KEY = os.getenv("ALPACA_LIVE_SECRET_KEY") # Use your live secret
BASE_URL = "https://api.alpaca.markets" # Live trading URL

if not API_KEY_ID or not SECRET_KEY:
    logger.error("Alpaca API Key ID or Secret Key not found in .env file. Exiting.")
    exit()

try:
    logger.info(f"Initializing Alpaca REST client for {BASE_URL}...")
    api = REST(key_id=API_KEY_ID, secret_key=SECRET_KEY, base_url=BASE_URL)
    logger.info("Alpaca REST client initialized.")

    symbol = "BTC/USD"
    timeframe = TimeFrame.Minute
    limit = 5 # Let's get a few recent bars

    logger.info(f"Attempting to fetch latest {limit} bars for {symbol} with NO end parameter (default latest)...")
    try:
        bars1 = api.get_crypto_bars(symbol, timeframe, limit=limit)
        df1 = bars1.df.get(symbol) if isinstance(bars1.df, dict) else bars1.df
        if df1 is not None and not df1.empty:
            logger.info(f"--- Result 1 (No End Param) ---")
            logger.info(f"Latest bar timestamp: {df1.index[-1]}")
            logger.info(f"DataFrame head:\n{df1.head()}")
            logger.info(f"DataFrame tail:\n{df1.tail()}")
        else:
            logger.warning("Result 1: No data returned or DataFrame was empty.")
    except Exception as e:
        logger.error(f"Error in Result 1: {e}", exc_info=True)

    logger.info("-" * 50)

    # Now try with an explicit end time
    end_dt = datetime.now(timezone.utc)
    end_iso = end_dt.isoformat()
    logger.info(f"Attempting to fetch latest {limit} bars for {symbol} with explicit end='{end_iso}'...")
    try:
        bars2 = api.get_crypto_bars(symbol, timeframe, limit=limit, end=end_iso)
        df2 = bars2.df.get(symbol) if isinstance(bars2.df, dict) else bars2.df
        if df2 is not None and not df2.empty:
            logger.info(f"--- Result 2 (With End Param) ---")
            logger.info(f"Latest bar timestamp: {df2.index[-1]}")
            logger.info(f"DataFrame head:\n{df2.head()}")
            logger.info(f"DataFrame tail:\n{df2.tail()}")
        else:
            logger.warning("Result 2: No data returned or DataFrame was empty.")
    except Exception as e:
        logger.error(f"Error in Result 2: {e}", exc_info=True)

except Exception as e:
    logger.error(f"An error occurred during the test: {e}", exc_info=True)