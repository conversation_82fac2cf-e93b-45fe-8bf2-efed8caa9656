import joblib
import pandas as pd
import os
import sys

# ---- USER SETTINGS ----
TRADE_HISTORY_CSV = "merged_training_data.csv"  # Use merged dataset here
MODEL_PATH = "trade_action_model.joblib"  # Your live model file
ENCODER_PATHS = {
    'trend': "trend_encoder.joblib",
    'trend_5m': "trend_5m_encoder.joblib",
    'regime': "regime_encoder.joblib"
}
REQUIRED_FEATURES = [
    'open', 'high', 'low', 'close', 'volume', 'num_trades',
    'taker_buy_base', 'taker_buy_quote', 'rsi',
    'sma_7', 'sma_14', 'sma_50',
    'ema_7', 'ema_14', 'ema_50',
    'volatility_14', 'momentum_10', 'macd', 'macd_signal',
    'trend', 'trend_5m', 'regime'
]

def main():
    # ---- 1. Check files exist ----
    if not os.path.isfile(MODEL_PATH):
        print(f"FAIL: Model file not found: {MODEL_PATH}")
        sys.exit(1)
    for col, enc_path in ENCODER_PATHS.items():
        if not os.path.isfile(enc_path):
            print(f"FAIL: Encoder file for {col} not found: {enc_path}")
            sys.exit(1)
    if not os.path.isfile(TRADE_HISTORY_CSV):
        print(f"FAIL: Data CSV not found: {TRADE_HISTORY_CSV}")
        sys.exit(1)

    # ---- 2. Load model and encoders ----
    try:
        model = joblib.load(MODEL_PATH)
        encoders = {k: joblib.load(v) for k, v in ENCODER_PATHS.items()}
    except Exception as e:
        print(f"FAIL: Error loading model/encoders: {e}")
        sys.exit(1)

    # ---- 3. Load data, skipping malformed lines ----
    try:
        df = pd.read_csv(TRADE_HISTORY_CSV, on_bad_lines='skip')
    except Exception as e:
        print(f"FAIL: Error loading data CSV (skipping bad lines): {e}")
        sys.exit(1)

    # ---- 4. Check for required features ----
    missing = [col for col in REQUIRED_FEATURES if col not in df.columns]
    if missing:
        print(f"FAIL: Missing required features in CSV: {missing}")
        sys.exit(1)

    # ---- 5. Encode categorical features ----
    for cat in ['trend', 'trend_5m', 'regime']:
        try:
            df[cat] = encoders[cat].transform(df[cat].astype(str))
        except Exception as e:
            print(f"FAIL: Encoder for {cat} failed: {e}")
            sys.exit(1)

    # ---- 6. Test model prediction ----
    test = df[REQUIRED_FEATURES].head(10)
    try:
        preds = model.predict(test)
        assert len(preds) == len(test)
    except Exception as e:
        print(f"FAIL: Model prediction error: {e}")
        sys.exit(1)

    print("PASS: Model and encoders are compatible with your data!")

if __name__ == "__main__":
    main()
