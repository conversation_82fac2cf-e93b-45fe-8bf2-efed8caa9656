"""
Centralized Error Handling System for Bitcoin AI Trading Bot

This module provides:
1. Custom exception classes for different error types
2. Standardized error logging with context
3. Alert management with rate limiting
4. Retry mechanisms and circuit breakers
5. Error recovery strategies
"""

import logging
import time
import traceback
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from enum import Enum
from functools import wraps
from typing import Any, Dict, Optional, Callable, Type, Union
import json
from collections import defaultdict, deque

from email_alert import send_alert


class ErrorSeverity(Enum):
    """Error severity levels for categorization and alerting"""
    LOW = "LOW"           # Minor issues, no immediate action needed
    MEDIUM = "MEDIUM"     # Important issues, should be addressed
    HIGH = "HIGH"         # Serious issues, immediate attention required
    CRITICAL = "CRITICAL" # System-threatening issues, emergency response needed


class ErrorCategory(Enum):
    """Error categories for better organization and handling"""
    TRADING = "TRADING"           # Trading execution errors
    API = "API"                   # External API errors
    DATA = "DATA"                 # Data processing/validation errors
    NETWORK = "NETWORK"           # Network connectivity errors
    SYSTEM = "SYSTEM"             # System/infrastructure errors
    CONFIGURATION = "CONFIG"      # Configuration errors
    AUTHENTICATION = "AUTH"       # Authentication/authorization errors
    VALIDATION = "VALIDATION"     # Input validation errors


# ===================================================================
# CUSTOM EXCEPTION CLASSES
# ===================================================================

class TradingBotError(Exception):
    """Base exception class for all trading bot errors"""
    
    def __init__(self, message: str, category: ErrorCategory, severity: ErrorSeverity, 
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.original_error = original_error
        self.timestamp = datetime.utcnow()
        self.error_id = f"{category.value}_{int(self.timestamp.timestamp())}"


class TradingError(TradingBotError):
    """Errors related to trading operations"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.HIGH, 
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.TRADING, severity, context, original_error)


class APIError(TradingBotError):
    """Errors related to external API calls"""
    
    def __init__(self, message: str, api_name: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        context = context or {}
        context['api_name'] = api_name
        super().__init__(message, ErrorCategory.API, severity, context, original_error)


class DataError(TradingBotError):
    """Errors related to data processing and validation"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.DATA, severity, context, original_error)


class NetworkError(TradingBotError):
    """Errors related to network connectivity"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.NETWORK, severity, context, original_error)


class SystemError(TradingBotError):
    """Errors related to system operations"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.HIGH,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.SYSTEM, severity, context, original_error)


class ConfigurationError(TradingBotError):
    """Errors related to configuration"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.HIGH,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.CONFIGURATION, severity, context, original_error)


class AuthenticationError(TradingBotError):
    """Errors related to authentication and authorization"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.CRITICAL,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.AUTHENTICATION, severity, context, original_error)


class ValidationError(TradingBotError):
    """Errors related to input validation"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.LOW,
                 context: Optional[Dict[str, Any]] = None, original_error: Optional[Exception] = None):
        super().__init__(message, ErrorCategory.VALIDATION, severity, context, original_error)


# ===================================================================
# ALERT RATE LIMITING
# ===================================================================

class AlertRateLimiter:
    """Rate limiter for email alerts to prevent spam"""
    
    def __init__(self):
        self.alert_history = defaultdict(deque)  # category -> deque of timestamps
        self.rate_limits = {
            ErrorSeverity.CRITICAL: (5, 300),    # 5 alerts per 5 minutes
            ErrorSeverity.HIGH: (3, 600),        # 3 alerts per 10 minutes  
            ErrorSeverity.MEDIUM: (2, 1800),     # 2 alerts per 30 minutes
            ErrorSeverity.LOW: (1, 3600),        # 1 alert per hour
        }
    
    def should_send_alert(self, severity: ErrorSeverity, category: ErrorCategory) -> bool:
        """Check if an alert should be sent based on rate limits"""
        key = f"{severity.value}_{category.value}"
        now = time.time()
        max_alerts, window_seconds = self.rate_limits[severity]
        
        # Clean old entries
        while self.alert_history[key] and now - self.alert_history[key][0] > window_seconds:
            self.alert_history[key].popleft()
        
        # Check if we can send
        if len(self.alert_history[key]) < max_alerts:
            self.alert_history[key].append(now)
            return True
        
        return False


# ===================================================================
# CENTRALIZED ERROR HANDLER
# ===================================================================

class ErrorHandler:
    """Centralized error handling system"""
    
    def __init__(self):
        self.logger = logging.getLogger('ErrorHandler')
        self.alert_limiter = AlertRateLimiter()
        self.error_counts = defaultdict(int)
        
    def handle_error(self, error: Union[TradingBotError, Exception], 
                    context: Optional[Dict[str, Any]] = None,
                    request_id: Optional[str] = None) -> None:
        """Handle an error with logging, alerting, and tracking"""
        
        # Convert regular exceptions to TradingBotError
        if not isinstance(error, TradingBotError):
            error = TradingBotError(
                message=str(error),
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.MEDIUM,
                context=context,
                original_error=error
            )
        
        # Add request ID to context
        if request_id:
            error.context['request_id'] = request_id
        
        # Log the error
        self._log_error(error)
        
        # Track error counts
        error_key = f"{error.category.value}_{error.severity.value}"
        self.error_counts[error_key] += 1
        
        # Send alert if appropriate
        self._send_alert_if_needed(error)
    
    def _log_error(self, error: TradingBotError) -> None:
        """Log error with appropriate level and context"""
        
        # Create structured log message
        log_data = {
            'error_id': error.error_id,
            'category': error.category.value,
            'severity': error.severity.value,
            'message': error.message,
            'context': error.context,
            'timestamp': error.timestamp.isoformat()
        }
        
        if error.original_error:
            log_data['original_error'] = str(error.original_error)
            log_data['traceback'] = traceback.format_exc()
        
        log_message = f"[{error.error_id}] {error.message}"
        
        # Log with appropriate level
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, extra={'error_data': log_data}, exc_info=error.original_error)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra={'error_data': log_data}, exc_info=error.original_error)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message, extra={'error_data': log_data})
        else:
            self.logger.info(log_message, extra={'error_data': log_data})
    
    def _send_alert_if_needed(self, error: TradingBotError) -> None:
        """Send email alert if severity and rate limits allow"""
        
        # Only alert for HIGH and CRITICAL errors
        if error.severity not in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            return
        
        # Check rate limits
        if not self.alert_limiter.should_send_alert(error.severity, error.category):
            self.logger.debug(f"Alert rate limited for {error.category.value} {error.severity.value}")
            return
        
        # Create alert subject and message
        subject = f"🚨 {error.severity.value} {error.category.value} Error - {error.error_id}"
        
        message_parts = [
            f"Error ID: {error.error_id}",
            f"Category: {error.category.value}",
            f"Severity: {error.severity.value}",
            f"Time: {error.timestamp.isoformat()}",
            f"Message: {error.message}",
        ]
        
        if error.context:
            message_parts.append(f"Context: {json.dumps(error.context, indent=2, default=str)}")
        
        if error.original_error:
            message_parts.append(f"Original Error: {str(error.original_error)}")
        
        message = "\n\n".join(message_parts)
        
        try:
            send_alert(subject, message)
            self.logger.info(f"Alert sent for error {error.error_id}")
        except Exception as e:
            self.logger.error(f"Failed to send alert for error {error.error_id}: {e}")


# Global error handler instance
error_handler = ErrorHandler()


# ===================================================================
# CONVENIENCE FUNCTIONS
# ===================================================================

def handle_error(error: Union[TradingBotError, Exception], 
                context: Optional[Dict[str, Any]] = None,
                request_id: Optional[str] = None) -> None:
    """Convenience function to handle errors"""
    error_handler.handle_error(error, context, request_id)


def log_and_handle(message: str, category: ErrorCategory, severity: ErrorSeverity,
                  context: Optional[Dict[str, Any]] = None,
                  request_id: Optional[str] = None) -> None:
    """Convenience function to create and handle an error"""
    error = TradingBotError(message, category, severity, context)
    handle_error(error, request_id=request_id)


# ===================================================================
# RETRY MECHANISMS AND CIRCUIT BREAKERS
# ===================================================================

class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "CLOSED"       # Normal operation
    OPEN = "OPEN"           # Failing, blocking requests
    HALF_OPEN = "HALF_OPEN" # Testing if service recovered


class CircuitBreaker:
    """Circuit breaker pattern implementation"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60,
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception

        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED

    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""

        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
            else:
                raise SystemError(
                    f"Circuit breaker is OPEN. Service unavailable.",
                    ErrorSeverity.HIGH,
                    context={'circuit_breaker': 'open', 'failure_count': self.failure_count}
                )

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result

        except self.expected_exception as e:
            self._on_failure()
            raise

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (time.time() - self.last_failure_time) >= self.recovery_timeout

    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED

    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN


def with_retry(max_attempts: int = 3, delay: float = 1.0, backoff_factor: float = 2.0,
               exceptions: tuple = (Exception,), circuit_breaker: Optional[CircuitBreaker] = None):
    """Decorator for adding retry logic with exponential backoff"""

    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    if circuit_breaker:
                        return circuit_breaker.call(func, *args, **kwargs)
                    else:
                        return func(*args, **kwargs)

                except exceptions as e:
                    last_exception = e

                    if attempt == max_attempts - 1:  # Last attempt
                        # Log the final failure
                        context = {
                            'function': func.__name__,
                            'attempt': attempt + 1,
                            'max_attempts': max_attempts,
                            'args': str(args)[:200],
                            'kwargs': str(kwargs)[:200]
                        }

                        if isinstance(e, TradingBotError):
                            handle_error(e, context=context)
                        else:
                            handle_error(
                                SystemError(
                                    f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}",
                                    ErrorSeverity.HIGH,
                                    context=context,
                                    original_error=e
                                )
                            )
                        raise

                    # Calculate delay for next attempt
                    sleep_time = delay * (backoff_factor ** attempt)

                    # Log retry attempt
                    logger = logging.getLogger('RetryHandler')
                    logger.warning(
                        f"Function {func.__name__} failed (attempt {attempt + 1}/{max_attempts}). "
                        f"Retrying in {sleep_time:.1f}s. Error: {str(e)}"
                    )

                    time.sleep(sleep_time)

            # This should never be reached, but just in case
            raise last_exception

        return wrapper
    return decorator


def safe_execute(func: Callable, default_return=None,
                context: Optional[Dict[str, Any]] = None,
                request_id: Optional[str] = None) -> Any:
    """Safely execute a function with error handling and default return"""

    try:
        return func()
    except TradingBotError as e:
        handle_error(e, context=context, request_id=request_id)
        return default_return
    except Exception as e:
        error = SystemError(
            f"Unexpected error in {func.__name__ if hasattr(func, '__name__') else 'function'}: {str(e)}",
            ErrorSeverity.MEDIUM,
            context=context,
            original_error=e
        )
        handle_error(error, request_id=request_id)
        return default_return


# ===================================================================
# ERROR CONTEXT MANAGERS
# ===================================================================

class ErrorContext:
    """Context manager for handling errors in code blocks"""

    def __init__(self, operation_name: str, category: ErrorCategory = ErrorCategory.SYSTEM,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None,
                 request_id: Optional[str] = None,
                 default_return=None):
        self.operation_name = operation_name
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.request_id = request_id
        self.default_return = default_return
        self.result = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            if isinstance(exc_val, TradingBotError):
                handle_error(exc_val, context=self.context, request_id=self.request_id)
            else:
                error = TradingBotError(
                    f"Error in {self.operation_name}: {str(exc_val)}",
                    self.category,
                    self.severity,
                    context=self.context,
                    original_error=exc_val
                )
                handle_error(error, request_id=self.request_id)

            # Suppress the exception and return default value
            self.result = self.default_return
            return True

        return False
