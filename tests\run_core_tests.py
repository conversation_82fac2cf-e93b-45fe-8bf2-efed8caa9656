"""
Core Test Runner - Execute Essential Safety Tests

This script runs the most critical test suites to ensure your Bitcoin AI 
trading bot is safe for operation with your $50 trade limit configuration.
"""

import unittest
import sys
import os
from io import StringIO
import time
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import core test modules
from tests.test_financial_safety import TestFinancialSafety
from tests.test_config_safety import TestConfigurationSafety
from tests.test_data_validation import TestMarketDataValidation, TestTechnicalIndicators, TestMLModelValidation


class CoreTestRunner:
    """Runs essential test suites for production readiness"""
    
    def __init__(self):
        self.test_suites = [
            # CRITICAL SAFETY TESTS (highest priority)
            ('Financial Safety Tests', TestFinancialSafety),
            ('Configuration Safety Tests', TestConfigurationSafety),
            
            # DATA VALIDATION TESTS
            ('Market Data Validation Tests', TestMarketDataValidation),
            ('Technical Indicators Tests', TestTechnicalIndicators),
            ('ML Model Validation Tests', TestMLModelValidation),
        ]
        
        self.results = {}
        self.total_tests = 0
        self.total_passed = 0
        self.total_failed = 0
        self.total_errors = 0
    
    def run_test_suite(self, suite_name, test_class):
        """Run a single test suite and capture results"""
        print(f"\n{'='*60}")
        print(f"RUNNING: {suite_name}")
        print(f"{'='*60}")
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(test_class)
        
        # Capture output
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2,
            buffer=True
        )
        
        # Run tests
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # Store results
        self.results[suite_name] = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
            'duration': end_time - start_time,
            'failure_details': result.failures,
            'error_details': result.errors
        }
        
        # Update totals
        self.total_tests += result.testsRun
        self.total_passed += (result.testsRun - len(result.failures) - len(result.errors))
        self.total_failed += len(result.failures)
        self.total_errors += len(result.errors)
        
        # Print immediate results
        if result.testsRun > 0:
            success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
            print(f"✅ Tests Run: {result.testsRun}")
            print(f"✅ Passed: {result.testsRun - len(result.failures) - len(result.errors)}")
            if len(result.failures) > 0:
                print(f"❌ Failed: {len(result.failures)}")
            if len(result.errors) > 0:
                print(f"💥 Errors: {len(result.errors)}")
            print(f"📊 Success Rate: {success_rate:.1f}%")
            print(f"⏱️  Duration: {end_time - start_time:.2f}s")
            
            # Show failures/errors immediately
            if result.failures:
                print(f"\n❌ FAILURES in {suite_name}:")
                for test, traceback in result.failures:
                    print(f"  - {test}")
            
            if result.errors:
                print(f"\n💥 ERRORS in {suite_name}:")
                for test, traceback in result.errors:
                    print(f"  - {test}")
        else:
            print("⚠️  No tests found in this suite")
    
    def run_all_tests(self):
        """Run all core test suites"""
        print("🚀 STARTING CORE BITCOIN AI TRADING BOT SAFETY TESTS")
        print(f"📅 Test Run Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🧪 Total Test Suites: {len(self.test_suites)}")
        print(f"💰 Your Configuration: $50 MAX_TRADE_VALUE_USD")
        
        overall_start_time = time.time()
        
        # Run each test suite
        for suite_name, test_class in self.test_suites:
            try:
                self.run_test_suite(suite_name, test_class)
            except Exception as e:
                print(f"💥 CRITICAL ERROR running {suite_name}: {e}")
                self.results[suite_name] = {
                    'tests_run': 0,
                    'failures': 0,
                    'errors': 1,
                    'success_rate': 0,
                    'duration': 0,
                    'failure_details': [],
                    'error_details': [(suite_name, str(e))]
                }
                self.total_errors += 1
        
        overall_end_time = time.time()
        
        # Generate safety report
        self.generate_safety_report(overall_end_time - overall_start_time)
    
    def generate_safety_report(self, total_duration):
        """Generate core safety report"""
        print(f"\n{'='*80}")
        print("🛡️  CORE BITCOIN AI TRADING BOT SAFETY REPORT")
        print(f"{'='*80}")
        
        # Overall statistics
        overall_success_rate = (self.total_passed / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"📊 OVERALL RESULTS:")
        print(f"   Total Tests Run: {self.total_tests}")
        print(f"   ✅ Passed: {self.total_passed}")
        print(f"   ❌ Failed: {self.total_failed}")
        print(f"   💥 Errors: {self.total_errors}")
        print(f"   📈 Overall Success Rate: {overall_success_rate:.1f}%")
        print(f"   ⏱️  Total Duration: {total_duration:.2f}s")
        
        # Safety assessment
        print(f"\n🛡️  SAFETY ASSESSMENT:")
        if overall_success_rate >= 95:
            print("   ✅ EXCELLENT - Bot is ready for production trading")
            safety_status = "PRODUCTION_READY"
        elif overall_success_rate >= 90:
            print("   ⚠️  GOOD - Bot is mostly safe, minor issues to address")
            safety_status = "MOSTLY_SAFE"
        elif overall_success_rate >= 80:
            print("   ⚠️  CAUTION - Significant issues found, review required")
            safety_status = "NEEDS_REVIEW"
        else:
            print("   🚨 DANGER - Critical issues found, DO NOT USE IN PRODUCTION")
            safety_status = "NOT_SAFE"
        
        # Detailed results by category
        print(f"\n📋 DETAILED RESULTS BY TEST CATEGORY:")
        print(f"{'Category':<35} {'Tests':<8} {'Pass':<8} {'Fail':<8} {'Error':<8} {'Rate':<8}")
        print("-" * 80)
        
        for suite_name, results in self.results.items():
            tests_run = results['tests_run']
            failures = results['failures']
            errors = results['errors']
            passed = tests_run - failures - errors
            rate = results['success_rate']
            
            print(f"{suite_name:<35} {tests_run:<8} {passed:<8} {failures:<8} {errors:<8} {rate:<7.1f}%")
        
        # Final verdict
        print(f"\n{'='*80}")
        if safety_status == "PRODUCTION_READY":
            print("🎉 VERDICT: BOT IS READY FOR PRODUCTION TRADING! 🎉")
            print("💰 Your $50 trade limit configuration is working correctly!")
        else:
            print("⚠️  VERDICT: BOT REQUIRES ADDITIONAL WORK BEFORE LIVE TRADING")
        print(f"{'='*80}")
        
        return safety_status


def main():
    """Main function to run core tests"""
    runner = CoreTestRunner()
    safety_status = runner.run_all_tests()
    
    # Exit with appropriate code
    if safety_status == "PRODUCTION_READY":
        sys.exit(0)  # Success
    else:
        sys.exit(1)  # Issues found


if __name__ == "__main__":
    main()
