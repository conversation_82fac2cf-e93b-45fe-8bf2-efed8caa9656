import os
import sys
import json
import subprocess
import configparser
import shutil # Added for file backup
from decimal import Decimal

# Add the parent directory to the Python path to allow for absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Define paths relative to the backtesting_suite folder
BACKTEST_CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'backtest_config.py')
BACKTESTER_PATH = os.path.join(os.path.dirname(__file__), 'backtester.py')
OPTIMIZATION_RESULTS_FILE = os.path.join(os.path.dirname(__file__), 'optimization_results.csv')



def run_optimization():
    print("--- Starting Parameter Optimization ---")

    # Define parameters to optimize and their ranges
    # Example: STOP_PERCENT from 0.01 to 0.03 with step 0.005
    # Example: TRAIL_PROFIT_BUFFER_PCT from 0.001 to 0.005 with step 0.001
    optimization_params = {
        "STOP_PERCENT": [Decimal(str(x)) for x in [0.005, 0.01, 0.02, 0.03, 0.04, 0.05]],
        "TRAIL_PROFIT_BUFFER_PCT": [Decimal(str(x)) for x in [0.001, 0.005, 0.01, 0.015, 0.02]],
    }

    results = []

    # Simple nested loop for demonstration. For more parameters, itertools.product would be used.
    for stop_pct in optimization_params["STOP_PERCENT"]:
        for trail_pct in optimization_params["TRAIL_PROFIT_BUFFER_PCT"]:
            print(f"\nRunning backtest for: STOP_PERCENT={stop_pct}, TRAIL_PROFIT_BUFFER_PCT={trail_pct}")

            # 1. Pass parameters as command-line arguments to backtester.py
            backtester_args = [
                str(stop_pct),
                str(trail_pct)
            ]

            # 2. Execute backtester.py
            try:
                print("  Executing backtester.py (streaming output)...")
                process = subprocess.Popen(
                    [sys.executable, BACKTESTER_PATH] + backtester_args,
                    stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1
                )
                
                output_lines = []
                for line in process.stdout:
                    print(f"    {line.strip()}") # Print each line as it comes
                    output_lines.append(line)
                
                process.wait() # Wait for the process to finish
                
                if process.returncode != 0:
                    raise subprocess.CalledProcessError(process.returncode, process.args, output=''.join(output_lines))

                # 3. Capture results by parsing backtester.py's output
                final_equity = Decimal("0.0")
                total_pl = Decimal("0.0")
                wins = 0
                losses = 0

                for line in output_lines:
                    if "Final Equity: $" in line:
                        final_equity = Decimal(line.split("$")[1].strip())
                    elif "Total P/L: $" in line:
                        total_pl = Decimal(line.split("$")[1].strip())
                    elif "Wins: " in line and "| Losses: " in line:
                        parts = line.split("|")
                        wins = int(parts[0].split(":")[1].strip())
                        losses = int(parts[1].split(":")[1].strip())

                print(f"  Results: P/L=${total_pl:.2f}, Wins={wins}, Losses={losses}")

                results.append({
                    "STOP_PERCENT": stop_pct,
                    "TRAIL_PROFIT_BUFFER_PCT": trail_pct,
                    "Total_PL": total_pl,
                    "Wins": wins,
                    "Losses": losses
                })

            except subprocess.CalledProcessError as e:
                print(f"  Error running backtester.py: {e}")
                print(f"  Stderr: {e.stderr}")
                print(f"  Stdout: {e.stdout}")
                results.append({
                    "STOP_PERCENT": stop_pct,
                    "TRAIL_PROFIT_BUFFER_PCT": trail_pct,
                    "Total_PL": Decimal("NaN"), # Indicate error
                    "Wins": 0,
                    "Losses": 0
                })
            except Exception as e:
                print(f"  An unexpected error occurred during backtest execution: {e}")
                results.append({
                    "STOP_PERCENT": stop_pct,
                    "TRAIL_PROFIT_BUFFER_PCT": trail_pct,
                    "Total_PL": Decimal("NaN"), # Indicate error
                    "Wins": 0,
                    "Losses": 0
                })

    print("\n--- Parameter Optimization Finished ---")

    # Save results to CSV
    if results:
        with open(OPTIMIZATION_RESULTS_FILE, 'w', newline='') as f:
            fieldnames = results[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)
        print(f"Optimization results saved to: {OPTIMIZATION_RESULTS_FILE}")
    else:
        print("No optimization results to save.")

    # Backup the optimization results file
    if os.path.exists(OPTIMIZATION_RESULTS_FILE):
        backup_path = os.path.join(os.path.dirname(__file__), 'backups', 'optimization_results_backup.csv')
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        try:
            shutil.copy2(OPTIMIZATION_RESULTS_FILE, backup_path)
            print(f"Optimization results backed up to: {backup_path}")
        except Exception as e:
            print(f"Error backing up optimization results: {e}")

if __name__ == "__main__":
    # Import csv here to avoid circular dependency if this file is imported elsewhere
    import csv
    run_optimization()
