# ===================================================================
# PINNED REQUIREMENTS - Bitcoin AI Trading Bot
# ===================================================================
# All versions are pinned to ensure reproducible environments
# Last updated: 2025-01-29
# Python version: 3.11+

# ===================================================================
# CORE TRADING BOT DEPENDENCIES
# ===================================================================

# Trading APIs & Market Data
alpaca-trade-api==3.2.0          # Alpaca trading API for order execution
ccxt==4.4.95                     # Cryptocurrency exchange trading library
websocket-client==1.8.0          # WebSocket client for real-time data streams

# AI & Machine Learning
google-generativeai==0.8.5       # Google Gemini AI for trading decisions
xgboost==3.0.2                   # Gradient boosting for ML predictions
scikit-learn==1.7.1              # Machine learning utilities and metrics
joblib==1.5.1                    # Model serialization and parallel processing

# Data Processing & Analysis
pandas==2.3.1                    # Data manipulation and analysis
numpy==2.3.1                     # Numerical computing
pandas-ta==0.3.14b0              # Technical analysis indicators

# Configuration & Environment
python-dotenv==1.1.1             # Environment variable management
pytz==2025.2                     # Timezone handling
schedule==1.2.2                  # Task scheduling

# HTTP & Network
requests==2.32.4                 # HTTP library for API calls
urllib3==1.26.20                 # HTTP client (pinned for security)

# Data Validation & Serialization
pydantic==2.11.7                 # Data validation and settings management
pydantic-core==2.33.2            # Pydantic core functionality

# Retry & Error Handling
tenacity==9.1.2                  # Retry library for robust API calls

# ===================================================================
# DASHBOARD & VISUALIZATION DEPENDENCIES
# ===================================================================

# Streamlit Dashboard
streamlit==1.47.0                # Web dashboard framework
altair==5.5.0                    # Declarative statistical visualization
pillow==11.3.0                   # Image processing for dashboard
pyarrow==21.0.0                  # Columnar data format for performance

# ===================================================================
# CRYPTOGRAPHY & SECURITY
# ===================================================================

cryptography==45.0.5             # Cryptographic recipes and primitives
certifi==2025.7.14               # Certificate bundle for SSL verification

# ===================================================================
# GOOGLE API DEPENDENCIES
# ===================================================================

google-api-core==2.25.1          # Google API client core library
google-api-python-client==2.176.0 # Google API client library
google-auth==2.40.3              # Google authentication library
google-auth-httplib2==0.2.0      # Google Auth HTTP library
googleapis-common-protos==1.70.0 # Common protocol buffer types
grpcio==1.73.1                   # gRPC Python library
grpcio-status==1.71.2            # gRPC status proto mapping
proto-plus==1.26.1               # Protocol buffer wrapper
protobuf==5.29.5                 # Protocol buffer runtime

# ===================================================================
# UTILITY DEPENDENCIES
# ===================================================================

# Date & Time
python-dateutil==2.9.0.post0     # Extensions to the standard datetime module

# Data Structures & Serialization
jsonschema==4.25.0               # JSON schema validation
jsonschema-specifications==2025.4.1 # JSON schema specifications
PyYAML==6.0.1                    # YAML parser and emitter

# Async & Networking
aiohttp==3.12.14                 # Async HTTP client/server framework
aiodns==3.5.0                    # Async DNS resolver
aiohappyeyeballs==2.6.1          # Happy Eyeballs for asyncio
aiosignal==1.4.0                 # Async signal handling
multidict==6.6.3                 # Multidict implementation
yarl==1.20.1                     # URL parsing library
frozenlist==1.7.0                # Immutable list implementation
propcache==0.3.2                 # Property caching

# ===================================================================
# DEVELOPMENT & TESTING DEPENDENCIES (Optional)
# ===================================================================

# These are included as they're currently installed and may be used
# Remove if not needed in production

# Version Control & Git
GitPython==3.1.44                # Git repository interaction
gitdb==4.0.12                    # Git object database

# Progress & Monitoring
tqdm==4.67.1                     # Progress bars
watchdog==6.0.0                  # File system event monitoring

# Type Checking & Annotations
typing-extensions==4.14.1        # Typing extensions for older Python
annotated-types==0.7.0           # Annotated types support

# Parsing & Templates
Jinja2==3.1.6                    # Template engine
MarkupSafe==3.0.2                # Safe string handling for templates
pyparsing==3.2.3                 # Parsing library

# Caching & Performance
cachetools==5.5.2                # Caching utilities
msgpack==1.0.3                   # MessagePack serialization

# Scientific Computing Support
scipy==1.16.0                    # Scientific computing library
threadpoolctl==3.6.0             # Thread pool control

# Miscellaneous
attrs==25.3.0                    # Classes without boilerplate
blinker==1.9.0                   # Signal/event system
click==8.2.1                     # Command line interface creation
colorama==0.4.6                  # Cross-platform colored terminal text
deprecation==2.1.0               # Deprecation warnings
httplib2==0.22.0                 # HTTP client library
idna==3.10                       # Internationalized domain names
keyboard==0.13.5                 # Keyboard input handling
narwhals==1.48.0                 # DataFrame interchange protocol
packaging==25.0                  # Core utilities for Python packages
pydeck==0.9.1                    # Deck.gl for Python
referencing==0.36.2              # JSON reference resolution
rpds-py==0.26.0                  # Rust-powered data structures
rsa==4.9.1                       # RSA cryptography
six==1.17.0                      # Python 2 and 3 compatibility
smmap==5.0.2                     # Memory-mapped file support
toml==0.10.2                     # TOML parser
tornado==6.5.1                   # Web framework and async networking
tzdata==2025.2                   # Timezone database
uritemplate==4.2.0               # URI template parsing
websockets==10.4                 # WebSocket implementation

# ===================================================================
# SYSTEM DEPENDENCIES
# ===================================================================

# These are typically installed by default but pinned for completeness
setuptools==80.9.0               # Package development utilities

# ===================================================================
# NOTES
# ===================================================================
#
# To update dependencies:
# 1. Test thoroughly in development environment
# 2. Update version numbers one at a time
# 3. Run full test suite after each update
# 4. Update this file with new versions
#
# To install:
# pip install -r requirements.txt
#
# To check for security vulnerabilities:
# pip-audit
#
# To check for outdated packages:
# pip list --outdated