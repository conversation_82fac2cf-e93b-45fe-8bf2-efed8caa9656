import csv
import json
import os
import math
import logging
import time
import threading
from contextlib import contextmanager
from datetime import datetime, UTC, timedelta, date
from decimal import Decimal, InvalidOperation, ROUND_DOWN, ROUND_HALF_UP

from config import (
    MIN_ORDER_VALUE_USD_FLOOR,
    MAX_TRADE_VALUE_USD,
    MAX_SCALP_TRADE_VALUE_USD,
    MIN_TRADE_QTY_BTC,
    CASH_RESERVE_PERCENT,
    CASH_RESERVE_MIN_USD,
    CASH_RESERVE_MAX_USD,
    MAX_PORTFOLIO_ALLOCATION_PERCENT,
    POSITION_SIZE_LIMIT_PERCENT,
    MIN_SELL_QTY_THRESHOLD,
    MIN_PROFIT_THRESHOLD_USD,
    MAX_AI_LOSS_EXIT_PCT,
    STOP_PERCENT,
    DUST_THRESHOLD_QTY,
    COOLDOWN_SELL_AFTER_BUY,
    COOLDOWN_BUY_AFTER_SELL,
    FEE_PERCENT,
    ENABLE_GUARDIAN_VETO,
    GUARDIAN_RSI_OVERBOUGHT,
    GUARDIAN_RSI_OVERSOLD,
    MIN_QTY_PCT_CONFIG,
    TRAIL_PROFIT_BUFFER_PCT,
    REFERENCE_ATR,
    GRANULAR_TAKE_PROFIT_PCT,
    DIP_BUY_PROFIT_TARGET_PCT,
    MOMENTUM_BUY_NET_PROFIT_TARGET_PCT
)
import config

import alpaca_service
from email_alert import send_alert, send_enhanced_alert, AlertPriority, AlertCategory
from bot_state import load_bot_state, save_bot_state
import order_tracker_service

from error_handler import ErrorSeverity, ErrorCategory as ErrorCat, handle_error
from error_utils import (
    TradingLogicErrorHandler, safe_json_operation, safe_decimal_operation,
    TradingError, DataError, SystemError
)

logger = logging.getLogger("TradingBotApp.TradeLogic")
debug_logger = logging.getLogger("TradingBotApp.Debug")

def calculate_dynamic_cash_reserve(account_equity):
    """
    Calculate dynamic cash reserve based on account equity.

    Args:
        account_equity (Decimal): Current account equity

    Returns:
        Decimal: Required cash reserve amount
    """
    # Calculate percentage-based reserve
    percentage_reserve = account_equity * CASH_RESERVE_PERCENT

    # Apply min/max bounds
    dynamic_reserve = max(CASH_RESERVE_MIN_USD,
                         min(CASH_RESERVE_MAX_USD, percentage_reserve))

    logger.debug(f"Dynamic cash reserve: ${dynamic_reserve:.2f} "
                f"({CASH_RESERVE_PERCENT:.1%} of ${account_equity:.2f} equity)")

    return dynamic_reserve

def calculate_portfolio_allocation_limit(account_equity):
    """
    Calculate maximum amount that can be allocated to positions.

    Args:
        account_equity (Decimal): Current account equity

    Returns:
        Decimal: Maximum portfolio allocation amount
    """
    max_allocation = account_equity * MAX_PORTFOLIO_ALLOCATION_PERCENT

    logger.debug(f"Max portfolio allocation: ${max_allocation:.2f} "
                f"({MAX_PORTFOLIO_ALLOCATION_PERCENT:.1%} of ${account_equity:.2f} equity)")

    return max_allocation

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
LOTS_LEDGER_FILE = os.path.join(BASE_DIR, "open_lots.json")

_open_lots_lock = threading.RLock()
open_lots = []

@contextmanager
def lots():
    with _open_lots_lock:
        yield

def atr_adjusted_value(base_value: Decimal, atr: Decimal, reference_atr: Decimal = config.REFERENCE_ATR) -> Decimal:
    if atr <= 0: 
        logger.debug(f"ATR-Sizing: ATR is zero or negative ({atr}), returning base value {base_value:.2f}.")
        return base_value
    factor = Decimal(str(reference_atr)) / max(Decimal(str(atr)), Decimal("1e-8"))
    adjusted = Decimal(str(base_value)) * factor
    logger.info(f"ATR-Sizing: Base=${base_value:.2f}, ATR={atr:.5f}, Factor={factor:.4f}, Adjusted=${adjusted:.2f}")
    return adjusted

def _load_json_lots(path: str, default_value: list) -> list[dict]:
    logger.debug(f"Attempting to load JSON lots from {path}.")
    with lots():
        if not os.path.exists(path) or os.path.getsize(path) == 0:
            logger.warning(f"Lot ledger file {path} not found or is empty. Returning default value.")
            return default_value
        try:
            with open(path, 'r') as f: 
                data = json.load(f)
            if not isinstance(data, list):
                logger.error(f"Loaded data from {path} is not a list. Returning default value.")
                return default_value
            
            processed_lots = []
            for item in data:
                if not isinstance(item, dict):
                    logger.warning(f"Skipping non-dictionary item in lots file: {item}")
                    continue
                
                lot = {}
                lot['lot_id'] = str(item.get('lot_id', item.get('order_id', f"MISSING_LOT_ID_{datetime.now(UTC).timestamp()}")))
                lot['buy_order_id'] = str(item.get('buy_order_id', lot['lot_id']))
                
                buy_timestamp_str = item.get('buy_timestamp') or item.get('last_buy_timestamp')
                lot['buy_timestamp'] = datetime.fromisoformat(str(buy_timestamp_str)).astimezone(UTC) if buy_timestamp_str else None

                lot['original_qty'] = Decimal(str(item.get('original_qty', item.get('qty', '0'))))
                lot['remaining_qty'] = Decimal(str(item.get('remaining_qty', item.get('qty', '0'))))
                lot['buy_price'] = Decimal(str(item.get('buy_price', item.get('entry_price', '0'))))
                lot['buy_fee_usd'] = Decimal(str(item.get('buy_fee_usd', '0')))
                
                cost_basis_str = item.get('cost_basis_per_unit')
                if lot['original_qty'] > 0 and lot['buy_price'] > 0 and cost_basis_str is None:
                    lot['cost_basis_per_unit'] = ((lot['buy_price'] * lot['original_qty']) + lot['buy_fee_usd']) / lot['original_qty']
                    logger.debug(f"Calculated cost_basis_per_unit for lot {lot['lot_id']}: {lot['cost_basis_per_unit']:.2f}")
                else:
                    lot['cost_basis_per_unit'] = Decimal(str(cost_basis_str or '0'))

                lot['initial_stop'] = Decimal(str(item.get('initial_stop', item.get('stop', '0'))))
                lot['current_stop'] = Decimal(str(item.get('current_stop', item.get('stop', lot.get('initial_stop','0')))))
                
                lot['type'] = str(item.get('type', "unknown_lot"))
                
                lot['strategy_type'] = str(item.get('strategy_type', 'MOMENTUM_BUY')) 

                raw_loaded_tp = item.get('take_profit_price')
                needs_recalculation = False
                try:
                    loaded_tp_dec = Decimal(str(raw_loaded_tp))
                    if loaded_tp_dec == Decimal("0"):
                        needs_recalculation = True
                except (InvalidOperation, TypeError):
                    needs_recalculation = True

                if needs_recalculation:
                    base_price_for_tp = lot['cost_basis_per_unit'] if lot['cost_basis_per_unit'] > 0 else lot['buy_price']
                    
                    if base_price_for_tp > 0:
                        if lot['strategy_type'] == "MOMENTUM_BUY" or lot['strategy_type'] == "SYNTHESIZED": 
                            effective_profit_multiplier = (Decimal("1") + config.MOMENTUM_BUY_NET_PROFIT_TARGET_PCT) / (Decimal("1") - config.FEE_PERCENT)
                            lot['take_profit_price'] = (base_price_for_tp * effective_profit_multiplier).quantize(Decimal("0.0001"))
                            logger.warning(f"Lot {lot['lot_id']} (Type: {lot['strategy_type']}) had missing/zero TP. Defaulting to {config.MOMENTUM_BUY_NET_PROFIT_TARGET_PCT*100:.2f}% NET profit: ${lot['take_profit_price']:.2f}.")
                        else: # DIP_ACCUMULATION or other types
                            lot['take_profit_price'] = (base_price_for_tp * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))
                            logger.warning(f"Lot {lot['lot_id']} (Type: {lot['strategy_type']}) had missing/zero TP. Defaulting to {config.DIP_BUY_PROFIT_TARGET_PCT*100:.2f}% GROSS profit: ${lot['take_profit_price']:.2f}.")
                    else:
                        lot['take_profit_price'] = Decimal("0")
                        logger.warning(f"Lot {lot['lot_id']} has zero base price for TP calculation. Setting TP to 0.")
                else:
                    lot['take_profit_price'] = loaded_tp_dec

                lot['original_order_ids'] = item.get('original_order_ids', [])
                if not isinstance(lot['original_order_ids'], list): lot['original_order_ids'] = []
                
                processed_lots.append(lot)
            logger.info(f"Successfully loaded {len(processed_lots)} lots from {path}.")
            return processed_lots
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {path}. The file is likely corrupted. Returning default. Error: {e}")
            return default_value
        except (IOError, OSError) as e:
            logger.error(f"File system error loading JSON from {path}: {e}. Returning default.")
            return default_value
        except Exception as e:
            error = DataError(
                f"Unexpected error loading JSON from {path}: {e}",
                ErrorSeverity.HIGH,
                {"path": path, "operation": "load_json"},
                e
            )
            handle_error(error)
            return default_value

def _save_json_lots(path: str, data: list[dict]):
    logger.debug(f"Attempting to save JSON lots to {path}.")
    with lots():
        tmp_path = path + ".tmp"
        try:
            with open(tmp_path, "w") as f:
                serializable_data = [{k: str(v) if isinstance(v, Decimal) else (v.isoformat() if isinstance(v, datetime) else v) for k, v in item.items()} for item in data]
                json.dump(serializable_data, f, indent=2)
            os.replace(tmp_path, path)
            logger.info(f"Successfully saved {len(data)} lots to {path}.")
        except (IOError, OSError) as e:
            logger.error(f"File system error saving JSON to {path}: {e}")
        except Exception as e:
            error = DataError(
                f"Unexpected error saving JSON to {path}: {e}",
                ErrorSeverity.HIGH,
                {"path": path, "operation": "save_json", "data_count": len(data)},
                e
            )
            handle_error(error)

def calculate_fees(qty: Decimal, price: Decimal) -> Decimal:
    fees = (Decimal(str(qty)) * Decimal(str(price)) * FEE_PERCENT).quantize(Decimal("0.00000001"), rounding=ROUND_HALF_UP)
    logger.debug(f"Calculated fees for Qty {qty:.8f} @ Price ${price:.2f}: ${fees:.8f}")
    return fees

def _calculate_ai_driven_trade_value(ai: dict, available_cash: Decimal, usable_cash: Decimal,
                                   total_equity: Decimal, current_atr: Decimal, logger) -> Decimal:
    """
    Phase 1 & 2: AI-Driven Dynamic Cash Allocation with Exposure Management

    Calculate trade value based on AI suggestions, confidence levels, market conditions,
    and dynamic exposure management. Replaces rigid $50 caps with intelligent allocation.

    Args:
        ai: AI decision dictionary containing quantity_percentage and confidence_level
        available_cash: Total cash available in account
        usable_cash: Cash available after reserves (legacy parameter)
        total_equity: Total account equity
        current_atr: Current Average True Range for volatility adjustment
        logger: Logger instance for debugging

    Returns:
        Decimal: Calculated trade value in USD
    """

    # Extract AI recommendations
    ai_quantity_percentage = ai.get("quantity_percentage")
    ai_confidence = ai.get("confidence_level", "MEDIUM").upper()
    is_dip_buy = ai.get("is_dip_buy", False)

    logger.debug(f"[AI Cash Allocation] AI quantity_percentage: {ai_quantity_percentage}, confidence: {ai_confidence}, is_dip_buy: {is_dip_buy}")

    # Phase 2: Calculate current exposure for dynamic cash management
    with lots():
        current_btc_position_value = sum(
            lot.get('remaining_qty', Decimal('0')) * lot.get('cost_basis_per_unit', Decimal('0'))
            for lot in open_lots
        )

    overall_exposure_percentage = (current_btc_position_value / total_equity) if total_equity > 0 else Decimal('0')
    cash_percentage = (available_cash / total_equity) if total_equity > 0 else Decimal('0')

    logger.debug(f"[AI Cash Allocation] Current exposure: {overall_exposure_percentage:.1%} (${current_btc_position_value:.2f}), Cash: {cash_percentage:.1%} (${available_cash:.2f})")

    # Phase 2: Dynamic cash reserve based on exposure
    if overall_exposure_percentage > Decimal('0.8'):  # 80%+ exposure
        dynamic_reserve_percentage = Decimal('0.4')  # Keep 40% of cash in reserve
        reserve_reason = "high exposure (>80%)"
    elif overall_exposure_percentage > Decimal('0.6'):  # 60%+ exposure
        dynamic_reserve_percentage = Decimal('0.2')  # Keep 20% of cash in reserve
        reserve_reason = "medium exposure (>60%)"
    else:
        dynamic_reserve_percentage = Decimal('0.1')  # Keep 10% of cash in reserve
        reserve_reason = "low exposure (<60%)"

    # Ensure minimum reserve of $25
    dynamic_reserve = max(
        available_cash * dynamic_reserve_percentage,
        Decimal('25.00')
    )

    # Calculate truly usable cash with dynamic reserves
    dynamic_usable_cash = available_cash - dynamic_reserve

    logger.debug(f"[AI Cash Allocation] Dynamic reserve: ${dynamic_reserve:.2f} ({dynamic_reserve_percentage:.0%} due to {reserve_reason}), Usable: ${dynamic_usable_cash:.2f}")

    # Calculate AI-suggested trade value
    if ai_quantity_percentage is not None and ai_quantity_percentage > 0:
        # AI provided specific quantity recommendation
        ai_suggested_usd = total_equity * Decimal(str(ai_quantity_percentage))
        logger.debug(f"[AI Cash Allocation] AI suggested: ${ai_suggested_usd:.2f} ({ai_quantity_percentage:.1%} of ${total_equity:.2f} equity)")
    else:
        # Fallback to traditional logic if AI doesn't provide quantity
        ai_suggested_usd = None
        logger.debug(f"[AI Cash Allocation] No AI quantity suggestion, using traditional logic")

    # Phase 2: Enhanced confidence-based adjustments with exposure consideration
    base_confidence_multiplier = {
        "HIGH": Decimal("1.2"),    # 20% bonus for high confidence
        "MEDIUM": Decimal("1.0"),  # No adjustment for medium confidence
        "LOW": Decimal("0.8")      # 20% reduction for low confidence
    }.get(ai_confidence, Decimal("1.0"))

    # Additional exposure-based adjustment
    if overall_exposure_percentage > Decimal('0.7'):  # High exposure
        exposure_multiplier = Decimal('0.7')  # Reduce size by 30%
        exposure_reason = "high exposure constraint"
    elif cash_percentage < Decimal('0.2'):  # Low cash
        exposure_multiplier = Decimal('0.8')  # Reduce size by 20%
        exposure_reason = "low cash constraint"
    else:
        exposure_multiplier = Decimal('1.0')  # No adjustment
        exposure_reason = "normal conditions"

    # Combined confidence and exposure multiplier
    final_multiplier = base_confidence_multiplier * exposure_multiplier

    logger.debug(f"[AI Cash Allocation] Multipliers - Confidence: {base_confidence_multiplier}, Exposure: {exposure_multiplier} ({exposure_reason}), Final: {final_multiplier}")

    # Calculate traditional max values for comparison (now using dynamic cash)
    traditional_max = min(MAX_TRADE_VALUE_USD, atr_adjusted_value(MAX_TRADE_VALUE_USD, current_atr))
    if is_dip_buy:
        traditional_max = min(MAX_SCALP_TRADE_VALUE_USD, atr_adjusted_value(MAX_SCALP_TRADE_VALUE_USD, current_atr))

    # Determine final trade value
    if ai_suggested_usd is not None:
        # Use AI suggestion with combined adjustments
        adjusted_ai_suggestion = ai_suggested_usd * final_multiplier

        # Phase 2: Intelligent cash utilization - use more cash when appropriate
        max_safe_amount = min(
            dynamic_usable_cash,  # Respect dynamic reserves
            available_cash * Decimal("0.9")  # Never use more than 90% of total cash
        )

        trade_value = min(adjusted_ai_suggestion, max_safe_amount)

        # Ensure minimum viable trade
        trade_value = max(trade_value, MIN_ORDER_VALUE_USD_FLOOR)

        logger.debug(f"[AI Cash Allocation] AI-driven: ${trade_value:.2f} (from ${ai_suggested_usd:.2f} * {final_multiplier} = ${adjusted_ai_suggestion:.2f})")
    else:
        # Enhanced fallback: use dynamic cash instead of rigid $50 cap
        enhanced_traditional_max = min(
            traditional_max,
            dynamic_usable_cash * Decimal("0.8")  # Use up to 80% of dynamic usable cash
        )
        trade_value = max(enhanced_traditional_max, MIN_ORDER_VALUE_USD_FLOOR)
        logger.debug(f"[AI Cash Allocation] Enhanced traditional: ${trade_value:.2f} (was capped at ${traditional_max:.2f})")

    # Final safety checks: ensure we don't exceed dynamic usable cash and never go negative
    final_trade_value = min(trade_value, dynamic_usable_cash)
    final_trade_value = max(final_trade_value, Decimal('0.00'))  # Never negative

    # If result is below minimum but we have some cash, try to use minimum viable amount
    if final_trade_value < MIN_ORDER_VALUE_USD_FLOOR and available_cash >= MIN_ORDER_VALUE_USD_FLOOR:
        final_trade_value = min(MIN_ORDER_VALUE_USD_FLOOR, available_cash - Decimal('1.00'))  # Leave $1 buffer
        logger.debug(f"[AI Cash Allocation] Adjusted to minimum viable: ${final_trade_value:.2f}")

    logger.info(f"[AI Cash Allocation] Final: ${final_trade_value:.2f} | AI: {ai_suggested_usd is not None} | Confidence: {ai_confidence} | Exposure: {overall_exposure_percentage:.1%} | Cash: {cash_percentage:.1%}")

    return final_trade_value

def _calculate_smart_minimum_trade_value(available_cash: Decimal, total_equity: Decimal,
                                       ai_confidence: str, logger) -> Decimal:
    """
    Phase 3: Smart Minimum Handling

    When AI/dynamic logic suggests amount below $12 minimum, intelligently calculate
    a viable trade amount that respects available cash and market conditions.

    Args:
        available_cash: Total cash available in account
        total_equity: Total account equity
        ai_confidence: AI confidence level (HIGH/MEDIUM/LOW)
        logger: Logger instance for debugging

    Returns:
        Decimal: Smart minimum trade value in USD
    """

    logger.debug(f"[Smart Minimum] Calculating intelligent minimum with ${available_cash:.2f} cash, confidence: {ai_confidence}")

    # Base minimum is always Alpaca's requirement
    base_minimum = MIN_ORDER_VALUE_USD_FLOOR

    # Calculate percentage of available cash to use based on confidence
    confidence_cash_percentage = {
        "HIGH": Decimal("0.8"),    # Use up to 80% of available cash for high confidence
        "MEDIUM": Decimal("0.6"),  # Use up to 60% of available cash for medium confidence
        "LOW": Decimal("0.4")      # Use up to 40% of available cash for low confidence
    }.get(ai_confidence.upper(), Decimal("0.5"))  # Default to 50%

    # Calculate smart amount based on available cash and confidence
    confidence_based_amount = available_cash * confidence_cash_percentage

    # Ensure we don't exceed reasonable limits
    max_reasonable_amount = min(
        available_cash * Decimal("0.9"),  # Never use more than 90% of cash
        total_equity * Decimal("0.05")    # Never more than 5% of total equity
    )

    # Choose the appropriate amount
    smart_amount = min(
        max(base_minimum, confidence_based_amount),  # At least minimum, prefer confidence-based
        max_reasonable_amount  # But not more than reasonable maximum
    )

    # Final safety: ensure we have at least $2 buffer remaining
    if smart_amount > available_cash - Decimal("2.00"):
        smart_amount = max(base_minimum, available_cash - Decimal("2.00"))

    logger.debug(f"[Smart Minimum] Confidence: {ai_confidence} ({confidence_cash_percentage:.0%}) -> ${confidence_based_amount:.2f}, Final: ${smart_amount:.2f}")

    return smart_amount

def log_trade_cycle(context: dict):
    filepath = os.path.join(BASE_DIR, "trade_cycle_log.csv")
    file_exists = os.path.isfile(filepath)
    logger.debug(f"Logging trade cycle to {filepath}.")
    try:
        with open(filepath, "a", newline="") as f:
            fieldnames = list(context.keys())
            log_data = context.copy()
            if 'open_lots' in log_data and isinstance(log_data['open_lots'], list):
                log_data['open_lots'] = json.dumps([
                    {k: str(v) if isinstance(v, Decimal) else v.isoformat() if isinstance(v, datetime) else v for k, v in lot.items()}
                    for lot in log_data['open_lots']
                ])
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            if not file_exists or f.tell() == 0:
                writer.writeheader()
            writer.writerow(log_data)
        logger.info(f"Trade cycle logged successfully. Action: {context.get('action')}, Decision: {context.get('decision')}")
    except (IOError, OSError, csv.Error) as e:
        logger.error(f"Error writing to trade_cycle_log.csv: {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred writing trade cycle log: {e}", exc_info=True)

def log_trade_to_csv(ts: str, action: str, qty: Decimal, price: Decimal, order_id: str, status: str, realized_pl: Decimal, fee: Decimal, exploratory: bool = False, entry_price: Decimal = None, lot_id: str = None, strategy_type: str = None):
    filepath = os.path.join(BASE_DIR, TRADE_HISTORY_FILE)
    file_exists = os.path.isfile(filepath)
    header = ["timestamp", "action", "quantity", "price", "order_id", "status", "realized_pl", "fee", "exploratory", "entry_price", "lot_id", "strategy_type"]
    row_data = [ts, action, str(qty), str(price), order_id, status, str(realized_pl), str(fee), str(exploratory), str(entry_price or ''), str(lot_id or ''), str(strategy_type or '')]
    logger.debug(f"Logging trade to history: Action={action}, Qty={qty:.8f}, Price=${price:.2f}, OrderID={order_id}")
    try:
        with open(filepath, "a", newline='') as f:
            writer = csv.writer(f)
            if not file_exists or f.tell() == 0:
                writer.writerow(header)
            writer.writerow(row_data)
        logger.info(f"Trade history logged successfully for order {order_id}.")
    except (IOError, OSError, csv.Error) as e:
        logger.error(f"Error writing to trade_history.csv: {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred writing trade history: {e}", exc_info=True)

def get_trade_history_for_dynamic_params() -> list[dict]:
    logger.debug(f"Attempting to load trade history from {TRADE_HISTORY_FILE} for dynamic parameters.")
    if not os.path.exists(TRADE_HISTORY_FILE): 
        logger.warning(f"Trade history file {TRADE_HISTORY_FILE} not found. Returning empty list.")
        return []
    try:
        with open(TRADE_HISTORY_FILE, 'r', newline='') as f:
            reader = csv.DictReader(f)
            history_data = [{
                **row,
                'quantity': Decimal(row.get('quantity', '0')),
                'price': Decimal(row.get('price', '0')),
                'realized_pl': Decimal(row.get('realized_pl', '0')),
                'fee': Decimal(row.get('fee', '0')),
                'entry_price': Decimal(row.get('entry_price', '0')) if row.get('entry_price') else None,
                'strategy_type': row.get('strategy_type')
            } for row in reader]
        logger.info(f"Successfully loaded {len(history_data)} entries from trade history.")
        return history_data
    except (IOError, OSError, csv.Error) as e:
        logger.error(f"Error reading trade history from {TRADE_HISTORY_FILE}: {e}")
        return []
    except Exception as e:
        logger.critical(f"An unexpected error occurred reading trade history: {e}", exc_info=True)
        return []

def _update_session_stats_for_sell(session_stats: dict, pnl_this_trade: Decimal, order_id: str, action_reason: str):
    logger.debug(f"Updating session stats for sell order {order_id} with PnL: {pnl_this_trade:.6f}.")
    session_stats["net_pl"] = session_stats.get("net_pl", Decimal("0.0")) + pnl_this_trade
    if pnl_this_trade > 0:
        session_stats["win_count"] += 1
        session_stats["win_streak"] += 1
        session_stats["loss_streak"] = 0
        logger.info(f"Sell order {order_id} resulted in a WIN. New net P/L: {session_stats['net_pl']:.2f}")
    else:
        session_stats["loss_count"] += 1
        if session_stats.get("last_action_was_loss", False):
            session_stats["loss_streak"] += 1
        else:
            session_stats["loss_streak"] = 1
        session_stats["win_streak"] = 0
        logger.info(f"Sell order {order_id} resulted in a LOSS. New net P/L: {session_stats['net_pl']:.2f}")
    session_stats["last_action_was_loss"] = (pnl_this_trade < 0)
    logger.info(f"Order {order_id} ({action_reason}) confirmed FILLED. Updated session stats with PnL: {pnl_this_trade:.6f}.")


def load_open_lots() -> list[dict]:
    global open_lots
    logger.debug("Loading open lots.")
    with lots():
        open_lots = _load_json_lots(LOTS_LEDGER_FILE, [])
    logger.info(f"Loaded {len(open_lots)} lots from {LOTS_LEDGER_FILE}.")
    return open_lots

def save_open_lots():
    logger.debug("Saving open lots.")
    with lots():
        _save_json_lots(LOTS_LEDGER_FILE, open_lots)
    logger.info(f"Saved {len(open_lots)} lots to {LOTS_LEDGER_FILE}.")

def recover_stuck_lots():
    """Recovery mechanism for lots stuck in deadlock state where stop-loss is below cost basis."""
    global open_lots
    logger.info("Starting recovery process for potentially stuck lots.")
    with lots():
        recovery_count = 0
        for lot in open_lots:
            cost_basis = lot.get('cost_basis_per_unit', Decimal('0'))
            current_stop = lot.get('current_stop', Decimal('0'))
            
            if cost_basis > Decimal('0') and current_stop < cost_basis:
                # Apply stop-loss buffer above cost basis
                stop_loss_buffer = getattr(config, 'STOP_LOSS_BUFFER_PCT', Decimal('0.001'))  # Default 0.1% buffer
                adjusted_stop = cost_basis * (Decimal('1') + stop_loss_buffer)
                
                lot['current_stop'] = adjusted_stop
                recovery_count += 1
                
                logger.warning(f"RECOVERED stuck lot {lot['lot_id']}: Adjusted stop-loss from ${current_stop:.2f} to ${adjusted_stop:.2f} (cost basis: ${cost_basis:.2f})")
                send_alert(
                    subject=f"AI Bot: Lot Recovery - {lot['lot_id']}", 
                    message=f"Recovered lot {lot['lot_id']} by adjusting stop-loss from ${current_stop:.2f} to ${adjusted_stop:.2f} to prevent deadlock."
                )
        
        if recovery_count > 0:
            save_open_lots()
            logger.info(f"Recovery complete: Fixed {recovery_count} stuck lots.")
        else:
            logger.info("Recovery complete: No stuck lots found.")

def update_trailing_stops(current_price_dec: Decimal, dynamic_stop_percent_dec: Decimal):
    """Update trailing stop-loss prices for all open lots, ensuring cost basis is protected."""
    global open_lots
    logger.debug(f"Updating trailing stops. Current price: ${current_price_dec:.2f}, Dynamic stop percent: {dynamic_stop_percent_dec*100:.2f}%")
    
    # Configuration for stop-loss buffer above cost basis
    stop_loss_buffer = getattr(config, 'STOP_LOSS_BUFFER_PCT', Decimal('0.001'))  # Default 0.1% buffer
    
    with lots():
        for lot in open_lots:
            lot_id = lot.get('lot_id', 'N/A')
            cost_basis_per_unit = lot.get('cost_basis_per_unit', Decimal('0'))
            remaining_qty = lot.get('remaining_qty', Decimal('0'))
            
            if remaining_qty <= DUST_THRESHOLD_QTY:
                logger.debug(f"Skipping trailing stop update for lot {lot_id}: Remaining quantity {remaining_qty:.8f} is below dust threshold.")
                continue
            
            if cost_basis_per_unit <= Decimal('0'):
                logger.warning(f"Cannot update trailing stop for lot {lot_id}: Invalid cost basis {cost_basis_per_unit:.2f}")
                continue
            
            # Calculate new stop-loss price based on current market price
            new_stop_from_price = current_price_dec * (Decimal('1') - dynamic_stop_percent_dec)
            
            # Enforce cost basis as floor with buffer to prevent loss sales
            cost_basis_floor = cost_basis_per_unit * (Decimal('1') + stop_loss_buffer)
            new_stop_for_lot = max(cost_basis_floor, new_stop_from_price)
            
            # Only update if the new stop is higher than the current stop (trailing up)
            current_stop = lot.get('current_stop', Decimal('0'))
            if new_stop_for_lot > current_stop:
                old_stop = current_stop
                lot['current_stop'] = new_stop_for_lot
                
                # Enhanced logging for stop-loss adjustments
                if new_stop_for_lot == cost_basis_floor:
                    logger.warning(f"LOT {lot_id}: Stop-loss adjusted to cost basis floor ${new_stop_for_lot:.2f} (buffer: {stop_loss_buffer*100:.2f}%) to prevent loss sale. Previous stop: ${old_stop:.2f}")
                    send_alert(
                        subject=f"AI Bot: Stop-Loss Adjusted to Cost Basis - {lot_id}",
                        message=f"Lot {lot_id} stop-loss adjusted to cost basis floor ${new_stop_for_lot:.2f} to prevent loss sale. Market price: ${current_price_dec:.2f}"
                    )
                else:
                    logger.info(f"LOT {lot_id}: Trailing stop updated from ${old_stop:.2f} to ${new_stop_for_lot:.2f} (market-based)")
                
                logger.debug(f"LOT {lot_id}: Cost basis: ${cost_basis_per_unit:.2f}, Market price: ${current_price_dec:.2f}, Calculated stop: ${new_stop_from_price:.2f}, Final stop: ${new_stop_for_lot:.2f}")
            else:
                logger.debug(f"LOT {lot_id}: Current stop ${current_stop:.2f} is already higher than calculated new stop ${new_stop_for_lot:.2f}. No update needed.")

def prune_lots_to_available(api, pos: dict, ind: dict):
    global open_lots
    logger.info("Starting lot quantity reconciliation with exchange.")
    with lots():
        debug_logger.debug(f"Executing lot quantity check. Initial in-memory lots: {len(open_lots)}")
        try:
            api_total_qty = Decimal(pos.get("qty", "0.0")) if pos else Decimal("0.0")
            bot_total_qty = sum(l.get("remaining_qty", Decimal("0")) for l in open_lots)
            
            logger.info(f"[Lot Sync] Exchange reports TOTAL position quantity of: {api_total_qty:.8f} BTC")
            logger.info(f"[Lot Sync] Bot's internal TOTAL remaining quantity: {bot_total_qty:.8f} BTC from {len(open_lots)} lot(s).")
            
            discrepancy = api_total_qty - bot_total_qty # Positive if Alpaca has more, negative if bot has more

            if abs(discrepancy) <= config.DUST_THRESHOLD_QTY:
                logger.info(f"[Lot Sync] Discrepancy ({discrepancy:.8f} BTC) is within dust threshold ({config.DUST_THRESHOLD_QTY:.8f}). Assuming sync for now.")
                return

            if api_total_qty > config.MIN_SELL_QTY_THRESHOLD and bot_total_qty <= config.DUST_THRESHOLD_QTY:
                logger.critical("[Lot Sync] STATE DISCREPANCY RECOVERY: Exchange has a position but bot has none (or dust). Synthesizing lot.")
                avg_entry_price = Decimal(pos.get('avg_entry_price', '0.0'))
                
                if avg_entry_price <= 0:
                    logger.error(f"[Lot Sync] CRITICAL: Cannot synthesize lot: Alpaca reports invalid avg_entry_price ({avg_entry_price:.2f}). Manual review needed for position {api_total_qty:.8f} BTC.")
                    send_alert(subject="AI Bot: Lot Sync ERROR - Invalid Alpaca Entry Price", message=f"Cannot synthesize lot for {api_total_qty:.8f} BTC: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual intervention required to establish cost basis.")
                    return 

                synthetic_lot_id = f"synthesized_recovery_{datetime.now(UTC).timestamp()}"
                initial_stop_calc = (avg_entry_price * (Decimal("1") - config.STOP_PERCENT)).quantize(Decimal("0.0001"))
                take_profit_calc = (avg_entry_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))

                synthetic_lot = {
                    "lot_id": synthetic_lot_id, "buy_order_id": "synthesized",
                    "buy_timestamp": datetime.now(UTC),
                    "original_qty": api_total_qty,
                    "remaining_qty": api_total_qty,
                    "buy_price": avg_entry_price, 
                    "buy_fee_usd": Decimal("0.0"),
                    "cost_basis_per_unit": avg_entry_price,
                    "initial_stop": initial_stop_calc,
                    "current_stop": initial_stop_calc, 
                    "take_profit_price": take_profit_calc,
                    "type": "synthesized_recovery",
                    "strategy_type": "SYNTHESIZED",
                    "original_order_ids": []
                }
                open_lots.append(synthetic_lot)
                save_open_lots()
                logger.warning(f"[Lot Sync] Successfully synthesized lot {synthetic_lot_id}: Qty {api_total_qty:.8f} @ avg_entry ${avg_entry_price:.2f}. Please verify on Alpaca.")
                send_alert(subject="AI Bot: Synthesized Lot Recovery", message=f"Bot found discrepancy (Alpaca has position, bot has none). Synthesized lot {synthetic_lot_id} for {api_total_qty:.8f} @ ${avg_entry_price:.2f}.")

            elif api_total_qty <= config.DUST_THRESHOLD_QTY and bot_total_qty > config.DUST_THRESHOLD_QTY:
                # EMERGENCY SAFETY: NEVER auto-wipe lots during API failures
                logger.critical(f"[Lot Sync] DANGEROUS CONDITION DETECTED: Exchange reports zero ({api_total_qty:.8f} BTC) but bot has {bot_total_qty:.8f} BTC in lots.")
                logger.critical(f"[Lot Sync] SAFETY OVERRIDE: Refusing to auto-wipe lots. This may be an API failure during network disconnect.")
                logger.critical(f"[Lot Sync] MANUAL INTERVENTION REQUIRED: Please verify exchange position manually before proceeding.")
                send_alert(subject="AI Bot: CRITICAL - Lot Sync Safety Override",
                          message=f"EMERGENCY: Exchange reports {api_total_qty:.8f} BTC but bot has {bot_total_qty:.8f} BTC in lots. "
                                 f"Auto-wipe DISABLED for safety. Manual verification required. This may be API failure during disconnect.")
                # DO NOT CLEAR LOTS - SAFETY OVERRIDE
                return

            elif discrepancy > config.DUST_THRESHOLD_QTY:
                logger.critical(f"CRITICAL: Exchange has MORE BTC ({api_total_qty:.8f}) than bot thinks ({bot_total_qty:.8f}). Discrepancy: {discrepancy:.8f} BTC. Attempting to add synthetic lot for the excess.")
                
                avg_entry_price = Decimal(pos.get('avg_entry_price', '0.0'))
                if avg_entry_price <= 0:
                    logger.error(f"[Lot Sync] CRITICAL: Cannot synthesize excess lot for {discrepancy:.8f} BTC: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual review needed.")
                    send_alert(subject="AI Bot: Lot Sync ERROR - Invalid Alpaca Entry Price for Excess", message=f"Cannot synthesize lot for {discrepancy:.8f} BTC excess: Alpaca's avg_entry_price is {avg_entry_price:.2f}. Manual intervention required to establish cost basis.")
                    return 

                excess_lot_id = f"synthesized_excess_{datetime.now(UTC).timestamp()}"
                initial_stop_calc = (avg_entry_price * (Decimal("1") - config.STOP_PERCENT)).quantize(Decimal("0.0001"))
                take_profit_calc = (avg_entry_price * (Decimal("1") + config.DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))

                synthetic_excess_lot = {
                    "lot_id": excess_lot_id, "buy_order_id": "synthesized_excess",
                    "buy_timestamp": datetime.now(UTC),
                    "original_qty": discrepancy,
                    "remaining_qty": discrepancy,
                    "buy_price": avg_entry_price,
                    "buy_fee_usd": Decimal("0.0"),
                    "cost_basis_per_unit": avg_entry_price,
                    "initial_stop": initial_stop_calc,
                    "current_stop": initial_stop_calc,
                    "take_profit_price": take_profit_calc,
                    "type": "synthesized_excess_recovery",
                    "strategy_type": "SYNTHESIZED",
                    "original_order_ids": []
                }
                open_lots.append(synthetic_excess_lot)
                save_open_lots()
                logger.critical(f"[Lot Sync] Synthesized new lot {excess_lot_id} for {discrepancy:.8f} BTC excess at Alpaca's average price ${avg_entry_price:.2f}.")
                send_alert(subject="AI Bot: Lot Sync - Excess BTC Found", message=f"Alpaca has {discrepancy:.8f} BTC more than bot. Synthesized lot {excess_lot_id} at ${avg_entry_price:.2f}. Manual check advised.")

            elif discrepancy < -config.DUST_THRESHOLD_QTY:
                abs_discrepancy = abs(discrepancy)
                logger.critical(f"CRITICAL: Bot thinks it has MORE BTC ({bot_total_qty:.8f}) than Alpaca ({api_total_qty:.8f}). Discrepancy: {abs_discrepancy:.8f} BTC. Attempting to reduce bot's internal lots.")
                
                lots_to_consider_reduction = sorted([l for l in open_lots if l['remaining_qty'] > config.DUST_THRESHOLD_QTY], key=lambda x: x['buy_timestamp']) 
                
                amount_reduced = Decimal("0.0")
                for lot in lots_to_consider_reduction:
                    if amount_reduced >= abs_discrepancy:
                        break 

                    reduction_amount = min(lot['remaining_qty'], abs_discrepancy - amount_reduced)
                    if reduction_amount <= Decimal("0"):
                        continue
                    
                    lot['remaining_qty'] -= reduction_amount
                    amount_reduced += reduction_amount
                    
                    logger.warning(f"[Lot Sync] Reduced lot {lot['lot_id']} by {reduction_amount:.8f} BTC. New remaining: {lot['remaining_qty']:.8f}.")

                open_lots[:] = [l for l in open_lots if l['remaining_qty'] > config.DUST_THRESHOLD_QTY]
                save_open_lots()
                
                if amount_reduced < abs_discrepancy:
                    remaining_unresolved = abs_discrepancy - amount_reduced
                    logger.error(f"[Lot Sync] Still have {remaining_unresolved:.8f} BTC discrepancy after trimming lots. Manual review needed.")
                    send_alert(subject="AI Bot: Lot Sync ERROR - Remaining Discrepancy After Trimming", message=f"Bot reconciled internal lots by reducing them, but {remaining_unresolved:.8f} BTC discrepancy still exists. Manual intervention required.")
                else:
                    logger.critical(f"[Lot Sync] Successfully reconciled bot's internal lots by reducing/removing {amount_reduced:.8f} BTC.")
                    send_alert(subject="AI Bot: Lot Sync - Trimmed Lots", message=f"Bot reconciled internal lots by reducing/removing {amount_reduced:.8f} BTC.")
            else:
                logger.info("[Lot Sync] Bot and exchange quantities are in sync (or within dust threshold).")
        except Exception as e:
            context = {
                "operation": "lot_quantity_check",
                "bot_total_qty": float(bot_total_qty) if 'bot_total_qty' in locals() else 0,
                "api_total_qty": float(api_total_qty) if 'api_total_qty' in locals() else 0
            }
            TradingLogicErrorHandler.handle_lot_sync_error(e, context)

            send_enhanced_alert(
                "CRITICAL Lot Sync Error",
                f"An unexpected error occurred during lot reconciliation: {e}. Manual intervention may be required.",
                AlertPriority.URGENT,
                AlertCategory.TRADING,
                context
            )


def process_ai_decision_and_trade(api, ai: dict, acct: dict, pos: dict, ind: dict, buy_cnt: int, sell_cnt: int, dynamic_params: dict, bot_state_data: dict, session_stats: dict) -> tuple[dict, int, int]:
    global open_lots

    # GOVERNOR REMOVED: Bot should trade freely following core safety rules
    # The daily target governor was blocking legitimate trading opportunities
    # Bot's core rule: NEVER sell at a loss (enforced by MIN_PROFIT_THRESHOLD_USD)
    # Bot should buy/sell freely within safety parameters, not be restricted by arbitrary daily targets
    with lots():
        # Ensure ai is a dictionary
        if not isinstance(ai, dict):
            logger.error(f"AI parameter is not a dict: {type(ai)}, value: {ai}")
            ai = {
                "decision": "HOLD",
                "reasoning": "Error: AI parameter was not a dictionary",
                "confidence_level": "LOW",
                "is_dip_buy": False,
                "quantity_percentage": None
            }

        decision = ai.get("decision")
        trade_action_summary = "No action"
        current_price = Decimal(str(ind.get("avg_price", "0.0")))
        current_atr = Decimal(str(ind.get("atr", "0.0")))
        now_ts = datetime.now(UTC)
        current_rsi = Decimal(str(ind.get("rsi", "50.0")))

        current_rsi_val = ind.get("rsi")
        rsi_slope_val = ind.get("rsi_slope")
        macd_hist_val = ind.get("macd_hist")
        volume_spike_val = ind.get("volume_spike")
        trend_5m_val = ind.get("trend_5m")

        current_rsi_safe = Decimal(str(current_rsi_val)) if current_rsi_val is not None else Decimal("50.0")
        rsi_slope_safe = Decimal(str(rsi_slope_val)) if rsi_slope_val is not None else Decimal("0.0")
        macd_hist_safe = Decimal(str(macd_hist_val)) if macd_hist_val is not None else Decimal("0.0")
        volume_spike_safe = Decimal(str(volume_spike_val)) if volume_spike_val is not None else Decimal("0.0")
        
        overall_unrealized_pl = Decimal(str(ind.get("total_unrealized_pl", "0.0"))) 
        in_losing_overall_position = len(open_lots) > 0 and overall_unrealized_pl < Decimal("0.0")

        debug_logger.debug(f"--- Trade Cycle Start for Decision ---")
        debug_logger.debug(f"Current Price: ${current_price:.2f}, Current ATR: {current_atr:.2f}")
        debug_logger.debug(f"Market Indicators: RSI={current_rsi_safe:.2f}, RSI_Slope={rsi_slope_safe:.2f}, MACD_Hist={macd_hist_safe:.2f}, Volume_Spike={volume_spike_safe:.2f}, Trend_5m={trend_5m_val}")
        debug_logger.debug(f"Overall Position: {len(open_lots)} lots, Total Unrealized P/L: ${overall_unrealized_pl:.2f}, In Losing Position: {in_losing_overall_position}")
        debug_logger.debug(f"AI's Initial Decision (from cache or fresh): {ai.get('decision')}, AI is_dip_buy: {ai.get('is_dip_buy')}, AI Confidence: {ai.get('confidence_level')}")

        logger.info(f"Processing AI decision: {decision} with {len(open_lots)} open lot(s).")

        last_trade_timestamp = bot_state_data.get("last_trade_timestamp")
        last_trade_side = bot_state_data.get("last_trade_side")
        if last_trade_timestamp:
            time_since_last_trade = now_ts - last_trade_timestamp
            if decision == "BUY" and last_trade_side == "SELL" and time_since_last_trade < COOLDOWN_BUY_AFTER_SELL:
                logger.warning(f"Cooldown VETO: AI 'BUY' overruled. In BUY cooldown for another {COOLDOWN_BUY_AFTER_SELL - time_since_last_trade}.")
                debug_logger.debug(f"Cooldown VETO applied. Original AI decision: {decision}. New decision: HOLD (for BUY).")
                return {
                    "timestamp": now_ts.isoformat(),
                    "action": "No action (BUY cooldown)",
                    "decision": "HOLD",
                    "current_price": str(current_price),
                    "current_rsi": str(current_rsi),
                    "rsi_slope": str(rsi_slope_safe),
                    "macd_hist": str(macd_hist_safe),
                    "volume_spike": str(volume_spike_safe),
                    "trend_5m": trend_5m_val,
                    "open_lots": open_lots,
                    "session_net_pl": str(session_stats.get("net_pl", Decimal("0"))),
                    "session_win_count": session_stats.get("win_count", 0),
                    "session_loss_count": session_stats.get("loss_count", 0),
                    "session_win_streak": session_stats.get("win_streak", 0),
                    "session_loss_streak": session_stats.get("loss_streak", 0)
                }, buy_cnt, sell_cnt
            if decision == "SELL" and last_trade_side == "BUY" and time_since_last_trade < COOLDOWN_SELL_AFTER_BUY:
                logger.warning(f"Cooldown VETO: AI 'SELL' overruled. In SELL cooldown for another {COOLDOWN_SELL_AFTER_BUY - time_since_last_trade}.")
                debug_logger.debug(f"Cooldown VETO applied. Original AI decision: {decision}. New decision: HOLD (for SELL).")
                return {
                    "timestamp": now_ts.isoformat(),
                    "action": "No action (SELL cooldown)",
                    "decision": "HOLD",
                    "current_price": str(current_price),
                    "current_rsi": str(current_rsi),
                    "rsi_slope": str(rsi_slope_safe),
                    "macd_hist": str(macd_hist_safe),
                    "volume_spike": str(volume_spike_safe),
                    "trend_5m": trend_5m_val,
                    "open_lots": open_lots,
                    "session_net_pl": str(session_stats.get("net_pl", Decimal("0"))),
                    "session_win_count": session_stats.get("win_count", 0),
                    "session_loss_count": session_stats.get("loss_count", 0),
                    "session_win_streak": session_stats.get("win_streak", 0),
                    "session_loss_streak": session_stats.get("loss_streak", 0)
                }, buy_cnt, sell_cnt

        if ENABLE_GUARDIAN_VETO:
            guardian_veto_reason = None
            if decision == "BUY" and current_rsi is not None and current_rsi > GUARDIAN_RSI_OVERBOUGHT:
                guardian_veto_reason = f"Guardian VETO: AI 'BUY' overruled. RSI ({current_rsi:.2f}) > {GUARDIAN_RSI_OVERBOUGHT}."
            if decision == "SELL" and open_lots and current_rsi is not None and current_rsi < GUARDIAN_RSI_OVERSOLD:
                guardian_veto_reason = f"Guardian VETO: AI 'SELL' overruled. RSI ({current_rsi:.2f}) < {GUARDIAN_RSI_OVERSOLD}."
            if guardian_veto_reason:
                logger.critical(guardian_veto_reason)
                debug_logger.debug(f"Guardian VETO applied. Original AI decision: {decision}. New decision: HOLD. Reason: {guardian_veto_reason}")
                decision = "HOLD"
                trade_action_summary = guardian_veto_reason

        # Stop-Loss/AI Controlled Trailing Stop Check
        lots_to_keep_after_stop_sells = []
        any_stop_sell_occurred_this_cycle = False
        prevented_sales_count = 0  # Track prevented sales for summary

        for lot_data in list(open_lots):
            lot_remaining_qty = lot_data.get('remaining_qty', Decimal("0"))
            cost_basis_unit = lot_data.get('cost_basis_per_unit', lot_data.get('buy_price', Decimal("0")))

            trigger_stop_loss = False
            if lot_data.get('current_stop', Decimal("0")) > Decimal("0") and current_price <= lot_data['current_stop']:
                logger.info(f"STOP-LOSS TRIGGERED for lot {lot_data.get('lot_id', 'N/A')} (Type: {lot_data.get('strategy_type')}). Stop: ${lot_data['current_stop']:.2f}, Current Price: ${current_price:.2f}")
                trigger_stop_loss = True
            elif lot_data.get('ai_controlled_stop', Decimal("0")) > Decimal("0") and current_price <= lot_data['ai_controlled_stop']:
                logger.info(f"AI CONTROLLED TRAILING STOP TRIGGERED for lot {lot_data.get('lot_id', 'N/A')} (Type: {lot_data.get('strategy_type')}). AI Stop: ${lot_data['ai_controlled_stop']:.2f}, Current Price: ${current_price:.2f}")
                trigger_stop_loss = True

            if trigger_stop_loss:
                # Calculate PnL before submitting order for stop-loss/controlled loss
                estimated_sell_fee_sl = calculate_fees(lot_remaining_qty, current_price)
                estimated_pnl_this_lot_sl = (current_price - cost_basis_unit) * lot_remaining_qty - estimated_sell_fee_sl
                debug_logger.debug(f"Stop-Loss/AI CTL Pre-Check: Est. SellPrice=${current_price:.2f}, CostBasis=${cost_basis_unit:.2f}, Qty={lot_remaining_qty:.8f}, Est. Fee=${estimated_sell_fee_sl:.8f}, Est. PnL=${estimated_pnl_this_lot_sl:.8f}")

                if estimated_pnl_this_lot_sl <= config.MIN_PROFIT_THRESHOLD_USD:
                    # Use DEBUG level for routine insufficient profit - not a problem, just protection working
                    debug_logger.debug(f"Stop-loss prevented for lot {lot_data.get('lot_id', 'N/A')}: Est. PnL ${estimated_pnl_this_lot_sl:.2f} below threshold ${config.MIN_PROFIT_THRESHOLD_USD:.2f}")
                    prevented_sales_count += 1
                    lots_to_keep_after_stop_sells.append(lot_data)
                    continue

                # Proceed with sell if profitable
                try:
                    sl_sell_order = api.submit_order(symbol="BTCUSD", qty=str(lot_remaining_qty), side="sell", type="market", time_in_force="gtc")
                    order_tracker_service.add_order(sl_sell_order._raw)

                    time.sleep(5)
                    filled_sl_sell_order = api.get_order(sl_sell_order.id)

                    actual_sold_qty_sl = Decimal(str(filled_sl_sell_order.filled_qty))

                    if actual_sold_qty_sl > Decimal("0"):
                        avg_sell_price_sl = Decimal(str(filled_sl_sell_order.filled_avg_price))
                        sell_fee_sl = calculate_fees(actual_sold_qty_sl, avg_sell_price_sl)
                        pnl_this_lot_sl = (avg_sell_price_sl - cost_basis_unit) * actual_sold_qty_sl - sell_fee_sl
                        debug_logger.debug(f"Stop-Loss/AI CTL Post-Fill: SellPrice=${avg_sell_price_sl:.2f}, CostBasis=${cost_basis_unit:.2f}, Qty={actual_sold_qty_sl:.8f}, Fee=${sell_fee_sl:.8f}, PnL=${pnl_this_lot_sl:.8f}")

                        if pnl_this_lot_sl <= config.MIN_PROFIT_THRESHOLD_USD:
                            logger.critical(f"STOP-LOSS/AI CONTROLLED LOSS SELL order {sl_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} resulted in insufficient profit (${pnl_this_lot_sl:.2f} after fees). This should have been prevented by pre-check. NOT UPDATING NET P/L POSITIVELY.")
                            send_alert(subject=f"AI Bot: Stop-Loss/AI CTL Sell Executed with Insufficient Profit", message=f"Lot {lot_data.get('lot_id', 'N/A')} sold with PnL: ${pnl_this_lot_sl:.2f}. Expected > ${config.MIN_PROFIT_THRESHOLD_USD:.2f}.")
                            # Do NOT update session_stats positively if it resulted in insufficient profit
                        else:
                            log_action = "STOP_LOSS_SELL" if current_price <= lot_data['current_stop'] else "AI_CONTROLLED_LOSS_SELL"
                            if filled_sl_sell_order.status == 'partially_filled':
                                log_action += "_PARTIAL"
                                logger.warning(f"Lot {lot_data.get('lot_id', 'N/A')} PARTIALLY FILLED ({log_action}): Sold {actual_sold_qty_sl:.8f} @ ${avg_sell_price_sl:.2f}. Remaining Qty: {lot_remaining_qty - actual_sold_qty_sl:.8f}")

                            log_trade_to_csv(ts=now_ts.isoformat(), action=log_action, qty=actual_sold_qty_sl, price=avg_sell_price_sl, order_id=sl_sell_order.id, status=str(filled_sl_sell_order.status), realized_pl=pnl_this_lot_sl, fee=sell_fee_sl, entry_price=lot_data['buy_price'], lot_id=lot_data.get('lot_id', 'N/A'), strategy_type=lot_data.get('strategy_type'))
                            _update_session_stats_for_sell(session_stats, pnl_this_lot_sl, sl_sell_order.id, "Stop-Loss/AI CTL")

                        lot_data['remaining_qty'] -= actual_sold_qty_sl

                        trade_action_summary = f"Stop-Loss/AI CTL Sell: Lot {lot_data.get('lot_id', 'N/A')} Qty {actual_sold_qty_sl:.8f} @ ${avg_sell_price_sl:.2f}. P/L: ${pnl_this_lot_sl:.2f}. Status: {filled_sl_sell_order.status}"
                        logger.info(trade_action_summary)

                        # Send email notification for successful Stop-Loss/AI CTL SELL
                        sell_type = "Stop-Loss" if current_price <= lot_data['current_stop'] else "AI Controlled"
                        send_enhanced_alert(
                            f"🔴 {sell_type} SELL Executed - {lot_data.get('lot_id', 'N/A')}",
                            f"Successfully sold {actual_sold_qty_sl:.8f} BTC at ${avg_sell_price_sl:.2f}\n\n"
                            f"Lot ID: {lot_data.get('lot_id', 'N/A')}\n"
                            f"Sell Type: {sell_type}\n"
                            f"Profit/Loss: ${pnl_this_lot_sl:.2f}\n"
                            f"Fee: ${sell_fee_sl:.2f}\n"
                            f"Total Proceeds: ${(actual_sold_qty_sl * avg_sell_price_sl):.2f}",
                            AlertPriority.NORMAL,
                            AlertCategory.TRADING,
                            {"lot_id": lot_data.get('lot_id', 'N/A'), "qty": float(actual_sold_qty_sl), "price": float(avg_sell_price_sl), "pnl": float(pnl_this_lot_sl), "action": "SELL", "type": sell_type}
                        )

                        sell_cnt += 1
                        bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "SELL"})
                        any_stop_sell_occurred_this_cycle = True

                        if lot_data['remaining_qty'] > DUST_THRESHOLD_QTY:
                            lots_to_keep_after_stop_sells.append(lot_data)
                        else:
                            logger.info(f"Lot {lot_data.get('lot_id', 'N/A')} fully sold or reduced to dust threshold ({lot_data['remaining_qty']:.8f}). Removing from active lots.")
                    else:
                        logger.warning(f"Stop-Loss/AI CTL SELL order {sl_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} received 0 filled_qty. Status: {filled_sl_sell_order.status}. Lot remains unchanged for next attempt.")
                        lots_to_keep_after_stop_sells.append(lot_data)

                except Exception as e_sl_sell:
                    logger.error(f"Stop-Loss/AI CTL SELL attempt for lot {lot_data.get('lot_id', 'N/A')} failed due to exception: {e_sl_sell}", exc_info=True)
                    lots_to_keep_after_stop_sells.append(lot_data)
            else:
                lots_to_keep_after_stop_sells.append(lot_data)
            
            if any_stop_sell_occurred_this_cycle:
                open_lots[:] = lots_to_keep_after_stop_sells
                save_open_lots()

            # Summary logging instead of spam
            if prevented_sales_count > 0:
                logger.info(f"Stop-loss protection: {prevented_sales_count} lots protected from unprofitable sales")

                # Only send alert if threshold exceeded (indicating potential issue)
                if prevented_sales_count >= config.ALERT_ON_PREVENTED_SALES_THRESHOLD:
                    send_alert(
                        subject=f"AI Bot: High Stop-Loss Prevention Count ({prevented_sales_count})",
                        message=f"Prevented {prevented_sales_count} stop-loss sales in this cycle. This may indicate market conditions requiring attention."
                    )

            debug_logger.debug(f"Finished Stop-Loss/AI CTL check. Lots remaining after check: {len(open_lots)}")

        # Granular Take-Profit Check (MODIFIED FOR ROBUST LEDGER UPDATES)
        if open_lots and config.GRANULAR_TAKE_PROFIT_PCT > Decimal("0"):
            lots_to_keep_after_tp_sells = []
            any_tp_sell_occurred_this_cycle = False
            for lot_data in list(open_lots): 
                lot_tp_price = lot_data.get('take_profit_price', Decimal("0"))
                lot_remaining_qty = lot_data.get('remaining_qty', Decimal("0"))
                
                if lot_remaining_qty <= DUST_THRESHOLD_QTY:
                    debug_logger.debug(f"Skipping Lot {lot_data.get('lot_id')} for TP check: Remaining Qty {lot_remaining_qty:.8f} is below dust threshold. Removing from active lots if present.")
                    continue 
                
                if current_price >= lot_tp_price and lot_tp_price > Decimal("0"):
                    logger.info(f"GRANULAR TAKE-PROFIT TRIGGERED for lot {lot_data.get('lot_id', 'N/A')} (Type: {lot_data.get('strategy_type')}). TP: ${lot_tp_price:.2f}, Current Price: ${current_price:.2f}")

                    qty_to_sell_tp = lot_remaining_qty

                    if qty_to_sell_tp < MIN_SELL_QTY_THRESHOLD:
                        logger.warning(f"Granular TP for lot {lot_data.get('lot_id', 'N/A')}: Qty {qty_to_sell_tp:.8f} is below minimum ({MIN_SELL_QTY_THRESHOLD:.8f}). Cannot submit sell order for this lot. Lot remains.")
                        lots_to_keep_after_tp_sells.append(lot_data)
                        continue

                    # Calculate PnL before submitting order
                    cost_basis_unit_tp = lot_data['cost_basis_per_unit']
                    # Estimate sell fee for pre-check
                    estimated_sell_fee_tp = calculate_fees(qty_to_sell_tp, current_price)
                    estimated_pnl_this_lot_tp = (current_price - cost_basis_unit_tp) * qty_to_sell_tp - estimated_sell_fee_tp
                    debug_logger.debug(f"Granular TP Pre-Check: Est. SellPrice=${current_price:.2f}, CostBasis=${cost_basis_unit_tp:.2f}, Qty={qty_to_sell_tp:.8f}, Est. Fee=${estimated_sell_fee_tp:.8f}, Est. PnL=${estimated_pnl_this_lot_tp:.8f}")

                    if estimated_pnl_this_lot_tp <= config.MIN_PROFIT_THRESHOLD_USD:
                        logger.critical(f"Granular TP SELL order for lot {lot_data.get('lot_id', 'N/A')} would result in insufficient profit (${estimated_pnl_this_lot_tp:.2f} after fees). NOT EXECUTING. Lot remains unchanged.")
                        send_alert(subject=f"AI Bot: Granular TP Sell Prevented (Insufficient Profit)", message=f"Lot {lot_data.get('lot_id', 'N/A')} sell prevented. Est. PnL: ${estimated_pnl_this_lot_tp:.2f}. Min Profit Threshold: ${config.MIN_PROFIT_THRESHOLD_USD:.2f}.")
                        lots_to_keep_after_tp_sells.append(lot_data)
                        continue

                    try:
                        tp_sell_order = api.submit_order(symbol="BTCUSD", qty=str(qty_to_sell_tp), side="sell", type="market", time_in_force="gtc")
                        order_tracker_service.add_order(tp_sell_order._raw)

                        time.sleep(5)
                        filled_tp_sell_order = api.get_order(tp_sell_order.id)

                        actual_sold_qty_tp = Decimal(str(filled_tp_sell_order.filled_qty))

                        if actual_sold_qty_tp > Decimal("0"):
                            avg_sell_price_tp = Decimal(str(filled_tp_sell_order.filled_avg_price))
                            sell_fee_tp = calculate_fees(actual_sold_qty_tp, avg_sell_price_tp)
                            pnl_this_lot_tp = (avg_sell_price_tp - cost_basis_unit_tp) * actual_sold_qty_tp - sell_fee_tp
                            debug_logger.debug(f"Granular TP Post-Fill: SellPrice=${avg_sell_price_tp:.2f}, CostBasis=${cost_basis_unit_tp:.2f}, Qty={actual_sold_qty_tp:.8f}, Fee=${sell_fee_tp:.8f}, PnL=${pnl_this_lot_tp:.8f}")

                            if pnl_this_lot_tp <= config.MIN_PROFIT_THRESHOLD_USD:
                                logger.critical(f"Granular TP SELL order {tp_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} resulted in insufficient profit (${pnl_this_lot_tp:.2f} after fees). This should have been prevented by pre-check. NOT UPDATING NET P/L POSITIVELY.")
                                send_alert(subject=f"AI Bot: Granular TP Sell Executed with Insufficient Profit", message=f"Lot {lot_data.get('lot_id', 'N/A')} sold with PnL: ${pnl_this_lot_tp:.2f}. Expected > ${config.MIN_PROFIT_THRESHOLD_USD:.2f}.")
                                # Do NOT update session_stats positively if it resulted in insufficient profit
                            else:
                                log_action = "GRANULAR_TP_SELL"
                                if filled_tp_sell_order.status == 'partially_filled':
                                    log_action = "GRANULAR_TP_SELL_PARTIAL"
                                    logger.warning(f"Lot {lot_data.get('lot_id', 'N/A')} PARTIALLY FILLED: Sold {actual_sold_qty_tp:.8f} @ ${avg_sell_price_tp:.2f}. Remaining Qty: {lot_remaining_qty - actual_sold_qty_tp:.8f}")

                                log_trade_to_csv(ts=now_ts.isoformat(), action=log_action, qty=actual_sold_qty_tp, price=avg_sell_price_tp, order_id=tp_sell_order.id, status=str(filled_tp_sell_order.status), realized_pl=pnl_this_lot_tp, fee=sell_fee_tp, entry_price=lot_data['buy_price'], lot_id=lot_data.get('lot_id', 'N/A'), strategy_type=lot_data.get('strategy_type'))
                                _update_session_stats_for_sell(session_stats, pnl_this_lot_tp, tp_sell_order.id, "Granular TP")

                            lot_data['remaining_qty'] -= actual_sold_qty_tp

                            trade_action_summary = f"Granular TP Sell: Lot {lot_data.get('lot_id', 'N/A')} Qty {actual_sold_qty_tp:.8f} @ ${avg_sell_price_tp:.2f}. P/L: ${pnl_this_lot_tp:.2f}. Status: {filled_tp_sell_order.status}"
                            logger.info(trade_action_summary)

                            # Send email notification for successful Granular TP SELL
                            send_enhanced_alert(
                                f"💰 Granular Take-Profit SELL - {lot_data.get('lot_id', 'N/A')}",
                                f"Successfully sold {actual_sold_qty_tp:.8f} BTC at ${avg_sell_price_tp:.2f}\n\n"
                                f"Lot ID: {lot_data.get('lot_id', 'N/A')}\n"
                                f"Sell Type: Granular Take-Profit\n"
                                f"Profit: ${pnl_this_lot_tp:.2f}\n"
                                f"Fee: ${sell_fee_tp:.2f}\n"
                                f"Total Proceeds: ${(actual_sold_qty_tp * avg_sell_price_tp):.2f}",
                                AlertPriority.NORMAL,
                                AlertCategory.TRADING,
                                {"lot_id": lot_data.get('lot_id', 'N/A'), "qty": float(actual_sold_qty_tp), "price": float(avg_sell_price_tp), "pnl": float(pnl_this_lot_tp), "action": "SELL", "type": "Granular_TP"}
                            )

                            sell_cnt += 1
                            bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "SELL"})
                            any_tp_sell_occurred_this_cycle = True

                            if lot_data['remaining_qty'] > DUST_THRESHOLD_QTY:
                                lots_to_keep_after_tp_sells.append(lot_data)
                            else:
                                logger.info(f"Lot {lot_data.get('lot_id', 'N/A')} fully sold or reduced to dust threshold ({lot_data['remaining_qty']:.8f}). Removing from active lots.")

                        else:
                            logger.warning(f"Granular TP SELL order {tp_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} received 0 filled_qty. Status: {filled_tp_sell_order.status}. Lot remains unchanged for next attempt.")
                            lots_to_keep_after_tp_sells.append(lot_data)

                    except Exception as e_tp_sell:
                        logger.error(f"Granular TP SELL attempt for lot {lot_data.get('lot_id', 'N/A')} failed due to exception: {e_tp_sell}", exc_info=True)
                        lots_to_keep_after_tp_sells.append(lot_data)
                else:
                    lots_to_keep_after_tp_sells.append(lot_data) 
            
            if any_tp_sell_occurred_this_cycle:
                open_lots[:] = lots_to_keep_after_tp_sells
                save_open_lots()
            debug_logger.debug(f"Finished Granular TP check. Lots remaining after check: {len(open_lots)}")

        # Time-Based Profit Taking Check
        if open_lots and hasattr(config, 'TIME_BASED_PROFIT_TAKING_THRESHOLD_HOURS') and config.TIME_BASED_PROFIT_TAKING_THRESHOLD_HOURS > 0 and hasattr(config, 'TIME_BASED_PROFIT_TAKING_QTY_PCT') and config.TIME_BASED_PROFIT_TAKING_QTY_PCT > Decimal("0"):
            lots_to_keep_after_time_sells = []
            any_time_sell_occurred_this_cycle = False
            time_threshold = timedelta(hours=config.TIME_BASED_PROFIT_TAKING_THRESHOLD_HOURS)

            for lot_data in list(open_lots):
                lot_remaining_qty = lot_data.get('remaining_qty', Decimal("0"))
                lot_buy_timestamp = lot_data.get('buy_timestamp')
                cost_basis_unit = lot_data.get('cost_basis_per_unit', lot_data.get('buy_price', Decimal("0")))

                if lot_remaining_qty <= DUST_THRESHOLD_QTY:
                    debug_logger.debug(f"Skipping Lot {lot_data.get('lot_id')} for Time-Based TP check: Remaining Qty {lot_remaining_qty:.8f} is below dust threshold. Removing from active lots if present.")
                    continue

                if lot_buy_timestamp and (now_ts - lot_buy_timestamp) >= time_threshold:
                    # Check if lot value is large enough for time-based selling
                    lot_current_value = current_price * lot_remaining_qty
                    if lot_current_value < config.MIN_LOT_VALUE_FOR_TIME_BASED_USD:
                        logger.debug(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Lot value ${lot_current_value:.2f} is below minimum ${config.MIN_LOT_VALUE_FOR_TIME_BASED_USD:.2f}. Skipping to avoid small lot spam.")
                        lots_to_keep_after_time_sells.append(lot_data)
                        continue

                    # Calculate unrealized P/L for this specific lot
                    unrealized_pl_lot = (current_price - cost_basis_unit) * lot_remaining_qty

                    if unrealized_pl_lot <= config.MIN_PROFIT_THRESHOLD_USD: # Ensure it's profitable after fees
                        logger.debug(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Not profitable enough (${unrealized_pl_lot:.2f}). Skipping.")
                        lots_to_keep_after_time_sells.append(lot_data)
                        continue

                    logger.info(f"TIME-BASED PROFIT TRIGGERED for lot {lot_data.get('lot_id', 'N/A')} (Type: {lot_data.get('strategy_type')}). Held for {(now_ts - lot_buy_timestamp)}. Unrealized P/L: ${unrealized_pl_lot:.2f}")

                    qty_to_sell_time_based = (lot_remaining_qty * config.TIME_BASED_PROFIT_TAKING_QTY_PCT).quantize(Decimal("0.00000001"), ROUND_DOWN)

                    if qty_to_sell_time_based < MIN_SELL_QTY_THRESHOLD:
                        logger.warning(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Calculated Qty {qty_to_sell_time_based:.8f} is below minimum ({MIN_SELL_QTY_THRESHOLD:.8f}). Cannot submit sell order for this lot. Lot remains.")
                        lots_to_keep_after_time_sells.append(lot_data)
                        continue

                    # Calculate PnL before submitting order
                    estimated_sell_fee_time = calculate_fees(qty_to_sell_time_based, current_price)
                    estimated_pnl_this_lot_time = (current_price - cost_basis_unit) * qty_to_sell_time_based - estimated_sell_fee_time

                    # Smart quantity adjustment for small lots: if initial percentage would be unprofitable, try larger percentages
                    time_based_threshold = getattr(config, 'MIN_PROFIT_THRESHOLD_TIME_BASED_USD', config.MIN_PROFIT_THRESHOLD_USD)
                    if estimated_pnl_this_lot_time <= time_based_threshold:
                        logger.debug(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Initial {config.TIME_BASED_PROFIT_TAKING_QTY_PCT*100:.0f}% sale would be unprofitable (${estimated_pnl_this_lot_time:.6f}). Trying larger percentages...")

                        # Try progressively larger percentages: 75%, 100%
                        for try_pct in [Decimal("0.75"), Decimal("1.00")]:
                            try_qty = (lot_remaining_qty * try_pct).quantize(Decimal("0.00000001"), ROUND_DOWN)
                            if try_qty < MIN_SELL_QTY_THRESHOLD:
                                continue

                            try_fee = calculate_fees(try_qty, current_price)
                            try_pnl = (current_price - cost_basis_unit) * try_qty - try_fee

                            if try_pnl > time_based_threshold:
                                logger.info(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Adjusted to {try_pct*100:.0f}% sale for profitable trade. Est. PnL: ${try_pnl:.6f}")
                                qty_to_sell_time_based = try_qty
                                estimated_sell_fee_time = try_fee
                                estimated_pnl_this_lot_time = try_pnl
                                break
                        else:
                            # If even 100% sale isn't profitable enough, skip this lot
                            logger.debug(f"Time-Based TP for lot {lot_data.get('lot_id', 'N/A')}: Even 100% sale would be unprofitable. Skipping.")
                            lots_to_keep_after_time_sells.append(lot_data)
                            continue
                    debug_logger.debug(f"Time-Based TP Pre-Check: Est. SellPrice=${current_price:.2f}, CostBasis=${cost_basis_unit:.2f}, Qty={qty_to_sell_time_based:.8f}, Est. Fee=${estimated_sell_fee_time:.8f}, Est. PnL=${estimated_pnl_this_lot_time:.8f}")

                    # Use higher threshold for time-based sales to reduce email spam
                    time_based_threshold = getattr(config, 'MIN_PROFIT_THRESHOLD_TIME_BASED_USD', config.MIN_PROFIT_THRESHOLD_USD)
                    if estimated_pnl_this_lot_time <= time_based_threshold:
                        logger.critical(f"Time-Based TP SELL order for lot {lot_data.get('lot_id', 'N/A')} would result in insufficient profit (${estimated_pnl_this_lot_time:.2f} after fees). NOT EXECUTING. Lot remains unchanged.")

                        # Email rate limiting: only send email if enough time has passed since last one
                        import time as time_module
                        current_time_minutes = time_module.time() / 60
                        last_email_key = f"time_based_email_{lot_data.get('lot_id', 'unknown')}"

                        if not hasattr(process_ai_decision_and_trade, '_last_time_based_emails'):
                            process_ai_decision_and_trade._last_time_based_emails = {}

                        last_email_time = process_ai_decision_and_trade._last_time_based_emails.get(last_email_key, 0)
                        cooldown_minutes = getattr(config, 'TIME_BASED_EMAIL_COOLDOWN_MINUTES', 60)

                        if current_time_minutes - last_email_time >= cooldown_minutes:
                            send_alert(subject=f"AI Bot: Time-Based TP Sell Prevented (Insufficient Profit)", message=f"Lot {lot_data.get('lot_id', 'N/A')} time-based sell prevented. Est. PnL: ${estimated_pnl_this_lot_time:.2f}. Min Profit Threshold: ${time_based_threshold:.2f}. (Rate limited - next email in {cooldown_minutes} min)")
                            process_ai_decision_and_trade._last_time_based_emails[last_email_key] = current_time_minutes

                        lots_to_keep_after_time_sells.append(lot_data)
                        continue

                    try:
                        time_sell_order = api.submit_order(symbol="BTCUSD", qty=str(qty_to_sell_time_based), side="sell", type="market", time_in_force="gtc")
                        order_tracker_service.add_order(time_sell_order._raw)

                        time.sleep(5)
                        filled_time_sell_order = api.get_order(time_sell_order.id)

                        actual_sold_qty_time = Decimal(str(filled_time_sell_order.filled_qty))

                        if actual_sold_qty_time > Decimal("0"):
                            avg_sell_price_time = Decimal(str(filled_time_sell_order.filled_avg_price))
                            sell_fee_time = calculate_fees(actual_sold_qty_time, avg_sell_price_time)
                            pnl_this_lot_time = (avg_sell_price_time - cost_basis_unit) * actual_sold_qty_time - sell_fee_time
                            debug_logger.debug(f"Time-Based TP Post-Fill: SellPrice=${avg_sell_price_time:.2f}, CostBasis=${cost_basis_unit:.2f}, Qty={actual_sold_qty_time:.8f}, Fee=${sell_fee_time:.8f}, PnL=${pnl_this_lot_time:.8f}")

                            if pnl_this_lot_time <= config.MIN_PROFIT_THRESHOLD_USD:
                                logger.critical(f"Time-Based TP SELL order {time_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} resulted in insufficient profit (${pnl_this_lot_time:.2f} after fees). This should have been prevented by pre-check. NOT UPDATING NET P/L POSITIVELY.")
                                send_alert(subject=f"AI Bot: Time-Based TP Sell Executed with Insufficient Profit", message=f"Lot {lot_data.get('lot_id', 'N/A')} sold with PnL: ${pnl_this_lot_time:.2f}. Expected > ${config.MIN_PROFIT_THRESHOLD_USD:.2f}.")
                                # Do NOT update session_stats positively if it resulted in insufficient profit
                            else:
                                log_action = "TIME_BASED_TP_SELL"
                                if filled_time_sell_order.status == 'partially_filled':
                                    log_action = "TIME_BASED_TP_SELL_PARTIAL"
                                    logger.warning(f"Lot {lot_data.get('lot_id', 'N/A')} PARTIALLY FILLED (Time-Based): Sold {actual_sold_qty_time:.8f} @ ${avg_sell_price_time:.2f}. Remaining Qty: {lot_remaining_qty - actual_sold_qty_time:.8f}")

                                log_trade_to_csv(ts=now_ts.isoformat(), action=log_action, qty=actual_sold_qty_time, price=avg_sell_price_time, order_id=time_sell_order.id, status=str(filled_time_sell_order.status), realized_pl=pnl_this_lot_time, fee=sell_fee_time, entry_price=lot_data['buy_price'], lot_id=lot_data.get('lot_id', 'N/A'), strategy_type=lot_data.get('strategy_type'))
                                _update_session_stats_for_sell(session_stats, pnl_this_lot_time, time_sell_order.id, "Time-Based TP")

                            lot_data['remaining_qty'] -= actual_sold_qty_time

                            trade_action_summary = f"Time-Based TP Sell: Lot {lot_data.get('lot_id', 'N/A')} Qty {actual_sold_qty_time:.8f} @ ${avg_sell_price_time:.2f}. P/L: ${pnl_this_lot_time:.2f}. Status: {filled_time_sell_order.status}"
                            logger.info(trade_action_summary)

                            # Send email notification for successful Time-Based TP SELL
                            send_enhanced_alert(
                                f"⏰ Time-Based Take-Profit SELL - {lot_data.get('lot_id', 'N/A')}",
                                f"Successfully sold {actual_sold_qty_time:.8f} BTC at ${avg_sell_price_time:.2f}\n\n"
                                f"Lot ID: {lot_data.get('lot_id', 'N/A')}\n"
                                f"Sell Type: Time-Based Take-Profit\n"
                                f"Profit: ${pnl_this_lot_time:.2f}\n"
                                f"Fee: ${sell_fee_time:.2f}\n"
                                f"Total Proceeds: ${(actual_sold_qty_time * avg_sell_price_time):.2f}",
                                AlertPriority.NORMAL,
                                AlertCategory.TRADING,
                                {"lot_id": lot_data.get('lot_id', 'N/A'), "qty": float(actual_sold_qty_time), "price": float(avg_sell_price_time), "pnl": float(pnl_this_lot_time), "action": "SELL", "type": "Time_Based_TP"}
                            )

                            sell_cnt += 1
                            bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "SELL"})
                            any_time_sell_occurred_this_cycle = True

                            if lot_data['remaining_qty'] > DUST_THRESHOLD_QTY:
                                lots_to_keep_after_time_sells.append(lot_data)
                            else:
                                logger.info(f"Lot {lot_data.get('lot_id', 'N/A')} fully sold or reduced to dust threshold ({lot_data['remaining_qty']:.8f}). Removing from active lots.")

                        else:
                            logger.warning(f"Time-Based TP SELL order {time_sell_order.id} for lot {lot_data.get('lot_id', 'N/A')} received 0 filled_qty. Status: {filled_time_sell_order.status}. Lot remains unchanged for next attempt.")
                            lots_to_keep_after_time_sells.append(lot_data)

                    except Exception as e_time_sell:
                        logger.error(f"Time-Based TP SELL attempt for lot {lot_data.get('lot_id', 'N/A')} failed due to exception: {e_time_sell}", exc_info=True)
                        lots_to_keep_after_time_sells.append(lot_data)
                else:
                    lots_to_keep_after_time_sells.append(lot_data)
            
            if any_time_sell_occurred_this_cycle:
                open_lots[:] = lots_to_keep_after_time_sells
                save_open_lots()
            debug_logger.debug(f"Finished Time-Based TP check. Lots remaining after check: {len(open_lots)}")

        # AI Decision Execution (BUY/SELL)
        if decision == "BUY":
            available_cash = Decimal(str(acct.get("cash", "0.0")))
            total_equity = Decimal(str(acct.get("equity", "0.0")))

            # Calculate dynamic cash reserve based on current equity
            dynamic_cash_reserve = calculate_dynamic_cash_reserve(total_equity)
            usable_cash = available_cash - dynamic_cash_reserve

            # Calculate current portfolio allocation
            with lots():
                current_position_value = sum(
                    lot.get('remaining_qty', Decimal('0')) * lot.get('cost_basis_per_unit', Decimal('0'))
                    for lot in open_lots
                )

            portfolio_allocation_limit = calculate_portfolio_allocation_limit(total_equity)
            remaining_allocation_capacity = portfolio_allocation_limit - current_position_value

            # Enhanced cash management checks
            if available_cash < MIN_ORDER_VALUE_USD_FLOOR:
                logger.warning(f"Insufficient total cash for BUY. Available: ${available_cash:.2f}, Min Required: ${MIN_ORDER_VALUE_USD_FLOOR:.2f}")
                trade_action_summary = "No action (insufficient total cash)"
            elif usable_cash < MIN_ORDER_VALUE_USD_FLOOR:
                logger.warning(f"Insufficient usable cash for BUY. Available: ${available_cash:.2f}, Reserve: ${dynamic_cash_reserve:.2f}, Usable: ${usable_cash:.2f}")
                trade_action_summary = "No action (insufficient usable cash - preserving reserve)"
            elif remaining_allocation_capacity < MIN_ORDER_VALUE_USD_FLOOR:
                logger.warning(f"Portfolio allocation limit reached. Current: ${current_position_value:.2f}, Limit: ${portfolio_allocation_limit:.2f}, Remaining: ${remaining_allocation_capacity:.2f}")
                trade_action_summary = "No action (portfolio allocation limit reached)"
            else:
                try:
                    # Phase 1 & 2: AI-Driven Dynamic Cash Allocation with Portfolio Limits
                    max_trade_value = min(usable_cash, remaining_allocation_capacity)

                    trade_value_usd = _calculate_ai_driven_trade_value(
                        ai=ai,
                        available_cash=available_cash,
                        usable_cash=max_trade_value,  # Use constrained usable cash
                        total_equity=total_equity,
                        current_atr=current_atr,
                        logger=logger
                    )

                    # Phase 3: Smart Minimum Handling
                    if trade_value_usd < MIN_ORDER_VALUE_USD_FLOOR:
                        # AI/Dynamic logic suggested amount below minimum
                        if available_cash >= MIN_ORDER_VALUE_USD_FLOOR:
                            # We have enough cash - use intelligent minimum allocation
                            smart_minimum_value = _calculate_smart_minimum_trade_value(
                                available_cash=available_cash,
                                total_equity=total_equity,
                                ai_confidence=ai.get("confidence_level", "MEDIUM"),
                                logger=logger
                            )
                            trade_value_usd = smart_minimum_value
                            logger.info(f"[Smart Minimum] Upgraded trade from ${trade_value_usd:.2f} to ${smart_minimum_value:.2f}")
                        else:
                            logger.warning(f"[Smart Minimum] Cannot upgrade - insufficient cash. Need ${MIN_ORDER_VALUE_USD_FLOOR:.2f}, have ${available_cash:.2f}")
                            trade_action_summary = "No action (insufficient cash for minimum)"
                            # Skip trade execution
                            trade_value_usd = Decimal('0.00')

                    # Only proceed with trade if we have a valid trade value
                    if trade_value_usd >= MIN_ORDER_VALUE_USD_FLOOR:
                        qty_to_buy = (trade_value_usd / current_price).quantize(Decimal("0.00000001"), ROUND_DOWN)

                        if qty_to_buy < MIN_TRADE_QTY_BTC:
                            logger.warning(f"Calculated buy quantity {qty_to_buy:.8f} BTC is below minimum {MIN_TRADE_QTY_BTC:.8f}. Skipping buy.")
                            trade_action_summary = "No action (quantity too small)"
                        else:
                            buy_order = api.submit_order(symbol="BTCUSD", qty=str(qty_to_buy), side="buy", type="market", time_in_force="gtc")
                            order_tracker_service.add_order(buy_order._raw)

                            time.sleep(5)
                            filled_buy_order = api.get_order(buy_order.id)

                            actual_bought_qty = Decimal(str(filled_buy_order.filled_qty))

                            if actual_bought_qty > Decimal("0"):
                                avg_buy_price = Decimal(str(filled_buy_order.filled_avg_price))
                                buy_fee = calculate_fees(actual_bought_qty, avg_buy_price)

                                lot_id = f"lot_{buy_order.id}_{datetime.now(UTC).timestamp()}"
                                initial_stop = (avg_buy_price * (Decimal("1") - Decimal(str(dynamic_params["stop_percent"])))).quantize(Decimal("0.0001"))

                                # Apply the enhanced stop-loss calculation with cost basis protection
                                stop_loss_buffer = getattr(config, 'STOP_LOSS_BUFFER_PCT', Decimal('0.001'))
                                cost_basis_floor = avg_buy_price * (Decimal('1') + stop_loss_buffer)
                                protected_stop = max(cost_basis_floor, initial_stop)

                                take_profit_price = (avg_buy_price * (Decimal("1") + DIP_BUY_PROFIT_TARGET_PCT)).quantize(Decimal("0.0001"))

                                new_lot = {
                                    "lot_id": lot_id,
                                    "buy_order_id": buy_order.id,
                                    "buy_timestamp": now_ts,
                                    "original_qty": actual_bought_qty,
                                    "remaining_qty": actual_bought_qty,
                                    "buy_price": avg_buy_price,
                                    "buy_fee_usd": buy_fee,
                                    "cost_basis_per_unit": ((avg_buy_price * actual_bought_qty) + buy_fee) / actual_bought_qty,
                                    "initial_stop": protected_stop,
                                    "current_stop": protected_stop,
                                    "take_profit_price": take_profit_price,
                                    "type": "dip_accumulation" if ai.get("is_dip_buy", False) else "momentum_buy",
                                    "strategy_type": "DIP_ACCUMULATION" if ai.get("is_dip_buy", False) else "MOMENTUM_BUY",
                                    "original_order_ids": [buy_order.id]
                                }

                                open_lots.append(new_lot)
                                save_open_lots()

                                log_trade_to_csv(ts=now_ts.isoformat(), action="BUY", qty=actual_bought_qty, price=avg_buy_price, order_id=buy_order.id, status=str(filled_buy_order.status), realized_pl=Decimal("0"), fee=buy_fee, lot_id=lot_id, strategy_type=new_lot['strategy_type'])

                                trade_action_summary = f"BUY: {actual_bought_qty:.8f} BTC @ ${avg_buy_price:.2f} (Lot: {lot_id})"
                                logger.info(trade_action_summary)

                                # Send email notification for successful BUY
                                send_enhanced_alert(
                                    f"🟢 BUY Order Executed - {lot_id}",
                                    f"Successfully purchased {actual_bought_qty:.8f} BTC at ${avg_buy_price:.2f}\n\n"
                                    f"Lot ID: {lot_id}\n"
                                    f"Strategy: {new_lot['strategy_type']}\n"
                                    f"Total Cost: ${(actual_bought_qty * avg_buy_price):.2f}\n"
                                    f"Fee: ${buy_fee:.2f}\n"
                                    f"Stop-Loss: ${new_lot['current_stop']:.2f}",
                                    AlertPriority.NORMAL,
                                    AlertCategory.TRADING,
                                    {"lot_id": lot_id, "qty": float(actual_bought_qty), "price": float(avg_buy_price), "action": "BUY"}
                                )

                                buy_cnt += 1
                                bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "BUY"})

                                if protected_stop > initial_stop:
                                    logger.info(f"BUY order {buy_order.id}: Stop-loss protected at ${protected_stop:.2f} (initial would have been ${initial_stop:.2f})")
                            else:
                                logger.warning(f"BUY order {buy_order.id} received 0 filled_qty. Status: {filled_buy_order.status}")

                except Exception as e_buy:
                    logger.error(f"BUY attempt failed due to exception: {e_buy}", exc_info=True)
        
        elif decision == "SELL" and open_lots:
            total_available_qty = sum(lot.get('remaining_qty', Decimal("0")) for lot in open_lots)
            
            if total_available_qty < MIN_SELL_QTY_THRESHOLD:
                logger.warning(f"Total available quantity {total_available_qty:.8f} BTC is below minimum sell threshold {MIN_SELL_QTY_THRESHOLD:.8f}. Cannot sell.")
                trade_action_summary = "No action (insufficient quantity)"
            else:
                try:
                    sell_order = api.submit_order(symbol="BTCUSD", qty=str(total_available_qty), side="sell", type="market", time_in_force="gtc")
                    order_tracker_service.add_order(sell_order._raw)
                    
                    time.sleep(5)
                    filled_sell_order = api.get_order(sell_order.id)
                    
                    actual_sold_qty = Decimal(str(filled_sell_order.filled_qty))
                    
                    if actual_sold_qty > Decimal("0"):
                        avg_sell_price = Decimal(str(filled_sell_order.filled_avg_price))
                        sell_fee = calculate_fees(actual_sold_qty, avg_sell_price)
                        
                        # Calculate weighted average cost basis for all lots
                        total_cost_basis = Decimal("0")
                        total_qty_sold = Decimal("0")
                        
                        lots_to_keep_after_sell = []
                        remaining_qty_to_sell = actual_sold_qty
                        
                        for lot in sorted(open_lots, key=lambda x: x['buy_timestamp']):
                            if remaining_qty_to_sell <= Decimal("0"):
                                lots_to_keep_after_sell.append(lot)
                                continue
                            
                            lot_qty = lot.get('remaining_qty', Decimal("0"))
                            qty_from_this_lot = min(lot_qty, remaining_qty_to_sell)
                            
                            if qty_from_this_lot > Decimal("0"):
                                cost_basis_unit = lot.get('cost_basis_per_unit', lot.get('buy_price', Decimal("0")))
                                total_cost_basis += cost_basis_unit * qty_from_this_lot
                                total_qty_sold += qty_from_this_lot
                                
                                lot['remaining_qty'] -= qty_from_this_lot
                                remaining_qty_to_sell -= qty_from_this_lot
                                
                                if lot['remaining_qty'] > DUST_THRESHOLD_QTY:
                                    lots_to_keep_after_sell.append(lot)
                        
                        weighted_avg_cost_basis = total_cost_basis / total_qty_sold if total_qty_sold > Decimal("0") else Decimal("0")
                        pnl_this_trade = (avg_sell_price - weighted_avg_cost_basis) * actual_sold_qty - sell_fee
                        
                        log_trade_to_csv(ts=now_ts.isoformat(), action="SELL", qty=actual_sold_qty, price=avg_sell_price, order_id=sell_order.id, status=str(filled_sell_order.status), realized_pl=pnl_this_trade, fee=sell_fee, lot_id="multiple_lots", strategy_type="AI_SELL")
                        _update_session_stats_for_sell(session_stats, pnl_this_trade, sell_order.id, "AI Decision")
                        
                        open_lots[:] = [lot for lot in lots_to_keep_after_sell if lot.get('remaining_qty', Decimal("0")) > DUST_THRESHOLD_QTY]
                        save_open_lots()
                        
                        trade_action_summary = f"SELL: {actual_sold_qty:.8f} BTC @ ${avg_sell_price:.2f}. P/L: ${pnl_this_trade:.2f}"
                        logger.info(trade_action_summary)

                        # Send email notification for successful AI Decision SELL
                        send_enhanced_alert(
                            f"🤖 AI Decision SELL Executed - Multiple Lots",
                            f"Successfully sold {actual_sold_qty:.8f} BTC at ${avg_sell_price:.2f}\n\n"
                            f"Sell Type: AI Decision (Full Position)\n"
                            f"Total Profit/Loss: ${pnl_this_trade:.2f}\n"
                            f"Fee: ${sell_fee:.2f}\n"
                            f"Total Proceeds: ${(actual_sold_qty * avg_sell_price):.2f}\n"
                            f"Lots Sold: {len([lot for lot in open_lots if lot.get('remaining_qty', Decimal('0')) > DUST_THRESHOLD_QTY])}",
                            AlertPriority.NORMAL,
                            AlertCategory.TRADING,
                            {"qty": float(actual_sold_qty), "price": float(avg_sell_price), "pnl": float(pnl_this_trade), "action": "SELL", "type": "AI_Decision"}
                        )

                        sell_cnt += 1
                        bot_state_data.update({"last_trade_timestamp": now_ts, "last_trade_side": "SELL"})
                    else:
                        logger.warning(f"SELL order {sell_order.id} received 0 filled_qty. Status: {filled_sell_order.status}")
                
                except Exception as e_sell:
                    logger.error(f"SELL attempt failed due to exception: {e_sell}", exc_info=True)
        
        elif decision == "HOLD":
            trade_action_summary = "No action (AI decision: HOLD)"
        
        # Log trade cycle context
        cycle_context = {
            "timestamp": now_ts.isoformat(),
            "action": trade_action_summary,
            "decision": decision,
            "current_price": str(current_price),
            "current_rsi": str(current_rsi),
            "rsi_slope": str(rsi_slope_safe),
            "macd_hist": str(macd_hist_safe),
            "volume_spike": str(volume_spike_safe),
            "trend_5m": trend_5m_val,
            "open_lots": open_lots,
            "session_net_pl": str(session_stats.get("net_pl", Decimal("0"))),
            "session_win_count": session_stats.get("win_count", 0),
            "session_loss_count": session_stats.get("loss_count", 0),
            "session_win_streak": session_stats.get("win_streak", 0),
            "session_loss_streak": session_stats.get("loss_streak", 0)
        }
        log_trade_cycle(cycle_context)
        
        debug_logger.debug(f"--- Trade Cycle Complete ---")
        debug_logger.debug(f"Final Action: {trade_action_summary}")
        debug_logger.debug(f"Open Lots Remaining: {len(open_lots)}")
        debug_logger.debug(f"Session Stats: Net P/L: {session_stats.get('net_pl', Decimal('0')):.2f}, Wins: {session_stats.get('win_count', 0)}, Losses: {session_stats.get('loss_count', 0)}")
        
        return cycle_context, buy_cnt, sell_cnt
        