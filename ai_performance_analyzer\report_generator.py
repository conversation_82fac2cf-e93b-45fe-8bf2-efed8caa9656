"""
Report Generator
================

Generates comprehensive performance reports and visualizations for the AI Performance Analyzer system.
Creates daily/weekly reports with insights, charts, and actionable recommendations.
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import matplotlib
matplotlib.use('Agg')  # Use non-GUI backend for threading compatibility
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.figure import Figure
import pandas as pd
import numpy as np

from .config import REPORTING, LOGGING, REPORTS_DIR, INSIGHTS_DIR
from .utils import setup_logger, safe_float, parse_timestamp

class ReportGenerator:
    """Generates comprehensive performance reports and visualizations."""
    
    def __init__(self):
        self.logger = setup_logger("ReportGenerator", LOGGING)
        self.reports_dir = REPORTS_DIR
        self.insights_dir = INSIGHTS_DIR
        
        # Ensure directories exist
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.insights_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_daily_report(self, analysis_results: Dict[str, Any]) -> Dict[str, str]:
        """Generate a comprehensive daily performance report."""
        self.logger.info("Generating daily performance report...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generate different report formats
            reports = {
                "markdown": self._generate_markdown_report(analysis_results, "daily"),
                "json": self._generate_json_report(analysis_results, "daily"),
                "summary": self._generate_summary_report(analysis_results)
            }
            
            # Save reports to files
            report_files = {}
            for format_type, content in reports.items():
                filename = f"daily_report_{timestamp}.{format_type}"
                filepath = self.reports_dir / filename
                
                if format_type == "json":
                    with open(filepath, 'w') as f:
                        json.dump(content, f, indent=2, default=str)
                else:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                report_files[format_type] = str(filepath)
                self.logger.info(f"Generated {format_type} report: {filepath}")
            
            # Generate visualizations
            chart_files = self._generate_visualizations(analysis_results, timestamp)
            report_files.update(chart_files)
            
            return report_files
            
        except Exception as e:
            self.logger.error(f"Error generating daily report: {e}")
            raise
    
    def generate_weekly_report(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate a comprehensive weekly performance report."""
        self.logger.info("Generating weekly performance report...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Aggregate weekly data
            aggregated_data = self._aggregate_weekly_data(weekly_data)
            
            # Generate reports
            reports = {
                "markdown": self._generate_markdown_report(aggregated_data, "weekly"),
                "json": self._generate_json_report(aggregated_data, "weekly"),
                "summary": self._generate_weekly_summary(aggregated_data)
            }
            
            # Save reports
            report_files = {}
            for format_type, content in reports.items():
                filename = f"weekly_report_{timestamp}.{format_type}"
                filepath = self.reports_dir / filename
                
                if format_type == "json":
                    with open(filepath, 'w') as f:
                        json.dump(content, f, indent=2, default=str)
                else:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                report_files[format_type] = str(filepath)
            
            # Generate weekly visualizations
            chart_files = self._generate_weekly_visualizations(aggregated_data, timestamp)
            report_files.update(chart_files)
            
            return report_files
            
        except Exception as e:
            self.logger.error(f"Error generating weekly report: {e}")
            raise
    
    def _generate_markdown_report(self, analysis_results: Dict[str, Any], report_type: str) -> str:
        """Generate a markdown-formatted report."""
        timestamp = analysis_results.get("analysis_timestamp", datetime.now().isoformat())
        
        report = f"""# AI Trading Bot Performance Report ({report_type.title()})

**Generated:** {timestamp}
**Report Type:** {report_type.title()} Performance Analysis

---

## Executive Summary

"""
        
        # Add executive summary
        insights = analysis_results.get("insights", {})
        key_findings = insights.get("key_findings", [])
        
        if key_findings:
            report += "### Key Findings\n\n"
            for finding in key_findings:
                report += f"- {finding}\n"
            report += "\n"
        
        # Trade Analysis Section
        trade_analysis = analysis_results.get("trade_analysis", {})
        position_analysis = trade_analysis.get("position_analysis", {})
        
        if position_analysis:
            report += "## Position Analysis\n\n"
            report += f"- **Total Open Positions:** {position_analysis.get('total_lots', 0)}\n"
            report += f"- **Total Portfolio Value:** ${position_analysis.get('total_value', 0):,.2f}\n"
            report += f"- **Average Position Size:** ${position_analysis.get('average_lot_value', 0):,.2f}\n\n"
        
        # Strategy Analysis
        strategy_analysis = trade_analysis.get("strategy_effectiveness", {})
        if strategy_analysis:
            report += "## Strategy Performance\n\n"
            
            strategy_dist = strategy_analysis.get("strategy_distribution", {})
            if strategy_dist:
                report += "### Strategy Distribution\n\n"
                for strategy, count in strategy_dist.items():
                    report += f"- **{strategy}:** {count} positions\n"
                report += "\n"
            
            most_used = strategy_analysis.get("most_used_strategy")
            if most_used:
                report += f"**Most Active Strategy:** {most_used}\n\n"
        
        # Holding Period Analysis
        holding_analysis = trade_analysis.get("holding_period_analysis", {})
        if holding_analysis:
            report += "## Holding Period Analysis\n\n"
            report += f"- **Average Holding Period:** {holding_analysis.get('average_holding_period', 0):.1f} hours\n"
            report += f"- **Median Holding Period:** {holding_analysis.get('median_holding_period', 0):.1f} hours\n"
            report += f"- **Longest Position:** {holding_analysis.get('max_holding_period', 0):.1f} hours\n"
            report += f"- **Shortest Position:** {holding_analysis.get('min_holding_period', 0):.1f} hours\n\n"
        
        # Risk Analysis
        risk_analysis = analysis_results.get("risk_analysis", {})
        exposure_analysis = risk_analysis.get("exposure_analysis", {})
        
        if exposure_analysis:
            report += "## Risk Analysis\n\n"
            report += f"- **Total Market Exposure:** ${exposure_analysis.get('total_exposure', 0):,.2f}\n"
            report += f"- **Average Position Exposure:** ${exposure_analysis.get('average_lot_exposure', 0):,.2f}\n"
            report += f"- **Largest Single Position:** ${exposure_analysis.get('largest_lot_exposure', 0):,.2f}\n\n"
        
        # Performance Metrics
        performance_metrics = analysis_results.get("performance_metrics", {})
        efficiency_metrics = performance_metrics.get("efficiency_metrics", {})
        
        if efficiency_metrics:
            report += "## Efficiency Metrics\n\n"
            cash_util = efficiency_metrics.get("cash_utilization", {})
            if cash_util:
                report += f"- **Cash Utilization:** {cash_util.get('utilization_rate', 0):.1%}\n"
        
        # Recommendations
        recommendations = insights.get("recommendations", [])
        if recommendations:
            report += "## Recommendations\n\n"
            for rec in recommendations:
                report += f"- {rec}\n"
            report += "\n"
        
        # Performance Alerts
        alerts = insights.get("performance_alerts", [])
        if alerts:
            report += "## Performance Alerts\n\n"
            for alert in alerts:
                report += f"⚠️ {alert}\n"
            report += "\n"
        
        report += "---\n\n"
        report += f"*Report generated by AI Performance Analyzer at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        return report
    
    def _generate_json_report(self, analysis_results: Dict[str, Any], report_type: str) -> Dict[str, Any]:
        """Generate a JSON-formatted report."""
        return {
            "report_metadata": {
                "type": report_type,
                "generated_at": datetime.now().isoformat(),
                "analysis_timestamp": analysis_results.get("analysis_timestamp"),
                "version": "1.0"
            },
            "analysis_results": analysis_results,
            "report_summary": self._create_report_summary(analysis_results)
        }
    
    def _generate_summary_report(self, analysis_results: Dict[str, Any]) -> str:
        """Generate a concise summary report."""
        summary = "AI TRADING BOT - DAILY PERFORMANCE SUMMARY\n"
        summary += "=" * 50 + "\n\n"
        
        # Key metrics
        trade_analysis = analysis_results.get("trade_analysis", {})
        position_analysis = trade_analysis.get("position_analysis", {})
        
        if position_analysis:
            summary += f"POSITIONS: {position_analysis.get('total_lots', 0)} open\n"
            summary += f"PORTFOLIO VALUE: ${position_analysis.get('total_value', 0):,.2f}\n"
            summary += f"AVG POSITION: ${position_analysis.get('average_lot_value', 0):,.2f}\n\n"
        
        # Strategy summary
        strategy_analysis = trade_analysis.get("strategy_effectiveness", {})
        most_used = strategy_analysis.get("most_used_strategy")
        if most_used:
            summary += f"PRIMARY STRATEGY: {most_used}\n\n"
        
        # Key findings
        insights = analysis_results.get("insights", {})
        key_findings = insights.get("key_findings", [])
        
        if key_findings:
            summary += "KEY FINDINGS:\n"
            for finding in key_findings[:3]:  # Top 3 findings
                summary += f"• {finding}\n"
            summary += "\n"
        
        # Recommendations
        recommendations = insights.get("recommendations", [])
        if recommendations:
            summary += "RECOMMENDATIONS:\n"
            for rec in recommendations[:2]:  # Top 2 recommendations
                summary += f"• {rec}\n"
        
        return summary
    
    def _generate_visualizations(self, analysis_results: Dict[str, Any], timestamp: str) -> Dict[str, str]:
        """Generate visualization charts for the report."""
        chart_files = {}
        
        try:
            # Strategy Distribution Pie Chart
            strategy_chart = self._create_strategy_distribution_chart(analysis_results, timestamp)
            if strategy_chart:
                chart_files["strategy_chart"] = strategy_chart
            
            # Holding Period Distribution
            holding_chart = self._create_holding_period_chart(analysis_results, timestamp)
            if holding_chart:
                chart_files["holding_chart"] = holding_chart
            
            # Risk Exposure Chart
            risk_chart = self._create_risk_exposure_chart(analysis_results, timestamp)
            if risk_chart:
                chart_files["risk_chart"] = risk_chart
            
        except Exception as e:
            self.logger.error(f"Error generating visualizations: {e}")
        
        return chart_files
    
    def _create_strategy_distribution_chart(self, analysis_results: Dict[str, Any], timestamp: str) -> Optional[str]:
        """Create a pie chart showing strategy distribution."""
        try:
            trade_analysis = analysis_results.get("trade_analysis", {})
            strategy_analysis = trade_analysis.get("strategy_effectiveness", {})
            strategy_dist = strategy_analysis.get("strategy_distribution", {})
            
            if not strategy_dist:
                return None
            
            # Create pie chart
            fig, ax = plt.subplots(figsize=(10, 8))
            
            strategies = list(strategy_dist.keys())
            counts = list(strategy_dist.values())
            
            # Shorten strategy names for display
            display_names = [name.replace("DIP_ACCUMULATION_", "DIP_").replace("_Initial", "") for name in strategies]
            
            wedges, texts, autotexts = ax.pie(counts, labels=display_names, autopct='%1.1f%%', startangle=90)
            
            ax.set_title('Trading Strategy Distribution', fontsize=16, fontweight='bold')
            
            # Improve text readability
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            plt.tight_layout()
            
            # Save chart
            chart_path = self.reports_dir / f"strategy_distribution_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"Error creating strategy distribution chart: {e}")
            return None
    
    def _create_holding_period_chart(self, analysis_results: Dict[str, Any], timestamp: str) -> Optional[str]:
        """Create a histogram showing holding period distribution."""
        try:
            # This would require individual lot holding periods
            # For now, create a simple summary chart
            trade_analysis = analysis_results.get("trade_analysis", {})
            holding_analysis = trade_analysis.get("holding_period_analysis", {})
            
            if not holding_analysis:
                return None
            
            # Create summary bar chart
            fig, ax = plt.subplots(figsize=(10, 6))
            
            metrics = ['Average', 'Median', 'Maximum', 'Minimum']
            values = [
                holding_analysis.get('average_holding_period', 0),
                holding_analysis.get('median_holding_period', 0),
                holding_analysis.get('max_holding_period', 0),
                holding_analysis.get('min_holding_period', 0)
            ]
            
            bars = ax.bar(metrics, values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
            
            ax.set_title('Holding Period Analysis (Hours)', fontsize=16, fontweight='bold')
            ax.set_ylabel('Hours')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{value:.1f}h', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # Save chart
            chart_path = self.reports_dir / f"holding_periods_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"Error creating holding period chart: {e}")
            return None
    
    def _create_risk_exposure_chart(self, analysis_results: Dict[str, Any], timestamp: str) -> Optional[str]:
        """Create a chart showing risk exposure metrics."""
        try:
            risk_analysis = analysis_results.get("risk_analysis", {})
            exposure_analysis = risk_analysis.get("exposure_analysis", {})
            
            if not exposure_analysis:
                return None
            
            # Create risk metrics chart
            fig, ax = plt.subplots(figsize=(10, 6))
            
            metrics = ['Total Exposure', 'Average Position', 'Largest Position']
            values = [
                exposure_analysis.get('total_exposure', 0),
                exposure_analysis.get('average_lot_exposure', 0),
                exposure_analysis.get('largest_lot_exposure', 0)
            ]
            
            bars = ax.bar(metrics, values, color=['#ff6b6b', '#4ecdc4', '#45b7d1'])
            
            ax.set_title('Risk Exposure Analysis ($USD)', fontsize=16, fontweight='bold')
            ax.set_ylabel('USD Value')
            
            # Format y-axis as currency
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 10,
                       f'${value:,.0f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # Save chart
            chart_path = self.reports_dir / f"risk_exposure_{timestamp}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"Error creating risk exposure chart: {e}")
            return None
    
    def _create_report_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create a structured summary of the analysis results."""
        summary = {
            "key_metrics": {},
            "performance_highlights": [],
            "risk_indicators": [],
            "recommendations": []
        }
        
        # Extract key metrics
        trade_analysis = analysis_results.get("trade_analysis", {})
        position_analysis = trade_analysis.get("position_analysis", {})
        
        if position_analysis:
            summary["key_metrics"] = {
                "total_positions": position_analysis.get("total_lots", 0),
                "portfolio_value": position_analysis.get("total_value", 0),
                "average_position_size": position_analysis.get("average_lot_value", 0)
            }
        
        # Extract insights
        insights = analysis_results.get("insights", {})
        summary["performance_highlights"] = insights.get("key_findings", [])
        summary["recommendations"] = insights.get("recommendations", [])
        summary["risk_indicators"] = insights.get("performance_alerts", [])
        
        return summary
    
    def _aggregate_weekly_data(self, weekly_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate multiple daily analyses into weekly summary."""
        # This would implement weekly aggregation logic
        # For now, return the most recent analysis
        if weekly_data:
            return weekly_data[-1]
        return {}
    
    def _generate_weekly_summary(self, aggregated_data: Dict[str, Any]) -> str:
        """Generate weekly summary report."""
        return self._generate_summary_report(aggregated_data).replace("DAILY", "WEEKLY")
    
    def _generate_weekly_visualizations(self, aggregated_data: Dict[str, Any], timestamp: str) -> Dict[str, str]:
        """Generate weekly visualization charts."""
        # For now, use the same visualization logic as daily
        return self._generate_visualizations(aggregated_data, f"weekly_{timestamp}")
