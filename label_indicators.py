import pandas as pd
import numpy as np # Required for NaN handling and numerical operations

# --- Configuration for Labeling ---
# This defines how a BUY/SELL/HOLD label is generated based on future price action.
# FUTURE_LOOK_AHEAD_MINUTES: How many minutes into the future to look for price movement.
#   - e.g., if set to 5, we look at the 'close' price 5 minutes after the current row's timestamp.
FUTURE_LOOK_AHEAD_MINUTES = 5 

# PROFIT_THRESHOLD_PCT: Percentage increase to consider a 'BUY' signal.
#   - If future_close is >= current_close * (1 + PROFIT_THRESHOLD_PCT), label as BUY.
#   - Example: 0.001 means a 0.1% increase.
PROFIT_THRESHOLD_PCT = 0.001 

# LOSS_THRESHOLD_PCT: Percentage decrease to consider a 'SELL' signal.
#   - If future_close is <= current_close * (1 + LOSS_THRESHOLD_PCT), label as SELL.
#   - Note: This is a negative value. Example: -0.001 means a 0.1% decrease.
LOSS_THRESHOLD_PCT = -0.001 

# --- Data Loading ---
# Load 'trade_cycle_log.csv'.
# IMPORTANT: For this labeling to work correctly, 'trade_cycle_log.csv' MUST contain
# a 'close' price column (or 'avg_price' that can serve as 'close').
# This file should also contain all the raw market data and technical indicators
# that you intend to use as features for your ML model (e.g., OHLCV, RSI, MACD, etc.).
df = pd.read_csv('trade_cycle_log.csv', engine='python', on_bad_lines='skip')

# --- Data Cleaning and Preprocessing ---
# Convert 'datetime' column to datetime objects and handle invalid entries.
df = df[pd.to_datetime(df["datetime"], errors="coerce").notnull()]
df["datetime"] = pd.to_datetime(df["datetime"], errors="coerce")

# Sort data chronologically. This is CRUCIAL for time-series operations like .shift().
df.sort_values("datetime", inplace=True)

# Ensure 'close' column is numeric and fill any missing values.
# Prioritize 'close' if available, otherwise fallback to 'avg_price'.
# Fill NaNs using forward-fill then backward-fill to ensure no gaps remain.
if 'close' not in df.columns or df['close'].isnull().all():
    print("Warning: 'close' column is missing or all NaN. Attempting to use 'avg_price' as fallback.")
    if 'avg_price' in df.columns:
        df['close'] = pd.to_numeric(df['avg_price'], errors='coerce')
    else:
        print("Error: Neither 'close' nor 'avg_price' found. Cannot perform forward-looking labeling.")
        # If no valid price column, fill with a placeholder (0.0) and warn.
        # This will likely result in a poor label, but prevents script crash.
        df['close'] = 0.0 
        
df['close'] = pd.to_numeric(df['close'], errors='coerce')
df['close'] = df['close'].fillna(method='ffill').fillna(method='bfill') # Fill NaNs for close price


# --- Core Labeling Logic: Forward-Looking Price Action ---
# Calculate the future close price by shifting the 'close' column upwards.
# Rows at the end of the DataFrame will have NaN for 'future_close' because there's no future data.
df['future_close'] = df['close'].shift(-FUTURE_LOOK_AHEAD_MINUTES)

# Calculate the percentage return from the current close to the future close.
df['future_return'] = (df['future_close'] - df['close']) / df['close']

# Initialize 'label' column. Default is HOLD (0).
df['label'] = 0 

# Apply BUY (1) and SELL (-1) labels based on the calculated future return thresholds.
# A BUY label is applied if the price increases sufficiently.
df.loc[df['future_return'] >= PROFIT_THRESHOLD_PCT, 'label'] = 1 
# A SELL label is applied if the price decreases sufficiently.
df.loc[df['future_return'] <= LOSS_THRESHOLD_PCT, 'label'] = -1 

# After labeling, drop rows where 'future_close' (and thus 'label') could not be calculated.
# These are the last 'FUTURE_LOOK_AHEAD_MINUTES' rows of the dataset.
df.dropna(subset=['label'], inplace=True) 
df['label'] = df['label'].astype(int) # Ensure label is integer type for machine learning.

# Drop the temporary columns used for labeling to keep the dataset clean for ML.
df.drop(columns=['future_close', 'future_return'], errors='ignore', inplace=True)


# --- Define Features for ML (Critically, EXCLUDING Leaking Features) ---
# This list specifies which columns from your DataFrame will be used as features
# for the machine learning model.
#
# CRITICAL CHANGE: Removed all features that represent OUTCOMES or INTERNAL BOT STATES,
# as these cause data leakage and lead to unrealistic 100% accuracy.
# Examples of removed leaking features: "decision", "confidence", "quantity_pct",
# "trade_action", "session_pnl", "net_win", "net_loss", "max_drawdown", "loss_streak",
# "win_streak", "open_lots".
#
# This list should only contain features that are DERIVABLE from raw market data
# and valid technical indicators available *at the specific timestamp* of the trade decision.
model_features = [
    "open", "high", "low", "close", "volume", "num_trades",
    "taker_buy_base", "taker_buy_quote", "rsi",
    "sma_7", "sma_10", "sma_14", "sma_50", 
    "ema_7", "ema_14", "ema_50",
    "atr", "volatility", 
    "momentum_10", "macd", "macd_signal", "macd_hist",
    "trend", "trend_5m", "regime",
    "avg_price" # Keep 'avg_price' if it's a distinct feature in your data.
                # If 'close' is always the same as 'avg_price', you might remove one to avoid redundancy.
]

# Filter the `model_features` list to include only those columns that actually exist in the DataFrame.
all_features = [col for col in model_features if col in df.columns]

# --- Final Dataset Preparation ---
# Check if the 'label' column was successfully created and is present.
if 'label' not in df.columns:
    print("Error: 'label' column was not successfully created. Please review the labeling logic.")
    # Exit or handle the error gracefully if the label is missing.
    exit("Labeling failed: 'label' column is missing.")

# Select only the chosen features and the newly created 'label' column for the final dataset.
dataset = df[all_features + ["label"]]

# --- Save Prepared Data ---
# Save the cleaned, labeled, and feature-selected dataset to a new CSV file.
# This file will be used by 'train_trade_model.py' for model training.
dataset.to_csv("prepared_training_data.csv", index=False)
print(f"Auto-labeled ML dataset ready: {len(dataset)} rows saved to prepared_training_data.csv.")

# Display the head of the prepared dataset for quick verification.
print(dataset.head())