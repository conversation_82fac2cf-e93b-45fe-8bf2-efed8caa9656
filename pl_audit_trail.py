import os
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import APIError

# ==============================================================================
# --- P/L AUDIT TRAIL SCRIPT ---
# ==============================================================================
# This is a READ-ONLY diagnostic script. It does not modify any state files.
# Its sole purpose is to create a human-readable "paper trail" of how the
# Realized P/L is calculated, to help diagnose logical errors in the
# calculation itself.
#
# WHAT IT DOES:
# 1. Fetches the complete, raw order history from Alpaca.
# 2. Processes every buy and sell in chronological order.
# 3. For every single sell, it writes a detailed breakdown of the P/L
#    calculation to a text file named 'pl_audit_trail.txt'.
# 4. It sums these individual P/L calculations to show the final total.
# ==============================================================================

# --- CONFIGURATION ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    import config
except ImportError:
    logging.error("CRITICAL: config.py not found. Make sure this script is in the same directory as your bot.")
    exit()

# --- FILE PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
AUDIT_TRAIL_FILE = os.path.join(BASE_DIR, "pl_audit_trail.txt")

def fetch_all_historical_orders(api):
    """
    Fetches all historical orders from Alpaca, handling pagination correctly.
    """
    logging.info("Connecting to Alpaca to fetch complete order history...")
    all_orders = []
    try:
        # Fetch in ascending order to process trades chronologically
        orders = api.list_orders(status='all', limit=500, nested=True, direction='asc')
        all_orders.extend(orders)
        logging.info(f"Fetched initial {len(orders)} orders.")
        
        while len(orders) == 500:
            last_order_time = orders[-1].submitted_at
            formatted_time = last_order_time.isoformat().replace('+00:00', 'Z')
            logging.info(f"Paginating... fetching orders submitted after {formatted_time}")
            orders = api.list_orders(status='all', limit=500, after=formatted_time, nested=True, direction='asc')
            all_orders.extend(orders)
            logging.info(f"Fetched another {len(orders)} orders.")

    except APIError as e:
        logging.error(f"Alpaca API error while fetching orders: {e}")
        return None
        
    logging.info(f"Total historical orders fetched: {len(all_orders)}")
    return all_orders

def generate_audit_trail():
    """
    Performs the audit and generates the P/L audit trail text file.
    """
    api = tradeapi.REST(config.ALPACA_API_KEY_ID, config.ALPACA_SECRET_KEY, base_url=config.ALPACA_BASE_URL)
    
    orders = fetch_all_historical_orders(api)
    if not orders:
        logging.error("Could not fetch order history. Aborting audit.")
        return

    logging.info("Beginning P/L audit trail generation...")

    # Sort orders by fill time to process in the exact order of execution.
    orders.sort(key=lambda o: o.filled_at if o.filled_at else o.submitted_at)

    live_lots = []
    total_calculated_pl = Decimal("0.0")

    with open(AUDIT_TRAIL_FILE, 'w') as f:
        f.write("="*80 + "\n")
        f.write(" Profit & Loss Audit Trail\n")
        f.write("="*80 + "\n\n")

        for order in orders:
            if order.status != 'filled':
                continue

            order_id = str(order.id)
            qty = Decimal(order.filled_qty)
            price = Decimal(order.filled_avg_price)
            fee = (qty * price * config.FEE_PERCENT).quantize(Decimal("0.00000001"))
            
            if order.side == 'buy':
                cost_basis = ((price * qty) + fee) / qty
                new_lot = {
                    "lot_id": order_id,
                    "buy_timestamp": order.filled_at,
                    "original_qty": qty,
                    "remaining_qty": qty,
                    "buy_price": price,
                    "cost_basis_per_unit": cost_basis,
                }
                live_lots.append(new_lot)
                f.write(f"--- BUY Order {order_id} on {order.filled_at.isoformat()} ---\n")
                f.write(f"  - Added Lot: {qty} BTC @ ${price:,.2f} (Cost Basis: ${cost_basis:,.2f}/BTC)\n\n")

            elif order.side == 'sell':
                f.write(f"--- SELL Order {order_id} on {order.filled_at.isoformat()} ---\n")
                f.write(f"  - Order Details: Sell {qty} BTC @ ${price:,.2f}\n")
                
                sell_qty_to_match = qty
                total_cost_of_sold_btc = Decimal("0.0")
                
                # Match sell against oldest lots first (FIFO)
                for lot in live_lots:
                    if sell_qty_to_match <= 0: break
                    
                    if lot["remaining_qty"] > 0:
                        qty_from_this_lot = min(lot["remaining_qty"], sell_qty_to_match)
                        
                        cost_for_this_portion = qty_from_this_lot * lot["cost_basis_per_unit"]
                        total_cost_of_sold_btc += cost_for_this_portion
                        
                        f.write(f"  - Matched {qty_from_this_lot} BTC against BUY Lot {lot['lot_id']}\n")
                        f.write(f"    - Cost of this portion: {qty_from_this_lot} * ${lot['cost_basis_per_unit']:,.2f} = ${cost_for_this_portion:,.2f}\n")
                        
                        lot["remaining_qty"] -= qty_from_this_lot
                        sell_qty_to_match -= qty_from_this_lot

                if sell_qty_to_match > Decimal('1e-9'): # Using a small threshold for floating point comparison
                    f.write(f"  - WARNING: Unmatched sell quantity of {sell_qty_to_match} BTC. "
                            f"This can happen if the bot sold a position that was present before tracking began. "
                            f"This trade will be EXCLUDED from the final P/L calculation.\n\n")
                else:
                    proceeds = price * qty
                    realized_pl = proceeds - total_cost_of_sold_btc - fee
                    total_calculated_pl += realized_pl
                    
                    f.write(f"  - Summary for this SELL:\n")
                    f.write(f"    - Total Proceeds: ${proceeds:,.2f}\n")
                    f.write(f"    - Total Cost Basis: ${total_cost_of_sold_btc:,.2f}\n")
                    f.write(f"    - Fee: ${fee:,.2f}\n")
                    f.write(f"  - P/L For This Trade: ${proceeds:,.2f} - ${total_cost_of_sold_btc:,.2f} - ${fee:,.2f} = ${realized_pl:,.2f}\n\n")
        
        f.write("="*80 + "\n")
        f.write(f"FINAL AUDITED REALIZED P/L: ${total_calculated_pl:,.2f}\n")
        f.write("="*80 + "\n")

    logging.info(f"Audit trail successfully written to: {AUDIT_TRAIL_FILE}")
    print(f"\n✅ Audit Complete. A detailed report has been saved to:\n{AUDIT_TRAIL_FILE}")
    print("\nPlease review this file to see the line-by-line calculation of your P/L.")

if __name__ == "__main__":
    input("CRITICAL: This is a READ-ONLY diagnostic script. It will not change any of your core data files. "
          "Press Enter to continue...")
    generate_audit_trail()
