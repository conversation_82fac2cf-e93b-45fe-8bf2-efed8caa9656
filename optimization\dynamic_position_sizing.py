#!/usr/bin/env python3
"""
Dynamic Position Sizing System - Phase 5 Implementation

This system implements intelligent position sizing using:
1. Kelly Criterion for optimal bet sizing
2. Regime-aware risk adjustments
3. Volatility-based position scaling
4. Portfolio heat management
5. Drawdown protection mechanisms

The system dynamically adjusts position sizes based on:
- Historical win rate and profit/loss ratios
- Current market regime (bull, bear, sideways, etc.)
- Market volatility levels
- Account balance and risk tolerance
- Recent performance metrics

Author: Bitcoin AI Trading Bot - Phase 5 Dynamic Position Sizing
Date: July 30, 2025
"""

import logging
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque
import numpy as np
import pandas as pd
from statistics import mean, stdev

from error_handler import ErrorSeverity, ErrorCategory, handle_error
from error_utils import SystemError, DataError, safe_execute

logger = logging.getLogger("TradingBotApp.DynamicPositionSizing")

@dataclass
class TradeResult:
    """Individual trade result for Kelly Criterion calculation."""
    entry_price: float
    exit_price: float
    position_size: float
    profit_loss: float
    profit_loss_pct: float
    trade_duration_hours: float
    regime: str
    timestamp: datetime
    
    @property
    def is_winner(self) -> bool:
        """Check if trade was profitable."""
        return self.profit_loss > 0

@dataclass
class PositionSizingMetrics:
    """Metrics for position sizing calculations."""
    win_rate: float
    avg_win_pct: float
    avg_loss_pct: float
    profit_factor: float
    kelly_fraction: float
    volatility_adjustment: float
    regime_adjustment: float
    final_position_size: float
    confidence_score: float
    risk_level: str

@dataclass
class RiskParameters:
    """Risk management parameters."""
    max_position_size: float = 0.25  # Maximum 25% of portfolio
    min_position_size: float = 0.01  # Minimum 1% of portfolio
    max_kelly_fraction: float = 0.25  # Cap Kelly at 25%
    volatility_lookback_days: int = 30
    performance_lookback_trades: int = 50
    drawdown_protection_threshold: float = 0.10  # 10% drawdown triggers protection
    regime_risk_multipliers: Dict[str, float] = None
    
    def __post_init__(self):
        """Initialize regime risk multipliers."""
        if self.regime_risk_multipliers is None:
            self.regime_risk_multipliers = {
                'bull_market': 1.2,      # Increase size in bull markets
                'bear_market': 0.6,      # Reduce size in bear markets
                'sideways_market': 0.8,  # Moderate reduction in sideways
                'high_volatility': 0.7,  # Reduce size in high volatility
                'low_volatility': 1.1,   # Slight increase in low volatility
                'breakout_bull': 1.3,    # Aggressive in bull breakouts
                'breakout_bear': 0.5,    # Very conservative in bear breakouts
                'reversal_bull': 1.0,    # Neutral in bull reversals
                'reversal_bear': 0.8,    # Slightly conservative in bear reversals
                'unknown': 0.9           # Conservative when regime unclear
            }

class DynamicPositionSizing:
    """
    Dynamic position sizing system using Kelly Criterion and regime awareness.
    
    This system calculates optimal position sizes by:
    1. Analyzing historical trade performance
    2. Calculating Kelly Criterion optimal fraction
    3. Adjusting for current market regime
    4. Applying volatility-based scaling
    5. Implementing risk management overlays
    """
    
    def __init__(self, risk_params: Optional[RiskParameters] = None):
        """Initialize the dynamic position sizing system."""
        
        self.risk_params = risk_params or RiskParameters()
        
        # Trade history storage
        self.trade_history: deque = deque(maxlen=1000)
        self.regime_performance: Dict[str, List[TradeResult]] = {}
        
        # Current state
        self.current_metrics: Optional[PositionSizingMetrics] = None
        self.current_regime: str = 'unknown'
        self.current_volatility: float = 0.0
        self.account_balance: float = 10000.0  # Default starting balance
        self.current_drawdown: float = 0.0
        
        # Threading and synchronization
        self.data_lock = threading.RLock()
        self.last_update: Optional[datetime] = None
        
        # Performance tracking
        self.stats = {
            'total_calculations': 0,
            'kelly_calculations': 0,
            'regime_adjustments': 0,
            'volatility_adjustments': 0,
            'risk_reductions': 0
        }
        
        logger.info("Dynamic Position Sizing System initialized")
        logger.info(f"Risk parameters: max_size={self.risk_params.max_position_size:.1%}, "
                   f"kelly_cap={self.risk_params.max_kelly_fraction:.1%}")
    
    def add_trade_result(self, trade_result: TradeResult):
        """Add a completed trade result to the history."""
        
        with self.data_lock:
            self.trade_history.append(trade_result)
            
            # Add to regime-specific performance
            regime = trade_result.regime
            if regime not in self.regime_performance:
                self.regime_performance[regime] = []
            self.regime_performance[regime].append(trade_result)
            
            # Keep regime history manageable
            if len(self.regime_performance[regime]) > 200:
                self.regime_performance[regime] = self.regime_performance[regime][-100:]
            
            logger.debug(f"Added trade result: {trade_result.profit_loss_pct:.2%} profit in {regime} regime")
    
    def update_market_conditions(self, regime: str, volatility: float, account_balance: float):
        """Update current market conditions for position sizing."""
        
        with self.data_lock:
            self.current_regime = regime
            self.current_volatility = volatility
            self.account_balance = account_balance
            self.last_update = datetime.now()
            
            # Calculate current drawdown
            if self.trade_history:
                recent_trades_for_peak = list(self.trade_history)[-min(20, len(self.trade_history)):]
                recent_trades_for_balance = list(self.trade_history)[-min(5, len(self.trade_history)):]
                peak_balance = max(trade.profit_loss for trade in recent_trades_for_peak)
                current_balance = sum(trade.profit_loss for trade in recent_trades_for_balance)
                if peak_balance > 0:
                    self.current_drawdown = max(0, (peak_balance - current_balance) / peak_balance)
            
            logger.debug(f"Updated conditions: regime={regime}, volatility={volatility:.3f}, "
                        f"balance=${account_balance:.2f}, drawdown={self.current_drawdown:.2%}")
    
    def calculate_optimal_position_size(self, 
                                      expected_return: float = None,
                                      confidence_level: float = 0.8) -> PositionSizingMetrics:
        """
        Calculate optimal position size using Kelly Criterion and risk adjustments.
        
        Args:
            expected_return: Expected return for the trade (optional)
            confidence_level: Confidence in the trade setup (0.0 to 1.0)
            
        Returns:
            PositionSizingMetrics with calculated position size and metrics
        """
        
        with self.data_lock:
            self.stats['total_calculations'] += 1
            
            try:
                # Calculate base Kelly fraction
                kelly_fraction = self._calculate_kelly_fraction()
                self.stats['kelly_calculations'] += 1
                
                # Apply regime adjustment
                regime_adjustment = self._calculate_regime_adjustment()
                self.stats['regime_adjustments'] += 1
                
                # Apply volatility adjustment
                volatility_adjustment = self._calculate_volatility_adjustment()
                self.stats['volatility_adjustments'] += 1
                
                # Calculate base position size
                base_position_size = kelly_fraction * regime_adjustment * volatility_adjustment
                
                # Apply confidence scaling
                confidence_adjusted_size = base_position_size * confidence_level
                
                # Apply risk management overlays
                final_position_size = self._apply_risk_management(confidence_adjusted_size)
                
                # Calculate performance metrics
                win_rate, avg_win_pct, avg_loss_pct, profit_factor = self._calculate_performance_metrics()
                
                # Determine risk level
                risk_level = self._determine_risk_level(final_position_size)
                
                # Create metrics object
                metrics = PositionSizingMetrics(
                    win_rate=win_rate,
                    avg_win_pct=avg_win_pct,
                    avg_loss_pct=avg_loss_pct,
                    profit_factor=profit_factor,
                    kelly_fraction=kelly_fraction,
                    volatility_adjustment=volatility_adjustment,
                    regime_adjustment=regime_adjustment,
                    final_position_size=final_position_size,
                    confidence_score=confidence_level,
                    risk_level=risk_level
                )
                
                self.current_metrics = metrics
                
                logger.info(f"Position size calculated: {final_position_size:.1%} "
                           f"(Kelly: {kelly_fraction:.1%}, Regime: {regime_adjustment:.2f}x, "
                           f"Vol: {volatility_adjustment:.2f}x, Risk: {risk_level})")
                
                return metrics
                
            except Exception as e:
                logger.error(f"Error calculating position size: {e}")
                
                # Return conservative fallback
                return PositionSizingMetrics(
                    win_rate=0.5,
                    avg_win_pct=1.0,
                    avg_loss_pct=1.0,
                    profit_factor=1.0,
                    kelly_fraction=0.05,
                    volatility_adjustment=0.8,
                    regime_adjustment=0.8,
                    final_position_size=self.risk_params.min_position_size,
                    confidence_score=confidence_level,
                    risk_level='CONSERVATIVE'
                )

    def _calculate_kelly_fraction(self) -> float:
        """Calculate Kelly Criterion optimal fraction."""

        if len(self.trade_history) < 10:
            logger.warning("Insufficient trade history for Kelly calculation, using conservative default")
            return 0.05  # Conservative default

        # Get recent trades for calculation
        lookback_count = min(self.risk_params.performance_lookback_trades, len(self.trade_history))
        recent_trades = list(self.trade_history)[-lookback_count:] if lookback_count > 0 else []

        if not recent_trades:
            return 0.05

        # Calculate win rate
        winners = [t for t in recent_trades if t.is_winner]
        win_rate = len(winners) / len(recent_trades)

        if win_rate == 0 or win_rate == 1:
            logger.warning(f"Extreme win rate ({win_rate:.1%}), using conservative Kelly")
            return 0.05

        # Calculate average win and loss percentages
        if winners:
            avg_win_pct = mean([t.profit_loss_pct for t in winners])
        else:
            avg_win_pct = 0.01  # 1% default

        losers = [t for t in recent_trades if not t.is_winner]
        if losers:
            avg_loss_pct = abs(mean([t.profit_loss_pct for t in losers]))
        else:
            avg_loss_pct = 0.01  # 1% default

        # Kelly Criterion: f = (bp - q) / b
        # where: b = odds received (avg_win_pct), p = win_rate, q = loss_rate
        if avg_loss_pct > 0:
            kelly_fraction = (avg_win_pct * win_rate - (1 - win_rate)) / avg_win_pct
        else:
            kelly_fraction = 0.05

        # Apply safety caps
        kelly_fraction = max(0.01, min(kelly_fraction, self.risk_params.max_kelly_fraction))

        logger.debug(f"Kelly calculation: win_rate={win_rate:.1%}, avg_win={avg_win_pct:.1%}, "
                    f"avg_loss={avg_loss_pct:.1%}, kelly={kelly_fraction:.1%}")

        return kelly_fraction

    def _calculate_regime_adjustment(self) -> float:
        """Calculate regime-based position size adjustment."""

        regime_multiplier = self.risk_params.regime_risk_multipliers.get(
            self.current_regime, 0.9  # Conservative default
        )

        # Additional adjustment based on regime-specific performance
        if self.current_regime in self.regime_performance:
            regime_trades = self.regime_performance[self.current_regime]

            if len(regime_trades) >= 5:
                # Calculate regime-specific win rate
                regime_winners = [t for t in regime_trades if t.is_winner]
                regime_win_rate = len(regime_winners) / len(regime_trades)

                # Adjust multiplier based on regime performance
                if regime_win_rate > 0.6:
                    regime_multiplier *= 1.1  # Boost for good performance
                elif regime_win_rate < 0.4:
                    regime_multiplier *= 0.9  # Reduce for poor performance

        # Cap the adjustment
        regime_multiplier = max(0.3, min(regime_multiplier, 1.5))

        logger.debug(f"Regime adjustment: {self.current_regime} -> {regime_multiplier:.2f}x")

        return regime_multiplier

    def _calculate_volatility_adjustment(self) -> float:
        """Calculate volatility-based position size adjustment."""

        # Base volatility adjustment
        if self.current_volatility > 0.05:  # High volatility (>5%)
            vol_adjustment = 0.7
        elif self.current_volatility > 0.03:  # Medium volatility (3-5%)
            vol_adjustment = 0.85
        elif self.current_volatility > 0.01:  # Low volatility (1-3%)
            vol_adjustment = 1.0
        else:  # Very low volatility (<1%)
            vol_adjustment = 1.1

        # Additional adjustment based on recent volatility trend
        if len(self.trade_history) >= 10:
            recent_volatilities = []
            recent_trades_for_vol = list(self.trade_history)[-min(10, len(self.trade_history)):]
            for trade in recent_trades_for_vol:
                if hasattr(trade, 'volatility'):
                    recent_volatilities.append(trade.volatility)

            if recent_volatilities and len(recent_volatilities) >= 5:
                vol_trend = np.polyfit(range(len(recent_volatilities)), recent_volatilities, 1)[0]
                if vol_trend > 0.001:  # Increasing volatility
                    vol_adjustment *= 0.95
                elif vol_trend < -0.001:  # Decreasing volatility
                    vol_adjustment *= 1.05

        # Cap the adjustment
        vol_adjustment = max(0.5, min(vol_adjustment, 1.3))

        logger.debug(f"Volatility adjustment: {self.current_volatility:.3f} -> {vol_adjustment:.2f}x")

        return vol_adjustment

    def _apply_risk_management(self, base_position_size: float) -> float:
        """Apply risk management overlays to position size."""

        position_size = base_position_size

        # Apply absolute limits
        position_size = max(self.risk_params.min_position_size,
                           min(position_size, self.risk_params.max_position_size))

        # Drawdown protection
        if self.current_drawdown > self.risk_params.drawdown_protection_threshold:
            drawdown_reduction = 1 - (self.current_drawdown * 2)  # Reduce size based on drawdown
            drawdown_reduction = max(0.3, drawdown_reduction)  # Don't reduce below 30%
            position_size *= drawdown_reduction
            self.stats['risk_reductions'] += 1

            logger.warning(f"Drawdown protection applied: {self.current_drawdown:.1%} drawdown, "
                          f"size reduced by {(1-drawdown_reduction):.1%}")

        # Recent performance protection
        if len(self.trade_history) >= 5:
            recent_trades = list(self.trade_history)[-min(5, len(self.trade_history)):]
            recent_losses = [t for t in recent_trades if not t.is_winner]

            if len(recent_losses) >= 4:  # 4+ losses in last 5 trades
                position_size *= 0.7  # Reduce size by 30%
                self.stats['risk_reductions'] += 1
                logger.warning("Recent performance protection: reducing size due to recent losses")

        # Ensure final limits
        position_size = max(self.risk_params.min_position_size,
                           min(position_size, self.risk_params.max_position_size))

        return position_size

    def _calculate_performance_metrics(self) -> Tuple[float, float, float, float]:
        """Calculate current performance metrics."""

        if len(self.trade_history) < 5:
            return 0.5, 1.0, 1.0, 1.0  # Default values

        lookback_count = min(self.risk_params.performance_lookback_trades, len(self.trade_history))
        recent_trades = list(self.trade_history)[-lookback_count:] if lookback_count > 0 else []

        # Win rate
        winners = [t for t in recent_trades if t.is_winner]
        win_rate = len(winners) / len(recent_trades)

        # Average win/loss percentages
        if winners:
            avg_win_pct = mean([t.profit_loss_pct for t in winners]) * 100
        else:
            avg_win_pct = 1.0

        losers = [t for t in recent_trades if not t.is_winner]
        if losers:
            avg_loss_pct = abs(mean([t.profit_loss_pct for t in losers])) * 100
        else:
            avg_loss_pct = 1.0

        # Profit factor
        total_wins = sum([t.profit_loss for t in winners]) if winners else 0
        total_losses = abs(sum([t.profit_loss for t in losers])) if losers else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else 1.0

        return win_rate, avg_win_pct, avg_loss_pct, profit_factor

    def _determine_risk_level(self, position_size: float) -> str:
        """Determine risk level based on position size."""

        if position_size >= 0.20:
            return 'AGGRESSIVE'
        elif position_size >= 0.10:
            return 'MODERATE'
        elif position_size >= 0.05:
            return 'CONSERVATIVE'
        else:
            return 'VERY_CONSERVATIVE'

    def get_position_size_usd(self, confidence_level: float = 0.8) -> float:
        """Get position size in USD based on current account balance."""

        metrics = self.calculate_optimal_position_size(confidence_level=confidence_level)
        return self.account_balance * metrics.final_position_size

    def get_current_metrics(self) -> Optional[PositionSizingMetrics]:
        """Get the most recent position sizing metrics."""

        with self.data_lock:
            return self.current_metrics

    def get_regime_performance(self, regime: str = None) -> Dict[str, Any]:
        """Get performance statistics for a specific regime or all regimes."""

        with self.data_lock:
            if regime:
                if regime in self.regime_performance:
                    trades = self.regime_performance[regime]
                    winners = [t for t in trades if t.is_winner]

                    return {
                        'regime': regime,
                        'total_trades': len(trades),
                        'win_rate': len(winners) / len(trades) if trades else 0,
                        'avg_profit_pct': mean([t.profit_loss_pct for t in winners]) if winners else 0,
                        'avg_loss_pct': abs(mean([t.profit_loss_pct for t in trades if not t.is_winner])) if trades else 0,
                        'total_profit': sum([t.profit_loss for t in trades])
                    }
                else:
                    return {'regime': regime, 'total_trades': 0}
            else:
                # Return all regime performance
                result = {}
                for regime_name in self.regime_performance:
                    result[regime_name] = self.get_regime_performance(regime_name)
                return result

    def get_system_stats(self) -> Dict[str, Any]:
        """Get system performance statistics."""

        with self.data_lock:
            stats = self.stats.copy()
            stats.update({
                'total_trades': len(self.trade_history),
                'regimes_tracked': len(self.regime_performance),
                'current_regime': self.current_regime,
                'current_volatility': self.current_volatility,
                'current_drawdown': self.current_drawdown,
                'account_balance': self.account_balance,
                'last_update': self.last_update.isoformat() if self.last_update else None
            })

            if self.current_metrics:
                stats.update({
                    'current_position_size_pct': self.current_metrics.final_position_size * 100,
                    'current_kelly_fraction_pct': self.current_metrics.kelly_fraction * 100,
                    'current_risk_level': self.current_metrics.risk_level
                })

            return stats

    def simulate_position_sizes(self, scenarios: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Simulate position sizes for different scenarios."""

        results = []

        for scenario in scenarios:
            # Temporarily update conditions
            original_regime = self.current_regime
            original_volatility = self.current_volatility
            original_balance = self.account_balance

            try:
                self.update_market_conditions(
                    regime=scenario.get('regime', self.current_regime),
                    volatility=scenario.get('volatility', self.current_volatility),
                    account_balance=scenario.get('balance', self.account_balance)
                )

                metrics = self.calculate_optimal_position_size(
                    confidence_level=scenario.get('confidence', 0.8)
                )

                results.append({
                    'scenario': scenario,
                    'position_size_pct': metrics.final_position_size * 100,
                    'position_size_usd': self.account_balance * metrics.final_position_size,
                    'kelly_fraction': metrics.kelly_fraction,
                    'risk_level': metrics.risk_level,
                    'metrics': asdict(metrics)
                })

            finally:
                # Restore original conditions
                self.update_market_conditions(original_regime, original_volatility, original_balance)

        return results

    def export_trade_history(self) -> List[Dict[str, Any]]:
        """Export trade history for analysis."""

        with self.data_lock:
            return [asdict(trade) for trade in self.trade_history]

    def import_trade_history(self, trades_data: List[Dict[str, Any]]):
        """Import trade history from external source."""

        with self.data_lock:
            for trade_data in trades_data:
                # Convert timestamp if it's a string
                if isinstance(trade_data['timestamp'], str):
                    trade_data['timestamp'] = datetime.fromisoformat(trade_data['timestamp'])

                trade = TradeResult(**trade_data)
                self.add_trade_result(trade)

        logger.info(f"Imported {len(trades_data)} trade results")


# Global instance for the bot to use
_dynamic_position_sizing = None

def get_position_sizing_system() -> DynamicPositionSizing:
    """Get the global dynamic position sizing instance."""

    global _dynamic_position_sizing

    if _dynamic_position_sizing is None:
        _dynamic_position_sizing = DynamicPositionSizing()

    return _dynamic_position_sizing

def calculate_position_size(regime: str,
                          volatility: float,
                          account_balance: float,
                          confidence_level: float = 0.8) -> float:
    """
    Calculate optimal position size for a trade.

    Args:
        regime: Current market regime
        volatility: Current market volatility
        account_balance: Current account balance
        confidence_level: Confidence in the trade setup (0.0 to 1.0)

    Returns:
        Position size as fraction of account balance (0.0 to 1.0)
    """

    sizing_system = get_position_sizing_system()
    sizing_system.update_market_conditions(regime, volatility, account_balance)

    metrics = sizing_system.calculate_optimal_position_size(confidence_level=confidence_level)
    return metrics.final_position_size

def calculate_position_size_usd(regime: str,
                               volatility: float,
                               account_balance: float,
                               confidence_level: float = 0.8) -> float:
    """
    Calculate optimal position size in USD.

    Returns:
        Position size in USD
    """

    position_fraction = calculate_position_size(regime, volatility, account_balance, confidence_level)
    return account_balance * position_fraction

def add_trade_result(entry_price: float,
                    exit_price: float,
                    position_size: float,
                    regime: str) -> None:
    """
    Add a completed trade result to the position sizing system.

    Args:
        entry_price: Trade entry price
        exit_price: Trade exit price
        position_size: Position size used
        regime: Market regime during the trade
    """

    # Calculate trade metrics
    profit_loss = (exit_price - entry_price) * position_size
    profit_loss_pct = (exit_price - entry_price) / entry_price

    trade_result = TradeResult(
        entry_price=entry_price,
        exit_price=exit_price,
        position_size=position_size,
        profit_loss=profit_loss,
        profit_loss_pct=profit_loss_pct,
        trade_duration_hours=1.0,  # Default duration
        regime=regime,
        timestamp=datetime.now()
    )

    sizing_system = get_position_sizing_system()
    sizing_system.add_trade_result(trade_result)

def get_position_sizing_metrics() -> Optional[PositionSizingMetrics]:
    """Get current position sizing metrics."""

    sizing_system = get_position_sizing_system()
    return sizing_system.get_current_metrics()

def get_sizing_system_stats() -> Dict[str, Any]:
    """Get position sizing system statistics."""

    sizing_system = get_position_sizing_system()
    return sizing_system.get_system_stats()
