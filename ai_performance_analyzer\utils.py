"""
Utility Functions for AI Performance Analyzer
=============================================

Common utility functions used across the AI Performance Analyzer system.
"""

import logging
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from decimal import Decimal
import pandas as pd

def setup_logger(name: str, config: Dict[str, Any]) -> logging.Logger:
    """Set up a logger with the specified configuration."""
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, config.get("level", "INFO")))
    
    formatter = logging.Formatter(config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
    
    # Console handler
    if config.get("console_handler", True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if config.get("file_handler", True):
        from .config import LOGS_DIR
        log_file = LOGS_DIR / f"{name}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def safe_decimal(value: Union[str, int, float, Decimal], default: Decimal = Decimal("0")) -> Decimal:
    """Safely convert a value to Decimal."""
    try:
        if isinstance(value, Decimal):
            return value
        return Decimal(str(value))
    except (ValueError, TypeError, InvalidOperation):
        return default

def safe_float(value: Union[str, int, float, Decimal], default: float = 0.0) -> float:
    """Safely convert a value to float."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value: Union[str, int, float], default: int = 0) -> int:
    """Safely convert a value to int."""
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return default

def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
    """Parse various timestamp formats to datetime object."""
    if not timestamp_str:
        return None
        
    # Common timestamp formats
    formats = [
        "%Y-%m-%dT%H:%M:%S.%f%z",
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(timestamp_str.replace("Z", "+00:00"), fmt)
        except ValueError:
            continue
    
    return None

def calculate_percentage_change(old_value: Union[float, Decimal], new_value: Union[float, Decimal]) -> float:
    """Calculate percentage change between two values."""
    try:
        old_val = float(old_value)
        new_val = float(new_value)
        
        if old_val == 0:
            return 0.0 if new_val == 0 else float('inf')
        
        return ((new_val - old_val) / old_val) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0.0

def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.0) -> float:
    """Calculate Sharpe ratio from a list of returns."""
    try:
        if not returns or len(returns) < 2:
            return 0.0
        
        df = pd.Series(returns)
        excess_returns = df - risk_free_rate
        
        if excess_returns.std() == 0:
            return 0.0
        
        return excess_returns.mean() / excess_returns.std()
    except Exception:
        return 0.0

def calculate_max_drawdown(equity_curve: List[float]) -> float:
    """Calculate maximum drawdown from equity curve."""
    try:
        if not equity_curve or len(equity_curve) < 2:
            return 0.0
        
        df = pd.Series(equity_curve)
        rolling_max = df.expanding().max()
        drawdown = (df - rolling_max) / rolling_max
        
        return abs(drawdown.min())
    except Exception:
        return 0.0

def calculate_win_rate(trades: List[Dict[str, Any]]) -> float:
    """Calculate win rate from a list of trades."""
    try:
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if safe_float(trade.get("pnl", 0)) > 0)
        return winning_trades / len(trades)
    except Exception:
        return 0.0

def calculate_profit_factor(trades: List[Dict[str, Any]]) -> float:
    """Calculate profit factor from a list of trades."""
    try:
        if not trades:
            return 0.0
        
        gross_profit = sum(safe_float(trade.get("pnl", 0)) for trade in trades if safe_float(trade.get("pnl", 0)) > 0)
        gross_loss = abs(sum(safe_float(trade.get("pnl", 0)) for trade in trades if safe_float(trade.get("pnl", 0)) < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    except Exception:
        return 0.0

def load_json_file(filepath: Path, default: Any = None) -> Any:
    """Safely load a JSON file."""
    try:
        if filepath.exists():
            with open(filepath, 'r') as f:
                return json.load(f)
    except Exception:
        pass
    return default

def save_json_file(data: Any, filepath: Path) -> bool:
    """Safely save data to a JSON file."""
    try:
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        return True
    except Exception:
        return False

def create_database_connection(db_path: Path) -> sqlite3.Connection:
    """Create a SQLite database connection."""
    db_path.parent.mkdir(parents=True, exist_ok=True)
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # Enable column access by name
    return conn

def execute_sql_query(conn: sqlite3.Connection, query: str, params: tuple = ()) -> List[sqlite3.Row]:
    """Execute a SQL query and return results."""
    try:
        cursor = conn.cursor()
        cursor.execute(query, params)
        return cursor.fetchall()
    except Exception as e:
        logging.error(f"SQL query error: {e}")
        return []

def get_date_range(days_back: int) -> tuple[datetime, datetime]:
    """Get date range for analysis."""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    return start_date, end_date

def format_currency(amount: Union[float, Decimal], currency: str = "USD") -> str:
    """Format amount as currency string."""
    try:
        return f"${float(amount):,.2f}"
    except (ValueError, TypeError):
        return "$0.00"

def format_percentage(value: Union[float, Decimal], decimal_places: int = 2) -> str:
    """Format value as percentage string."""
    try:
        return f"{float(value):.{decimal_places}f}%"
    except (ValueError, TypeError):
        return "0.00%"

def truncate_string(text: str, max_length: int = 100) -> str:
    """Truncate string to maximum length."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def validate_data_structure(data: Dict[str, Any], required_fields: List[str]) -> bool:
    """Validate that data contains required fields."""
    try:
        for field in required_fields:
            if field not in data:
                return False
        return True
    except Exception:
        return False

def clean_filename(filename: str) -> str:
    """Clean filename for safe file system usage."""
    import re
    # Remove or replace invalid characters
    cleaned = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple underscores
    cleaned = re.sub(r'_+', '_', cleaned)
    # Remove leading/trailing underscores
    cleaned = cleaned.strip('_')
    return cleaned

def get_file_age_days(filepath: Path) -> float:
    """Get age of file in days."""
    try:
        if filepath.exists():
            file_time = datetime.fromtimestamp(filepath.stat().st_mtime)
            age = datetime.now() - file_time
            return age.total_seconds() / (24 * 3600)  # Convert to days
    except Exception:
        pass
    return 0.0

def compress_old_files(directory: Path, days_old: int = 30, extensions: List[str] = None):
    """Compress old files in directory."""
    import gzip
    import shutil
    
    if extensions is None:
        extensions = ['.json', '.log', '.csv']
    
    try:
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.suffix in extensions:
                if get_file_age_days(file_path) > days_old:
                    # Compress file
                    compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
                    with open(file_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # Remove original file
                    file_path.unlink()
    except Exception as e:
        logging.error(f"Error compressing old files: {e}")

def memory_usage_mb() -> float:
    """Get current memory usage in MB."""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        return 0.0
