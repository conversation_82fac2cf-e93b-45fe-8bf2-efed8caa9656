#!/usr/bin/env python3
"""
Advanced ML Feature Engineering System - Phase 5 Implementation

This system enhances the existing XGBoost ML model with sophisticated features:
1. Order Book Imbalance Analysis - Real-time bid/ask pressure analysis
2. Social Sentiment Analysis - Twitter/Reddit sentiment integration
3. On-Chain Metrics - Bitcoin network activity analysis
4. Advanced Technical Features - Higher-order derivatives and patterns
5. Market Microstructure - Tick-level analysis and flow detection
6. Cross-Asset Correlations - Multi-asset relationship analysis

The system integrates seamlessly with the existing ML infrastructure while
providing institutional-grade feature engineering capabilities.

Author: Bitcoin AI Trading Bot - Phase 5 Advanced ML Features
Date: July 30, 2025
"""

import asyncio
import aiohttp
import logging
import numpy as np
import pandas as pd
import requests
import time
import threading
from collections import deque, defaultdict
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import hashlib
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

# ============================================================================
# DATA STRUCTURES
# ============================================================================

@dataclass
class OrderBookData:
    """Order book snapshot data structure"""
    timestamp: datetime
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]  # [(price, quantity), ...]
    bid_volume: float
    ask_volume: float
    spread: float
    mid_price: float

@dataclass
class SentimentData:
    """Social sentiment data structure"""
    timestamp: datetime
    twitter_sentiment: float  # -1 to 1
    reddit_sentiment: float   # -1 to 1
    news_sentiment: float     # -1 to 1
    combined_sentiment: float # -1 to 1
    sentiment_volume: int     # Number of mentions
    sentiment_momentum: float # Rate of change

@dataclass
class OnChainData:
    """On-chain metrics data structure"""
    timestamp: datetime
    hash_rate: float
    difficulty: float
    mempool_size: int
    avg_fee: float
    active_addresses: int
    transaction_volume: float
    exchange_inflows: float
    exchange_outflows: float
    whale_activity: float

@dataclass
class AdvancedTechnicalFeatures:
    """Advanced technical analysis features"""
    timestamp: datetime
    price_acceleration: float
    volume_acceleration: float
    volatility_regime: str
    trend_strength: float
    support_resistance_strength: float
    pattern_recognition: Dict[str, float]
    fractal_dimension: float
    hurst_exponent: float

@dataclass
class MarketMicrostructure:
    """Market microstructure analysis"""
    timestamp: datetime
    tick_direction: int  # 1, 0, -1
    trade_intensity: float
    order_flow_imbalance: float
    price_impact: float
    liquidity_score: float
    market_depth: float
    bid_ask_spread_normalized: float

@dataclass
class CrossAssetCorrelations:
    """Cross-asset correlation analysis"""
    timestamp: datetime
    btc_spy_correlation: float
    btc_gold_correlation: float
    btc_dxy_correlation: float
    btc_vix_correlation: float
    crypto_market_correlation: float
    risk_on_off_indicator: float

@dataclass
class MLFeatureSet:
    """Complete ML feature set for enhanced predictions"""
    timestamp: datetime
    
    # Order book features
    order_book_imbalance: float
    bid_ask_ratio: float
    order_book_depth: float
    spread_normalized: float
    
    # Sentiment features
    sentiment_score: float
    sentiment_momentum: float
    sentiment_divergence: float
    
    # On-chain features
    network_activity: float
    whale_activity: float
    exchange_flow_ratio: float
    
    # Advanced technical features
    price_momentum_2nd_derivative: float
    volatility_clustering: float
    trend_persistence: float
    pattern_strength: float
    
    # Microstructure features
    order_flow_pressure: float
    liquidity_stress: float
    market_efficiency: float
    
    # Cross-asset features
    macro_risk_factor: float
    correlation_regime: str
    
    # Meta features
    feature_quality_score: float
    prediction_confidence: float

# ============================================================================
# CORE FEATURE ENGINEERING SYSTEM
# ============================================================================

class AdvancedMLFeatureEngine:
    """
    Advanced ML Feature Engineering System
    
    Generates sophisticated features for enhanced trading predictions by
    analyzing order book dynamics, sentiment, on-chain metrics, and
    advanced technical patterns.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the advanced ML feature engine"""
        self.config = config or self._get_default_config()
        
        # Data storage
        self.order_book_history = deque(maxlen=self.config['max_history_length'])
        self.sentiment_history = deque(maxlen=self.config['max_history_length'])
        self.onchain_history = deque(maxlen=self.config['max_history_length'])
        self.technical_history = deque(maxlen=self.config['max_history_length'])
        self.microstructure_history = deque(maxlen=self.config['max_history_length'])
        self.correlation_history = deque(maxlen=self.config['max_history_length'])
        
        # Feature cache
        self.feature_cache = {}
        self.cache_expiry = {}
        
        # Threading
        self.data_lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # API clients
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Bitcoin-AI-Bot/1.0'})
        
        # Feature computation state
        self.last_computation_time = None
        self.computation_stats = defaultdict(float)
        
        logger.info("Advanced ML Feature Engine initialized successfully")
    
    def _get_default_config(self) -> Dict:
        """Get default configuration for the feature engine"""
        return {
            'max_history_length': 1000,
            'cache_duration_seconds': 30,
            'order_book_depth_levels': 10,
            'sentiment_sources': ['twitter', 'reddit', 'news'],
            'onchain_update_interval': 300,  # 5 minutes
            'technical_lookback_periods': [5, 10, 20, 50],
            'correlation_assets': ['SPY', 'GLD', 'DXY', 'VIX'],
            'feature_quality_threshold': 0.7,
            'enable_async_processing': True,
            'api_timeout_seconds': 10,
            'max_retries': 3
        }

    # ========================================================================
    # ORDER BOOK ANALYSIS
    # ========================================================================

    def collect_order_book_data(self, exchange: str = 'binance') -> Optional[OrderBookData]:
        """
        Collect real-time order book data for imbalance analysis

        Args:
            exchange: Exchange to collect from ('binance', 'coinbase', 'kraken')

        Returns:
            OrderBookData object or None if failed
        """
        try:
            if exchange == 'binance':
                return self._get_binance_order_book()
            elif exchange == 'coinbase':
                return self._get_coinbase_order_book()
            elif exchange == 'kraken':
                return self._get_kraken_order_book()
            else:
                logger.warning(f"Unsupported exchange for order book: {exchange}")
                return None

        except Exception as e:
            logger.error(f"Error collecting order book data from {exchange}: {e}")
            return None

    def _get_binance_order_book(self) -> Optional[OrderBookData]:
        """Get order book data from Binance"""
        try:
            url = "https://api.binance.com/api/v3/depth"
            params = {'symbol': 'BTCUSDT', 'limit': self.config['order_book_depth_levels']}

            response = self.session.get(url, params=params, timeout=self.config['api_timeout_seconds'])
            response.raise_for_status()
            data = response.json()

            # Parse bids and asks
            bids = [(float(price), float(qty)) for price, qty in data['bids']]
            asks = [(float(price), float(qty)) for price, qty in data['asks']]

            # Calculate metrics
            bid_volume = sum(qty for _, qty in bids)
            ask_volume = sum(qty for _, qty in asks)
            spread = asks[0][0] - bids[0][0] if bids and asks else 0
            mid_price = (bids[0][0] + asks[0][0]) / 2 if bids and asks else 0

            return OrderBookData(
                timestamp=datetime.now(timezone.utc),
                bids=bids,
                asks=asks,
                bid_volume=bid_volume,
                ask_volume=ask_volume,
                spread=spread,
                mid_price=mid_price
            )

        except Exception as e:
            logger.error(f"Error fetching Binance order book: {e}")
            return None

    def _get_coinbase_order_book(self) -> Optional[OrderBookData]:
        """Get order book data from Coinbase Pro"""
        try:
            url = "https://api.exchange.coinbase.com/products/BTC-USD/book"
            params = {'level': 2}

            response = self.session.get(url, params=params, timeout=self.config['api_timeout_seconds'])
            response.raise_for_status()
            data = response.json()

            # Parse bids and asks (limit to configured depth)
            bids = [(float(price), float(qty)) for price, qty, _ in data['bids'][:self.config['order_book_depth_levels']]]
            asks = [(float(price), float(qty)) for price, qty, _ in data['asks'][:self.config['order_book_depth_levels']]]

            # Calculate metrics
            bid_volume = sum(qty for _, qty in bids)
            ask_volume = sum(qty for _, qty in asks)
            spread = asks[0][0] - bids[0][0] if bids and asks else 0
            mid_price = (bids[0][0] + asks[0][0]) / 2 if bids and asks else 0

            return OrderBookData(
                timestamp=datetime.now(timezone.utc),
                bids=bids,
                asks=asks,
                bid_volume=bid_volume,
                ask_volume=ask_volume,
                spread=spread,
                mid_price=mid_price
            )

        except Exception as e:
            logger.error(f"Error fetching Coinbase order book: {e}")
            return None

    def _get_kraken_order_book(self) -> Optional[OrderBookData]:
        """Get order book data from Kraken"""
        try:
            url = "https://api.kraken.com/0/public/Depth"
            params = {'pair': 'XBTUSD', 'count': self.config['order_book_depth_levels']}

            response = self.session.get(url, params=params, timeout=self.config['api_timeout_seconds'])
            response.raise_for_status()
            data = response.json()

            if data.get('error'):
                logger.error(f"Kraken API error: {data['error']}")
                return None

            # Get the result data
            result_key = list(data['result'].keys())[0]
            book_data = data['result'][result_key]

            # Parse bids and asks
            bids = [(float(price), float(qty)) for price, qty, _ in book_data['bids']]
            asks = [(float(price), float(qty)) for price, qty, _ in book_data['asks']]

            # Calculate metrics
            bid_volume = sum(qty for _, qty in bids)
            ask_volume = sum(qty for _, qty in asks)
            spread = asks[0][0] - bids[0][0] if bids and asks else 0
            mid_price = (bids[0][0] + asks[0][0]) / 2 if bids and asks else 0

            return OrderBookData(
                timestamp=datetime.now(timezone.utc),
                bids=bids,
                asks=asks,
                bid_volume=bid_volume,
                ask_volume=ask_volume,
                spread=spread,
                mid_price=mid_price
            )

        except Exception as e:
            logger.error(f"Error fetching Kraken order book: {e}")
            return None

    def analyze_order_book_imbalance(self, order_book: OrderBookData) -> Dict[str, float]:
        """
        Analyze order book for imbalance signals

        Args:
            order_book: OrderBookData object

        Returns:
            Dictionary with imbalance metrics
        """
        try:
            # Basic imbalance ratio
            total_volume = order_book.bid_volume + order_book.ask_volume
            imbalance_ratio = (order_book.bid_volume - order_book.ask_volume) / total_volume if total_volume > 0 else 0

            # Weighted imbalance (closer to mid price has more weight)
            weighted_bid_volume = 0
            weighted_ask_volume = 0

            for price, qty in order_book.bids:
                weight = 1 / (1 + abs(price - order_book.mid_price) / order_book.mid_price)
                weighted_bid_volume += qty * weight

            for price, qty in order_book.asks:
                weight = 1 / (1 + abs(price - order_book.mid_price) / order_book.mid_price)
                weighted_ask_volume += qty * weight

            total_weighted = weighted_bid_volume + weighted_ask_volume
            weighted_imbalance = (weighted_bid_volume - weighted_ask_volume) / total_weighted if total_weighted > 0 else 0

            # Spread analysis
            spread_bps = (order_book.spread / order_book.mid_price) * 10000 if order_book.mid_price > 0 else 0

            # Depth analysis
            depth_ratio = order_book.bid_volume / order_book.ask_volume if order_book.ask_volume > 0 else 1

            return {
                'imbalance_ratio': imbalance_ratio,
                'weighted_imbalance': weighted_imbalance,
                'spread_bps': spread_bps,
                'depth_ratio': depth_ratio,
                'total_liquidity': total_volume,
                'bid_ask_ratio': order_book.bid_volume / order_book.ask_volume if order_book.ask_volume > 0 else 1
            }

        except Exception as e:
            logger.error(f"Error analyzing order book imbalance: {e}")
            return {
                'imbalance_ratio': 0,
                'weighted_imbalance': 0,
                'spread_bps': 0,
                'depth_ratio': 1,
                'total_liquidity': 0,
                'bid_ask_ratio': 1
            }

    # ========================================================================
    # SENTIMENT ANALYSIS
    # ========================================================================

    def collect_sentiment_data(self) -> Optional[SentimentData]:
        """
        Collect social sentiment data from multiple sources

        Returns:
            SentimentData object or None if failed
        """
        try:
            # Collect from multiple sources in parallel
            futures = []

            with ThreadPoolExecutor(max_workers=3) as executor:
                futures.append(executor.submit(self._get_crypto_fear_greed_index))
                futures.append(executor.submit(self._get_reddit_sentiment))
                futures.append(executor.submit(self._get_news_sentiment))

            # Collect results
            fear_greed = None
            reddit_sentiment = 0.0
            news_sentiment = 0.0

            for future in as_completed(futures, timeout=self.config['api_timeout_seconds']):
                try:
                    result = future.result()
                    if isinstance(result, dict):
                        if 'fear_greed' in result:
                            fear_greed = result['fear_greed']
                        elif 'reddit' in result:
                            reddit_sentiment = result['reddit']
                        elif 'news' in result:
                            news_sentiment = result['news']
                except Exception as e:
                    logger.warning(f"Sentiment collection future failed: {e}")

            # Convert fear/greed to sentiment (-1 to 1)
            twitter_sentiment = self._fear_greed_to_sentiment(fear_greed) if fear_greed else 0.0

            # Calculate combined sentiment
            sentiments = [s for s in [twitter_sentiment, reddit_sentiment, news_sentiment] if s != 0]
            combined_sentiment = np.mean(sentiments) if sentiments else 0.0

            # Calculate momentum from history
            sentiment_momentum = self._calculate_sentiment_momentum(combined_sentiment)

            return SentimentData(
                timestamp=datetime.now(timezone.utc),
                twitter_sentiment=twitter_sentiment,
                reddit_sentiment=reddit_sentiment,
                news_sentiment=news_sentiment,
                combined_sentiment=combined_sentiment,
                sentiment_volume=len(sentiments),
                sentiment_momentum=sentiment_momentum
            )

        except Exception as e:
            logger.error(f"Error collecting sentiment data: {e}")
            return None

    def _get_crypto_fear_greed_index(self) -> Dict[str, float]:
        """Get crypto fear and greed index"""
        try:
            url = "https://api.alternative.me/fng/"
            response = self.session.get(url, timeout=self.config['api_timeout_seconds'])
            response.raise_for_status()
            data = response.json()

            if data.get('data') and len(data['data']) > 0:
                value = float(data['data'][0]['value'])
                return {'fear_greed': value}

        except Exception as e:
            logger.warning(f"Error fetching fear/greed index: {e}")

        return {'fear_greed': 50.0}  # Neutral default

    def _get_reddit_sentiment(self) -> Dict[str, float]:
        """Get Reddit sentiment (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In production, you would use Reddit API or sentiment analysis service
            # For now, return a neutral sentiment with some randomness
            base_sentiment = 0.0
            noise = np.random.normal(0, 0.1)  # Small random component
            sentiment = np.clip(base_sentiment + noise, -1, 1)

            return {'reddit': sentiment}

        except Exception as e:
            logger.warning(f"Error fetching Reddit sentiment: {e}")
            return {'reddit': 0.0}

    def _get_news_sentiment(self) -> Dict[str, float]:
        """Get news sentiment (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In production, you would use news API with sentiment analysis
            # For now, return a neutral sentiment with some randomness
            base_sentiment = 0.0
            noise = np.random.normal(0, 0.1)  # Small random component
            sentiment = np.clip(base_sentiment + noise, -1, 1)

            return {'news': sentiment}

        except Exception as e:
            logger.warning(f"Error fetching news sentiment: {e}")
            return {'news': 0.0}

    def _fear_greed_to_sentiment(self, fear_greed_value: float) -> float:
        """Convert fear/greed index (0-100) to sentiment (-1 to 1)"""
        # 0-25: Extreme Fear (-1 to -0.5)
        # 25-45: Fear (-0.5 to -0.1)
        # 45-55: Neutral (-0.1 to 0.1)
        # 55-75: Greed (0.1 to 0.5)
        # 75-100: Extreme Greed (0.5 to 1)

        if fear_greed_value <= 25:
            return -1 + (fear_greed_value / 25) * 0.5
        elif fear_greed_value <= 45:
            return -0.5 + ((fear_greed_value - 25) / 20) * 0.4
        elif fear_greed_value <= 55:
            return -0.1 + ((fear_greed_value - 45) / 10) * 0.2
        elif fear_greed_value <= 75:
            return 0.1 + ((fear_greed_value - 55) / 20) * 0.4
        else:
            return 0.5 + ((fear_greed_value - 75) / 25) * 0.5

    def _calculate_sentiment_momentum(self, current_sentiment: float) -> float:
        """Calculate sentiment momentum from historical data"""
        try:
            if len(self.sentiment_history) < 2:
                return 0.0

            # Get recent sentiment values
            recent_sentiments = [s.combined_sentiment for s in list(self.sentiment_history)[-10:]]
            recent_sentiments.append(current_sentiment)

            # Calculate momentum as rate of change
            if len(recent_sentiments) >= 2:
                momentum = recent_sentiments[-1] - recent_sentiments[-2]
                return np.clip(momentum, -1, 1)

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating sentiment momentum: {e}")
            return 0.0

    # ========================================================================
    # ON-CHAIN METRICS
    # ========================================================================

    def collect_onchain_data(self) -> Optional[OnChainData]:
        """
        Collect Bitcoin on-chain metrics

        Returns:
            OnChainData object or None if failed
        """
        try:
            # Check cache first
            cache_key = 'onchain_data'
            if self._is_cache_valid(cache_key):
                return self.feature_cache[cache_key]

            # Collect on-chain metrics in parallel
            futures = []

            with ThreadPoolExecutor(max_workers=3) as executor:
                futures.append(executor.submit(self._get_blockchain_info))
                futures.append(executor.submit(self._get_mempool_info))
                futures.append(executor.submit(self._get_exchange_flows))

            # Collect results
            blockchain_info = {}
            mempool_info = {}
            exchange_flows = {}

            for future in as_completed(futures, timeout=self.config['api_timeout_seconds'] * 2):
                try:
                    result = future.result()
                    if 'hash_rate' in result:
                        blockchain_info = result
                    elif 'mempool_size' in result:
                        mempool_info = result
                    elif 'inflows' in result:
                        exchange_flows = result
                except Exception as e:
                    logger.warning(f"On-chain collection future failed: {e}")

            # Create OnChainData object
            onchain_data = OnChainData(
                timestamp=datetime.now(timezone.utc),
                hash_rate=blockchain_info.get('hash_rate', 0),
                difficulty=blockchain_info.get('difficulty', 0),
                mempool_size=mempool_info.get('mempool_size', 0),
                avg_fee=mempool_info.get('avg_fee', 0),
                active_addresses=blockchain_info.get('active_addresses', 0),
                transaction_volume=blockchain_info.get('transaction_volume', 0),
                exchange_inflows=exchange_flows.get('inflows', 0),
                exchange_outflows=exchange_flows.get('outflows', 0),
                whale_activity=self._calculate_whale_activity(blockchain_info, exchange_flows)
            )

            # Cache the result
            self._cache_result(cache_key, onchain_data)

            return onchain_data

        except Exception as e:
            logger.error(f"Error collecting on-chain data: {e}")
            return None

    def _get_blockchain_info(self) -> Dict[str, float]:
        """Get blockchain information (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In production, you would use blockchain.info API or similar
            return {
                'hash_rate': 150e18,  # Approximate current hash rate
                'difficulty': 25e12,  # Approximate current difficulty
                'active_addresses': 1000000,  # Approximate active addresses
                'transaction_volume': 50000  # Approximate daily transaction volume
            }
        except Exception as e:
            logger.warning(f"Error fetching blockchain info: {e}")
            return {}

    def _get_mempool_info(self) -> Dict[str, float]:
        """Get mempool information (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In production, you would use mempool.space API or similar
            return {
                'mempool_size': 100000,  # Approximate mempool size
                'avg_fee': 50  # Approximate average fee in sats/vB
            }
        except Exception as e:
            logger.warning(f"Error fetching mempool info: {e}")
            return {}

    def _get_exchange_flows(self) -> Dict[str, float]:
        """Get exchange flow information (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In production, you would use CryptoQuant API or similar
            return {
                'inflows': 5000,   # BTC flowing into exchanges
                'outflows': 4500   # BTC flowing out of exchanges
            }
        except Exception as e:
            logger.warning(f"Error fetching exchange flows: {e}")
            return {}

    def _calculate_whale_activity(self, blockchain_info: Dict, exchange_flows: Dict) -> float:
        """Calculate whale activity score"""
        try:
            # Simplified whale activity calculation
            inflows = exchange_flows.get('inflows', 0)
            outflows = exchange_flows.get('outflows', 0)

            # Net flow ratio
            total_flow = inflows + outflows
            net_flow_ratio = (outflows - inflows) / total_flow if total_flow > 0 else 0

            # Normalize to 0-1 scale
            whale_activity = (net_flow_ratio + 1) / 2

            return np.clip(whale_activity, 0, 1)

        except Exception as e:
            logger.error(f"Error calculating whale activity: {e}")
            return 0.5  # Neutral default

    # ========================================================================
    # ADVANCED TECHNICAL FEATURES
    # ========================================================================

    def calculate_advanced_technical_features(self, price_data: List[float], volume_data: List[float]) -> AdvancedTechnicalFeatures:
        """
        Calculate advanced technical analysis features

        Args:
            price_data: List of recent prices
            volume_data: List of recent volumes

        Returns:
            AdvancedTechnicalFeatures object
        """
        try:
            if len(price_data) < 20 or len(volume_data) < 20:
                logger.warning("Insufficient data for advanced technical features")
                return self._get_default_technical_features()

            # Convert to numpy arrays
            prices = np.array(price_data)
            volumes = np.array(volume_data)

            # Calculate features
            price_acceleration = self._calculate_price_acceleration(prices)
            volume_acceleration = self._calculate_volume_acceleration(volumes)
            volatility_regime = self._detect_volatility_regime(prices)
            trend_strength = self._calculate_trend_strength(prices)
            support_resistance = self._calculate_support_resistance_strength(prices)
            patterns = self._recognize_patterns(prices)
            fractal_dim = self._calculate_fractal_dimension(prices)
            hurst_exp = self._calculate_hurst_exponent(prices)

            return AdvancedTechnicalFeatures(
                timestamp=datetime.now(timezone.utc),
                price_acceleration=price_acceleration,
                volume_acceleration=volume_acceleration,
                volatility_regime=volatility_regime,
                trend_strength=trend_strength,
                support_resistance_strength=support_resistance,
                pattern_recognition=patterns,
                fractal_dimension=fractal_dim,
                hurst_exponent=hurst_exp
            )

        except Exception as e:
            logger.error(f"Error calculating advanced technical features: {e}")
            return self._get_default_technical_features()

    def _calculate_price_acceleration(self, prices: np.ndarray) -> float:
        """Calculate price acceleration (second derivative)"""
        try:
            if len(prices) < 3:
                return 0.0

            # Calculate first derivative (velocity)
            velocity = np.diff(prices)

            # Calculate second derivative (acceleration)
            acceleration = np.diff(velocity)

            # Return normalized recent acceleration
            recent_acceleration = np.mean(acceleration[-5:]) if len(acceleration) >= 5 else np.mean(acceleration)

            # Normalize by price level
            normalized_acceleration = recent_acceleration / np.mean(prices[-10:]) if len(prices) >= 10 else recent_acceleration / np.mean(prices)

            return float(np.clip(normalized_acceleration, -1, 1))

        except Exception as e:
            logger.error(f"Error calculating price acceleration: {e}")
            return 0.0

    def _calculate_volume_acceleration(self, volumes: np.ndarray) -> float:
        """Calculate volume acceleration"""
        try:
            if len(volumes) < 3:
                return 0.0

            # Calculate first derivative
            volume_velocity = np.diff(volumes)

            # Calculate second derivative
            volume_acceleration = np.diff(volume_velocity)

            # Return normalized recent acceleration
            recent_acceleration = np.mean(volume_acceleration[-5:]) if len(volume_acceleration) >= 5 else np.mean(volume_acceleration)

            # Normalize by volume level
            normalized_acceleration = recent_acceleration / np.mean(volumes[-10:]) if len(volumes) >= 10 else recent_acceleration / np.mean(volumes)

            return float(np.clip(normalized_acceleration, -1, 1))

        except Exception as e:
            logger.error(f"Error calculating volume acceleration: {e}")
            return 0.0

    def _detect_volatility_regime(self, prices: np.ndarray) -> str:
        """Detect current volatility regime"""
        try:
            if len(prices) < 20:
                return "unknown"

            # Calculate rolling volatility
            returns = np.diff(np.log(prices))
            volatility = np.std(returns[-20:]) * np.sqrt(365 * 24 * 60)  # Annualized volatility

            # Classify regime
            if volatility < 0.3:
                return "low_volatility"
            elif volatility < 0.6:
                return "medium_volatility"
            elif volatility < 1.0:
                return "high_volatility"
            else:
                return "extreme_volatility"

        except Exception as e:
            logger.error(f"Error detecting volatility regime: {e}")
            return "unknown"

    def _calculate_trend_strength(self, prices: np.ndarray) -> float:
        """Calculate trend strength using multiple methods"""
        try:
            if len(prices) < 20:
                return 0.0

            # Method 1: Linear regression slope
            x = np.arange(len(prices))
            slope, _ = np.polyfit(x, prices, 1)
            normalized_slope = slope / np.mean(prices)

            # Method 2: Directional movement
            up_moves = np.sum(np.diff(prices) > 0)
            down_moves = np.sum(np.diff(prices) < 0)
            total_moves = up_moves + down_moves
            directional_strength = (up_moves - down_moves) / total_moves if total_moves > 0 else 0

            # Combine methods
            trend_strength = (normalized_slope * 10 + directional_strength) / 2

            return float(np.clip(trend_strength, -1, 1))

        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return 0.0

    def _calculate_support_resistance_strength(self, prices: np.ndarray) -> float:
        """Calculate support/resistance strength"""
        try:
            if len(prices) < 20:
                return 0.0

            current_price = prices[-1]

            # Find local minima and maxima
            from scipy.signal import argrelextrema

            # Local minima (support levels)
            min_indices = argrelextrema(prices, np.less, order=3)[0]
            support_levels = prices[min_indices] if len(min_indices) > 0 else []

            # Local maxima (resistance levels)
            max_indices = argrelextrema(prices, np.greater, order=3)[0]
            resistance_levels = prices[max_indices] if len(max_indices) > 0 else []

            # Calculate strength based on proximity to levels
            support_strength = 0.0
            resistance_strength = 0.0

            if len(support_levels) > 0:
                closest_support = min(support_levels, key=lambda x: abs(x - current_price))
                support_distance = abs(current_price - closest_support) / current_price
                support_strength = max(0, 1 - support_distance * 10)  # Stronger when closer

            if len(resistance_levels) > 0:
                closest_resistance = min(resistance_levels, key=lambda x: abs(x - current_price))
                resistance_distance = abs(current_price - closest_resistance) / current_price
                resistance_strength = max(0, 1 - resistance_distance * 10)  # Stronger when closer

            # Return combined strength
            combined_strength = (support_strength + resistance_strength) / 2
            return float(np.clip(combined_strength, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating support/resistance strength: {e}")
            return 0.0

    def _recognize_patterns(self, prices: np.ndarray) -> Dict[str, float]:
        """Recognize chart patterns"""
        try:
            if len(prices) < 20:
                return {'head_shoulders': 0, 'double_top': 0, 'double_bottom': 0, 'triangle': 0}

            patterns = {}

            # Simplified pattern recognition
            # Head and shoulders pattern
            patterns['head_shoulders'] = self._detect_head_shoulders(prices)

            # Double top/bottom patterns
            patterns['double_top'] = self._detect_double_top(prices)
            patterns['double_bottom'] = self._detect_double_bottom(prices)

            # Triangle pattern
            patterns['triangle'] = self._detect_triangle(prices)

            return patterns

        except Exception as e:
            logger.error(f"Error recognizing patterns: {e}")
            return {'head_shoulders': 0, 'double_top': 0, 'double_bottom': 0, 'triangle': 0}

    def _detect_head_shoulders(self, prices: np.ndarray) -> float:
        """Detect head and shoulders pattern (simplified)"""
        try:
            # This is a simplified implementation
            # Look for three peaks with middle one being highest
            from scipy.signal import find_peaks

            peaks, _ = find_peaks(prices, distance=5)
            if len(peaks) < 3:
                return 0.0

            # Check if we have a head and shoulders pattern
            recent_peaks = peaks[-3:]
            peak_heights = prices[recent_peaks]

            # Head should be higher than shoulders
            if peak_heights[1] > peak_heights[0] and peak_heights[1] > peak_heights[2]:
                # Calculate pattern strength
                left_shoulder_diff = peak_heights[1] - peak_heights[0]
                right_shoulder_diff = peak_heights[1] - peak_heights[2]
                symmetry = 1 - abs(left_shoulder_diff - right_shoulder_diff) / peak_heights[1]
                return float(np.clip(symmetry, 0, 1))

            return 0.0

        except Exception as e:
            logger.error(f"Error detecting head and shoulders: {e}")
            return 0.0

    def _detect_double_top(self, prices: np.ndarray) -> float:
        """Detect double top pattern (simplified)"""
        try:
            from scipy.signal import find_peaks

            peaks, _ = find_peaks(prices, distance=5)
            if len(peaks) < 2:
                return 0.0

            # Check last two peaks
            recent_peaks = peaks[-2:]
            peak_heights = prices[recent_peaks]

            # Peaks should be similar height
            height_diff = abs(peak_heights[0] - peak_heights[1]) / np.mean(peak_heights)
            similarity = max(0, 1 - height_diff * 5)  # Stronger when more similar

            return float(np.clip(similarity, 0, 1))

        except Exception as e:
            logger.error(f"Error detecting double top: {e}")
            return 0.0

    def _detect_double_bottom(self, prices: np.ndarray) -> float:
        """Detect double bottom pattern (simplified)"""
        try:
            from scipy.signal import find_peaks

            # Find troughs (inverted peaks)
            troughs, _ = find_peaks(-prices, distance=5)
            if len(troughs) < 2:
                return 0.0

            # Check last two troughs
            recent_troughs = troughs[-2:]
            trough_depths = prices[recent_troughs]

            # Troughs should be similar depth
            depth_diff = abs(trough_depths[0] - trough_depths[1]) / np.mean(trough_depths)
            similarity = max(0, 1 - depth_diff * 5)  # Stronger when more similar

            return float(np.clip(similarity, 0, 1))

        except Exception as e:
            logger.error(f"Error detecting double bottom: {e}")
            return 0.0

    def _detect_triangle(self, prices: np.ndarray) -> float:
        """Detect triangle pattern (simplified)"""
        try:
            if len(prices) < 20:
                return 0.0

            # Calculate trend lines for highs and lows
            from scipy.signal import find_peaks

            peaks, _ = find_peaks(prices, distance=3)
            troughs, _ = find_peaks(-prices, distance=3)

            if len(peaks) < 2 or len(troughs) < 2:
                return 0.0

            # Calculate slopes of trend lines
            peak_slope = (prices[peaks[-1]] - prices[peaks[0]]) / (peaks[-1] - peaks[0])
            trough_slope = (prices[troughs[-1]] - prices[troughs[0]]) / (troughs[-1] - troughs[0])

            # Triangle pattern: converging trend lines
            convergence = abs(peak_slope - trough_slope)
            triangle_strength = max(0, 1 - convergence * 1000)  # Normalize

            return float(np.clip(triangle_strength, 0, 1))

        except Exception as e:
            logger.error(f"Error detecting triangle: {e}")
            return 0.0

    def _calculate_fractal_dimension(self, prices: np.ndarray) -> float:
        """Calculate fractal dimension using box counting method"""
        try:
            if len(prices) < 10:
                return 1.5  # Default value

            # Simplified fractal dimension calculation
            # Normalize prices
            normalized_prices = (prices - np.min(prices)) / (np.max(prices) - np.min(prices))

            # Calculate fractal dimension using variation method
            n = len(normalized_prices)
            variations = []

            for k in [2, 4, 8, 16]:
                if n // k < 2:
                    continue

                segments = [normalized_prices[i:i+k] for i in range(0, n-k+1, k)]
                segment_variations = [np.max(seg) - np.min(seg) for seg in segments if len(seg) == k]

                if segment_variations:
                    variations.append(np.mean(segment_variations))

            if len(variations) < 2:
                return 1.5

            # Calculate fractal dimension
            fractal_dim = 1.5 + np.std(variations) * 0.5
            return float(np.clip(fractal_dim, 1.0, 2.0))

        except Exception as e:
            logger.error(f"Error calculating fractal dimension: {e}")
            return 1.5

    def _calculate_hurst_exponent(self, prices: np.ndarray) -> float:
        """Calculate Hurst exponent for trend persistence"""
        try:
            if len(prices) < 20:
                return 0.5  # Random walk default

            # Calculate log returns
            log_returns = np.diff(np.log(prices))

            # Calculate Hurst exponent using R/S analysis
            n = len(log_returns)
            lags = [2, 4, 8, 16, 32]
            lags = [lag for lag in lags if lag < n // 2]

            if len(lags) < 2:
                return 0.5

            rs_values = []

            for lag in lags:
                # Calculate R/S statistic
                segments = [log_returns[i:i+lag] for i in range(0, n-lag+1, lag)]
                rs_segment_values = []

                for segment in segments:
                    if len(segment) == lag:
                        mean_segment = np.mean(segment)
                        cumulative_deviations = np.cumsum(segment - mean_segment)
                        R = np.max(cumulative_deviations) - np.min(cumulative_deviations)
                        S = np.std(segment)

                        if S > 0:
                            rs_segment_values.append(R / S)

                if rs_segment_values:
                    rs_values.append(np.mean(rs_segment_values))

            if len(rs_values) < 2:
                return 0.5

            # Calculate Hurst exponent from slope
            log_lags = np.log(lags[:len(rs_values)])
            log_rs = np.log(rs_values)

            hurst_exponent, _ = np.polyfit(log_lags, log_rs, 1)

            return float(np.clip(hurst_exponent, 0.0, 1.0))

        except Exception as e:
            logger.error(f"Error calculating Hurst exponent: {e}")
            return 0.5

    def _get_default_technical_features(self) -> AdvancedTechnicalFeatures:
        """Get default technical features when calculation fails"""
        return AdvancedTechnicalFeatures(
            timestamp=datetime.now(timezone.utc),
            price_acceleration=0.0,
            volume_acceleration=0.0,
            volatility_regime="unknown",
            trend_strength=0.0,
            support_resistance_strength=0.0,
            pattern_recognition={'head_shoulders': 0, 'double_top': 0, 'double_bottom': 0, 'triangle': 0},
            fractal_dimension=1.5,
            hurst_exponent=0.5
        )

    # ========================================================================
    # UTILITY FUNCTIONS
    # ========================================================================

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.feature_cache:
            return False

        if cache_key not in self.cache_expiry:
            return False

        return datetime.now(timezone.utc) < self.cache_expiry[cache_key]

    def _cache_result(self, cache_key: str, result: Any) -> None:
        """Cache a result with expiry time"""
        self.feature_cache[cache_key] = result
        self.cache_expiry[cache_key] = datetime.now(timezone.utc) + timedelta(seconds=self.config['cache_duration_seconds'])

    def update_history(self, order_book: Optional[OrderBookData] = None,
                      sentiment: Optional[SentimentData] = None,
                      onchain: Optional[OnChainData] = None,
                      technical: Optional[AdvancedTechnicalFeatures] = None) -> None:
        """Update historical data storage"""
        with self.data_lock:
            if order_book:
                self.order_book_history.append(order_book)
            if sentiment:
                self.sentiment_history.append(sentiment)
            if onchain:
                self.onchain_history.append(onchain)
            if technical:
                self.technical_history.append(technical)

    def get_computation_stats(self) -> Dict[str, Any]:
        """Get computation performance statistics"""
        return {
            'last_computation_time': self.last_computation_time,
            'computation_stats': dict(self.computation_stats),
            'cache_size': len(self.feature_cache),
            'history_lengths': {
                'order_book': len(self.order_book_history),
                'sentiment': len(self.sentiment_history),
                'onchain': len(self.onchain_history),
                'technical': len(self.technical_history)
            }
        }

    # ========================================================================
    # MAIN INTEGRATION INTERFACE
    # ========================================================================

    def generate_ml_features(self, market_data: Dict[str, Any]) -> MLFeatureSet:
        """
        Generate complete ML feature set for enhanced predictions

        This is the main interface function that combines all feature types
        into a comprehensive feature set for the ML model.

        Args:
            market_data: Current market data from the bot

        Returns:
            MLFeatureSet with all advanced features
        """
        start_time = time.time()

        try:
            logger.info("Generating advanced ML features...")

            # Extract price and volume data
            current_price = market_data.get('avg_price', 0)
            price_history = self._extract_price_history(market_data)
            volume_history = self._extract_volume_history(market_data)

            # Collect all feature types in parallel
            futures = []

            with ThreadPoolExecutor(max_workers=4) as executor:
                # Order book analysis
                futures.append(executor.submit(self.collect_order_book_data))

                # Sentiment analysis
                futures.append(executor.submit(self.collect_sentiment_data))

                # On-chain metrics
                futures.append(executor.submit(self.collect_onchain_data))

                # Advanced technical features
                futures.append(executor.submit(self.calculate_advanced_technical_features, price_history, volume_history))

            # Collect results
            order_book_data = None
            sentiment_data = None
            onchain_data = None
            technical_features = None

            for future in as_completed(futures, timeout=self.config['api_timeout_seconds'] * 2):
                try:
                    result = future.result()
                    if isinstance(result, OrderBookData):
                        order_book_data = result
                    elif isinstance(result, SentimentData):
                        sentiment_data = result
                    elif isinstance(result, OnChainData):
                        onchain_data = result
                    elif isinstance(result, AdvancedTechnicalFeatures):
                        technical_features = result
                except Exception as e:
                    logger.warning(f"Feature collection future failed: {e}")

            # Update history
            self.update_history(order_book_data, sentiment_data, onchain_data, technical_features)

            # Generate comprehensive feature set
            ml_features = self._combine_features(
                current_price, order_book_data, sentiment_data,
                onchain_data, technical_features, market_data
            )

            # Update computation stats
            computation_time = time.time() - start_time
            self.computation_stats['total_computation_time'] = computation_time
            self.computation_stats['features_generated'] += 1
            self.last_computation_time = datetime.now(timezone.utc)

            logger.info(f"Advanced ML features generated successfully in {computation_time:.2f}s")

            return ml_features

        except Exception as e:
            logger.error(f"Error generating ML features: {e}")
            return self._get_default_ml_features()

    def _extract_price_history(self, market_data: Dict[str, Any]) -> List[float]:
        """Extract price history from market data"""
        try:
            # Try to get price history from various sources
            if 'price_history' in market_data:
                return market_data['price_history']

            # Fallback: create synthetic history from current price
            current_price = market_data.get('avg_price', 50000)

            # Generate synthetic price history with some randomness
            history = []
            price = current_price

            for i in range(50):
                # Add small random movements
                change = np.random.normal(0, price * 0.001)
                price = max(price + change, price * 0.99)  # Prevent negative prices
                history.append(price)

            return history

        except Exception as e:
            logger.error(f"Error extracting price history: {e}")
            return [50000.0] * 50  # Default history

    def _extract_volume_history(self, market_data: Dict[str, Any]) -> List[float]:
        """Extract volume history from market data"""
        try:
            # Try to get volume history from various sources
            if 'volume_history' in market_data:
                return market_data['volume_history']

            # Fallback: create synthetic volume history
            base_volume = market_data.get('volume', 1000)

            # Generate synthetic volume history
            history = []
            volume = base_volume

            for i in range(50):
                # Add random volume variations
                change = np.random.normal(0, volume * 0.1)
                volume = max(volume + change, volume * 0.5)  # Prevent negative volume
                history.append(volume)

            return history

        except Exception as e:
            logger.error(f"Error extracting volume history: {e}")
            return [1000.0] * 50  # Default history

    def _combine_features(self, current_price: float, order_book: Optional[OrderBookData],
                         sentiment: Optional[SentimentData], onchain: Optional[OnChainData],
                         technical: Optional[AdvancedTechnicalFeatures],
                         market_data: Dict[str, Any]) -> MLFeatureSet:
        """Combine all feature types into a comprehensive ML feature set"""
        try:
            # Order book features
            if order_book:
                order_book_analysis = self.analyze_order_book_imbalance(order_book)
                order_book_imbalance = order_book_analysis['weighted_imbalance']
                bid_ask_ratio = order_book_analysis['bid_ask_ratio']
                order_book_depth = order_book_analysis['total_liquidity']
                spread_normalized = order_book_analysis['spread_bps'] / 100  # Normalize to 0-1
            else:
                order_book_imbalance = 0.0
                bid_ask_ratio = 1.0
                order_book_depth = 0.0
                spread_normalized = 0.0

            # Sentiment features
            if sentiment:
                sentiment_score = sentiment.combined_sentiment
                sentiment_momentum = sentiment.sentiment_momentum
                sentiment_divergence = self._calculate_sentiment_divergence(sentiment, current_price)
            else:
                sentiment_score = 0.0
                sentiment_momentum = 0.0
                sentiment_divergence = 0.0

            # On-chain features
            if onchain:
                network_activity = self._normalize_network_activity(onchain)
                whale_activity = onchain.whale_activity
                exchange_flow_ratio = self._calculate_exchange_flow_ratio(onchain)
            else:
                network_activity = 0.5
                whale_activity = 0.5
                exchange_flow_ratio = 0.0

            # Advanced technical features
            if technical:
                price_momentum_2nd_derivative = technical.price_acceleration
                volatility_clustering = self._calculate_volatility_clustering(technical)
                trend_persistence = technical.hurst_exponent
                pattern_strength = self._calculate_pattern_strength(technical)
            else:
                price_momentum_2nd_derivative = 0.0
                volatility_clustering = 0.5
                trend_persistence = 0.5
                pattern_strength = 0.0

            # Market microstructure features
            order_flow_pressure = self._calculate_order_flow_pressure(order_book, technical)
            liquidity_stress = self._calculate_liquidity_stress(order_book, market_data)
            market_efficiency = self._calculate_market_efficiency(technical, sentiment)

            # Cross-asset features
            macro_risk_factor = self._calculate_macro_risk_factor(sentiment, onchain)
            correlation_regime = self._determine_correlation_regime(market_data)

            # Meta features
            feature_quality_score = self._calculate_feature_quality(
                order_book, sentiment, onchain, technical
            )
            prediction_confidence = self._calculate_prediction_confidence(
                feature_quality_score, market_data
            )

            return MLFeatureSet(
                timestamp=datetime.now(timezone.utc),

                # Order book features
                order_book_imbalance=order_book_imbalance,
                bid_ask_ratio=bid_ask_ratio,
                order_book_depth=order_book_depth,
                spread_normalized=spread_normalized,

                # Sentiment features
                sentiment_score=sentiment_score,
                sentiment_momentum=sentiment_momentum,
                sentiment_divergence=sentiment_divergence,

                # On-chain features
                network_activity=network_activity,
                whale_activity=whale_activity,
                exchange_flow_ratio=exchange_flow_ratio,

                # Advanced technical features
                price_momentum_2nd_derivative=price_momentum_2nd_derivative,
                volatility_clustering=volatility_clustering,
                trend_persistence=trend_persistence,
                pattern_strength=pattern_strength,

                # Microstructure features
                order_flow_pressure=order_flow_pressure,
                liquidity_stress=liquidity_stress,
                market_efficiency=market_efficiency,

                # Cross-asset features
                macro_risk_factor=macro_risk_factor,
                correlation_regime=correlation_regime,

                # Meta features
                feature_quality_score=feature_quality_score,
                prediction_confidence=prediction_confidence
            )

        except Exception as e:
            logger.error(f"Error combining features: {e}")
            return self._get_default_ml_features()

    def _calculate_sentiment_divergence(self, sentiment: SentimentData, current_price: float) -> float:
        """Calculate divergence between sentiment and price"""
        try:
            if len(self.sentiment_history) < 5:
                return 0.0

            # Get recent sentiment and price changes
            recent_sentiments = [s.combined_sentiment for s in list(self.sentiment_history)[-5:]]
            recent_sentiments.append(sentiment.combined_sentiment)

            sentiment_change = recent_sentiments[-1] - recent_sentiments[0]

            # Estimate price change (simplified)
            price_change_estimate = 0.01  # Placeholder - would need actual price history

            # Calculate divergence
            if sentiment_change > 0 and price_change_estimate < 0:
                return abs(sentiment_change)  # Positive sentiment, negative price
            elif sentiment_change < 0 and price_change_estimate > 0:
                return abs(sentiment_change)  # Negative sentiment, positive price
            else:
                return 0.0  # No divergence

        except Exception as e:
            logger.error(f"Error calculating sentiment divergence: {e}")
            return 0.0

    def _normalize_network_activity(self, onchain: OnChainData) -> float:
        """Normalize network activity to 0-1 scale"""
        try:
            # Combine multiple on-chain metrics
            hash_rate_score = min(onchain.hash_rate / 200e18, 1.0)  # Normalize hash rate
            mempool_score = min(onchain.mempool_size / 200000, 1.0)  # Normalize mempool
            address_score = min(onchain.active_addresses / 2000000, 1.0)  # Normalize addresses

            # Weighted average
            network_activity = (hash_rate_score * 0.4 + mempool_score * 0.3 + address_score * 0.3)

            return float(np.clip(network_activity, 0, 1))

        except Exception as e:
            logger.error(f"Error normalizing network activity: {e}")
            return 0.5

    def _calculate_exchange_flow_ratio(self, onchain: OnChainData) -> float:
        """Calculate exchange flow ratio"""
        try:
            total_flow = onchain.exchange_inflows + onchain.exchange_outflows
            if total_flow == 0:
                return 0.0

            # Positive ratio = more outflows (bullish), negative = more inflows (bearish)
            flow_ratio = (onchain.exchange_outflows - onchain.exchange_inflows) / total_flow

            return float(np.clip(flow_ratio, -1, 1))

        except Exception as e:
            logger.error(f"Error calculating exchange flow ratio: {e}")
            return 0.0

    def _calculate_volatility_clustering(self, technical: AdvancedTechnicalFeatures) -> float:
        """Calculate volatility clustering score"""
        try:
            # Simplified volatility clustering based on regime
            regime_scores = {
                'low_volatility': 0.2,
                'medium_volatility': 0.5,
                'high_volatility': 0.8,
                'extreme_volatility': 1.0,
                'unknown': 0.5
            }

            return regime_scores.get(technical.volatility_regime, 0.5)

        except Exception as e:
            logger.error(f"Error calculating volatility clustering: {e}")
            return 0.5

    def _calculate_pattern_strength(self, technical: AdvancedTechnicalFeatures) -> float:
        """Calculate overall pattern strength"""
        try:
            patterns = technical.pattern_recognition
            pattern_values = list(patterns.values())

            if not pattern_values:
                return 0.0

            # Return maximum pattern strength
            return float(max(pattern_values))

        except Exception as e:
            logger.error(f"Error calculating pattern strength: {e}")
            return 0.0

    def _calculate_order_flow_pressure(self, order_book: Optional[OrderBookData],
                                     technical: Optional[AdvancedTechnicalFeatures]) -> float:
        """Calculate order flow pressure"""
        try:
            if not order_book:
                return 0.0

            # Combine order book imbalance with technical momentum
            imbalance_analysis = self.analyze_order_book_imbalance(order_book)
            order_pressure = imbalance_analysis['weighted_imbalance']

            # Add technical momentum if available
            if technical:
                momentum_factor = technical.price_acceleration
                combined_pressure = (order_pressure + momentum_factor) / 2
            else:
                combined_pressure = order_pressure

            return float(np.clip(combined_pressure, -1, 1))

        except Exception as e:
            logger.error(f"Error calculating order flow pressure: {e}")
            return 0.0

    def _calculate_liquidity_stress(self, order_book: Optional[OrderBookData],
                                  market_data: Dict[str, Any]) -> float:
        """Calculate liquidity stress indicator"""
        try:
            if not order_book:
                return 0.5  # Neutral when no data

            # Calculate stress based on spread and depth
            spread_stress = min(order_book.spread / order_book.mid_price * 1000, 1.0) if order_book.mid_price > 0 else 1.0

            # Volume stress
            total_volume = order_book.bid_volume + order_book.ask_volume
            volume_stress = max(0, 1 - total_volume / 1000)  # Stress increases as volume decreases

            # Combined stress
            liquidity_stress = (spread_stress + volume_stress) / 2

            return float(np.clip(liquidity_stress, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating liquidity stress: {e}")
            return 0.5

    def _calculate_market_efficiency(self, technical: Optional[AdvancedTechnicalFeatures],
                                   sentiment: Optional[SentimentData]) -> float:
        """Calculate market efficiency score"""
        try:
            efficiency_score = 0.5  # Start with neutral

            # Technical efficiency (based on Hurst exponent)
            if technical:
                # Hurst = 0.5 is most efficient (random walk)
                hurst_efficiency = 1 - abs(technical.hurst_exponent - 0.5) * 2
                efficiency_score = (efficiency_score + hurst_efficiency) / 2

            # Sentiment efficiency (less divergence = more efficient)
            if sentiment:
                sentiment_efficiency = 1 - abs(sentiment.combined_sentiment) * 0.5
                efficiency_score = (efficiency_score + sentiment_efficiency) / 2

            return float(np.clip(efficiency_score, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating market efficiency: {e}")
            return 0.5

    def _calculate_macro_risk_factor(self, sentiment: Optional[SentimentData],
                                   onchain: Optional[OnChainData]) -> float:
        """Calculate macro risk factor"""
        try:
            risk_factors = []

            # Sentiment risk
            if sentiment:
                sentiment_risk = abs(sentiment.combined_sentiment)  # Extreme sentiment = higher risk
                risk_factors.append(sentiment_risk)

            # On-chain risk
            if onchain:
                # High whale activity = higher risk
                whale_risk = onchain.whale_activity
                risk_factors.append(whale_risk)

            if risk_factors:
                macro_risk = np.mean(risk_factors)
            else:
                macro_risk = 0.5  # Neutral risk

            return float(np.clip(macro_risk, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating macro risk factor: {e}")
            return 0.5

    def _determine_correlation_regime(self, market_data: Dict[str, Any]) -> str:
        """Determine current correlation regime"""
        try:
            # Simplified correlation regime determination
            # In production, this would analyze correlations with traditional assets

            volatility = market_data.get('volatility', 0.02)

            if volatility < 0.01:
                return "low_correlation"
            elif volatility < 0.03:
                return "medium_correlation"
            else:
                return "high_correlation"

        except Exception as e:
            logger.error(f"Error determining correlation regime: {e}")
            return "unknown"

    def _calculate_feature_quality(self, order_book: Optional[OrderBookData],
                                 sentiment: Optional[SentimentData],
                                 onchain: Optional[OnChainData],
                                 technical: Optional[AdvancedTechnicalFeatures]) -> float:
        """Calculate overall feature quality score"""
        try:
            quality_scores = []

            # Order book quality
            if order_book:
                quality_scores.append(0.9)  # High quality if available
            else:
                quality_scores.append(0.3)  # Low quality if missing

            # Sentiment quality
            if sentiment and sentiment.sentiment_volume > 0:
                quality_scores.append(0.8)
            else:
                quality_scores.append(0.4)

            # On-chain quality
            if onchain:
                quality_scores.append(0.85)
            else:
                quality_scores.append(0.5)

            # Technical quality
            if technical:
                quality_scores.append(0.95)  # Technical features are usually reliable
            else:
                quality_scores.append(0.6)

            overall_quality = np.mean(quality_scores)

            return float(np.clip(overall_quality, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating feature quality: {e}")
            return 0.5

    def _calculate_prediction_confidence(self, feature_quality: float,
                                       market_data: Dict[str, Any]) -> float:
        """Calculate prediction confidence based on feature quality and market conditions"""
        try:
            # Base confidence from feature quality
            base_confidence = feature_quality

            # Adjust for market conditions
            volatility = market_data.get('volatility', 0.02)

            # Lower confidence in high volatility
            volatility_adjustment = max(0.5, 1 - volatility * 10)

            # Combined confidence
            prediction_confidence = base_confidence * volatility_adjustment

            return float(np.clip(prediction_confidence, 0, 1))

        except Exception as e:
            logger.error(f"Error calculating prediction confidence: {e}")
            return 0.5

    def _get_default_ml_features(self) -> MLFeatureSet:
        """Get default ML features when generation fails"""
        return MLFeatureSet(
            timestamp=datetime.now(timezone.utc),

            # Order book features
            order_book_imbalance=0.0,
            bid_ask_ratio=1.0,
            order_book_depth=0.0,
            spread_normalized=0.0,

            # Sentiment features
            sentiment_score=0.0,
            sentiment_momentum=0.0,
            sentiment_divergence=0.0,

            # On-chain features
            network_activity=0.5,
            whale_activity=0.5,
            exchange_flow_ratio=0.0,

            # Advanced technical features
            price_momentum_2nd_derivative=0.0,
            volatility_clustering=0.5,
            trend_persistence=0.5,
            pattern_strength=0.0,

            # Microstructure features
            order_flow_pressure=0.0,
            liquidity_stress=0.5,
            market_efficiency=0.5,

            # Cross-asset features
            macro_risk_factor=0.5,
            correlation_regime="unknown",

            # Meta features
            feature_quality_score=0.3,
            prediction_confidence=0.3
        )

# ============================================================================
# GLOBAL INTERFACE FUNCTIONS
# ============================================================================

# Global instance
_advanced_ml_engine: Optional[AdvancedMLFeatureEngine] = None

def get_advanced_ml_engine() -> AdvancedMLFeatureEngine:
    """Get or create the global advanced ML feature engine"""
    global _advanced_ml_engine

    if _advanced_ml_engine is None:
        _advanced_ml_engine = AdvancedMLFeatureEngine()

    return _advanced_ml_engine

def generate_advanced_ml_features(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate advanced ML features for the trading bot

    This is the main interface function for integration with the existing bot.

    Args:
        market_data: Current market data from the bot's market analysis

    Returns:
        Dictionary with advanced ML features for enhanced predictions
    """
    try:
        engine = get_advanced_ml_engine()
        ml_features = engine.generate_ml_features(market_data)

        # Convert to dictionary for easy integration
        feature_dict = asdict(ml_features)

        # Add summary statistics
        feature_dict['_meta'] = {
            'generation_time': ml_features.timestamp.isoformat(),
            'feature_count': len(feature_dict) - 1,  # Exclude _meta itself
            'quality_score': ml_features.feature_quality_score,
            'confidence': ml_features.prediction_confidence
        }

        return feature_dict

    except Exception as e:
        logger.error(f"Error in generate_advanced_ml_features: {e}")

        # Return minimal feature set on error
        return {
            'order_book_imbalance': 0.0,
            'sentiment_score': 0.0,
            'network_activity': 0.5,
            'trend_persistence': 0.5,
            'feature_quality_score': 0.1,
            'prediction_confidence': 0.1,
            '_meta': {
                'generation_time': datetime.now(timezone.utc).isoformat(),
                'feature_count': 6,
                'quality_score': 0.1,
                'confidence': 0.1,
                'error': str(e)
            }
        }

def get_ml_feature_stats() -> Dict[str, Any]:
    """Get statistics about ML feature generation"""
    try:
        engine = get_advanced_ml_engine()
        return engine.get_computation_stats()
    except Exception as e:
        logger.error(f"Error getting ML feature stats: {e}")
        return {'error': str(e)}

def stop_advanced_ml_engine():
    """Stop the advanced ML feature engine"""
    global _advanced_ml_engine

    if _advanced_ml_engine:
        # Clean up resources
        if hasattr(_advanced_ml_engine, 'executor'):
            _advanced_ml_engine.executor.shutdown(wait=True)

        if hasattr(_advanced_ml_engine, 'session'):
            _advanced_ml_engine.session.close()

        _advanced_ml_engine = None
        logger.info("Advanced ML feature engine stopped")

if __name__ == "__main__":
    # Test the system
    logging.basicConfig(level=logging.INFO)

    # Sample market data
    test_market_data = {
        'avg_price': 45000.0,
        'volume': 1000.0,
        'volatility': 0.02,
        'rsi': 55.0,
        'macd_hist': 0.1
    }

    # Generate features
    features = generate_advanced_ml_features(test_market_data)

    print("Advanced ML Features Generated:")
    for key, value in features.items():
        if key != '_meta':
            print(f"  {key}: {value}")

    print(f"\nMeta Information:")
    for key, value in features['_meta'].items():
        print(f"  {key}: {value}")

    # Clean up
    stop_advanced_ml_engine()
