# close_alpaca_btc_position.py
import os
import logging
from dotenv import load_dotenv
from alpaca_trade_api.rest import REST, APIError
import time # <<< ADDED IMPORT FOR TIME MODULE

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Load Environment Variables for API Keys ---
load_dotenv()

# --- Alpaca API Configuration ---
API_KEY_ID = os.getenv("ALPACA_LIVE_API_KEY_ID")
SECRET_KEY = os.getenv("ALPACA_LIVE_SECRET_KEY")
BASE_URL = "https://api.alpaca.markets" 

ALPACA_ORDER_SYMBOL = "BTCUSD" 

if not API_KEY_ID or not SECRET_KEY:
    logger.error("Alpaca API Key ID or Secret Key not found in .env file. Exiting.")
    exit()

def close_btc_position(api_client):
    """Attempts to close any existing BTC/USD position."""
    try:
        logger.info(f"Checking for existing position in {ALPACA_ORDER_SYMBOL}...")
        position = api_client.get_position(ALPACA_ORDER_SYMBOL)
        # Alpaca position object has qty as a string, convert to float for comparison
        position_qty_float = float(position.qty) 

        # Use a small threshold to consider if the position is effectively zero (dust)
        # This helps if Alpaca sometimes leaves extremely tiny residuals.
        DUST_THRESHOLD = 0.00000001 

        if abs(position_qty_float) < DUST_THRESHOLD:
            logger.info(f"No significant position found for {ALPACA_ORDER_SYMBOL} (Qty: {position_qty_float}). Nothing to close.")
            return True
        
        logger.warning(f"Position found: Qty {position.qty} of {ALPACA_ORDER_SYMBOL}. Attempting to close by submitting a market order.")
        
        # Use api.close_position() for simplicity. It handles long/short.
        order = api_client.close_position(ALPACA_ORDER_SYMBOL)
        
        logger.info(f"Close order submitted for {ALPACA_ORDER_SYMBOL}. Order ID: {order.id}, Status: {order.status}")
        
        logger.info("Waiting a few seconds for the order to potentially fill/process...")
        time.sleep(5) # Give some time for the order to process

        # Re-check position after attempting close
        try:
            updated_position = api_client.get_position(ALPACA_ORDER_SYMBOL)
            updated_qty_float = float(updated_position.qty)
            logger.info(f"Position status after attempting close: Qty {updated_position.qty}")
            if abs(updated_qty_float) < DUST_THRESHOLD:
                logger.info(f"Position for {ALPACA_ORDER_SYMBOL} successfully closed (or reduced to dust).")
                return True
            else:
                logger.warning(f"Position for {ALPACA_ORDER_SYMBOL} may not be fully closed. Current Qty: {updated_position.qty}")
                return False
        except APIError as e_check: # This is expected if position is truly gone
            if e_check.status_code == 404:
                logger.info(f"Position for {ALPACA_ORDER_SYMBOL} successfully closed (no longer exists).")
                return True
            else: # Some other API error while re-checking
                logger.error(f"API error while re-checking position after close attempt: {e_check}")
                return False


    except APIError as e:
        if e.status_code == 404: # Initial get_position call found no position
            logger.info(f"No position found for {ALPACA_ORDER_SYMBOL}. Nothing to close.")
            return True
        else:
            logger.error(f"Alpaca API error when trying to close position {ALPACA_ORDER_SYMBOL}: {e}")
            return False
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("Attempting to initialize Alpaca API client...")
    try:
        alpaca_api = REST(key_id=API_KEY_ID, secret_key=SECRET_KEY, base_url=BASE_URL)
        
        account_info = alpaca_api.get_account()
        logger.info(f"Connected to Alpaca. Account ID: {account_info.id}, Status: {account_info.status}")
        logger.info(f"Current Equity: ${float(account_info.equity):.2f}")

        if close_btc_position(alpaca_api):
            logger.info("Script finished: Position check/close process complete.")
        else:
            logger.error("Script finished: There was an issue closing the position or an error occurred.")
            logger.info("Please check your Alpaca account dashboard to confirm the position status.")

    except Exception as e:
        logger.critical(f"Failed to initialize Alpaca client or run script: {e}", exc_info=True)