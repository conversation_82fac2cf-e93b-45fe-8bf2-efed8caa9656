"""
Error Handling Utilities for Bitcoin AI Trading Bot

This module provides common error handling patterns and utilities
specifically designed for the trading bot's services.
"""

import logging
from typing import Any, Dict, Optional, Callable, Union
from decimal import Decimal
import json
from functools import wraps

from error_handler import (
    TradingError, APIError, DataError, NetworkError, SystemError,
    ErrorSeverity, ErrorCategory, handle_error, with_retry, safe_execute,
    CircuitBreaker
)

# ===================================================================
# SERVICE-SPECIFIC ERROR HANDLERS
# ===================================================================

class AlpacaErrorHandler:
    """Error handling utilities for Alpaca API operations"""
    
    @staticmethod
    def handle_api_error(operation: str, error: Exception, context: Optional[Dict] = None) -> None:
        """Handle Alpaca API errors with appropriate categorization"""
        
        context = context or {}
        context['operation'] = operation
        context['api'] = 'alpaca'
        
        # Categorize based on error type
        if 'rate limit' in str(error).lower():
            severity = ErrorSeverity.MEDIUM
            message = f"Alpaca rate limit exceeded during {operation}"
        elif 'unauthorized' in str(error).lower() or 'forbidden' in str(error).lower():
            severity = ErrorSeverity.CRITICAL
            message = f"Alpaca authentication failed during {operation}"
        elif 'timeout' in str(error).lower():
            severity = ErrorSeverity.MEDIUM
            message = f"Alpaca API timeout during {operation}"
        else:
            severity = ErrorSeverity.HIGH
            message = f"Alpaca API error during {operation}: {str(error)}"
        
        api_error = APIError(message, 'alpaca', severity, context, error)
        handle_error(api_error)
    
    @staticmethod
    def safe_account_info(api_func: Callable, default_return: Dict = None) -> Dict:
        """Safely get account info with error handling"""
        default_return = default_return or {
            "equity": 0.0, "buying_power": 0.0, "cash": 0.0
        }
        
        def operation():
            try:
                return api_func()
            except Exception as e:
                AlpacaErrorHandler.handle_api_error("get_account_info", e)
                raise
        
        return safe_execute(operation, default_return, {'operation': 'account_info'})
    
    @staticmethod
    def safe_positions(api_func: Callable, symbol: str, default_return: Dict = None) -> Dict:
        """Safely get positions with error handling"""
        default_return = default_return or {
            "qty": 0.0, "avg_entry_price": 0.0, "market_value": 0.0,
            "unrealized_pl": 0.0, "unrealized_intraday_pl": 0.0, "current_price": 0.0
        }
        
        def operation():
            try:
                return api_func(symbol)
            except Exception as e:
                AlpacaErrorHandler.handle_api_error("get_positions", e, {'symbol': symbol})
                raise
        
        return safe_execute(operation, default_return, {'operation': 'positions', 'symbol': symbol})


class BinanceErrorHandler:
    """Error handling utilities for Binance WebSocket operations"""
    
    @staticmethod
    def handle_websocket_error(error: Exception, context: Optional[Dict] = None) -> None:
        """Handle Binance WebSocket errors"""
        
        context = context or {}
        context['service'] = 'binance_websocket'
        
        error_str = str(error).lower()
        
        if '10060' in error_str or 'timeout' in error_str:
            severity = ErrorSeverity.LOW
            message = f"Binance WebSocket transient timeout: {error}"
        elif 'connection' in error_str:
            severity = ErrorSeverity.MEDIUM
            message = f"Binance WebSocket connection error: {error}"
        else:
            severity = ErrorSeverity.HIGH
            message = f"Binance WebSocket error: {error}"
        
        network_error = NetworkError(message, severity, context, error)
        handle_error(network_error)
    
    @staticmethod
    def handle_data_processing_error(error: Exception, message_data: str = "", 
                                   context: Optional[Dict] = None) -> None:
        """Handle Binance data processing errors"""
        
        context = context or {}
        context['service'] = 'binance_data_processing'
        context['message_preview'] = message_data[:100] if message_data else ""
        
        if isinstance(error, json.JSONDecodeError):
            severity = ErrorSeverity.MEDIUM
            message = f"Error decoding JSON from Binance WebSocket: {error}"
        elif isinstance(error, KeyError):
            severity = ErrorSeverity.MEDIUM
            message = f"Missing expected key in Binance data: {error}"
        else:
            severity = ErrorSeverity.HIGH
            message = f"Unexpected error processing Binance data: {error}"
        
        data_error = DataError(message, severity, context, error)
        handle_error(data_error)


class AIServiceErrorHandler:
    """Error handling utilities for AI decision service"""
    
    @staticmethod
    def handle_gemini_error(error: Exception, request_id: Optional[str] = None,
                          context: Optional[Dict] = None) -> None:
        """Handle Google Gemini API errors"""
        
        context = context or {}
        context['service'] = 'gemini_ai'
        if request_id:
            context['request_id'] = request_id
        
        error_str = str(error).lower()
        
        if 'rate limit' in error_str or 'quota' in error_str:
            severity = ErrorSeverity.HIGH
            message = f"Gemini API rate limit/quota exceeded: {error}"
        elif 'timeout' in error_str or 'deadline' in error_str:
            severity = ErrorSeverity.MEDIUM
            message = f"Gemini API timeout: {error}"
        elif 'unauthorized' in error_str or 'forbidden' in error_str:
            severity = ErrorSeverity.CRITICAL
            message = f"Gemini API authentication failed: {error}"
        else:
            severity = ErrorSeverity.HIGH
            message = f"Gemini API error: {error}"
        
        api_error = APIError(message, 'gemini', severity, context, error)
        handle_error(api_error)
    
    @staticmethod
    def handle_validation_error(error: Exception, request_id: Optional[str] = None,
                              context: Optional[Dict] = None) -> None:
        """Handle AI response validation errors"""
        
        context = context or {}
        context['service'] = 'ai_validation'
        if request_id:
            context['request_id'] = request_id
        
        message = f"AI response validation error: {error}"
        validation_error = DataError(message, ErrorSeverity.MEDIUM, context, error)
        handle_error(validation_error)


class TradingLogicErrorHandler:
    """Error handling utilities for trading logic operations"""
    
    @staticmethod
    def handle_lot_sync_error(error: Exception, context: Optional[Dict] = None) -> None:
        """Handle lot synchronization errors"""
        
        context = context or {}
        context['operation'] = 'lot_sync'
        
        message = f"Critical lot synchronization error: {error}"
        trading_error = TradingError(message, ErrorSeverity.CRITICAL, context, error)
        handle_error(trading_error)
    
    @staticmethod
    def handle_position_calculation_error(error: Exception, context: Optional[Dict] = None) -> None:
        """Handle position calculation errors"""
        
        context = context or {}
        context['operation'] = 'position_calculation'
        
        message = f"Position calculation error: {error}"
        trading_error = TradingError(message, ErrorSeverity.HIGH, context, error)
        handle_error(trading_error)
    
    @staticmethod
    def handle_file_operation_error(error: Exception, file_path: str, 
                                  operation: str, context: Optional[Dict] = None) -> None:
        """Handle file operation errors"""
        
        context = context or {}
        context['file_path'] = file_path
        context['operation'] = operation
        
        if isinstance(error, (IOError, OSError)):
            severity = ErrorSeverity.MEDIUM
            message = f"File system error during {operation} on {file_path}: {error}"
        elif isinstance(error, json.JSONDecodeError):
            severity = ErrorSeverity.HIGH
            message = f"JSON decode error in {file_path}: {error}"
        else:
            severity = ErrorSeverity.HIGH
            message = f"Unexpected error during {operation} on {file_path}: {error}"
        
        system_error = SystemError(message, severity, context, error)
        handle_error(system_error)


# ===================================================================
# CIRCUIT BREAKERS FOR CRITICAL SERVICES
# ===================================================================

# Circuit breakers for external services
alpaca_circuit_breaker = CircuitBreaker(
    failure_threshold=3,
    recovery_timeout=300,  # 5 minutes
    expected_exception=Exception
)

gemini_circuit_breaker = CircuitBreaker(
    failure_threshold=5,
    recovery_timeout=600,  # 10 minutes
    expected_exception=Exception
)

binance_circuit_breaker = CircuitBreaker(
    failure_threshold=3,
    recovery_timeout=180,  # 3 minutes
    expected_exception=Exception
)


# ===================================================================
# DECORATORS FOR COMMON PATTERNS
# ===================================================================

def alpaca_api_call(max_retries: int = 3):
    """Decorator for Alpaca API calls with retry and circuit breaker"""
    return with_retry(
        max_attempts=max_retries,
        delay=2.0,
        backoff_factor=2.0,
        exceptions=(Exception,),
        circuit_breaker=alpaca_circuit_breaker
    )


def gemini_api_call(max_retries: int = 3):
    """Decorator for Gemini API calls with retry and circuit breaker"""
    return with_retry(
        max_attempts=max_retries,
        delay=5.0,
        backoff_factor=2.0,
        exceptions=(Exception,),
        circuit_breaker=gemini_circuit_breaker
    )


def safe_json_operation(default_return=None):
    """Decorator for safe JSON operations"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except json.JSONDecodeError as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],
                    'kwargs': str(kwargs)[:200]
                }
                TradingLogicErrorHandler.handle_file_operation_error(
                    e, kwargs.get('path', 'unknown'), 'json_operation', context
                )
                return default_return
            except (IOError, OSError) as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],
                    'kwargs': str(kwargs)[:200]
                }
                TradingLogicErrorHandler.handle_file_operation_error(
                    e, kwargs.get('path', 'unknown'), 'file_operation', context
                )
                return default_return
        return wrapper
    return decorator


def safe_decimal_operation(default_return: Decimal = Decimal('0')):
    """Decorator for safe decimal operations"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except (ValueError, TypeError, ArithmeticError) as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],
                    'kwargs': str(kwargs)[:200]
                }
                error = DataError(
                    f"Decimal operation error in {func.__name__}: {e}",
                    ErrorSeverity.MEDIUM,
                    context,
                    e
                )
                handle_error(error)
                return default_return
        return wrapper
    return decorator
