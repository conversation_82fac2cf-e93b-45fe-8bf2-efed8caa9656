import pandas as pd

# Load the clean ML features file
df = pd.read_csv('btc_ml_features_clean.csv')

# Ensure numeric columns are treated as float
numeric_cols = ['open', 'high', 'low', 'close', 'volume']
for col in numeric_cols:
    if col in df.columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')

# Add SMA/EMA indicators
for period in [7, 14, 50]:
    df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
    df[f'ema_{period}'] = df['close'].ewm(span=period, adjust=False).mean()

# Add volatility (rolling std deviation of close price)
df['volatility_14'] = df['close'].rolling(window=14).std()

# Momentum: difference between close now and close n periods ago
df['momentum_10'] = df['close'] - df['close'].shift(10)

# MACD and Signal
ema12 = df['close'].ewm(span=12, adjust=False).mean()
ema26 = df['close'].ewm(span=26, adjust=False).mean()
df['macd'] = ema12 - ema26
df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()

# Drop rows with any NaNs created by indicators (keep dataset clean)
df = df.dropna()

# Save the enhanced file
df.to_csv('btc_ml_features_indicators.csv', index=False)
print(f"Enhanced ML features file saved as btc_ml_features_indicators.csv ({len(df)} rows).")
print(df.head())
