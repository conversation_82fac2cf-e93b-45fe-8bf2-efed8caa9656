import json
import os
from email_alert import send_alert
from datetime import date
import config

SAFETY_TRIGGERED = False
SAFETY_REASON = ""
SAFETY_DAY = None

def check_safety_net(acc_info):
    global SAFETY_TRIGGERED, SAFETY_REASON, SAFETY_DAY
    session_stats = {}
    today = date.today()
    if SAFETY_DAY != today:
        SAFETY_TRIGGERED = False
        SAFETY_REASON = ""
        SAFETY_DAY = today
        session_stats["equity_start"] = float(acc_info.get("equity", 0))

    start_equity = session_stats.get("equity_start")
    current_equity = float(acc_info.get("equity", 0))
    min_equity = config.MIN_ACCOUNT_EQUITY
    loss_limit = (start_equity or current_equity) * config.MAX_DAILY_LOSS_PCT

    if current_equity <= min_equity:
        SAFETY_TRIGGERED = True
        SAFETY_REASON = f"TRADING HALTED: Account equity (${current_equity:.2f}) fell below minimum (${min_equity:.2f})"
        send_alert(
            subject="🚨 Trading HALTED: Account Equity Too Low",
            message=SAFETY_REASON
        )
        return True

    if (start_equity is not None) and (current_equity <= start_equity - loss_limit):
        SAFETY_TRIGGERED = True
        SAFETY_REASON = f"TRADING HALTED: Daily loss exceeded {config.MAX_DAILY_LOSS_PCT*100:.2f}% ({start_equity-current_equity:.2f} USD loss)"
        send_alert(
            subject="🚨 Trading HALTED: Daily Loss Limit Exceeded",
            message=SAFETY_REASON
        )
        return True

    return False
