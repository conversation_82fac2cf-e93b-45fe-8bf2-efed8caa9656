import pandas as pd

# Load your big historical file
df = pd.read_csv('rsi_labeled_data.csv')

# Columns to keep for clean ML features file
keep_cols = [
    'open_time', 'open', 'high', 'low', 'close', 'volume',
    'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'label'
]

# Only keep those columns that exist in the file
keep_cols = [col for col in keep_cols if col in df.columns]
df_clean = df[keep_cols]

# Save the cleaned dataset
df_clean.to_csv('btc_ml_features_clean.csv', index=False)
print(f"Clean ML features file saved as btc_ml_features_clean.csv ({len(df_clean)} rows).")
print(df_clean.head())
