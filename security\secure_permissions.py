#!/usr/bin/env python3
"""
Secure File Permissions Script for Bitcoin AI Trading Bot
Sets appropriate file permissions for security-sensitive files
"""

import os
import stat
import logging
import platform
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class FilePermissionManager:
    """
    Manages file permissions for security-sensitive files
    """
    
    def __init__(self):
        """Initialize permission manager"""
        self.is_windows = platform.system() == "Windows"
        self.sensitive_files = [
            "security/credentials.enc",
            ".env.backup_*",
            "config.py",
            "security/credential_manager.py",
            "security/secure_config.py",
            "security/access_control.py"
        ]
        self.sensitive_directories = [
            "security/",
            "logs/"
        ]
    
    def set_file_permissions(self, file_path: str, mode: int = 0o600) -> bool:
        """
        Set file permissions (owner read/write only by default)
        
        Args:
            file_path: Path to the file
            mode: Permission mode (default: 0o600 - owner read/write only)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                logger.debug(f"File not found: {file_path}")
                return True  # Not an error if file doesn't exist
            
            if self.is_windows:
                # Windows has limited permission model
                # Just make file read-only for others
                try:
                    os.chmod(file_path, stat.S_IREAD | stat.S_IWRITE)
                    logger.debug(f"✅ Set Windows permissions for: {file_path}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not set Windows permissions for {file_path}: {e}")
            else:
                # Unix-like systems
                os.chmod(file_path, mode)
                logger.debug(f"✅ Set permissions {oct(mode)} for: {file_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to set permissions for {file_path}: {e}")
            return False
    
    def set_directory_permissions(self, dir_path: str, mode: int = 0o700) -> bool:
        """
        Set directory permissions (owner read/write/execute only by default)
        
        Args:
            dir_path: Path to the directory
            mode: Permission mode (default: 0o700 - owner full access only)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(dir_path):
                logger.debug(f"Directory not found: {dir_path}")
                return True
            
            if self.is_windows:
                # Windows has limited permission model
                logger.debug(f"✅ Windows directory permissions (limited): {dir_path}")
            else:
                # Unix-like systems
                os.chmod(dir_path, mode)
                logger.debug(f"✅ Set directory permissions {oct(mode)} for: {dir_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to set directory permissions for {dir_path}: {e}")
            return False
    
    def secure_all_files(self) -> Dict[str, Any]:
        """
        Secure all sensitive files and directories
        
        Returns:
            Dictionary with results
        """
        results = {
            "files_secured": 0,
            "files_failed": 0,
            "directories_secured": 0,
            "directories_failed": 0,
            "errors": []
        }
        
        print("🔒 Securing sensitive files and directories...")
        
        # Secure directories first
        for dir_path in self.sensitive_directories:
            if self.set_directory_permissions(dir_path):
                results["directories_secured"] += 1
                print(f"✅ Secured directory: {dir_path}")
            else:
                results["directories_failed"] += 1
                results["errors"].append(f"Failed to secure directory: {dir_path}")
        
        # Secure individual files
        for file_pattern in self.sensitive_files:
            if "*" in file_pattern:
                # Handle wildcard patterns
                import glob
                matching_files = glob.glob(file_pattern)
                for file_path in matching_files:
                    if self.set_file_permissions(file_path):
                        results["files_secured"] += 1
                        print(f"✅ Secured file: {file_path}")
                    else:
                        results["files_failed"] += 1
                        results["errors"].append(f"Failed to secure file: {file_path}")
            else:
                # Handle specific files
                if self.set_file_permissions(file_pattern):
                    results["files_secured"] += 1
                    print(f"✅ Secured file: {file_pattern}")
                else:
                    results["files_failed"] += 1
                    results["errors"].append(f"Failed to secure file: {file_pattern}")
        
        return results
    
    def check_permissions(self, file_path: str) -> Dict[str, Any]:
        """
        Check current file permissions
        
        Args:
            file_path: Path to check
        
        Returns:
            Dictionary with permission information
        """
        try:
            if not os.path.exists(file_path):
                return {"exists": False}
            
            file_stat = os.stat(file_path)
            mode = file_stat.st_mode
            
            return {
                "exists": True,
                "mode": mode,
                "octal": oct(mode & 0o777),
                "readable_by_owner": bool(mode & stat.S_IRUSR),
                "writable_by_owner": bool(mode & stat.S_IWUSR),
                "executable_by_owner": bool(mode & stat.S_IXUSR),
                "readable_by_group": bool(mode & stat.S_IRGRP),
                "writable_by_group": bool(mode & stat.S_IWGRP),
                "executable_by_group": bool(mode & stat.S_IXGRP),
                "readable_by_others": bool(mode & stat.S_IROTH),
                "writable_by_others": bool(mode & stat.S_IWOTH),
                "executable_by_others": bool(mode & stat.S_IXOTH),
                "is_secure": not (mode & (stat.S_IRGRP | stat.S_IWGRP | stat.S_IROTH | stat.S_IWOTH))
            }
            
        except Exception as e:
            logger.error(f"Error checking permissions for {file_path}: {e}")
            return {"exists": False, "error": str(e)}
    
    def audit_permissions(self) -> Dict[str, Any]:
        """
        Audit permissions of all sensitive files
        
        Returns:
            Dictionary with audit results
        """
        audit_results = {
            "secure_files": [],
            "insecure_files": [],
            "missing_files": [],
            "total_checked": 0
        }
        
        print("\n🔍 Auditing file permissions...")
        
        all_files = self.sensitive_files + [f"{d}*" for d in self.sensitive_directories]
        
        for file_pattern in all_files:
            if "*" in file_pattern:
                import glob
                matching_files = glob.glob(file_pattern)
                for file_path in matching_files:
                    self._audit_single_file(file_path, audit_results)
            else:
                self._audit_single_file(file_pattern, audit_results)
        
        return audit_results
    
    def _audit_single_file(self, file_path: str, audit_results: Dict[str, Any]):
        """Audit a single file's permissions"""
        audit_results["total_checked"] += 1
        perm_info = self.check_permissions(file_path)
        
        if not perm_info["exists"]:
            audit_results["missing_files"].append(file_path)
            print(f"⚠️ Missing: {file_path}")
        elif perm_info.get("is_secure", False) or self.is_windows:
            audit_results["secure_files"].append(file_path)
            print(f"✅ Secure: {file_path} ({perm_info.get('octal', 'N/A')})")
        else:
            audit_results["insecure_files"].append(file_path)
            print(f"❌ Insecure: {file_path} ({perm_info.get('octal', 'N/A')})")

def secure_bot_files():
    """Convenience function to secure all bot files"""
    manager = FilePermissionManager()
    return manager.secure_all_files()

def audit_bot_permissions():
    """Convenience function to audit bot file permissions"""
    manager = FilePermissionManager()
    return manager.audit_permissions()

def main():
    """Main function for standalone execution"""
    print("🔒 Bitcoin AI Trading Bot - File Permission Security")
    print("=" * 55)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    manager = FilePermissionManager()
    
    # First, audit current permissions
    print("\n📋 STEP 1: Auditing current permissions...")
    audit_results = manager.audit_permissions()
    
    # Show audit summary
    print(f"\n📊 Audit Summary:")
    print(f"   ✅ Secure files: {len(audit_results['secure_files'])}")
    print(f"   ❌ Insecure files: {len(audit_results['insecure_files'])}")
    print(f"   ⚠️ Missing files: {len(audit_results['missing_files'])}")
    print(f"   📁 Total checked: {audit_results['total_checked']}")
    
    # Secure files if needed
    if audit_results['insecure_files']:
        print(f"\n🔧 STEP 2: Securing {len(audit_results['insecure_files'])} insecure files...")
        results = manager.secure_all_files()
        
        print(f"\n📊 Security Results:")
        print(f"   ✅ Files secured: {results['files_secured']}")
        print(f"   ✅ Directories secured: {results['directories_secured']}")
        print(f"   ❌ Files failed: {results['files_failed']}")
        print(f"   ❌ Directories failed: {results['directories_failed']}")
        
        if results['errors']:
            print(f"\n⚠️ Errors encountered:")
            for error in results['errors']:
                print(f"   - {error}")
    else:
        print("\n✅ All files are already secure!")
    
    print("\n🎉 File permission security check complete!")

if __name__ == "__main__":
    main()
