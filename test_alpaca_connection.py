#!/usr/bin/env python3
"""
Quick test to diagnose Alpaca API connection and equity retrieval issue
"""

import logging
import config
import alpaca_service
from alpaca_trade_api.rest import REST

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_alpaca_connection():
    """Test Alpaca API connection and account info retrieval"""
    print("🔍 TESTING ALPACA API CONNECTION")
    print("=" * 50)
    
    try:
        # Initialize API client
        print("1. Initializing Alpaca API client...")
        api_client = REST(
            key_id=config.ALPACA_API_KEY_ID, 
            secret_key=config.ALPACA_SECRET_KEY, 
            base_url=config.ALPACA_BASE_URL, 
            api_version='v2'
        )
        print("✅ API client initialized successfully")
        
        # Test direct API call
        print("\n2. Testing direct API call...")
        account = api_client.get_account()
        print(f"✅ Direct API call successful")
        print(f"   Raw equity: {account.equity}")
        print(f"   Raw cash: {account.cash}")
        print(f"   Raw buying_power: {account.buying_power}")
        
        # Test our service wrapper
        print("\n3. Testing alpaca_service.get_account_info()...")
        account_info = alpaca_service.get_account_info(api_client)
        print(f"✅ Service wrapper result: {account_info}")
        
        # Compare values
        print("\n4. Comparing values...")
        print(f"   Direct equity: {float(account.equity)}")
        print(f"   Service equity: {account_info.get('equity', 'MISSING')}")
        print(f"   Match: {float(account.equity) == account_info.get('equity', -1)}")
        
        return account_info
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Full error details:")
        return None

if __name__ == "__main__":
    result = test_alpaca_connection()
    
    if result and result.get('equity', 0) > 0:
        print(f"\n🎉 SUCCESS: Alpaca API is working correctly!")
        print(f"   Your actual equity: ${result['equity']:.2f}")
    else:
        print(f"\n🚨 PROBLEM: Alpaca API is not returning correct equity data!")
        print(f"   This explains why your bot shows $0.00 equity")
