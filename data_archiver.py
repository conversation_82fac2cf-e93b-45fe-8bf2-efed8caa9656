import os
import pandas as pd
from datetime import datetime
import logging

# --- Configuration ---
# Configure logging for this script
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DataArchiver")

# Define how many days of recent data to keep in the "live" files.
# Any data older than this will be archived.
DAYS_TO_KEEP = 180  # Approximately 6 months

# List of files to manage
FILES_TO_MANAGE = [
    "trade_history.csv",
    "decision_log_full.csv"
]

def archive_and_prune(file_path: str):
    """
    Archives old data from a given file and prunes the file to keep it lean.
    """
    logger.info(f"Processing file: {file_path}...")

    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}. Skipping.")
        return

    try:
        # Load the entire dataset using pandas
        df = pd.read_csv(file_path)
        
        # Ensure the 'timestamp' column exists and is in datetime format
        if 'timestamp' not in df.columns:
            logger.error(f"'timestamp' column not found in {file_path}. Cannot process. Skipping.")
            return
        
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        logger.info(f"Loaded {len(df)} rows from {file_path}.")

        # Determine the cutoff date for archiving
        cutoff_date = datetime.utcnow() - pd.Timedelta(days=DAYS_TO_KEEP)
        
        # Split the dataframe into old data (to be archived) and new data (to be kept)
        df_to_archive = df[df['timestamp'] < cutoff_date]
        df_to_keep = df[df['timestamp'] >= cutoff_date]

        if df_to_archive.empty:
            logger.info("No old data to archive for this file.")
        else:
            logger.warning(f"Found {len(df_to_archive)} old rows to archive.")
            
            # Group old data by year for partitioning
            for year, group in df_to_archive.groupby(df_to_archive['timestamp'].dt.year):
                # Define the compressed archive filename
                archive_filename = f"{file_path.replace('.csv', '')}_archive_{year}.csv.gz"
                
                logger.info(f"Archiving {len(group)} rows for year {year} to {archive_filename}...")
                
                # Save the group to a compressed CSV file
                # The 'compression='gz'' argument handles the compression automatically.
                group.to_csv(archive_filename, index=False, compression='gzip')
                logger.info(f"Successfully created compressed archive: {archive_filename}")

        # Now, safely overwrite the original file with only the recent data
        logger.info(f"Pruning original file to keep last {DAYS_TO_KEEP} days ({len(df_to_keep)} rows).")
        df_to_keep.to_csv(file_path, index=False)
        logger.warning(f"Successfully pruned {file_path}.")

    except Exception as e:
        logger.error(f"An error occurred while processing {file_path}: {e}", exc_info=True)


def main():
    """
    Main function to run the archiving and pruning process for all managed files.
    """
    logger.info("--- Starting Data Archiving and Pruning Maintenance ---")
    for file_name in FILES_TO_MANAGE:
        archive_and_prune(file_name)
    logger.info("--- Maintenance Process Complete ---")


if __name__ == "__main__":
    main()