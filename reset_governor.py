#!/usr/bin/env python3
"""
Manual Governor Reset Utility

This utility allows manual reset of the daily target governor when it gets stuck.
The governor should reset automatically, but this provides a manual override.

Usage: python reset_governor.py
"""

import json
import os
import logging
from datetime import datetime, timezone
from bot_state import load_bot_state, save_bot_state

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def reset_governor():
    """Reset the daily target governor manually."""
    try:
        # Load current bot state
        bot_state_data = load_bot_state()
        
        # Check current governor status
        governor_active = bot_state_data.get("pause_trading_today", False)
        last_trade = bot_state_data.get("last_trade_timestamp", "Never")
        
        print("=" * 60)
        print("🤖 BITCOIN AI TRADING BOT - GOVERNOR RESET UTILITY")
        print("=" * 60)
        print(f"Current Governor Status: {'🔴 ACTIVE (BLOCKING TRADES)' if governor_active else '🟢 INACTIVE'}")
        print(f"Last Trade Timestamp  : {last_trade}")
        print(f"Bot State File        : bot_state.json")
        print("-" * 60)
        
        if governor_active:
            print("⚠️  GOVERNOR IS CURRENTLY ACTIVE")
            print("   This means the bot believes it has hit its daily profit target.")
            print("   However, the bot should trade freely following its core safety rules.")
            print()
            
            # Ask for confirmation
            response = input("Do you want to reset the governor? (y/N): ").strip().lower()
            
            if response in ['y', 'yes']:
                # Reset the governor
                bot_state_data["pause_trading_today"] = False
                save_bot_state(bot_state_data)
                
                logger.info("✅ Governor has been reset successfully!")
                print("✅ Governor has been reset successfully!")
                print("   The bot can now trade freely following its core safety rules.")
                print("   Core rule: NEVER sell at a loss after considering exchange fees.")
                
            else:
                print("❌ Governor reset cancelled.")
                
        else:
            print("✅ Governor is already inactive - no reset needed.")
            print("   The bot is free to trade following its core safety rules.")
        
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Error resetting governor: {e}")
        print(f"❌ Error resetting governor: {e}")

if __name__ == "__main__":
    reset_governor()
