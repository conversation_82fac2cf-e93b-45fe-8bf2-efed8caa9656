{"report_metadata": {"type": "daily", "generated_at": "2025-08-12T20:44:54.938312", "analysis_timestamp": "2025-08-12T20:44:54.930308", "version": "1.0"}, "analysis_results": {"analysis_timestamp": "2025-08-12T20:44:54.930308", "trade_analysis": {"position_analysis": {"total_lots": 1, "total_value": 537.3067686449978, "average_lot_value": 537.3067686449978, "value_distribution": "uniform"}, "lot_performance": {"total_lots": 1, "performance_summary": "Requires current market data for P&L calculation"}, "strategy_effectiveness": {"strategy_distribution": {"dip_accumulation": 1}, "strategy_values": {"dip_accumulation": 537.3067686449978}, "most_used_strategy": "dip_accumulation"}, "holding_period_analysis": {"average_holding_period": 38.9143699475, "median_holding_period": 38.9143699475, "max_holding_period": 38.9143699475, "min_holding_period": 38.9143699475, "total_lots_analyzed": 1}}, "decision_analysis": {"decision_distribution": {"total_decisions": 0, "decision_counts": {}, "decision_percentages": {}}, "decision_timing": {"total_decisions": 0, "analysis_note": "Timing analysis requires time series implementation"}, "decision_quality": {"total_decisions": 0, "total_actions": 0, "decision_to_action_ratio": 0}, "error_patterns": {"total_errors": 5, "error_analysis": "Error pattern analysis requires error categorization"}}, "performance_metrics": {"profitability": {"total_lots": 1, "total_position_value": 537.3067686449979, "average_lot_size": 537.3067686449978, "unrealized_pnl": 0}, "risk_metrics": {"position_concentration": 0.0, "strategy_diversification": 0.0, "holding_period_risk": {"average_holding_hours": 0.0, "max_holding_hours": 0.0, "holding_period_variance": 0.0, "long_term_exposure_ratio": 0.0}}, "efficiency_metrics": {}, "benchmark_comparison": {}}, "pattern_recognition": {"temporal_patterns": {"hourly_patterns": "Temporal analysis requires time-series data", "daily_patterns": "Daily pattern analysis not yet implemented", "weekly_patterns": "Weekly pattern analysis not yet implemented", "seasonal_patterns": "Seasonal analysis requires longer data history"}, "strategy_patterns": {"strategy_effectiveness": "Strategy pattern analysis requires historical data", "strategy_timing": "Timing analysis not yet implemented", "strategy_correlation": "Correlation analysis requires market data"}, "market_condition_patterns": {"market_regime": "neutral", "volatility_pattern": "low", "activity_pattern": "normal", "rsi_level": 50, "macd_level": 0, "trend_strength": "moderate", "market_efficiency": "normal"}, "decision_patterns": {"decision_consistency": "consistent", "confidence_pattern": "stable", "recent_decision": "UNKNOWN", "recent_confidence": "MEDIUM", "decision_frequency": "normal", "risk_appetite": "moderate"}}, "risk_analysis": {"exposure_analysis": {"total_exposure": 537.3067686449978, "average_lot_exposure": 537.3067686449978, "largest_lot_exposure": 537.3067686449978}, "concentration_risk": {"strategy_concentration": {"dip_accumulation": 537.3067686449978}, "max_strategy_exposure": 537.3067686449978, "strategy_count": 1}, "strategy_risk": {}}, "efficiency_analysis": {"decision_efficiency": {"decision_to_action_ratio": 0, "total_decisions": 0, "total_actions": 0}, "execution_efficiency": {}, "error_efficiency": {"error_rate": 0, "total_errors": 5, "errors_per_hour": 0}}, "insights": {"key_findings": ["Currently managing 1 open positions", "Average holding period: 38.9 hours"], "recommendations": ["Continue monitoring performance patterns", "Consider implementing automated alerts for significant changes"], "performance_alerts": ["Low cash availability for new trades"], "optimization_opportunities": ["Monitor for strategy performance patterns", "Implement automated rebalancing alerts"]}}, "report_summary": {"key_metrics": {"total_positions": 1, "portfolio_value": 537.3067686449978, "average_position_size": 537.3067686449978}, "performance_highlights": ["Currently managing 1 open positions", "Average holding period: 38.9 hours"], "risk_indicators": ["Low cash availability for new trades"], "recommendations": ["Continue monitoring performance patterns", "Consider implementing automated alerts for significant changes"]}}