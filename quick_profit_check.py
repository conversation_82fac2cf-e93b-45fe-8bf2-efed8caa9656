#!/usr/bin/env python3
"""
Quick Profit Check and Override

A simple script to quickly check profit opportunities and execute manual sells.
"""

import sys
import json
from manual_profit_override import ManualProfitOverride

def main():
    override = ManualProfitOverride()
    
    print("=== BITCOIN AI BOT - PROFIT OPPORTUNITY CHECK ===\n")
    
    # Check current opportunity
    opportunity = override.check_profit_opportunity()
    
    if 'error' in opportunity:
        print(f"❌ Error: {opportunity['error']}")
        return
    
    # Display current status
    print(f"📊 Current Price: ${opportunity['current_price']:,.2f}")
    print(f"💰 Daily P/L: ${opportunity['daily_pnl']:,.2f}")
    print(f"📈 Total Unrealized P/L: ${opportunity['total_unrealized_pl']:,.2f}")
    print(f"🎯 RSI: {opportunity['rsi']:.1f}")
    print(f"📊 MACD Histogram: {opportunity['macd_histogram']:.2f}")
    print(f"🏆 Opportunity Score: {opportunity['opportunity_score']:.0f}/100")
    print(f"💡 Recommendation: {opportunity['recommendation']}")
    print(f"📦 Profitable Lots: {opportunity['profitable_lots_count']}/{opportunity['total_lots_count']}")
    
    # Color-coded alerts
    score = opportunity['opportunity_score']
    daily_pnl = opportunity['daily_pnl']
    
    if score >= 80 or daily_pnl >= 30:
        print("\n🚨 STRONG PROFIT-TAKING OPPORTUNITY DETECTED! 🚨")
        print("Consider executing manual sell immediately!")
    elif score >= 60 or daily_pnl >= 20:
        print("\n⚠️  GOOD PROFIT-TAKING OPPORTUNITY ⚠️")
        print("Consider taking some profits.")
    elif score >= 40 or daily_pnl >= 10:
        print("\n💡 MODERATE OPPORTUNITY")
        print("Watch for stronger signals.")
    else:
        print("\n✅ NO IMMEDIATE ACTION NEEDED")
    
    # Interactive prompt for manual action
    if len(sys.argv) == 1:  # No command line arguments
        print("\n" + "="*50)
        print("MANUAL ACTIONS:")
        print("1. Take 25% profits")
        print("2. Take 50% profits") 
        print("3. Emergency sell 75%")
        print("4. Exit")
        
        try:
            choice = input("\nEnter choice (1-4): ").strip()
            
            if choice == "1":
                execute_sell(override, 25, "25% profit taking")
            elif choice == "2":
                execute_sell(override, 50, "50% profit taking")
            elif choice == "3":
                confirm = input("⚠️  EMERGENCY SELL 75%? Type 'CONFIRM' to proceed: ")
                if confirm == "CONFIRM":
                    execute_sell(override, 75, "Emergency 75% sell")
                else:
                    print("❌ Emergency sell cancelled.")
            elif choice == "4":
                print("👋 Exiting...")
            else:
                print("❌ Invalid choice.")
                
        except KeyboardInterrupt:
            print("\n👋 Exiting...")

def execute_sell(override, percentage, reason):
    """Execute a manual sell with confirmation"""
    print(f"\n🔄 Executing {percentage}% sell...")
    
    result = override.force_sell(percentage, reason)
    
    if result.get('success'):
        print(f"✅ SUCCESS!")
        print(f"💰 Sold {result['lots_sold']} lots")
        print(f"💵 Total value: ${result['total_sell_value']:,.2f}")
        print(f"📧 Alert email sent")
    else:
        print(f"❌ FAILED: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
