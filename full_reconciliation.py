import os
import json
import logging
from datetime import datetime, timezone
from decimal import Decimal
import pandas as pd
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import APIError

# Import config from the parent directory
import config

# --- CONFIGURATION ---
# Set up logging to see the progress
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- FILE PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOTS_LEDGER_FILE = os.path.join(BASE_DIR, "open_lots.json")
ACTIVE_ORDERS_FILE = os.path.join(BASE_DIR, "active_orders.json")
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
SESSION_STATS_FILE = os.path.join(BASE_DIR, "session_stats.json")


def calculate_fees(qty, price):
    """Helper to calculate fees based on a percentage."""
    return (qty * price * config.FEE_PERCENT).quantize(Decimal("0.00000001"))

def fetch_all_historical_orders(api):
    """
    Fetches all historical orders from Alpaca, handling pagination.
    """
    logging.info("Fetching all historical orders from Alpaca...")
    
    all_orders = []
    # Fetch orders in chunks of 500 (the maximum)
    try:
        orders = api.list_orders(status='all', limit=500, nested=True)
        all_orders.extend(orders)
        
        # Keep fetching until we get a batch with less than 500 orders
        while len(orders) == 500:
            last_order_time = orders[-1].submitted_at
            # Format the timestamp to the specific RFC3339 format required by the API
            formatted_time = last_order_time.isoformat().replace('+00:00', 'Z')
            orders = api.list_orders(status='all', limit=500, until=formatted_time, nested=True)
            all_orders.extend(orders)
            logging.info(f"Fetched another {len(orders)} orders, up to {formatted_time}")

    except APIError as e:
        logging.error(f"Alpaca API error while fetching orders: {e}")
        return None
        
    logging.info(f"Total historical orders fetched: {len(all_orders)}")
    return all_orders

def recalculate_stats_from_history():
    """
    Recalculates win/loss, P/L, and streak stats from the trade_history.csv.
    """
    if not os.path.exists(TRADE_HISTORY_FILE):
        logging.warning("Trade history file not found. Cannot recalculate stats.")
        return None

    logging.info("Recalculating all performance statistics from trade history...")
    
    try:
        df = pd.read_csv(TRADE_HISTORY_FILE)
        if 'realized_pl' not in df.columns:
            logging.error("Trade history CSV is missing 'realized_pl' column.")
            return None

        sells = df[df['action'].str.contains("SELL", na=False)].copy()
        sells['realized_pl'] = pd.to_numeric(sells['realized_pl'], errors='coerce').fillna(0)

        total_realized_pl = Decimal(str(sells['realized_pl'].sum()))
        win_count = int(sells[sells['realized_pl'] > 0].shape[0])
        loss_count = int(sells[sells['realized_pl'] < 0].shape[0])

        # Recalculate streaks
        win_streak = 0
        loss_streak = 0
        current_win_streak = 0
        current_loss_streak = 0
        for pl in sells['realized_pl']:
            if pl > 0:
                current_win_streak += 1
                current_loss_streak = 0
            elif pl < 0:
                current_loss_streak += 1
                current_win_streak = 0
            win_streak = max(win_streak, current_win_streak)
            loss_streak = max(loss_streak, current_loss_streak)

        stats = {
            "net_pl": str(total_realized_pl),
            "win_count": win_count,
            "loss_count": loss_count,
            "win_streak": win_streak, # This is max streak, not current
            "loss_streak": loss_streak, # This is max streak, not current
        }
        
        logging.info(f"Recalculation complete: Realized P/L: ${total_realized_pl:.2f}, "
                     f"Wins: {win_count}, Losses: {loss_count}")
        return stats

    except Exception as e:
        logging.error(f"Failed to recalculate stats from trade history: {e}")
        return None

def rebuild_state_from_orders(orders):
    """
    Rebuilds the open_lots.json and active_orders.json files from a complete
    list of historical orders.
    """
    if not orders:
        logging.error("No orders provided to rebuild state. Aborting.")
        return

    logging.info("Rebuilding state from historical orders...")

    # Sort orders chronologically by submission time
    orders.sort(key=lambda o: o.submitted_at)

    open_lots = {}
    active_orders = []

    for order in orders:
        order_id = str(order.id)
        status = order.status
        
        # 1. Identify genuinely active orders for active_orders.json
        if status in ['new', 'partially_filled', 'held', 'accepted', 'pending_new']:
            active_orders.append(order._raw)
            logging.info(f"Found genuinely active order: {order_id} (Status: {status})")

        # 2. Rebuild lots from 'filled' buy and sell orders
        if status == 'filled':
            qty = Decimal(order.filled_qty)
            price = Decimal(order.filled_avg_price)
            
            if order.side == 'buy':
                fee = calculate_fees(qty, price)
                cost_basis = ((price * qty) + fee) / qty
                
                # Create a new lot
                open_lots[order_id] = {
                    "lot_id": order_id,
                    "buy_order_id": order_id,
                    "buy_timestamp": order.submitted_at.isoformat(),
                    "original_qty": str(qty),
                    "remaining_qty": str(qty),
                    "buy_price": str(price),
                    "buy_fee_usd": str(fee),
                    "cost_basis_per_unit": str(cost_basis),
                    "strategy_type": "HISTORICAL_RECON"
                }
                logging.info(f"Reconstructed BUY lot: {order_id} (Qty: {qty})")

            elif order.side == 'sell':
                # This logic assumes sells are FIFO against the reconstructed lots
                sell_qty_remaining = qty
                
                # Sort lots by time to ensure FIFO
                sorted_lot_ids = sorted(open_lots.keys(), key=lambda k: open_lots[k]['buy_timestamp'])

                for lot_id in sorted_lot_ids:
                    if sell_qty_remaining <= 0:
                        break
                    
                    lot = open_lots[lot_id]
                    lot_qty = Decimal(lot['remaining_qty'])
                    
                    if lot_qty > 0:
                        qty_to_sell_from_lot = min(lot_qty, sell_qty_remaining)
                        
                        lot['remaining_qty'] = str(lot_qty - qty_to_sell_from_lot)
                        sell_qty_remaining -= qty_to_sell_from_lot
                        
                        logging.info(f"Applied SELL {order_id} to lot {lot_id}. "
                                     f"Sold {qty_to_sell_from_lot}, remaining in lot: {lot['remaining_qty']}")

    # Filter out empty lots
    final_lots = [lot for lot in open_lots.values() if Decimal(lot['remaining_qty']) > 0]
    
    # --- SAVE THE REBUILT FILES ---
    logging.info(f"Saving {len(final_lots)} rebuilt lots to {LOTS_LEDGER_FILE}")
    with open(LOTS_LEDGER_FILE, 'w') as f:
        json.dump(final_lots, f, indent=4)

    logging.info(f"Saving {len(active_orders)} genuinely active orders to {ACTIVE_ORDERS_FILE}")
    with open(ACTIVE_ORDERS_FILE, 'w') as f:
        json.dump(active_orders, f, indent=4)
        
    logging.info("State reconstruction complete.")

def main():
    """Main function to run the reconciliation."""
    api = tradeapi.REST(config.ALPACA_API_KEY_ID, config.ALPACA_SECRET_KEY, base_url=config.ALPACA_BASE_URL)
    
    # First, ensure the bot is not running.
    input("Please ensure the main trading bot is stopped, then press Enter to continue...")
    
    all_orders = fetch_all_historical_orders(api)
    
    if all_orders:
        rebuild_state_from_orders(all_orders)
        
        # Now, also recalculate the session stats
        recalculated_stats = recalculate_stats_from_history()
        
        if recalculated_stats:
            # Load existing stats to preserve non-recalculated values
            try:
                with open(SESSION_STATS_FILE, 'r') as f:
                    session_stats = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                session_stats = {} # Start fresh if it doesn't exist or is corrupt
            
            # Update with the newly recalculated values
            session_stats.update(recalculated_stats)
            
            # Save the updated stats file
            logging.info(f"Saving updated session stats to {SESSION_STATS_FILE}")
            with open(SESSION_STATS_FILE, 'w') as f:
                json.dump(session_stats, f, indent=4)
        
        logging.info("Reconciliation successful. You may now restart the main bot.")
    else:
        logging.error("Failed to fetch historical orders. State has not been changed.")

if __name__ == "__main__":
    main()
