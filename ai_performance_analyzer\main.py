"""
AI Performance Analyzer - Main Runner
====================================

Main entry point for the AI Performance Analyzer system.
Orchestrates data collection, analysis, and report generation.
"""

import sys
import json
import logging
import argparse
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from ai_performance_analyzer.data_collector import PerformanceDataCollector
from ai_performance_analyzer.analysis_engine import AnalysisEngine
from ai_performance_analyzer.report_generator import ReportGenerator
from ai_performance_analyzer.config import LOGGING, REPORTS_DIR, INSIGHTS_DIR
from ai_performance_analyzer.utils import setup_logger

class AIPerformanceAnalyzer:
    """Main orchestrator for the AI Performance Analyzer system."""
    
    def __init__(self):
        self.logger = setup_logger("AIPerformanceAnalyzer", LOGGING)
        self.data_collector = PerformanceDataCollector()
        self.analysis_engine = AnalysisEngine()
        self.report_generator = ReportGenerator()
        
    def run_daily_analysis(self) -> Dict[str, str]:
        """Run complete daily performance analysis."""
        self.logger.info("Starting daily performance analysis...")
        
        try:
            # Step 1: Collect performance data
            self.logger.info("Collecting performance data...")
            collected_data = self.data_collector.collect_all_data()
            
            # Step 2: Analyze the data
            self.logger.info("Analyzing performance data...")
            analysis_results = self.analysis_engine.analyze_performance_data(collected_data)
            
            # Step 3: Generate reports
            self.logger.info("Generating performance reports...")
            report_files = self.report_generator.generate_daily_report(analysis_results)
            
            # Step 4: Log summary
            self._log_analysis_summary(analysis_results)
            
            self.logger.info("Daily analysis completed successfully")
            return report_files
            
        except Exception as e:
            self.logger.error(f"Error during daily analysis: {e}")
            raise
    
    def run_weekly_analysis(self) -> Dict[str, str]:
        """Run weekly performance analysis."""
        self.logger.info("Starting weekly performance analysis...")
        
        try:
            # Collect data for the past week
            weekly_data = self._collect_weekly_data()
            
            # Generate weekly report
            report_files = self.report_generator.generate_weekly_report(weekly_data)
            
            self.logger.info("Weekly analysis completed successfully")
            return report_files
            
        except Exception as e:
            self.logger.error(f"Error during weekly analysis: {e}")
            raise
    
    def run_continuous_monitoring(self, interval_hours: int = 24):
        """Run continuous performance monitoring."""
        self.logger.info(f"Starting continuous monitoring (every {interval_hours} hours)...")
        
        try:
            import time
            
            while True:
                try:
                    # Run daily analysis
                    report_files = self.run_daily_analysis()
                    
                    # Log generated files
                    self.logger.info(f"Generated {len(report_files)} report files")
                    for report_type, filepath in report_files.items():
                        self.logger.info(f"  {report_type}: {Path(filepath).name}")
                    
                    # Wait for next interval
                    self.logger.info(f"Waiting {interval_hours} hours until next analysis...")
                    time.sleep(interval_hours * 3600)  # Convert hours to seconds
                    
                except KeyboardInterrupt:
                    self.logger.info("Continuous monitoring stopped by user")
                    break
                except Exception as e:
                    self.logger.error(f"Error in continuous monitoring: {e}")
                    # Continue monitoring despite errors
                    time.sleep(300)  # Wait 5 minutes before retrying
                    
        except Exception as e:
            self.logger.error(f"Fatal error in continuous monitoring: {e}")
            raise
    
    def generate_performance_summary(self) -> str:
        """Generate a quick performance summary."""
        try:
            # Collect current data
            collected_data = self.data_collector.collect_all_data()
            
            # Quick analysis
            trade_data = collected_data.get("trade_data", {})
            trade_summary = trade_data.get("trade_summary", {})
            
            summary = f"""
AI TRADING BOT - PERFORMANCE SUMMARY
===================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

CURRENT POSITIONS:
• Total Open Lots: {trade_summary.get('total_lots', 0)}
• Portfolio Value: ${trade_summary.get('total_value', 0):,.2f}
• Average Holding Period: {trade_summary.get('average_holding_period_hours', 0):.1f} hours

STRATEGY DISTRIBUTION:
"""
            
            lot_types = trade_summary.get('lot_types', {})
            for strategy, count in lot_types.items():
                summary += f"• {strategy}: {count} lots\n"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error generating performance summary: {e}")
            return f"Error generating summary: {e}"
    
    def _collect_weekly_data(self) -> List[Dict[str, Any]]:
        """Collect data for weekly analysis."""
        # For now, just return current data
        # In a full implementation, this would collect historical daily analyses
        current_data = self.data_collector.collect_all_data()
        current_analysis = self.analysis_engine.analyze_performance_data(current_data)
        return [current_analysis]
    
    def _log_analysis_summary(self, analysis_results: Dict[str, Any]):
        """Log a summary of the analysis results."""
        try:
            trade_analysis = analysis_results.get("trade_analysis", {})
            position_analysis = trade_analysis.get("position_analysis", {})
            
            if position_analysis:
                self.logger.info(f"Analysis Summary:")
                self.logger.info(f"  Total Positions: {position_analysis.get('total_lots', 0)}")
                self.logger.info(f"  Portfolio Value: ${position_analysis.get('total_value', 0):,.2f}")
                self.logger.info(f"  Average Position: ${position_analysis.get('average_lot_value', 0):,.2f}")
            
            # Log key insights
            insights = analysis_results.get("insights", {})
            key_findings = insights.get("key_findings", [])
            
            if key_findings:
                self.logger.info("Key Findings:")
                for finding in key_findings[:3]:  # Log top 3 findings
                    self.logger.info(f"  • {finding}")
                    
        except Exception as e:
            self.logger.error(f"Error logging analysis summary: {e}")

def main():
    """Main entry point for the AI Performance Analyzer."""
    parser = argparse.ArgumentParser(description="AI Performance Analyzer for Bitcoin Trading Bot")
    parser.add_argument("--mode", choices=["daily", "weekly", "continuous", "summary"], 
                       default="daily", help="Analysis mode to run")
    parser.add_argument("--interval", type=int, default=24, 
                       help="Interval in hours for continuous monitoring (default: 24)")
    
    args = parser.parse_args()
    
    try:
        analyzer = AIPerformanceAnalyzer()
        
        if args.mode == "daily":
            print("🚀 Running Daily Performance Analysis...")
            report_files = analyzer.run_daily_analysis()
            
            print("✅ Daily analysis completed!")
            print(f"📁 Generated {len(report_files)} report files:")
            for report_type, filepath in report_files.items():
                print(f"   • {report_type}: {Path(filepath).name}")
            
        elif args.mode == "weekly":
            print("📊 Running Weekly Performance Analysis...")
            report_files = analyzer.run_weekly_analysis()
            
            print("✅ Weekly analysis completed!")
            print(f"📁 Generated {len(report_files)} report files:")
            for report_type, filepath in report_files.items():
                print(f"   • {report_type}: {Path(filepath).name}")
            
        elif args.mode == "continuous":
            print(f"🔄 Starting Continuous Monitoring (every {args.interval} hours)...")
            print("Press Ctrl+C to stop monitoring")
            analyzer.run_continuous_monitoring(args.interval)
            
        elif args.mode == "summary":
            print("📋 Generating Performance Summary...")
            summary = analyzer.generate_performance_summary()
            print(summary)
        
        print(f"\n📁 Reports saved to: {REPORTS_DIR}")
        print(f"📁 Insights saved to: {INSIGHTS_DIR}")
        
    except KeyboardInterrupt:
        print("\n👋 Analysis stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
