# Bitcoin AI Trading Bot - Secure Startup Script (PowerShell)
# This script ensures the virtual environment is used correctly

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "   Bitcoin AI Trading Bot - Secure Startup" -ForegroundColor Yellow
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔐 Starting bot with secure configuration..." -ForegroundColor Green
Write-Host "📁 Using virtual environment: venv\Scripts\python.exe" -ForegroundColor Blue
Write-Host ""
Write-Host "💡 PASSWORD TIPS:" -ForegroundColor Yellow
Write-Host "   • Master password: SecureBitcoinBot2025!" -ForegroundColor White
Write-Host "   • You can copy-paste this password" -ForegroundColor White
Write-Host "   • Password field won't show characters (normal security)" -ForegroundColor White
Write-Host "   • Just paste and press Enter" -ForegroundColor White
Write-Host ""

# Change to the script directory
Set-Location $PSScriptRoot

# Check if virtual environment exists
if (-not (Test-Path "venv\Scripts\python.exe")) {
    Write-Host "❌ Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please ensure the venv folder exists in the bot directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Run the bot using the virtual environment Python
try {
    & "venv\Scripts\python.exe" "main_bot.py"
}
catch {
    Write-Host "❌ Error starting bot: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "   Bot has stopped. Press any key to exit." -ForegroundColor Yellow
Write-Host "========================================================" -ForegroundColor Cyan
Read-Host
