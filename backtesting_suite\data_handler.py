# Data handler for loading historical data
import pandas as pd
import pandas_ta as ta
import os

class DataHandler:
    """
    Loads historical market data from a CSV file and provides it for backtesting.
    """
    def __init__(self, data_file_path: str, base_dir: str = ''):
        """
        Initializes the DataHandler.

        Args:
            data_file_path (str): The path to the CSV data file.
            base_dir (str): The base directory of the project.
        """
        self.data_file_path = os.path.join(base_dir, data_file_path)
        self.data = None
        self.iterator = None

    def load_data(self):
        """
        Loads and prepares the data from the CSV file.
        """
        try:
            print(f"Loading data from: {self.data_file_path}")
            # Define the specific columns we need to prevent loading bad data
            self.data = pd.read_csv(
                self.data_file_path,
                header=0,
                parse_dates=['timestamp']
            )
            # Set the timestamp as the index
            self.data.set_index('timestamp', inplace=True)
            self.iterator = self.data.iterrows()
            print(f"Successfully loaded {len(self.data)} rows of data.")
        except FileNotFoundError:
            print(f"Error: Data file not found at {self.data_file_path}")
            self.data = pd.DataFrame()
        except Exception as e:
            print(f"An error occurred while loading data: {e}")
            self.data = pd.DataFrame()

    def calculate_indicators(self):
        """
        Calculates all necessary technical indicators using pandas-ta
        and appends them to the dataframe.
        """
        if self.data is None or self.data.empty:
            print("Error: Data not loaded. Cannot calculate indicators.")
            return

        print("Calculating technical indicators...")
        # Calculate RSI
        self.data.ta.rsi(length=14, append=True)
        # Calculate MACD
        self.data.ta.macd(fast=12, slow=26, signal=9, append=True)
        # Calculate ATR
        self.data.ta.atr(length=14, append=True)
        
        # Rename columns to match the existing bot's expectations
        self.data.rename(columns={
            "RSI_14": "rsi",
            "MACD_12_26_9": "macd",
            "MACDh_12_26_9": "macd_hist",
            "MACDs_12_26_9": "macd_signal",
            "ATRr_14": "atr"
        }, inplace=True)
        
        # Calculate other custom indicators your bot uses
        self.data['rsi_slope'] = self.data['rsi'].diff()
        self.data['volume_spike'] = self.data['volume'] / self.data['volume'].rolling(window=30).mean()
        
        print("Finished calculating indicators.")
        # Drop rows with NaN values that are generated by the indicator calculations
        self.data.dropna(inplace=True)
        self.iterator = self.data.iterrows()


    def get_next_tick(self):
        """
        Returns the next data point (tick) from the historical data.

        Returns:
            A tuple containing the timestamp and a series with the OHLCV and indicator data,
            or (None, None) if the data has been exhausted.
        """
        try:
            timestamp, tick_data = next(self.iterator)
            return timestamp, tick_data
        except (StopIteration, TypeError):
            return None, None
