@echo off
REM Bitcoin AI Trading Bot - Easy Startup (No Password Prompt)
REM This script sets the master password as an environment variable

echo.
echo ========================================================
echo   Bitcoin AI Trading Bot - Easy Startup
echo ========================================================
echo.
echo 🔐 Starting bot with automatic authentication...
echo 📁 Using virtual environment: venv\Scripts\python.exe
echo.

REM Change to the bot directory
cd /d "%~dp0"

REM Set the master password as environment variable (avoids prompt)
set TRADING_BOT_MASTER_PASSWORD=SecureBitcoinBot2025!

REM Run the bot using the virtual environment Python
venv\Scripts\python.exe main_bot.py

echo.
echo ========================================================
echo   Bo<PERSON> has stopped. Press any key to exit.
echo ========================================================
pause > nul
