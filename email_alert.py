import smtplib
import logging
import time
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ip<PERSON>
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from collections import defaultdict, deque
from enum import Enum
import json

# ======== CONFIGURATION ========
SMTP_HOST = "127.0.0.1"          # From ProtonMail Bridge
SMTP_PORT = 1025                 # From ProtonMail Bridge
USERNAME = "<EMAIL>"     # Replace with the username from your bridge screenshot
PASSWORD = "mbqjdmQ-MoTn4TMEQFNvBw"  # <<---- Put your bridge password here!
FROM_EMAIL = "<EMAIL>"   # Replace with your ProtonMail address
TO_EMAIL = "<EMAIL>"     # Where you want to receive alerts
# ===============================

logger = logging.getLogger('EmailAlert')


class AlertPriority(Enum):
    """Alert priority levels for escalation"""
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    URGENT = "URGENT"


class AlertCategory(Enum):
    """Alert categories for organization"""
    SYSTEM = "SYSTEM"
    TRADING = "TRADING"
    API = "API"
    SECURITY = "SECURITY"
    PERFORMANCE = "PERFORMANCE"


class EnhancedAlertSystem:
    """Enhanced alert system with categorization, rate limiting, and escalation"""

    def __init__(self):
        self.alert_history = defaultdict(deque)  # category -> deque of timestamps
        self.alert_counts = defaultdict(int)     # category -> count
        self.last_digest_time = defaultdict(float)  # category -> timestamp

        # Rate limits: (max_alerts, window_seconds)
        self.rate_limits = {
            AlertPriority.URGENT: (10, 300),    # 10 alerts per 5 minutes
            AlertPriority.HIGH: (5, 600),       # 5 alerts per 10 minutes
            AlertPriority.NORMAL: (3, 1800),    # 3 alerts per 30 minutes
            AlertPriority.LOW: (2, 3600),       # 2 alerts per hour
        }

        # Digest intervals (seconds)
        self.digest_intervals = {
            AlertCategory.SYSTEM: 1800,      # 30 minutes
            AlertCategory.TRADING: 3600,     # 1 hour
            AlertCategory.API: 1800,         # 30 minutes
            AlertCategory.SECURITY: 300,     # 5 minutes
            AlertCategory.PERFORMANCE: 3600, # 1 hour
        }

    def should_send_alert(self, priority: AlertPriority, category: AlertCategory) -> bool:
        """Check if an alert should be sent based on rate limits"""
        key = f"{priority.value}_{category.value}"
        now = time.time()
        max_alerts, window_seconds = self.rate_limits[priority]

        # Clean old entries
        while self.alert_history[key] and now - self.alert_history[key][0] > window_seconds:
            self.alert_history[key].popleft()

        # Check if we can send
        if len(self.alert_history[key]) < max_alerts:
            self.alert_history[key].append(now)
            return True

        return False

    def should_send_digest(self, category: AlertCategory) -> bool:
        """Check if a digest should be sent for accumulated alerts"""
        now = time.time()
        last_digest = self.last_digest_time.get(category.value, 0)
        interval = self.digest_intervals[category]

        return (now - last_digest) >= interval and self.alert_counts.get(category.value, 0) > 0

    def send_alert(self, subject: str, message: str,
                  priority: AlertPriority = AlertPriority.NORMAL,
                  category: AlertCategory = AlertCategory.SYSTEM,
                  metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Send an enhanced alert with categorization and rate limiting"""

        # Check rate limits
        if not self.should_send_alert(priority, category):
            logger.debug(f"Alert rate limited: {category.value} {priority.value}")
            self.alert_counts[category.value] += 1
            return False

        # Enhance subject with priority and category
        enhanced_subject = f"[{priority.value}] [{category.value}] {subject}"

        # Create enhanced message
        enhanced_message = self._create_enhanced_message(
            message, priority, category, metadata
        )

        # Send the alert
        success = self._send_email(enhanced_subject, enhanced_message)

        if success:
            logger.info(f"Alert sent: {category.value} {priority.value} - {subject}")
        else:
            logger.error(f"Failed to send alert: {category.value} {priority.value} - {subject}")
            self.alert_counts[category.value] += 1

        return success

    def send_digest(self, category: AlertCategory) -> bool:
        """Send a digest of accumulated alerts"""

        if not self.should_send_digest(category):
            return False

        count = self.alert_counts.get(category.value, 0)
        if count == 0:
            return False

        subject = f"[DIGEST] {category.value} Alert Summary - {count} alerts"

        message = f"""
Alert Digest Summary
===================

Category: {category.value}
Time Period: Last {self.digest_intervals[category] // 60} minutes
Total Alerts: {count} (rate limited or failed)

This digest represents alerts that were either rate-limited or failed to send.
Please check the system logs for detailed information about these alerts.

Generated at: {datetime.now().isoformat()}
        """.strip()

        success = self._send_email(subject, message)

        if success:
            self.alert_counts[category.value] = 0
            self.last_digest_time[category.value] = time.time()
            logger.info(f"Digest sent for {category.value}: {count} alerts")

        return success

    def _create_enhanced_message(self, message: str, priority: AlertPriority,
                               category: AlertCategory, metadata: Optional[Dict] = None) -> str:
        """Create an enhanced message with additional context"""

        parts = [
            f"Priority: {priority.value}",
            f"Category: {category.value}",
            f"Timestamp: {datetime.now().isoformat()}",
            "",
            "Message:",
            message
        ]

        if metadata:
            parts.extend([
                "",
                "Additional Context:",
                json.dumps(metadata, indent=2, default=str)
            ])

        parts.extend([
            "",
            "---",
            "Bitcoin AI Trading Bot Alert System"
        ])

        return "\n".join(parts)

    def _send_email(self, subject: str, message: str) -> bool:
        """Send email using ProtonMail Bridge SMTP"""
        try:
            # Build the email message
            msg = MIMEText(message)
            msg['Subject'] = subject
            msg['From'] = FROM_EMAIL
            msg['To'] = TO_EMAIL
            msg['Date'] = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")

            # Connect to ProtonMail Bridge SMTP
            with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
                server.starttls()
                server.login(USERNAME, PASSWORD)
                server.sendmail(FROM_EMAIL, TO_EMAIL, msg.as_string())

            return True

        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False


# Global enhanced alert system instance
enhanced_alert_system = EnhancedAlertSystem()


def send_alert(subject, message):
    """
    Legacy function for backward compatibility.
    Send an alert email using ProtonMail Bridge SMTP.
    """
    try:
        # Build the email message
        msg = MIMEText(message)
        msg['Subject'] = subject
        msg['From'] = FROM_EMAIL
        msg['To'] = TO_EMAIL

        # Connect to ProtonMail Bridge SMTP
        with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
            server.starttls()
            server.login(USERNAME, PASSWORD)
            server.sendmail(FROM_EMAIL, TO_EMAIL, msg.as_string())
        print(f"[ALERT SENT] {subject}")
    except Exception as e:
        print(f"[ALERT ERROR] Failed to send alert: {e}")


def send_enhanced_alert(subject: str, message: str,
                       priority: AlertPriority = AlertPriority.NORMAL,
                       category: AlertCategory = AlertCategory.SYSTEM,
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
    """
    Send an enhanced alert with categorization and rate limiting.

    Args:
        subject: Alert subject line
        message: Alert message body
        priority: Alert priority level
        category: Alert category
        metadata: Additional context data

    Returns:
        bool: True if alert was sent, False if rate limited or failed
    """
    return enhanced_alert_system.send_alert(subject, message, priority, category, metadata)


def send_digest_alerts():
    """Send digest alerts for all categories that need them"""
    for category in AlertCategory:
        enhanced_alert_system.send_digest(category)


if __name__ == "__main__":
    # Test the enhanced alert system
    logging.basicConfig(level=logging.INFO)

    # Test basic alert
    send_enhanced_alert(
        "Test Enhanced Alert",
        "This is a test of the enhanced alert system.",
        AlertPriority.NORMAL,
        AlertCategory.SYSTEM,
        {"test_data": "example", "timestamp": datetime.now().isoformat()}
    )

    # Test legacy function
    send_alert("Test Legacy Alert", "This is a test of the legacy alert function.")
