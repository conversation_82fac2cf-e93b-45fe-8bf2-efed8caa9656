from config import ALPACA_API_KEY_ID, ALPACA_SECRET_KEY, ALPACA_BASE_URL
from alpaca_trade_api.rest import REST
import pandas as pd
from alpaca_service import get_historical_crypto_data

def main():
    api = REST(ALPACA_API_KEY_ID, ALPACA_SECRET_KEY, base_url=ALPACA_BASE_URL)

    # Fetch 30 days of 1-minute bars (30 days * 24 hours * 60 minutes = 43200 bars)
    data = get_historical_crypto_data(api, 'BTC/USD', '1Min', 43200)

    df = pd.DataFrame(data)
    df.to_csv('btc_30d_1min.csv', index=False)
    print('Saved 30 days of 1-min BTCUSD data to btc_30d_1min.csv')

if __name__ == '__main__':
    main()
