#!/usr/bin/env python3
"""
Comprehensive performance metrics for trading strategy evaluation.
Provides risk-adjusted returns, drawdown analysis, and trade quality metrics.
"""

import numpy as np
import pandas as pd
from decimal import Decimal
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class PerformanceMetrics:
    """
    Calculate comprehensive performance metrics for trading strategies.
    """
    
    def __init__(self, trades_df: pd.DataFrame, equity_curve: pd.Series, 
                 initial_capital: Decimal = Decimal("10000.00")):
        """
        Initialize with trade data and equity curve.
        
        Args:
            trades_df: DataFrame with columns ['timestamp', 'type', 'price', 'quantity', 'pnl']
            equity_curve: Series with timestamp index and equity values
            initial_capital: Starting capital amount
        """
        self.trades = trades_df.copy() if not trades_df.empty else pd.DataFrame()
        self.equity = equity_curve.copy() if not equity_curve.empty else pd.Series()
        self.initial_capital = float(initial_capital)
        
        # Calculate returns if we have equity data
        if not self.equity.empty:
            self.returns = self.equity.pct_change().dropna()
        else:
            self.returns = pd.Series()
    
    def calculate_all_metrics(self) -> Dict[str, float]:
        """
        Calculate all performance metrics and return as dictionary.
        """
        if self.equity.empty or self.trades.empty:
            return self._empty_metrics()
        
        try:
            metrics = {
                # Profitability Metrics
                'total_return': self.total_return(),
                'annualized_return': self.annualized_return(),
                'sharpe_ratio': self.sharpe_ratio(),
                'sortino_ratio': self.sortino_ratio(),
                
                # Risk Metrics
                'max_drawdown': self.max_drawdown(),
                'max_drawdown_duration_hours': self.max_drawdown_duration(),
                'volatility_annualized': self.volatility(),
                'var_95': self.value_at_risk(0.05),
                
                # Trade Quality Metrics
                'win_rate': self.win_rate(),
                'profit_factor': self.profit_factor(),
                'avg_win_loss_ratio': self.avg_win_loss_ratio(),
                'max_consecutive_losses': self.max_consecutive_losses(),
                
                # Efficiency Metrics
                'calmar_ratio': self.calmar_ratio(),
                'recovery_factor': self.recovery_factor(),
                'expectancy': self.expectancy(),
                
                # Trade Statistics
                'total_trades': len(self.trades),
                'winning_trades': len(self.trades[self.trades['pnl'] > 0]),
                'losing_trades': len(self.trades[self.trades['pnl'] < 0]),
            }
            
            return metrics
            
        except Exception as e:
            print(f"Error calculating metrics: {e}")
            return self._empty_metrics()
    
    def total_return(self) -> float:
        """Total return as percentage."""
        if self.equity.empty:
            return 0.0
        return ((self.equity.iloc[-1] - self.initial_capital) / self.initial_capital) * 100
    
    def annualized_return(self) -> float:
        """Annualized return percentage."""
        if self.returns.empty or len(self.returns) < 2:
            return 0.0
        
        # Assume 15-minute data (96 periods per day, 365 days per year)
        periods_per_year = 96 * 365
        total_periods = len(self.returns)
        years = total_periods / periods_per_year
        
        if years <= 0:
            return 0.0
        
        total_return_decimal = (self.equity.iloc[-1] / self.initial_capital)
        return (total_return_decimal ** (1/years) - 1) * 100
    
    def sharpe_ratio(self) -> float:
        """Sharpe ratio (risk-adjusted return)."""
        if self.returns.empty or self.returns.std() == 0:
            return 0.0
        
        # Annualized Sharpe ratio for 15-minute data
        return (self.returns.mean() / self.returns.std()) * np.sqrt(96 * 365)
    
    def sortino_ratio(self) -> float:
        """Sortino ratio (downside risk-adjusted return)."""
        if self.returns.empty:
            return 0.0
        
        downside_returns = self.returns[self.returns < 0]
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return float('inf') if self.returns.mean() > 0 else 0.0
        
        return (self.returns.mean() / downside_returns.std()) * np.sqrt(96 * 365)
    
    def max_drawdown(self) -> float:
        """Maximum drawdown as percentage."""
        if self.equity.empty:
            return 0.0
        
        peak = self.equity.expanding().max()
        drawdown = (self.equity - peak) / peak * 100
        return float(drawdown.min())
    
    def max_drawdown_duration(self) -> float:
        """Maximum drawdown duration in hours."""
        if self.equity.empty:
            return 0.0
        
        peak = self.equity.expanding().max()
        drawdown = (self.equity - peak) / peak
        
        # Find periods in drawdown
        in_drawdown = drawdown < -0.001  # More than 0.1% drawdown
        
        if not in_drawdown.any():
            return 0.0
        
        # Calculate consecutive drawdown periods
        drawdown_periods = []
        current_period = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_period += 1
            else:
                if current_period > 0:
                    drawdown_periods.append(current_period)
                current_period = 0
        
        if current_period > 0:
            drawdown_periods.append(current_period)
        
        if not drawdown_periods:
            return 0.0
        
        # Convert to hours (15-minute periods)
        max_periods = max(drawdown_periods)
        return max_periods * 0.25  # 15 minutes = 0.25 hours
    
    def volatility(self) -> float:
        """Annualized volatility percentage."""
        if self.returns.empty:
            return 0.0
        return float(self.returns.std() * np.sqrt(96 * 365) * 100)
    
    def value_at_risk(self, confidence_level: float = 0.05) -> float:
        """Value at Risk at given confidence level."""
        if self.returns.empty:
            return 0.0
        return float(np.percentile(self.returns, confidence_level * 100) * 100)
    
    def win_rate(self) -> float:
        """Percentage of winning trades."""
        if self.trades.empty:
            return 0.0
        
        winning_trades = len(self.trades[self.trades['pnl'] > 0])
        return (winning_trades / len(self.trades)) * 100
    
    def profit_factor(self) -> float:
        """Ratio of gross profit to gross loss."""
        if self.trades.empty:
            return 0.0
        
        gross_profit = self.trades[self.trades['pnl'] > 0]['pnl'].sum()
        gross_loss = abs(self.trades[self.trades['pnl'] < 0]['pnl'].sum())
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return float(gross_profit / gross_loss)
    
    def avg_win_loss_ratio(self) -> float:
        """Ratio of average winning trade to average losing trade."""
        if self.trades.empty:
            return 0.0
        
        winning_trades = self.trades[self.trades['pnl'] > 0]['pnl']
        losing_trades = self.trades[self.trades['pnl'] < 0]['pnl']
        
        if len(winning_trades) == 0 or len(losing_trades) == 0:
            return 0.0
        
        avg_win = winning_trades.mean()
        avg_loss = abs(losing_trades.mean())
        
        if avg_loss == 0:
            return float('inf')
        
        return float(avg_win / avg_loss)
    
    def max_consecutive_losses(self) -> int:
        """Maximum number of consecutive losing trades."""
        if self.trades.empty:
            return 0
        
        consecutive_losses = 0
        max_consecutive = 0
        
        for pnl in self.trades['pnl']:
            if pnl < 0:
                consecutive_losses += 1
                max_consecutive = max(max_consecutive, consecutive_losses)
            else:
                consecutive_losses = 0
        
        return max_consecutive
    
    def calmar_ratio(self) -> float:
        """Calmar ratio (annualized return / max drawdown)."""
        max_dd = abs(self.max_drawdown())
        if max_dd == 0:
            return float('inf') if self.annualized_return() > 0 else 0.0
        
        return self.annualized_return() / max_dd
    
    def recovery_factor(self) -> float:
        """Recovery factor (total return / max drawdown)."""
        max_dd = abs(self.max_drawdown())
        if max_dd == 0:
            return float('inf') if self.total_return() > 0 else 0.0
        
        return self.total_return() / max_dd
    
    def expectancy(self) -> float:
        """Expected value per trade."""
        if self.trades.empty:
            return 0.0
        
        return float(self.trades['pnl'].mean())
    
    def _empty_metrics(self) -> Dict[str, float]:
        """Return empty metrics dictionary when no data available."""
        return {
            'total_return': 0.0,
            'annualized_return': 0.0,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'max_drawdown': 0.0,
            'max_drawdown_duration_hours': 0.0,
            'volatility_annualized': 0.0,
            'var_95': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'avg_win_loss_ratio': 0.0,
            'max_consecutive_losses': 0,
            'calmar_ratio': 0.0,
            'recovery_factor': 0.0,
            'expectancy': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
        }
    
    def print_summary(self) -> None:
        """Print a formatted summary of all metrics."""
        metrics = self.calculate_all_metrics()
        
        print("\n" + "="*60)
        print("📊 TRADING STRATEGY PERFORMANCE SUMMARY")
        print("="*60)
        
        print(f"\n💰 PROFITABILITY:")
        print(f"   Total Return:        {metrics['total_return']:>8.2f}%")
        print(f"   Annualized Return:   {metrics['annualized_return']:>8.2f}%")
        print(f"   Sharpe Ratio:        {metrics['sharpe_ratio']:>8.2f}")
        print(f"   Sortino Ratio:       {metrics['sortino_ratio']:>8.2f}")
        
        print(f"\n⚠️  RISK METRICS:")
        print(f"   Max Drawdown:        {metrics['max_drawdown']:>8.2f}%")
        print(f"   Drawdown Duration:   {metrics['max_drawdown_duration_hours']:>8.1f} hours")
        print(f"   Volatility:          {metrics['volatility_annualized']:>8.2f}%")
        print(f"   VaR (95%):           {metrics['var_95']:>8.2f}%")
        
        print(f"\n📈 TRADE QUALITY:")
        print(f"   Win Rate:            {metrics['win_rate']:>8.2f}%")
        print(f"   Profit Factor:       {metrics['profit_factor']:>8.2f}")
        print(f"   Avg Win/Loss Ratio:  {metrics['avg_win_loss_ratio']:>8.2f}")
        print(f"   Max Consecutive Loss:{metrics['max_consecutive_losses']:>8.0f}")
        
        print(f"\n🎯 EFFICIENCY:")
        print(f"   Calmar Ratio:        {metrics['calmar_ratio']:>8.2f}")
        print(f"   Recovery Factor:     {metrics['recovery_factor']:>8.2f}")
        print(f"   Expectancy:          {metrics['expectancy']:>8.2f}")
        
        print(f"\n📊 TRADE STATISTICS:")
        print(f"   Total Trades:        {metrics['total_trades']:>8.0f}")
        print(f"   Winning Trades:      {metrics['winning_trades']:>8.0f}")
        print(f"   Losing Trades:       {metrics['losing_trades']:>8.0f}")
        
        print("="*60)
