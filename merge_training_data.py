import pandas as pd

# Exact filenames based on your project files
numeric_file = "btc_ml_features_clean.csv"
labels_file = "prepared_training_data_labeled.csv"

output_file = "full_training_data.csv"

# Load datasets
numeric_df = pd.read_csv(numeric_file)
labels_df = pd.read_csv(labels_file)

# Merge on 'open_time' if present in both files
if 'open_time' in numeric_df.columns and 'open_time' in labels_df.columns:
    # Remove duplicate columns from labels_df except 'open_time'
    cols_to_drop = [col for col in numeric_df.columns if col != 'open_time' and col in labels_df.columns]
    labels_df_dedup = labels_df.drop(columns=cols_to_drop)
    merged_df = pd.merge(numeric_df, labels_df_dedup, on='open_time', how='left')
else:
    # Merge on index as fallback
    merged_df = pd.merge(numeric_df, labels_df, left_index=True, right_index=True, how='left')

# Save the merged dataset
merged_df.to_csv(output_file, index=False)

print(f"Merged dataset saved as {output_file} with {merged_df.shape[0]} rows and {merged_df.shape[1]} columns.")
