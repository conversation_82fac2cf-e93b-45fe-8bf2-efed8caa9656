"""
AI Performance Analyzer System
==============================

A comprehensive AI-driven performance monitoring system that analyzes the trading bot's 
decisions and outcomes to enable continuous improvement through machine learning insights.

This system creates a meta-learning layer that:
1. Monitors and analyzes trading bot performance
2. Identifies patterns and insights from historical data
3. Provides feedback to improve future trading decisions
4. Generates automated performance reports

Components:
- Data Collection: Extracts performance data from logs and trade cycles
- Analysis Engine: Processes data to identify patterns and insights
- Reporting System: Generates automated performance reports
- Feedback Integration: Feeds insights back into AI decision service

Author: AI Performance Analyzer System
Created: 2025-08-01
"""

__version__ = "1.0.0"
__author__ = "AI Performance Analyzer System"

# Import main components
from .data_collector import PerformanceDataCollector
from .analysis_engine import AnalysisEngine
from .report_generator import ReportGenerator

__all__ = [
    'PerformanceDataCollector',
    'AnalysisEngine',
    'ReportGenerator'
]

# TODO: Add feedback integrator when implemented
# from .feedback_integrator import FeedbackIntegrator
