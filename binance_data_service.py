import websocket
import json
import logging
import time
import threading
import pandas as pd
from collections import deque
import signal

from error_handler import <PERSON>rrorSeverity, ErrorCategory, handle_error
from error_utils import (
    BinanceErrorHandler, NetworkError, DataError, SystemError
)
from email_alert import send_enhanced_alert, AlertPriority, AlertCategory

logger = logging.getLogger("TradingBotApp.BinanceData")

MAX_KLINES_TO_STORE = 200
kline_data_deque = deque(maxlen=MAX_KLINES_TO_STORE)
kline_data_lock = threading.Lock()

ws_app_global: websocket.WebSocketApp | None = None
shutdown_event = threading.Event()

last_message_time: float | None = None
last_message_time_lock = threading.Lock()

_BACKOFF_INIT = 15
_BACKOFF_MAX  = 600
_backoff_seconds = _BACKOFF_INIT

def on_message(ws, message):
    global last_message_time
    with last_message_time_lock:
        last_message_time = time.time()
    try:
        data = json.loads(message)

        if data.get("e") == "kline":
            kline = data["k"]
            if kline["s"] == "BTCUSDT" and kline["i"] == "1m":
                is_final = kline["x"]
                kdict = {
                    "t": int(kline["t"]),
                    "o": float(kline["o"]),
                    "h": float(kline["h"]),
                    "l": float(kline["l"]),
                    "c": float(kline["c"]),
                    "v": float(kline["v"]),
                    "T": int(kline["T"]),
                    "final": is_final,
                }

                if is_final:
                    with kline_data_lock:
                        if kline_data_deque and kline_data_deque[-1]["t"] == kdict["t"]:
                            kline_data_deque[-1] = kdict
                            logger.debug(f"BINANCE FINAL KLINE [BTCUSDT|1m] Updated existing candle. CloseT {pd.to_datetime(kdict['T'], unit='ms', utc=True)} C {kdict['c']:.2f}, V {kdict['v']}")
                        else:
                            kline_data_deque.append(kdict)
                            logger.info(f"BINANCE FINAL KLINE [BTCUSDT|1m] New candle added. CloseT {pd.to_datetime(kdict['T'], unit='ms', utc=True)} C {kdict['c']:.2f}, V {kdict['v']}")
                else:
                    ev_ms = data.get("E")
                    ev_dt = (
                        pd.to_datetime(ev_ms, unit="ms", utc=True)
                        if ev_ms
                        else pd.Timestamp("now", tz="UTC")
                    )
                    logger.debug(f"BINANCE PARTIAL KLINE [BTCUSDT|1m] EventT {ev_dt} C {kdict['c']:.2f}")
            else:
                logger.debug(f"Received kline for unexpected symbol/interval: {kline['s']}@{kline['i']}")
        else:
            logger.debug(f"Other Binance message: {str(message)[:200]}")
    except json.JSONDecodeError as e:
        BinanceErrorHandler.handle_data_processing_error(e, str(message)[:100],
            {"operation": "json_decode", "message_preview": str(message)[:100]})
    except KeyError as e:
        BinanceErrorHandler.handle_data_processing_error(e, str(message)[:100],
            {"operation": "key_access", "message_preview": str(message)[:100]})
    except Exception as e:
        error = SystemError(
            f"Unexpected error in Binance WebSocket on_message: {e}",
            ErrorSeverity.CRITICAL,
            {"operation": "on_message", "message_preview": str(message)[:100]},
            e
        )
        handle_error(error)

        # Send critical alert for unexpected WebSocket errors
        send_enhanced_alert(
            "Binance WebSocket Critical Error",
            f"Unexpected error in Binance WebSocket message handler: {e}",
            AlertPriority.URGENT,
            AlertCategory.SYSTEM,
            {"error": str(e), "message_preview": str(message)[:100]}
        )

def on_error(ws, error):
    """Handle Binance WebSocket errors with enhanced error handling"""
    BinanceErrorHandler.handle_websocket_error(error, {"websocket_state": "error_callback"})

def on_close(ws, code, msg):
    logger.warning(f"Binance WS Connection Closed – code={code}, msg={msg}")

def on_open(ws):
    global _backoff_seconds, last_message_time
    _backoff_seconds = _BACKOFF_INIT
    with last_message_time_lock:
        last_message_time = time.time()
    print("✅ BINANCE: WebSocket Connected to btcusdt@kline_1m stream")
    logger.warning("Binance WS Connected to dedicated stream btcusdt@kline_1m.")

def _run_forever(app: websocket.WebSocketApp):
    try:
        logger.info("Binance WebSocket thread entering run_forever() …")
        app.run_forever(ping_interval=60, ping_timeout=30)
    except Exception as e:
        logger.error(f"run_forever() error: {e}", exc_info=True)
    finally:
        logger.info("Binance WebSocket thread exited run_forever().")

def run_binance_websocket_client_persistent():
    global ws_app_global, _backoff_seconds
    ws_url = "wss://stream.binance.com:9443/ws/btcusdt@kline_1m"

    print("🔗 BINANCE: Starting Binance Data Service persistent client...")
    logger.warning("Starting Binance Data Service persistent client.")
    while not shutdown_event.is_set():
        logger.info(f"Connecting to {ws_url} …")
        ws_app_global = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
        )

        ws_thread = threading.Thread(
            target=_run_forever, args=(ws_app_global,), daemon=True
        )
        ws_thread.start()

        while ws_thread.is_alive() and not shutdown_event.is_set():
            time.sleep(5) # Check every 5 seconds
            with last_message_time_lock:
                last_msg_ts = last_message_time

            if last_msg_ts and (time.time() - last_msg_ts) > 180: # 3 minutes
                logger.warning("Binance WS connection stale (no message in >3 minutes). Forcing reconnect.")
                if ws_app_global:
                    ws_app_global.close()
                break

        if shutdown_event.is_set():
            logger.info("Shutdown requested – closing Binance socket.")
            try:
                if ws_app_global and ws_app_global.sock and ws_app_global.sock.connected:
                    ws_app_global.close()
            except Exception as e:
                error = NetworkError(
                    f"Error while closing Binance WebSocket: {e}",
                    ErrorSeverity.MEDIUM,
                    {"operation": "websocket_close"},
                    e
                )
                handle_error(error)
            finally:
                ws_thread.join(timeout=5)
            break

        logger.warning("Binance WS disconnected. Attempting to reconnect.")
        if not shutdown_event.is_set():
            logger.info(f"Reconnecting in {_backoff_seconds} s …")
            t_end = time.time() + _backoff_seconds
            while time.time() < t_end and not shutdown_event.is_set():
                time.sleep(0.5)

            _backoff_seconds = min(_backoff_seconds * 2, _BACKOFF_MAX)

    logger.info("Binance Data Service stopped.")

def get_last_message_timestamp() -> float | None:
    """Returns the timestamp of the last received message."""
    with last_message_time_lock:
        return last_message_time

def wait_for_initial_data(timeout_seconds: int = 30) -> bool:
    """Wait for initial kline data to be available."""
    import time

    start_time = time.time()
    while time.time() - start_time < timeout_seconds:
        with kline_data_lock:
            if kline_data_deque:
                logger.info("Initial kline data available")
                return True
        time.sleep(0.5)

    logger.warning(f"Timeout waiting for initial kline data after {timeout_seconds}s")
    return False

def get_latest_klines_df() -> pd.DataFrame:
    logger.debug("Retrieving latest klines as DataFrame.")
    with kline_data_lock:
        if not kline_data_deque:
            logger.warning("Kline data deque is empty. Returning empty DataFrame.")
            return pd.DataFrame()

        df = pd.DataFrame(list(kline_data_deque))
    required = {"t", "o", "h", "l", "c", "v"}
    if not required.issubset(df.columns):
        logger.error(f"Kline deque missing expected columns: {required - set(df.columns)}. Returning empty DataFrame.")
        return pd.DataFrame()

    try:
        df["timestamp"] = pd.to_datetime(df["t"], unit="ms", utc=True)
        df = (
            df.set_index("timestamp")
            .loc[:, ["o", "h", "l", "c", "v"]]
            .rename(
                columns=dict(
                    o="open",
                    h="high",
                    l="low",
                    c="close",
                    v="volume",
                )
            )
            .astype(float)
        )
        logger.debug(f"Successfully converted {len(df)} klines from deque to DataFrame.")
        return df
    except Exception as e:
        error = DataError(
            f"Error processing klines to DataFrame: {e}",
            ErrorSeverity.MEDIUM,
            {"operation": "klines_to_dataframe", "klines_count": len(kline_data_deque)},
            e
        )
        handle_error(error)
        return pd.DataFrame()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s",
    )

    logger.info("Starting Binance Data Service (test mode).")

    def _sig_handler(sig, frame):
        logger.warning("CTRL-C caught – shutting down.")
        shutdown_event.set()
        if ws_app_global and ws_app_global.sock and ws_app_global.sock.connected:
            ws_app_global.close()

    signal.signal(signal.SIGINT, _sig_handler)
    signal.signal(signal.SIGTERM, _sig_handler)

    run_binance_websocket_client_persistent()

    logger.info("Binance Data Service test run finished.")
