# backtesting_suite/backtest_config.py

from decimal import Decimal

# --- Simulation Parameters ---
STARTING_CAPITAL = Decimal("10000.00") # Initial capital for the backtest simulation

# --- Cost Control (for generate_decision_log.py) ---
# The approximate cost per 1,000 calls to the Gemini API.
# This is a rough estimate and should be updated based on actuals.
COST_PER_1000_CALLS_USD = Decimal("0.05") 
# The maximum cost you are willing to incur in a single run of the generation script.
MAX_COST_USD = Decimal("5.00")

# --- Date Range (for generate_decision_log.py) ---
# Set a date range to process a smaller, more relevant subset of the data.
# Format: "YYYY-MM-DD"
START_DATE = "2024-04-01"
END_DATE = "2024-04-01"

# --- Timeframe Aggregation ---
# The timeframe to aggregate the data to (e.g., "15min" for 15 minutes, "1H" for 1 hour).
TIMEFRAME = "15min"

# --- Data Configuration ---
# Path to the historical data file to be used for the backtest.
# This will now point to our generated, time-aggregated decision log.
DATA_FILE_PATH = "backtesting_suite/decision_log_full.csv"

# --- Reporting ---
# Directory to save the results of the backtest (e.g., trade logs, performance reports)
RESULTS_DIR = "backtesting_suite/results"