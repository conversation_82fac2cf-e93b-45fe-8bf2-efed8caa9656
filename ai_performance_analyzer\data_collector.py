"""
Performance Data Collector
==========================

Collects and processes performance data from the trading bot's logs, state files,
and trade cycles to build a comprehensive dataset for analysis.
"""

import json
import logging
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
import pandas as pd

from .config import DATA_COLLECTION, DATA_DIR, LOGGING
from .utils import setup_logger

class PerformanceDataCollector:
    """Collects performance data from various bot sources."""
    
    def __init__(self):
        self.logger = setup_logger("PerformanceDataCollector", LOGGING)
        self.data_dir = DATA_DIR
        self.collected_data = {}
        
    def collect_all_data(self) -> Dict[str, Any]:
        """Collect all available performance data."""
        self.logger.info("Starting comprehensive data collection...")
        
        try:
            # Collect from different sources
            trade_data = self.collect_trade_data()
            bot_state_data = self.collect_bot_state_data()
            session_data = self.collect_session_data()
            log_data = self.collect_log_data()
            
            # Combine all data
            self.collected_data = {
                "collection_timestamp": datetime.now().isoformat(),
                "trade_data": trade_data,
                "bot_state": bot_state_data,
                "session_data": session_data,
                "log_analysis": log_data,
                "summary": self._generate_summary()
            }
            
            # Save collected data
            self._save_collected_data()
            
            self.logger.info("Data collection completed successfully")
            return self.collected_data
            
        except Exception as e:
            self.logger.error(f"Error during data collection: {e}")
            raise
    
    def collect_trade_data(self) -> Dict[str, Any]:
        """Collect trade-related data from open lots and trade history."""
        self.logger.info("Collecting trade data...")
        
        trade_data = {
            "open_lots": [],
            "trade_summary": {},
            "position_analysis": {}
        }
        
        try:
            # Load open lots
            open_lots_path = DATA_COLLECTION["open_lots_path"]
            if open_lots_path.exists():
                with open(open_lots_path, 'r') as f:
                    lots_data = json.load(f)
                    trade_data["open_lots"] = lots_data
                    trade_data["trade_summary"] = self._analyze_open_lots(lots_data)
            
            # Analyze position data
            trade_data["position_analysis"] = self._analyze_positions(trade_data["open_lots"])
            
        except Exception as e:
            self.logger.error(f"Error collecting trade data: {e}")
            
        return trade_data
    
    def collect_bot_state_data(self) -> Dict[str, Any]:
        """Collect bot state and configuration data."""
        self.logger.info("Collecting bot state data...")
        
        state_data = {}
        
        try:
            # Load bot state
            bot_state_path = DATA_COLLECTION["bot_state_path"]
            if bot_state_path.exists():
                with open(bot_state_path, 'r') as f:
                    state_data = json.load(f)
                    
        except Exception as e:
            self.logger.error(f"Error collecting bot state data: {e}")
            
        return state_data
    
    def collect_session_data(self) -> Dict[str, Any]:
        """Collect session statistics and performance data."""
        self.logger.info("Collecting session data...")
        
        session_data = {}
        
        try:
            # Load session stats
            session_path = DATA_COLLECTION["session_stats_path"]
            if session_path.exists():
                with open(session_path, 'r') as f:
                    session_data = json.load(f)
                    
        except Exception as e:
            self.logger.error(f"Error collecting session data: {e}")
            
        return session_data
    
    def collect_log_data(self) -> Dict[str, Any]:
        """Collect and analyze log data for decision patterns."""
        self.logger.info("Collecting log data...")
        
        log_data = {
            "ai_decisions": [],
            "trade_actions": [],
            "errors": [],
            "performance_metrics": {}
        }
        
        try:
            # Parse trading bot logs
            log_path = DATA_COLLECTION["trade_log_path"]
            if log_path.exists():
                log_data = self._parse_trading_logs(log_path)
                
        except Exception as e:
            self.logger.error(f"Error collecting log data: {e}")
            
        return log_data
    
    def _analyze_open_lots(self, lots_data: List[Dict]) -> Dict[str, Any]:
        """Analyze open lots for patterns and statistics."""
        if not lots_data:
            return {}
            
        total_lots = len(lots_data)
        total_value = sum(Decimal(lot.get("remaining_qty", "0")) * 
                         Decimal(lot.get("buy_price", "0")) for lot in lots_data)
        
        # Analyze lot types
        lot_types = {}
        for lot in lots_data:
            lot_type = lot.get("type", "UNKNOWN")
            lot_types[lot_type] = lot_types.get(lot_type, 0) + 1
        
        # Calculate average holding period
        now = datetime.now()
        holding_periods = []
        for lot in lots_data:
            buy_time = datetime.fromisoformat(lot.get("buy_timestamp", "").replace("Z", "+00:00"))
            holding_period = (now - buy_time.replace(tzinfo=None)).total_seconds() / 3600  # hours
            holding_periods.append(holding_period)
        
        avg_holding_period = sum(holding_periods) / len(holding_periods) if holding_periods else 0
        
        return {
            "total_lots": total_lots,
            "total_value": float(total_value),
            "lot_types": lot_types,
            "average_holding_period_hours": avg_holding_period,
            "oldest_lot_hours": max(holding_periods) if holding_periods else 0,
            "newest_lot_hours": min(holding_periods) if holding_periods else 0
        }
    
    def _analyze_positions(self, lots_data: List[Dict]) -> Dict[str, Any]:
        """Analyze position-level data."""
        if not lots_data:
            return {}
            
        # Calculate unrealized P&L
        total_unrealized_pnl = Decimal("0")
        profitable_lots = 0
        losing_lots = 0
        
        for lot in lots_data:
            # This would need current market price to calculate properly
            # For now, we'll use placeholder logic
            cost_basis = Decimal(lot.get("cost_basis_per_unit", "0"))
            # current_price would come from market data
            # unrealized_pnl = (current_price - cost_basis) * remaining_qty
            
        return {
            "total_unrealized_pnl": float(total_unrealized_pnl),
            "profitable_lots": profitable_lots,
            "losing_lots": losing_lots,
            "breakeven_lots": len(lots_data) - profitable_lots - losing_lots
        }
    
    def _parse_trading_logs(self, log_path: Path) -> Dict[str, Any]:
        """Parse trading logs for decision and action patterns."""
        log_data = {
            "ai_decisions": [],
            "trade_actions": [],
            "errors": [],
            "performance_metrics": {}
        }
        
        try:
            # Read recent log entries
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            # Process last N lines to avoid memory issues
            max_lines = DATA_COLLECTION["max_log_lines"]
            recent_lines = lines[-max_lines:] if len(lines) > max_lines else lines
            
            for line in recent_lines:
                # Parse AI decisions
                if "AI Decision" in line:
                    decision_data = self._extract_ai_decision(line)
                    if decision_data:
                        log_data["ai_decisions"].append(decision_data)
                
                # Parse trade actions
                if any(action in line for action in ["BUY:", "SELL:", "Action Taken"]):
                    action_data = self._extract_trade_action(line)
                    if action_data:
                        log_data["trade_actions"].append(action_data)
                
                # Parse errors
                if "ERROR" in line or "Exception" in line:
                    error_data = self._extract_error(line)
                    if error_data:
                        log_data["errors"].append(error_data)
            
            # Calculate performance metrics from parsed data
            log_data["performance_metrics"] = self._calculate_log_metrics(log_data)
            
        except Exception as e:
            self.logger.error(f"Error parsing trading logs: {e}")
            
        return log_data
    
    def _extract_ai_decision(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract AI decision data from log line."""
        try:
            # Extract timestamp
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', line)
            timestamp = timestamp_match.group(1) if timestamp_match else None
            
            # Extract decision
            if "AI Decision       : " in line:
                decision = line.split("AI Decision       : ")[1].strip()
                return {
                    "timestamp": timestamp,
                    "decision": decision,
                    "line": line.strip()
                }
                
        except Exception as e:
            self.logger.debug(f"Error extracting AI decision: {e}")
            
        return None
    
    def _extract_trade_action(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract trade action data from log line."""
        try:
            # Extract timestamp
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', line)
            timestamp = timestamp_match.group(1) if timestamp_match else None
            
            # Extract action
            if "Action Taken      : " in line:
                action = line.split("Action Taken      : ")[1].strip()
                return {
                    "timestamp": timestamp,
                    "action": action,
                    "line": line.strip()
                }
                
        except Exception as e:
            self.logger.debug(f"Error extracting trade action: {e}")
            
        return None
    
    def _extract_error(self, line: str) -> Optional[Dict[str, Any]]:
        """Extract error data from log line."""
        try:
            # Extract timestamp
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            timestamp = timestamp_match.group(1) if timestamp_match else None
            
            return {
                "timestamp": timestamp,
                "error": line.strip()
            }
                
        except Exception as e:
            self.logger.debug(f"Error extracting error: {e}")
            
        return None
    
    def _calculate_log_metrics(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance metrics from log data."""
        metrics = {}
        
        try:
            # Decision frequency
            decisions = log_data.get("ai_decisions", [])
            actions = log_data.get("trade_actions", [])
            errors = log_data.get("errors", [])
            
            metrics["total_decisions"] = len(decisions)
            metrics["total_actions"] = len(actions)
            metrics["total_errors"] = len(errors)
            
            # Decision distribution
            decision_counts = {}
            for decision in decisions:
                dec = decision.get("decision", "UNKNOWN")
                decision_counts[dec] = decision_counts.get(dec, 0) + 1
            
            metrics["decision_distribution"] = decision_counts
            
            # Error rate
            if len(decisions) > 0:
                metrics["error_rate"] = len(errors) / len(decisions)
            else:
                metrics["error_rate"] = 0
                
        except Exception as e:
            self.logger.error(f"Error calculating log metrics: {e}")
            
        return metrics
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate a summary of collected data."""
        summary = {
            "collection_time": datetime.now().isoformat(),
            "data_sources": [],
            "key_metrics": {}
        }
        
        # Identify available data sources
        if self.collected_data.get("trade_data", {}).get("open_lots"):
            summary["data_sources"].append("open_lots")
        if self.collected_data.get("bot_state"):
            summary["data_sources"].append("bot_state")
        if self.collected_data.get("session_data"):
            summary["data_sources"].append("session_data")
        if self.collected_data.get("log_analysis", {}).get("ai_decisions"):
            summary["data_sources"].append("trading_logs")
        
        # Extract key metrics
        trade_summary = self.collected_data.get("trade_data", {}).get("trade_summary", {})
        log_metrics = self.collected_data.get("log_analysis", {}).get("performance_metrics", {})
        
        summary["key_metrics"] = {
            "total_open_lots": trade_summary.get("total_lots", 0),
            "total_decisions": log_metrics.get("total_decisions", 0),
            "total_errors": log_metrics.get("total_errors", 0),
            "error_rate": log_metrics.get("error_rate", 0)
        }
        
        return summary
    
    def _save_collected_data(self):
        """Save collected data to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"collected_data_{timestamp}.json"
            filepath = self.data_dir / filename
            
            with open(filepath, 'w') as f:
                json.dump(self.collected_data, f, indent=2, default=str)
                
            self.logger.info(f"Collected data saved to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving collected data: {e}")
