import csv
from collections import Counter, defaultdict
from datetime import datetime

LOG_FILE = "trade_cycle_log.csv"

def try_float(x):
    try:
        return float(x)
    except:
        return 0.0

def main():
    rows = []
    with open(LOG_FILE, newline="") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            rows.append(row)
    if not rows:
        print("No trades logged yet!")
        return

    total = len(rows)
    win, loss, no_action = 0, 0, 0
    pnl = 0.0
    best_win = float('-inf')
    worst_loss = float('inf')
    win_streak = loss_streak = max_win_streak = max_loss_streak = 0
    regime_counter = Counter()
    action_counter = Counter()
    reason_counter = Counter()
    streak = 0
    last_result = None

    for row in rows:
        trade_action = row.get("trade_action", "")
        regime = row.get("regime", "")
        decision = row.get("decision", "")
        reason = row.get("reason_for_action", "")
        session_pnl = try_float(row.get("session_pnl", "0"))
        action_counter[trade_action] += 1
        regime_counter[regime] += 1
        reason_counter[reason] += 1

        # Track streaks and win/loss
        if "WIN" in row.get("trade_result", "").upper() or (try_float(row.get("session_pnl", 0)) > 0.01 and trade_action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            win += 1
            pnl += session_pnl
            if last_result == "win":
                win_streak += 1
            else:
                win_streak = 1
            max_win_streak = max(max_win_streak, win_streak)
            loss_streak = 0
            last_result = "win"
            best_win = max(best_win, session_pnl)
        elif "LOSS" in row.get("trade_result", "").upper() or (try_float(row.get("session_pnl", 0)) < -0.01 and trade_action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            loss += 1
            pnl += session_pnl
            if last_result == "loss":
                loss_streak += 1
            else:
                loss_streak = 1
            max_loss_streak = max(max_loss_streak, loss_streak)
            win_streak = 0
            last_result = "loss"
            worst_loss = min(worst_loss, session_pnl)
        elif trade_action in ("No action", "HOLD", ""):
            no_action += 1

    # Output summary
    print("="*40)
    print("TRADE PERFORMANCE SUMMARY")
    print("="*40)
    print(f"Total Trade Cycles: {total}")
    print(f" - Trades (BUY/SELL): {total - no_action}")
    print(f" - BUY actions: {action_counter['BUY']}")
    print(f" - SELL actions: {action_counter['SELL']}")
    print(f" - No Action/HOLD: {no_action}")
    print()
    print(f"Wins: {win}")
    print(f"Losses: {loss}")
    print(f"Win Rate: {win/(win+loss)*100:.2f}%" if (win+loss) else "Win Rate: N/A")
    print(f"Biggest Win: {best_win:.2f}")
    print(f"Worst Loss: {worst_loss:.2f}")
    print()
    print(f"Session PnL (sum of cycles): {pnl:.2f}")
    print(f"Longest Win Streak: {max_win_streak}")
    print(f"Longest Loss Streak: {max_loss_streak}")
    print()
    print("Most Common Market Regime:")
    for regime, count in regime_counter.most_common(3):
        print(f" - {regime}: {count} cycles")
    print()
    print("Most Common Reasons for No Trade/HOLD:")
    for reason, count in reason_counter.most_common(3):
        if reason:
            print(f" - {reason}: {count}")
    print()
    print("First cycle: ", rows[0].get("datetime"))
    print("Last cycle:  ", rows[-1].get("datetime"))
    print("="*40)

if __name__ == "__main__":
    main()
