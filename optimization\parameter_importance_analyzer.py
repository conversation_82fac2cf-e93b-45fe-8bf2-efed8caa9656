#!/usr/bin/env python3
"""
Parameter Importance Analyzer

Analyzes which parameters have the most impact on trading performance.
Uses various techniques to understand parameter sensitivity and importance.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from sklearn.ensemble import RandomForestRegressor
from sklearn.inspection import permutation_importance
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os


class ParameterImportanceAnalyzer:
    """
    Analyzes parameter importance from optimization results.
    """
    
    def __init__(self, results_dir: str = "optimization/results"):
        """
        Initialize the analyzer.
        
        Args:
            results_dir: Directory containing optimization results
        """
        self.results_dir = results_dir
        self.importance_scores = {}
        self.correlation_matrix = None
        self.feature_names = []
        
    def analyze_from_optimization_history(self, optimization_history: List[Dict[str, Any]], 
                                        target_metric: str = 'sharpe_ratio') -> Dict[str, Any]:
        """
        Analyze parameter importance from optimization history.
        
        Args:
            optimization_history: List of optimization results
            target_metric: Metric to analyze importance for
            
        Returns:
            Dictionary with importance analysis results
        """
        if not optimization_history:
            return {"error": "No optimization history provided"}
        
        print(f"🔍 Analyzing Parameter Importance")
        print(f"   📊 Dataset: {len(optimization_history)} optimization runs")
        print(f"   🎯 Target metric: {target_metric}")
        print("="*60)
        
        # Convert optimization history to DataFrame
        # Handle nested structure from Bayesian optimizer
        processed_data = []
        for entry in optimization_history:
            row = {}

            # Extract parameters
            if 'parameters' in entry:
                row.update(entry['parameters'])

            # Extract target metric from metrics or use score
            if 'metrics' in entry and isinstance(entry['metrics'], dict):
                if target_metric in entry['metrics']:
                    row[target_metric] = entry['metrics'][target_metric]
                else:
                    row[target_metric] = entry.get('score', 0)
            else:
                row[target_metric] = entry.get('score', 0)

            # Add other fields
            row['score'] = entry.get('score', 0)
            if 'iteration' in entry:
                row['iteration'] = entry['iteration']

            processed_data.append(row)

        df = pd.DataFrame(processed_data)

        # Separate features (parameters) and target
        parameter_columns = [col for col in df.columns if col not in ['score', 'iteration', target_metric, 'timestamp']]

        if not parameter_columns:
            return {"error": "No parameter columns found in optimization history"}

        X = df[parameter_columns]
        y = df[target_metric]
        
        self.feature_names = parameter_columns
        
        # 1. Random Forest Feature Importance
        rf_importance = self._calculate_rf_importance(X, y)
        
        # 2. Permutation Importance
        perm_importance = self._calculate_permutation_importance(X, y)
        
        # 3. Correlation Analysis
        correlation_analysis = self._calculate_correlation_analysis(X, y)
        
        # 4. Parameter Sensitivity Analysis
        sensitivity_analysis = self._calculate_sensitivity_analysis(df, parameter_columns, target_metric)
        
        # 5. Parameter Range Impact
        range_impact = self._calculate_range_impact(df, parameter_columns, target_metric)
        
        # Combine results
        results = {
            'random_forest_importance': rf_importance,
            'permutation_importance': perm_importance,
            'correlation_analysis': correlation_analysis,
            'sensitivity_analysis': sensitivity_analysis,
            'range_impact': range_impact,
            'summary': self._create_importance_summary(rf_importance, perm_importance, correlation_analysis),
            'recommendations': self._generate_recommendations(rf_importance, correlation_analysis, sensitivity_analysis)
        }
        
        # Save results
        self._save_importance_analysis(results)
        
        return results
    
    def _calculate_rf_importance(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """Calculate Random Forest feature importance."""
        print("1️⃣  Calculating Random Forest importance...")
        
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        importance_dict = dict(zip(X.columns, rf.feature_importances_))
        
        # Sort by importance
        sorted_importance = dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
        
        print(f"   ✅ Top 3 important parameters:")
        for i, (param, importance) in enumerate(list(sorted_importance.items())[:3]):
            print(f"      {i+1}. {param}: {importance:.4f}")
        
        return sorted_importance
    
    def _calculate_permutation_importance(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """Calculate permutation importance."""
        print("2️⃣  Calculating permutation importance...")
        
        rf = RandomForestRegressor(n_estimators=50, random_state=42)  # Smaller for speed
        rf.fit(X, y)
        
        perm_imp = permutation_importance(rf, X, y, n_repeats=10, random_state=42)
        
        importance_dict = dict(zip(X.columns, perm_imp.importances_mean))
        sorted_importance = dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
        
        print(f"   ✅ Top 3 permutation important parameters:")
        for i, (param, importance) in enumerate(list(sorted_importance.items())[:3]):
            print(f"      {i+1}. {param}: {importance:.4f}")
        
        return sorted_importance
    
    def _calculate_correlation_analysis(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """Calculate correlation between parameters and target metric."""
        print("3️⃣  Calculating correlation analysis...")
        
        correlations = X.corrwith(y).abs().sort_values(ascending=False)
        
        # Parameter correlations with each other
        param_correlations = X.corr()
        self.correlation_matrix = param_correlations
        
        # Find highly correlated parameter pairs
        high_correlations = []
        for i in range(len(param_correlations.columns)):
            for j in range(i+1, len(param_correlations.columns)):
                corr_val = abs(param_correlations.iloc[i, j])
                if corr_val > 0.7:  # High correlation threshold
                    high_correlations.append({
                        'param1': param_correlations.columns[i],
                        'param2': param_correlations.columns[j],
                        'correlation': corr_val
                    })
        
        print(f"   ✅ Top 3 correlated with target:")
        for i, (param, corr) in enumerate(correlations.head(3).items()):
            print(f"      {i+1}. {param}: {corr:.4f}")
        
        if high_correlations:
            print(f"   ⚠️  Found {len(high_correlations)} highly correlated parameter pairs")
        
        return {
            'target_correlations': correlations.to_dict(),
            'high_correlations': high_correlations,
            'correlation_matrix': param_correlations.to_dict()
        }
    
    def _calculate_sensitivity_analysis(self, df: pd.DataFrame, parameter_columns: List[str], 
                                      target_metric: str) -> Dict[str, Any]:
        """Calculate parameter sensitivity analysis."""
        print("4️⃣  Calculating sensitivity analysis...")
        
        sensitivity_scores = {}
        
        for param in parameter_columns:
            # Calculate how much the target metric changes with parameter changes
            param_values = df[param].values
            target_values = df[target_metric].values if target_metric in df.columns else df['score'].values
            
            # Calculate coefficient of variation
            param_cv = np.std(param_values) / np.mean(param_values) if np.mean(param_values) != 0 else 0
            target_cv = np.std(target_values) / np.mean(target_values) if np.mean(target_values) != 0 else 0
            
            # Sensitivity = how much target varies relative to parameter variation
            sensitivity = target_cv / param_cv if param_cv != 0 else 0
            sensitivity_scores[param] = sensitivity
        
        # Sort by sensitivity
        sorted_sensitivity = dict(sorted(sensitivity_scores.items(), key=lambda x: x[1], reverse=True))
        
        print(f"   ✅ Top 3 sensitive parameters:")
        for i, (param, sensitivity) in enumerate(list(sorted_sensitivity.items())[:3]):
            print(f"      {i+1}. {param}: {sensitivity:.4f}")
        
        return sorted_sensitivity
    
    def _calculate_range_impact(self, df: pd.DataFrame, parameter_columns: List[str], 
                              target_metric: str) -> Dict[str, Any]:
        """Calculate impact of parameter ranges on performance."""
        print("5️⃣  Calculating range impact analysis...")
        
        range_impacts = {}
        target_col = target_metric if target_metric in df.columns else 'score'
        
        for param in parameter_columns:
            param_values = df[param].values
            target_values = df[target_col].values
            
            # Divide parameter range into quartiles
            q1, q2, q3 = np.percentile(param_values, [25, 50, 75])
            
            # Calculate average performance in each quartile
            q1_mask = param_values <= q1
            q2_mask = (param_values > q1) & (param_values <= q2)
            q3_mask = (param_values > q2) & (param_values <= q3)
            q4_mask = param_values > q3
            
            q1_perf = np.mean(target_values[q1_mask]) if np.any(q1_mask) else 0
            q2_perf = np.mean(target_values[q2_mask]) if np.any(q2_mask) else 0
            q3_perf = np.mean(target_values[q3_mask]) if np.any(q3_mask) else 0
            q4_perf = np.mean(target_values[q4_mask]) if np.any(q4_mask) else 0
            
            # Calculate range impact as difference between best and worst quartile
            quartile_perfs = [q1_perf, q2_perf, q3_perf, q4_perf]
            range_impact = max(quartile_perfs) - min(quartile_perfs)
            
            range_impacts[param] = {
                'range_impact': range_impact,
                'quartile_performance': {
                    'Q1': q1_perf,
                    'Q2': q2_perf,
                    'Q3': q3_perf,
                    'Q4': q4_perf
                },
                'best_quartile': f"Q{quartile_perfs.index(max(quartile_perfs)) + 1}"
            }
        
        # Sort by range impact
        sorted_impacts = dict(sorted(range_impacts.items(), 
                                   key=lambda x: x[1]['range_impact'], reverse=True))
        
        print(f"   ✅ Top 3 parameters by range impact:")
        for i, (param, data) in enumerate(list(sorted_impacts.items())[:3]):
            print(f"      {i+1}. {param}: {data['range_impact']:.4f} (best: {data['best_quartile']})")
        
        return sorted_impacts
    
    def _create_importance_summary(self, rf_importance: Dict[str, float], 
                                 perm_importance: Dict[str, float],
                                 correlation_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of parameter importance across all methods."""
        
        # Get top parameters from each method
        rf_top = list(rf_importance.keys())[:5]
        perm_top = list(perm_importance.keys())[:5]
        corr_top = list(correlation_analysis['target_correlations'].keys())[:5]
        
        # Count how often each parameter appears in top 5
        param_counts = {}
        for param in rf_top + perm_top + corr_top:
            param_counts[param] = param_counts.get(param, 0) + 1
        
        # Sort by frequency
        consensus_ranking = dict(sorted(param_counts.items(), key=lambda x: x[1], reverse=True))
        
        return {
            'consensus_ranking': consensus_ranking,
            'rf_top5': rf_top,
            'permutation_top5': perm_top,
            'correlation_top5': corr_top,
            'most_consistent': max(consensus_ranking.items(), key=lambda x: x[1]) if consensus_ranking else None
        }
    
    def _generate_recommendations(self, rf_importance: Dict[str, float],
                                correlation_analysis: Dict[str, Any],
                                sensitivity_analysis: Dict[str, float]) -> List[str]:
        """Generate actionable recommendations based on importance analysis."""
        
        recommendations = []
        
        # Top important parameters
        top_rf = list(rf_importance.keys())[:3]
        recommendations.append(f"🎯 Focus optimization on: {', '.join(top_rf)}")
        
        # Highly correlated parameters
        high_corr = correlation_analysis.get('high_correlations', [])
        if high_corr:
            recommendations.append(f"⚠️  Consider removing redundant parameters: {len(high_corr)} highly correlated pairs found")
        
        # Low importance parameters
        low_importance = [param for param, score in rf_importance.items() if score < 0.01]
        if low_importance:
            recommendations.append(f"🗑️  Consider removing low-impact parameters: {', '.join(low_importance[:3])}")
        
        # High sensitivity parameters
        top_sensitive = list(sensitivity_analysis.keys())[:2]
        recommendations.append(f"🔧 Fine-tune these sensitive parameters: {', '.join(top_sensitive)}")
        
        return recommendations
    
    def _save_importance_analysis(self, results: Dict[str, Any]) -> None:
        """Save importance analysis results."""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save summary as text
        summary_file = os.path.join(self.results_dir, f"parameter_importance_summary_{timestamp}.txt")
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("Parameter Importance Analysis Summary\n")
            f.write("="*50 + "\n\n")
            
            f.write("Random Forest Importance (Top 10):\n")
            for i, (param, score) in enumerate(list(results['random_forest_importance'].items())[:10]):
                f.write(f"  {i+1:2d}. {param:30s}: {score:.6f}\n")
            
            f.write("\nRecommendations:\n")
            for rec in results['recommendations']:
                f.write(f"  • {rec}\n")
        
        print(f"   💾 Importance analysis saved to: {summary_file}")


def test_parameter_importance():
    """Test the parameter importance analyzer with sample data."""
    
    print("🧪 Testing Parameter Importance Analyzer")
    print("="*60)
    
    # Create sample optimization history
    np.random.seed(42)
    n_samples = 50
    
    sample_history = []
    for i in range(n_samples):
        # Create correlated parameters to test analysis
        stop_percent = np.random.uniform(0.005, 0.05)
        trail_buffer = np.random.uniform(0.001, 0.02)
        rsi_period = np.random.randint(7, 21)
        
        # Create synthetic performance based on parameters
        # Make stop_percent most important, rsi_period moderately important
        performance = (
            -2.0 * stop_percent +  # Lower stop loss = better performance
            1.0 * trail_buffer +   # Higher trail buffer = better performance
            0.1 * (14 - abs(rsi_period - 14)) +  # RSI near 14 is better
            np.random.normal(0, 0.1)  # Add noise
        )
        
        sample_history.append({
            'iteration': i,
            'stop_percent': stop_percent,
            'trail_profit_buffer_pct': trail_buffer,
            'rsi_period': rsi_period,
            'rsi_overbought': np.random.uniform(70, 90),
            'short_sma_period': np.random.randint(5, 15),
            'sharpe_ratio': performance,
            'score': performance
        })
    
    # Test analyzer
    analyzer = ParameterImportanceAnalyzer()
    results = analyzer.analyze_from_optimization_history(sample_history, 'sharpe_ratio')
    
    if 'error' not in results:
        print(f"\n✅ Parameter importance analysis completed!")
        print(f"   📊 Analyzed {len(sample_history)} optimization runs")
        print(f"   🎯 Generated {len(results['recommendations'])} recommendations")
        
        # Show consensus ranking
        consensus = results['summary']['consensus_ranking']
        print(f"\n🏆 Consensus Parameter Ranking:")
        for i, (param, count) in enumerate(list(consensus.items())[:5]):
            print(f"   {i+1}. {param}: appears in {count}/3 top-5 lists")
        
        return True
    else:
        print(f"❌ Analysis failed: {results['error']}")
        return False


if __name__ == "__main__":
    test_parameter_importance()
