import os
import pandas as pd
from datetime import datetime, UTC
import logging

# Configure logging for this script
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prune_csv_file(file_path: str, max_size_bytes: int):
    if not os.path.exists(file_path):
        logger.info(f"File not found: {file_path}. No pruning needed.")
        return

    current_size = os.path.getsize(file_path)
    logger.info(f"Current size of {file_path}: {current_size / (1024*1024):.2f} MB")

    if current_size <= max_size_bytes:
        logger.info(f"File size is within limit ({max_size_bytes / (1024*1024):.2f} MB). No pruning needed.")
        return

    logger.warning(f"File {file_path} exceeds {max_size_bytes / (1024*1024):.2f} MB. Pruning...")

    lines_to_keep = []
    bytes_kept = 0
    
    # Read file in reverse to keep most recent data
    with open(file_path, 'rb') as f: # Open in binary mode for seeking from end
        f.seek(0, os.SEEK_END)
        file_size = f.tell()
        
        # Start reading from the end, line by line
        offset = file_size - 1
        while offset >= 0 and bytes_kept < max_size_bytes:
            f.seek(offset)
            char = f.read(1)
            if char == b'\n' and offset != file_size - 1: # Found a newline, and not the very last char
                # Read the line
                line_start = f.tell()
                line = f.readline().decode('utf-8')
                lines_to_keep.append(line)
                bytes_kept += len(line.encode('utf-8')) # Add encoded length
            offset -= 1
        
        # Handle the first line if it's not preceded by a newline (i.e., the very beginning of the file)
        if offset < 0 and bytes_kept < max_size_bytes:
            f.seek(0)
            first_line = f.read(file_size - bytes_kept).decode('utf-8') # Read remaining part
            lines_to_keep.append(first_line)

    # Reverse the lines as they were read from end to start
    lines_to_keep.reverse()

    # Write to a temporary file
    temp_file_path = file_path + ".tmp"
    with open(temp_file_path, 'w', newline='') as temp_f:
        temp_f.writelines(lines_to_keep)

    # Replace original file with the pruned one
    os.replace(temp_file_path, file_path)
    logger.info(f"Pruning complete. New size of {file_path}: {os.path.getsize(file_path) / (1024*1024):.2f} MB")

if __name__ == "__main__":
    # This script is intended to be called from main_bot.py with the file path.
    # For standalone testing, you can uncomment and modify the following lines:
    # import sys
    # if len(sys.argv) > 2:
    #     file_to_prune = sys.argv[1]
    #     max_size_mb = int(sys.argv[2])
    #     prune_csv_file(file_to_prune, max_size_mb * 1024 * 1024)
    # else:
    #     print("Usage: python prune_historical_data.py <file_path> <max_size_mb>")
    pass