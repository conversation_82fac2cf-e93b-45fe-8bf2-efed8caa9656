#!/usr/bin/env python3
"""
Credential Migration Script for Bitcoin AI Trading Bot
Migrates credentials from .env file to secure encrypted storage
"""

import os
import sys
import logging
import shutil
from datetime import datetime

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from security.credential_manager import SecureCredentialManager
from security.secure_config import validate_security_setup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def backup_env_file(env_file: str = ".env") -> str:
    """Create a backup of the .env file"""
    try:
        if not os.path.exists(env_file):
            logger.warning(f"No {env_file} file found to backup")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{env_file}.backup_{timestamp}"
        
        shutil.copy2(env_file, backup_file)
        logger.info(f"✅ Created backup: {backup_file}")
        return backup_file
        
    except Exception as e:
        logger.error(f"❌ Failed to backup {env_file}: {e}")
        return None

def secure_delete_env_file(env_file: str = ".env") -> bool:
    """Securely delete the .env file after migration"""
    try:
        if not os.path.exists(env_file):
            logger.info(f"No {env_file} file to delete")
            return True
        
        # Get user confirmation
        print(f"\n🚨 IMPORTANT: About to delete {env_file}")
        print("This file contains your API keys in plaintext.")
        print("After migration, credentials will be stored securely encrypted.")
        print("\nA backup has been created for safety.")
        
        response = input("\nProceed with deleting the original .env file? (yes/no): ").lower().strip()
        
        if response in ['yes', 'y']:
            # Overwrite file with random data before deletion (basic secure delete)
            file_size = os.path.getsize(env_file)
            with open(env_file, 'wb') as f:
                f.write(os.urandom(file_size))
            
            os.remove(env_file)
            logger.info(f"✅ Securely deleted {env_file}")
            return True
        else:
            logger.info(f"⚠️ Keeping {env_file} - REMEMBER TO DELETE IT MANUALLY")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to delete {env_file}: {e}")
        return False

def create_secure_env_template() -> bool:
    """Create a secure .env template"""
    try:
        template_content = """# Bitcoin AI Trading Bot - Secure Configuration Template
# 
# IMPORTANT: Real API keys are now stored in encrypted format
# This template shows environment variables that can be used as fallbacks
#
# To set master password (optional - will prompt if not set):
# TRADING_BOT_MASTER_PASSWORD="your_secure_master_password"
#
# Fallback environment variables (only used if secure storage fails):
# GEMINI_API_KEY="your_gemini_api_key"
# ALPACA_LIVE_API_KEY_ID="your_alpaca_live_key_id"
# ALPACA_LIVE_SECRET_KEY="your_alpaca_live_secret"
# ALPACA_PAPER_API_KEY_ID="your_alpaca_paper_key_id"
# ALPACA_PAPER_SECRET_KEY="your_alpaca_paper_secret"
#
# Non-sensitive configuration:
TRADING_BOT_LOG_LEVEL="INFO"
TRADING_BOT_ENVIRONMENT="production"
"""
        
        with open(".env.template", 'w') as f:
            f.write(template_content)
        
        logger.info("✅ Created .env.template with secure configuration guide")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create .env template: {e}")
        return False

def main():
    """Main migration process"""
    print("🔐 Bitcoin AI Trading Bot - Credential Migration")
    print("=" * 50)
    
    try:
        # Step 1: Check if .env file exists
        env_file = ".env"
        if not os.path.exists(env_file):
            print(f"❌ No {env_file} file found. Nothing to migrate.")
            return False
        
        print(f"📁 Found {env_file} file")
        
        # Step 2: Create backup
        print("\n🔄 Creating backup...")
        backup_file = backup_env_file(env_file)
        if not backup_file:
            print("❌ Failed to create backup. Aborting migration.")
            return False
        
        # Step 3: Initialize secure credential manager
        print("\n🔐 Initializing secure credential storage...")

        # Set master password in environment to avoid multiple prompts
        if "TRADING_BOT_MASTER_PASSWORD" not in os.environ:
            print("🔑 Please enter a master password for credential encryption:")
            master_password = input("Master Password: ").strip()
            if not master_password:
                print("❌ Master password cannot be empty.")
                return False
            os.environ["TRADING_BOT_MASTER_PASSWORD"] = master_password

        manager = SecureCredentialManager()

        if not manager.initialize_credentials():
            print("❌ Failed to initialize secure credential storage.")
            return False

        # Unlock credentials for migration
        if not manager.unlock_credentials():
            print("❌ Failed to unlock credential storage.")
            return False

        # Step 4: Migrate credentials
        print("\n🔄 Migrating credentials...")
        if not manager.migrate_from_env(env_file):
            print("❌ Failed to migrate credentials.")
            return False
        
        # Step 5: Validate migration
        print("\n✅ Validating migration...")
        if not validate_security_setup():
            print("❌ Migration validation failed.")
            return False
        
        # Step 6: Create secure template
        print("\n📝 Creating secure configuration template...")
        create_secure_env_template()
        
        # Step 7: Secure delete original .env file
        print("\n🗑️ Cleaning up original .env file...")
        secure_delete_env_file(env_file)
        
        print("\n" + "=" * 50)
        print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print(f"✅ Credentials migrated to encrypted storage")
        print(f"✅ Backup created: {backup_file}")
        print(f"✅ Template created: .env.template")
        print("\n🔒 Your API keys are now stored securely encrypted.")
        print("🔑 You will be prompted for the master password when starting the bot.")
        print("\n⚠️  IMPORTANT: Keep your master password safe!")
        print("⚠️  If you lose it, you'll need to re-enter your API keys.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        print(f"\n❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
