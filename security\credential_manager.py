"""
Secure Credential Management System for Bitcoin AI Trading Bot
Provides encrypted storage and secure access to API credentials
"""

import os
import json
import base64
import logging
from typing import Dict, Optional, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import getpass
import hashlib

logger = logging.getLogger(__name__)

class SecureCredentialManager:
    """
    Secure credential management with encryption and access controls
    """
    
    def __init__(self, credentials_file: str = "security/credentials.enc"):
        """Initialize the credential manager"""
        self.credentials_file = credentials_file
        self.credentials_dir = os.path.dirname(credentials_file)
        self._fernet = None
        self._credentials = {}
        self._unlocked = False  # Track if credentials are already unlocked

        # Ensure security directory exists
        if not os.path.exists(self.credentials_dir):
            os.makedirs(self.credentials_dir, mode=0o700)  # Owner only

        # Set secure file permissions
        self._set_secure_permissions()
    
    def _set_secure_permissions(self):
        """Set secure file permissions for credentials directory"""
        try:
            if os.path.exists(self.credentials_dir):
                os.chmod(self.credentials_dir, 0o700)  # Owner read/write/execute only
            if os.path.exists(self.credentials_file):
                os.chmod(self.credentials_file, 0o600)  # Owner read/write only
        except Exception as e:
            logger.warning(f"Could not set secure permissions: {e}")
    
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def _get_master_password(self) -> str:
        """Get master password for credential encryption"""
        # In production, this could be from environment variable or secure input
        password = os.getenv("TRADING_BOT_MASTER_PASSWORD")
        if not password:
            print("🔐 Enter master password for credential access:")
            print("💡 Hint: SecureBitcoinBot2025! (you can copy-paste this)")
            password = getpass.getpass("Password: ")

            # Check if password seems too short (likely empty paste)
            if len(password.strip()) < 5:
                print("⚠️  Password seems too short. Did the paste work?")
                print("💡 Try copying 'SecureBitcoinBot2025!' and pasting again")
                password = getpass.getpass("Password (try again): ")

        return password
    
    def initialize_credentials(self, force_reset: bool = False) -> bool:
        """Initialize encrypted credential storage"""
        try:
            if os.path.exists(self.credentials_file) and not force_reset:
                logger.info("Credential file already exists")
                return True
            
            print("🔐 Initializing secure credential storage...")
            password = self._get_master_password()
            
            # Generate salt
            salt = os.urandom(16)
            key = self._derive_key(password, salt)
            self._fernet = Fernet(key)
            
            # Create initial empty credentials
            encrypted_data = self._fernet.encrypt(json.dumps({}).encode())
            
            # Store salt + encrypted data
            with open(self.credentials_file, 'wb') as f:
                f.write(salt + encrypted_data)
            
            self._set_secure_permissions()
            logger.info("✅ Secure credential storage initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize credentials: {e}")
            return False
    
    def unlock_credentials(self) -> bool:
        """Unlock and load encrypted credentials"""
        try:
            # If already unlocked, return True
            if self._unlocked and self._fernet is not None:
                return True

            if not os.path.exists(self.credentials_file):
                logger.error("Credential file not found. Run initialize_credentials() first.")
                return False

            password = self._get_master_password()

            # Read salt + encrypted data
            with open(self.credentials_file, 'rb') as f:
                data = f.read()

            salt = data[:16]
            encrypted_data = data[16:]

            # Derive key and decrypt
            key = self._derive_key(password, salt)
            self._fernet = Fernet(key)

            decrypted_data = self._fernet.decrypt(encrypted_data)
            self._credentials = json.loads(decrypted_data.decode())

            self._unlocked = True  # Mark as unlocked
            logger.info("✅ Credentials unlocked successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to unlock credentials: {e}")
            self._unlocked = False
            return False
    
    def store_credential(self, key: str, value: str) -> bool:
        """Store a credential securely"""
        try:
            if self._fernet is None:
                logger.error("Credentials not unlocked. Call unlock_credentials() first.")
                return False
            
            self._credentials[key] = value
            
            # Encrypt and save
            encrypted_data = self._fernet.encrypt(json.dumps(self._credentials).encode())
            
            # Read existing salt
            with open(self.credentials_file, 'rb') as f:
                salt = f.read(16)
            
            # Write salt + new encrypted data
            with open(self.credentials_file, 'wb') as f:
                f.write(salt + encrypted_data)
            
            self._set_secure_permissions()
            logger.info(f"✅ Credential '{key}' stored securely")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store credential '{key}': {e}")
            return False
    
    def get_credential(self, key: str) -> Optional[str]:
        """Retrieve a credential securely"""
        try:
            if self._fernet is None:
                logger.error("Credentials not unlocked. Call unlock_credentials() first.")
                return None
            
            return self._credentials.get(key)
            
        except Exception as e:
            logger.error(f"Failed to retrieve credential '{key}': {e}")
            return None
    
    def list_credential_keys(self) -> list:
        """List available credential keys (not values)"""
        if self._fernet is None:
            logger.error("Credentials not unlocked.")
            return []
        return list(self._credentials.keys())
    
    def migrate_from_env(self, env_file: str = ".env") -> bool:
        """Migrate credentials from .env file to secure storage"""
        try:
            if not os.path.exists(env_file):
                logger.warning(f"Environment file {env_file} not found")
                return False
            
            print(f"🔄 Migrating credentials from {env_file}...")
            
            # Read .env file
            env_vars = {}
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        # Remove quotes if present
                        value = value.strip('"\'')
                        env_vars[key] = value
            
            # Store important credentials
            credential_keys = [
                'GEMINI_API_KEY',
                'ALPACA_LIVE_API_KEY_ID',
                'ALPACA_LIVE_SECRET_KEY',
                'ALPACA_PAPER_API_KEY_ID',
                'ALPACA_PAPER_SECRET_KEY'
            ]
            
            migrated_count = 0
            for key in credential_keys:
                if key in env_vars:
                    if self.store_credential(key, env_vars[key]):
                        migrated_count += 1
                        logger.info(f"✅ Migrated {key}")
            
            logger.info(f"✅ Migration complete: {migrated_count} credentials migrated")
            return migrated_count > 0
            
        except Exception as e:
            logger.error(f"Failed to migrate credentials: {e}")
            return False
    
    def create_secure_env_template(self) -> bool:
        """Create a secure .env template without real credentials"""
        try:
            template_content = """# Secure Environment Template
# Real credentials are now stored in encrypted format
# Set these environment variables or use the credential manager

# Master password for credential encryption (optional - will prompt if not set)
# TRADING_BOT_MASTER_PASSWORD="your_secure_master_password"

# Backup environment variables (will be used if credential manager fails)
# GEMINI_API_KEY="backup_gemini_key"
# ALPACA_LIVE_API_KEY_ID="backup_alpaca_key_id"
# ALPACA_LIVE_SECRET_KEY="backup_alpaca_secret"

# Non-sensitive configuration
TRADING_BOT_LOG_LEVEL="INFO"
TRADING_BOT_ENVIRONMENT="production"
"""
            
            with open(".env.template", 'w') as f:
                f.write(template_content)
            
            logger.info("✅ Created secure .env template")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create template: {e}")
            return False

# Global instance
_credential_manager = None

def get_credential_manager() -> SecureCredentialManager:
    """Get the global credential manager instance"""
    global _credential_manager
    if _credential_manager is None:
        _credential_manager = SecureCredentialManager()
    return _credential_manager

def get_secure_credential(key: str, fallback_env: bool = True) -> Optional[str]:
    """
    Get a credential securely with fallback to environment variables
    
    Args:
        key: Credential key to retrieve
        fallback_env: Whether to fallback to environment variables if secure storage fails
    
    Returns:
        Credential value or None if not found
    """
    try:
        # Try secure credential manager first
        manager = get_credential_manager()
        if manager.unlock_credentials():
            credential = manager.get_credential(key)
            if credential:
                return credential
        
        # Fallback to environment variables if enabled
        if fallback_env:
            env_value = os.getenv(key)
            if env_value:
                logger.warning(f"Using fallback environment variable for {key}")
                return env_value
        
        logger.error(f"Credential '{key}' not found in secure storage or environment")
        return None
        
    except Exception as e:
        logger.error(f"Error retrieving credential '{key}': {e}")
        if fallback_env:
            return os.getenv(key)
        return None
