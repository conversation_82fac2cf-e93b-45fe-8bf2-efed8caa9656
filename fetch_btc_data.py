from config import ALPACA_API_KEY_ID, ALPACA_SECRET_KEY, ALPACA_BASE_URL
from alpaca_trade_api.rest import REST
from alpaca_service import get_historical_crypto_data
import pandas as pd

def main():
    api = REST(ALPACA_API_KEY_ID, ALPACA_SECRET_KEY, base_url=ALPACA_BASE_URL)
    data = get_historical_crypto_data(api, 'BTC/USD', 'Minute', 43200)  # 30 days of 1-min bars
    df = pd.DataFrame(data)
    df.to_csv('btc_30d_1min.csv', index=False)
    print('Saved 30 days of 1-min BTCUSD data to btc_30d_1min.csv')

if __name__ == '__main__':
    main()
