# START OF FILE dashboard.py

import streamlit as st
import pandas as pd
import json
import os
import time  # NEW: Import the 'time' module for the sleep function
from datetime import datetime, timezone

# --- Page Configuration ---
st.set_page_config(
    page_title="Trading Bot Live Dashboard",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# --- File Paths ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
OPEN_LOTS_FILE = os.path.join(BASE_DIR, "open_lots.json")
SESSION_STATS_FILE = os.path.join(BASE_DIR, "session_stats.json")
TRADE_CYCLE_LOG_FILE = os.path.join(BASE_DIR, "trade_cycle_log.csv")

# --- Helper Functions ---
def load_json(file_path, default=None):
    """Safely load a JSON file."""
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            st.error(f"Error loading {os.path.basename(file_path)}: {e}")
            return default
    return default

def load_csv(file_path, default_df=None, **kwargs):
    """Safely load a CSV file with flexible options."""
    if os.path.exists(file_path):
        try:
            return pd.read_csv(file_path, **kwargs)
        except Exception as e:
            st.error(f"Error loading {os.path.basename(file_path)}: {e}")
            return default_df
    return default_df

# --- Main Dashboard Logic ---
st.title("📈 Trading Bot Live Dashboard")
st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# --- Load Data ---
session_stats = load_json(SESSION_STATS_FILE, {})
open_lots_data = load_json(OPEN_LOTS_FILE, [])
trade_history = load_csv(TRADE_HISTORY_FILE, pd.DataFrame())
trade_cycle_log = load_csv(
    TRADE_CYCLE_LOG_FILE, 
    pd.DataFrame(), 
    on_bad_lines='skip', 
    encoding='utf-8', 
    encoding_errors='ignore'
)

# --- Metrics Row ---
col1, col2, col3, col4 = st.columns(4)

current_equity = session_stats.get('last_equity', 0.0)
col1.metric("Current Equity", f"${float(current_equity):,.2f}")

open_pl = 0.0
if not trade_cycle_log.empty:
    try:
        open_pl = trade_cycle_log['profit_loss'].iloc[-1]
    except (IndexError, KeyError):
        open_pl = 0.0 
col2.metric("Unrealized P/L", f"${float(open_pl):,.2f}")

net_pl = session_stats.get('net_pl', 0.0)
col3.metric("Session Realized P/L", f"${float(net_pl):,.2f}")

num_open_lots = len(open_lots_data)
col4.metric("Open Positions", f"{num_open_lots}")

st.divider()

# --- Display Open Positions ---
st.subheader("Current Open Positions")
if open_lots_data:
    open_lots_df = pd.DataFrame(open_lots_data)
    display_cols = ['order_id', 'qty', 'entry_price', 'stop', 'type', 'last_buy_timestamp']
    open_lots_df = open_lots_df[[col for col in display_cols if col in open_lots_df.columns]]
    st.dataframe(open_lots_df, use_container_width=True)
else:
    st.info("No open positions at the moment.")

st.divider()

# --- Display Today's Closed Trades ---
st.subheader("Today's Closed Trades")
if not trade_history.empty:
    if 'timestamp' not in trade_history.columns:
        st.error("Could not process trade history: 'timestamp' column not found in CSV file.")
    else:
        try:
            trade_history['timestamp'] = pd.to_datetime(trade_history['timestamp'], errors='coerce', utc=True)
            trade_history.dropna(subset=['timestamp'], inplace=True)
            
            if pd.api.types.is_datetime64_any_dtype(trade_history['timestamp']):
                today_utc = datetime.now(timezone.utc).date()
                todays_trades = trade_history[
                    (trade_history['timestamp'].dt.date == today_utc) &
                    (trade_history['status'] == 'filled')
                ].copy()

                if not todays_trades.empty:
                    todays_trades['realized_pl'] = pd.to_numeric(todays_trades['realized_pl'], errors='coerce').fillna(0)
                    todays_trades['price'] = pd.to_numeric(todays_trades['price'], errors='coerce').fillna(0)
                    
                    display_cols_history = ['timestamp', 'action', 'quantity', 'price', 'realized_pl', 'fee']
                    st.dataframe(
                        todays_trades[display_cols_history].style.format({
                            'price': '${:,.4f}',
                            'realized_pl': '${:,.4f}',
                            'fee': '${:,.4f}'
                        }),
                        use_container_width=True
                    )
                else:
                    st.info("No closed trades recorded today.")
            else:
                st.error("Failed to convert 'timestamp' column to a readable date format. Please check the trade_history.csv file for corruption.")

        except Exception as e:
            st.error(f"An unexpected error occurred while processing trade history: {e}")
else:
    st.info("Trade history is empty.")

st.sidebar.info("This dashboard reads data from the bot's log and state files.")
# MODIFIED: The caption now indicates that the page auto-refreshes.
st.caption("This dashboard will automatically refresh every 90 seconds.")

# --- AUTO-REFRESH LOGIC ---
# This will pause the script for 90 seconds and then tell Streamlit to rerun it from the top.
time.sleep(90)
st.rerun()