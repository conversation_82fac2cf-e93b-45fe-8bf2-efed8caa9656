# --- Analysis of your Bitcoin AI Agent CSV Files ---
Write-Host ""

# --- Analyzing binance_btcusdt_1m.csv (Primary Merged File) ---
Write-Host "--- binance_btcusdt_1m.csv (Primary Merged File) ---"
If (Test-Path "binance_btcusdt_1m.csv") {
    Write-Host "First 10 lines:"
    Get-Content "binance_btcusdt_1m.csv" -TotalCount 10
    Write-Host ""
    Write-Host "Last 10 lines:"
    Get-Content "binance_btcusdt_1m.csv" -Tail 10
    $mainFileLines = (Get-Content "binance_btcusdt_1m.csv" | Measure-Object -Line).Lines
    Write-Host "Total lines (including header): $mainFileLines"
} else {
    Write-Host "ERROR: File 'binance_btcusdt_1m.csv' not found."
    $mainFileLines = 0
}
Write-Host ""

# --- Analyzing binance_btcusdt_1m_NEW_DATA.csv (Newly Downloaded Data) ---
Write-Host "--- binance_btcusdt_1m_NEW_DATA.csv (Newly Downloaded Data) ---"
If (Test-Path "binance_btcusdt_1m_NEW_DATA.csv") {
    Write-Host "First 10 lines:"
    Get-Content "binance_btcusdt_1m_NEW_DATA.csv" -TotalCount 10
    Write-Host ""
    Write-Host "Last 10 lines:"
    Get-Content "binance_btcusdt_1m_NEW_DATA.csv" -Tail 10
    $newDataFileLines = (Get-Content "binance_btcusdt_1m_NEW_DATA.csv" | Measure-Object -Line).Lines
    Write-Host "Total lines (including header): $newDataFileLines"
} else {
    Write-Host "ERROR: File 'binance_btcusdt_1m_NEW_DATA.csv' not found."
    $newDataFileLines = 0
}
Write-Host ""

# --- Analyzing binance_btcusdt_1m_BACKUP_2021-03-03.csv (Backup File) ---
Write-Host "--- binance_btcusdt_1m_BACKUP_2021-03-03.csv (Backup File) ---"
If (Test-Path "binance_btcusdt_1m_BACKUP_2021-03-03.csv") {
    Write-Host "First 10 lines:"
    Get-Content "binance_btcusdt_1m_BACKUP_2021-03-03.csv" -TotalCount 10
    Write-Host ""
    Write-Host "Last 10 lines:"
    Get-Content "binance_btcusdt_1m_BACKUP_2021-03-03.csv" -Tail 10
    $backupFileLines = (Get-Content "binance_btcusdt_1m_BACKUP_2021-03-03.csv" | Measure-Object -Line).Lines
    Write-Host "Total lines (including header): $backupFileLines"
} else {
    Write-Host "ERROR: File 'binance_btcusdt_1m_BACKUP_2021-03-03.csv' not found. (Check filename if it differs from this exact name)."
    $backupFileLines = 0
}
Write-Host ""

# --- Summary and Cross-Checks ---
Write-Host "--- Summary and Cross-Checks ---"
$mainDataRows = $mainFileLines - 1
$newDataDataRows = $newDataFileLines - 1
$backupDataRows = $backupFileLines - 1

Write-Host "Primary Merged File Data Rows: $mainDataRows"
Write-Host "Newly Downloaded File Data Rows: $newDataDataRows"
Write-Host "Backup File Data Rows: $backupDataRows"

$expectedMergedRows = $newDataDataRows + $backupDataRows
Write-Host "Expected total data rows if New Data + Backup are perfectly merged: $expectedMergedRows"

If ($mainDataRows -eq $expectedMergedRows) {
    Write-Host "-> RESULT: The primary file's total data row count EXACTLY matches the sum of new data and backup data. This is a VERY strong indicator of a successful, complete merge (append)."
} Else {
    Write-Host "-> WARNING: The primary file's total data row count ($mainDataRows) does NOT exactly match the sum of new data and backup data ($expectedMergedRows). This could indicate a partial merge, data loss, overwrite, or another issue."
}

Write-Host ""
Write-Host "--- Analysis Limitations ---"
Write-Host "This check verifies file existence, headers, start/end timestamps, and overall line counts. It does NOT thoroughly check every single line for numerical errors or subtle, intermittent gaps within the vast amount of data."