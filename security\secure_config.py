"""
Secure Configuration Loader for Bitcoin AI Trading Bot
Integrates with existing config.py while providing secure credential access
"""

import os
import logging
from typing import Optional
from security.credential_manager import get_secure_credential

logger = logging.getLogger(__name__)

class SecureConfig:
    """
    Secure configuration loader that integrates with existing config system
    """
    
    def __init__(self):
        """Initialize secure configuration"""
        self._credentials_loaded = False
        self._load_credentials()
    
    def _load_credentials(self):
        """Load credentials from secure storage"""
        try:
            # Test if we can access secure credentials
            test_key = get_secure_credential("GEMINI_API_KEY", fallback_env=False)
            if test_key:
                self._credentials_loaded = True
                logger.info("✅ Secure credentials loaded successfully")
            else:
                logger.warning("⚠️ Secure credentials not available, using environment fallback")
        except Exception as e:
            logger.warning(f"⚠️ Could not load secure credentials: {e}")
    
    def get_api_key(self, key_name: str) -> Optional[str]:
        """
        Get API key securely with validation
        
        Args:
            key_name: Name of the API key (e.g., 'GEMINI_API_KEY')
        
        Returns:
            API key value or None if not found
        """
        try:
            # Get credential from secure storage with environment fallback
            credential = get_secure_credential(key_name, fallback_env=True)
            
            if not credential:
                logger.error(f"❌ API key '{key_name}' not found in secure storage or environment")
                return None
            
            # Basic validation
            if len(credential.strip()) < 10:
                logger.error(f"❌ API key '{key_name}' appears to be invalid (too short)")
                return None
            
            # Don't log the actual key value
            logger.debug(f"✅ API key '{key_name}' retrieved successfully")
            return credential.strip()
            
        except Exception as e:
            logger.error(f"❌ Error retrieving API key '{key_name}': {e}")
            return None
    
    def validate_required_credentials(self) -> bool:
        """
        Validate that all required credentials are available
        
        Returns:
            True if all required credentials are available, False otherwise
        """
        required_keys = [
            'GEMINI_API_KEY',
            'ALPACA_LIVE_API_KEY_ID',
            'ALPACA_LIVE_SECRET_KEY'
        ]
        
        missing_keys = []
        for key in required_keys:
            if not self.get_api_key(key):
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"❌ Missing required credentials: {missing_keys}")
            return False
        
        logger.info("✅ All required credentials validated")
        return True
    
    def get_alpaca_credentials(self, is_live_trading: bool = True) -> tuple:
        """
        Get Alpaca API credentials for live or paper trading
        
        Args:
            is_live_trading: True for live trading, False for paper trading
        
        Returns:
            Tuple of (api_key_id, secret_key, base_url)
        """
        try:
            if is_live_trading:
                api_key_id = self.get_api_key('ALPACA_LIVE_API_KEY_ID')
                secret_key = self.get_api_key('ALPACA_LIVE_SECRET_KEY')
                base_url = "https://api.alpaca.markets"
                logger.info("🔴 Using LIVE Alpaca trading credentials")
            else:
                api_key_id = self.get_api_key('ALPACA_PAPER_API_KEY_ID')
                secret_key = self.get_api_key('ALPACA_PAPER_SECRET_KEY')
                base_url = "https://paper-api.alpaca.markets"
                logger.info("📄 Using PAPER Alpaca trading credentials")
            
            if not api_key_id or not secret_key:
                logger.error(f"❌ Missing Alpaca credentials for {'LIVE' if is_live_trading else 'PAPER'} trading")
                return None, None, None
            
            return api_key_id, secret_key, base_url
            
        except Exception as e:
            logger.error(f"❌ Error getting Alpaca credentials: {e}")
            return None, None, None
    
    def get_gemini_api_key(self) -> Optional[str]:
        """
        Get Gemini API key securely
        
        Returns:
            Gemini API key or None if not found
        """
        return self.get_api_key('GEMINI_API_KEY')
    
    def is_secure_mode(self) -> bool:
        """
        Check if running in secure mode (credentials from encrypted storage)
        
        Returns:
            True if using secure credential storage, False if using environment fallback
        """
        return self._credentials_loaded

# Global secure config instance
_secure_config = None

def get_secure_config() -> SecureConfig:
    """Get the global secure configuration instance"""
    global _secure_config
    if _secure_config is None:
        _secure_config = SecureConfig()
    return _secure_config

def get_secure_api_key(key_name: str) -> Optional[str]:
    """
    Convenience function to get API key securely
    
    Args:
        key_name: Name of the API key
    
    Returns:
        API key value or None if not found
    """
    return get_secure_config().get_api_key(key_name)

def validate_security_setup() -> bool:
    """
    Validate that security setup is working correctly
    
    Returns:
        True if security setup is valid, False otherwise
    """
    try:
        config = get_secure_config()
        
        # Check if we can access credentials
        if not config.validate_required_credentials():
            logger.error("❌ Security validation failed: Missing required credentials")
            return False
        
        # Check if we're in secure mode
        if not config.is_secure_mode():
            logger.warning("⚠️ Running in fallback mode (environment variables)")
            logger.warning("⚠️ Consider migrating to secure credential storage")
        
        logger.info("✅ Security setup validation passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Security validation error: {e}")
        return False
