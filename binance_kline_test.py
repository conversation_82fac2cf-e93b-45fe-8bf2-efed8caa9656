# binance_data_service.py
import websocket # From websocket-client library
import json
import logging
import time
import threading # We'll use this later for running in main_bot, but good to have
import pandas as pd # For parsing timestamps in the logger

# Configure basic logging for this module
# Using __name__ will make log messages show 'binance_data_service' as the source
logger = logging.getLogger(__name__) 
# If you want to see these logs when running this file directly, you'll need basicConfig in the if __name__ == '__main__': block

# --- Global variable to control the main loop of the client, for graceful shutdown ---
# This will be set by a signal handler if we run this file directly for testing
shutdown_event = threading.Event()

def on_message(ws, message):
    """Called when a new message is received from the WebSocket."""
    data = json.loads(message)
    
    if 'e' in data and data['e'] == 'kline':
        kline_data = data['k']
        symbol = kline_data['s']
        interval = kline_data['i']
        
        if symbol == "BTCUSDT" and interval == "1m": # Ensure it's the kline we want
            open_price = kline_data['o']
            close_price = kline_data['c']
            high_price = kline_data['h']
            low_price = kline_data['l']
            volume = kline_data['v']
            # Using pandas to easily convert timestamp, though could do it manually
            kline_start_time = pd.to_datetime(kline_data['t'], unit='ms', utc=True)
            kline_close_time = pd.to_datetime(kline_data['T'], unit='ms', utc=True)
            is_kline_closed = kline_data['x']

            logger.info(
                f"BINANCE KLINE [{symbol}|{interval}] Start: {kline_start_time}, CloseT: {kline_close_time} "
                f"O: {open_price}, H: {high_price}, L: {low_price}, C: {close_price}, V: {volume}, "
                f"Final: {is_kline_closed}"
            )
    elif 'result' in data and data['result'] is None and 'id' in data:
        logger.info(f"Binance subscription confirmation: {data}")
    else:
        # Log other unexpected messages at debug level if needed
        logger.debug(f"Other Binance Message: {message[:200]}") # Log a snippet

def on_error(ws, error):
    """Called when a WebSocket error occurs."""
    logger.error(f"Binance WS Error: {error}")
    # The run_forever loop will typically exit on error, and our outer loop will attempt reconnect.

def on_close(ws, close_status_code, close_msg):
    """Called when the WebSocket connection is closed."""
    logger.warning(f"Binance WS Connection Closed. Code: {close_status_code}, Msg: {close_msg}")

def on_open(ws_app_instance): # Renamed 'ws' to 'ws_app_instance' to avoid conflict with outer scope 'ws'
    """Called when the WebSocket connection is successfully opened."""
    logger.info("Binance WS Connection Opened. Subscribing to BTCUSDT 1m klines...")
    subscribe_message = {
        "method": "SUBSCRIBE",
        "params": [
            "btcusdt@kline_1m"
        ],
        "id": 1 
    }
    try:
        ws_app_instance.send(json.dumps(subscribe_message))
        logger.info("Subscription message sent to Binance.")
    except Exception as e:
        logger.error(f"Error sending subscription message: {e}")


def run_binance_websocket_client_persistent():
    """
    Runs the Binance WebSocket client and attempts to reconnect on disconnection.
    This function is intended to be run in a thread by main_bot.py eventually.
    """
    ws_url = "wss://stream.binance.com:9443/ws"
    
    while not shutdown_event.is_set(): # Loop controlled by the shutdown_event
        logger.info(f"Binance Data Service: Attempting to connect to {ws_url}...")
        ws_app = websocket.WebSocketApp(ws_url,
                                      on_open=on_open,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close)
        try:
            # run_forever is blocking. It will run until the connection closes or an error.
            # Added ping_interval and ping_timeout for keep-alive.
            # disable_ssl_certificate_validation can be useful for some network environments, but use with caution.
            ws_app.run_forever(ping_interval=25, ping_timeout=10) # sslopt={"cert_reqs": ssl.CERT_NONE} if SSL issues
        except Exception as e:
            logger.error(f"Binance Data Service: WebSocketApp run_forever() encountered an error: {e}")
        
        if shutdown_event.is_set():
            logger.info("Binance Data Service: Shutdown event received, stopping reconnection attempts.")
            break # Exit the while loop

        logger.warning("Binance Data Service: Disconnected or run_forever() exited. Reconnecting in 15 seconds...")
        
        # Wait before attempting to reconnect, but check shutdown_event periodically
        for _ in range(150): # 150 * 0.1s = 15s
            if shutdown_event.is_set():
                break
            time.sleep(0.1)
        
    logger.info("Binance Data Service: run_binance_websocket_client_persistent() is terminating.")


# This block allows you to run this file directly for testing the Binance connection
if __name__ == '__main__':
    # Setup basic logging for direct testing of this file
    logging.basicConfig(level=logging.INFO, 
                        format='%(asctime)s - %(levelname)-8s - %(name)-15s - %(funcName)-20s - %(message)s')
    logger.info("Starting Binance Data Service directly for testing...")

    # Handle Ctrl+C for graceful shutdown when running directly
    def signal_handler(sig, frame):
        logger.warning('Ctrl+C received! Setting shutdown event for Binance client...')
        shutdown_event.set()
    
    import signal
    signal.signal(signal.SIGINT, signal_handler)

    # In a real application, this function would be started in a thread from main_bot.py
    # For testing, we run it directly.
    run_binance_websocket_client_persistent()

    logger.info("Binance Data Service test script finished.")