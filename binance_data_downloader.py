import requests
import csv
import time
import os
from datetime import datetime, timezone

symbol = "BTCUSDT"
interval = "1m"
limit = 1000  # max per request
filename = "binance_btcusdt_1m.csv"

def fetch_klines(start_ts):
    url = "https://api.binance.com/api/v3/klines"
    params = {
        "symbol": symbol,
        "interval": interval,
        "limit": limit,
        "startTime": start_ts
    }
    r = requests.get(url, params=params)
    return r.json()

def get_last_timestamp():
    """Return last open_time (ms) in the CSV, or None if file is empty/missing."""
    if not os.path.exists(filename):
        return None
    try:
        with open(filename, "r") as f:
            last_row = None
            for last_row in f:
                pass
            if last_row:
                return int(last_row.split(",")[0])
    except Exception:
        pass
    return None

def main():
    last_ts = get_last_timestamp()
    if last_ts:
        # Start from 1 min after the last open_time in the file
        start = last_ts + 60_000
    else:
        # No file or empty: start from 30 days ago
        start = int((datetime.now(timezone.utc).timestamp() - 60 * 60 * 24 * 30) * 1000)

    end = int(datetime.now(timezone.utc).timestamp() * 1000)
    header_written = os.path.exists(filename) and os.path.getsize(filename) > 0
    total_rows = 0

    with open(filename, "a", newline='') as f:
        writer = csv.writer(f)
        while start < end:
            data = fetch_klines(start)
            if not data: break
            # Show progress
            from_time = datetime.fromtimestamp(data[0][0] / 1000, tz=timezone.utc)
            to_time = datetime.fromtimestamp(data[-1][0] / 1000, tz=timezone.utc)
            print(f"Fetched {len(data)} rows: {from_time} to {to_time}")

            if not header_written:
                writer.writerow(["open_time", "open", "high", "low", "close", "volume",
                                 "close_time", "quote_asset_volume", "num_trades",
                                 "taker_buy_base", "taker_buy_quote", "ignore"])
                header_written = True
            for row in data:
                writer.writerow(row)
            total_rows += len(data)
            start = data[-1][0] + 60_000  # next candle
            time.sleep(0.2)  # avoid rate limit

    print(f"Download complete. {total_rows} new rows written to {filename}")

if __name__ == "__main__":
    main()
