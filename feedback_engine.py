import csv
from collections import Counter
from statistics import mean

LOG_FILE = "trade_cycle_log.csv"
NUM_CYCLES_TO_REVIEW = 20  # Change this if you want a different window

def try_float(x):
    try:
        return float(x)
    except:
        return 0.0

def main():
    rows = []
    with open(LOG_FILE, newline="") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            rows.append(row)
    if not rows:
        print("No trades logged yet!")
        return

    recent = rows[-NUM_CYCLES_TO_REVIEW:]
    if not recent:
        print("No recent cycles to analyze!")
        return

    # Basic stats
    win, loss, hold = 0, 0, 0
    pnl_list = []
    regime_counter = Counter()
    confidence_counter = Counter()
    reason_counter = Counter()
    action_counter = Counter()
    loss_streak, win_streak = 0, 0
    current_loss_streak, current_win_streak = 0, 0

    for row in recent:
        action = row.get("trade_action", "").upper()
        pnl = try_float(row.get("session_pnl", 0))
        regime = row.get("regime", "")
        confidence = row.get("confidence", "")
        reason = row.get("reason_for_action", "")
        action_counter[action] += 1
        regime_counter[regime] += 1
        confidence_counter[confidence] += 1
        reason_counter[reason] += 1

        if "WIN" in row.get("trade_result", "").upper() or (pnl > 0.01 and action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            win += 1
            pnl_list.append(pnl)
            current_win_streak += 1
            current_loss_streak = 0
        elif "LOSS" in row.get("trade_result", "").upper() or (pnl < -0.01 and action in ["SELL", "CLOSE", "SELL/CLOSE"]):
            loss += 1
            pnl_list.append(pnl)
            current_loss_streak += 1
            current_win_streak = 0
        else:
            hold += 1
            current_win_streak = 0
            current_loss_streak = 0

        loss_streak = max(loss_streak, current_loss_streak)
        win_streak = max(win_streak, current_win_streak)

    print("="*40)
    print(f"FEEDBACK ENGINE (last {NUM_CYCLES_TO_REVIEW} cycles)")
    print("="*40)
    print(f"Wins: {win}, Losses: {loss}, Holds/No Action: {hold}")
    print(f"Win Streak: {win_streak}, Loss Streak: {loss_streak}")
    print(f"Mean PnL: {mean(pnl_list):.2f}" if pnl_list else "Mean PnL: N/A")
    print()

    print("Most Common Market Regime (recent):")
    for regime, count in regime_counter.most_common(3):
        print(f" - {regime or '(blank)'}: {count}")
    print("Most Common Confidence (recent):")
    for conf, count in confidence_counter.most_common(3):
        print(f" - {conf or '(blank)'}: {count}")
    print("Most Common Reason for Action (recent):")
    for reason, count in reason_counter.most_common(3):
        if reason:
            print(f" - {reason}: {count}")
    print()

    # FEEDBACK / COACHING SECTION
    print("== SUGGESTIONS ==")
    if loss_streak >= 3:
        print(f"- Loss streak detected! ({loss_streak} losses in a row). Suggest: Reduce trade size or be more selective.")
    if win_streak >= 3:
        print(f"- Hot streak! ({win_streak} wins in a row). Maintain discipline, don't increase risk unless in strong regime.")
    if hold > (NUM_CYCLES_TO_REVIEW * 0.5):
        print("- Majority of recent cycles were HOLD/No Action. Suggest: Review entry rules or AI confidence logic for opportunities to trade safely.")
    if "volatile" in regime_counter and regime_counter["volatile"] > (NUM_CYCLES_TO_REVIEW * 0.3):
        print("- Volatile regime frequently detected. Suggest: Use defensive trading or stricter risk controls.")
    if confidence_counter.get("LOW", 0) > (NUM_CYCLES_TO_REVIEW * 0.5):
        print("- AI confidence is LOW in most recent cycles. Suggest: Review prompt/inputs or model to ensure it's seeing enough context.")
    if not (win or loss):
        print("- No recent trades detected. This is fine if markets are choppy, but double-check trade logic isn't too strict.")

    print("="*40)

if __name__ == "__main__":
    main()
