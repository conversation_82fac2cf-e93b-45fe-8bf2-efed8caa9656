import csv
import json
import os
import math
import logging
import time
from datetime import datetime
import alpaca_service
import config
from decimal import Decimal

logger = logging.getLogger("TradingBotApp")

BASE_DIR           = os.path.dirname(os.path.abspath(__file__))
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")
LAST_TRADE_FILE    = os.path.join(BASE_DIR, "last_trade.txt")
TRAILING_STOP_FILE = os.path.join(BASE_DIR, "trailing_stop.txt")
LOTS_LEDGER_FILE   = os.path.join(BASE_DIR, "open_lots.json")
DYNAMIC_CONFIG_PATH = os.path.join(BASE_DIR, "dynamic_config.json")

assert 0 < config.STOP_PERCENT < 1, "STOP_PERCENT must be between 0 and 1"
assert len(config.TAKE_PROFITS) == len(config.TAKE_PROFIT_SHARES), \
    "TAKE_PROFITS and TAKE_PROFIT_SHARES must be the same length"

def _load_json(path, default):
    try:
        with open(path) as f:
            return json.load(f)
    except Exception:
        return default

def _save_json(path, data):
    tmp = path + ".tmp"
    with open(tmp, "w") as f:
        json.dump(data, f, indent=2)
    os.replace(tmp, path)

# === Step 8: Continuous Learning ===
def analyze_and_autotune_parameters():
    try:
        if not os.path.exists(TRADE_HISTORY_FILE):
            logger.info("[Learning] No trade history found. Skipping autotune.")
            return
        with open(TRADE_HISTORY_FILE, "r") as f:
            reader = csv.DictReader(f)
            trades = list(reader)
        if len(trades) < 5:
            logger.info("[Learning] Not enough trades to analyze. Skipping autotune.")
            return

        equity = 0
        max_equity = 0
        min_equity = 0
        realized_pnl = 0
        equity_list = []

        for i, trade in enumerate(trades):
            try:
                qty = float(trade["quantity"])
                px = float(trade["price"])
                action = trade["action"].upper()
                if action == "BUY":
                    realized_pnl -= px * qty
                elif action == "SELL":
                    realized_pnl += px * qty
                equity_list.append(realized_pnl)
                if i == 0:
                    max_equity = realized_pnl
                    min_equity = realized_pnl
                else:
                    max_equity = max(max_equity, realized_pnl)
                    min_equity = min(min_equity, realized_pnl)
            except Exception:
                continue

        if max_equity == 0:
            logger.info("[Learning] No realized PNL to analyze.")
            return

        max_drawdown = (max_equity - min_equity) / (abs(max_equity) if max_equity else 1)
        logger.info(f"[Learning] Max Drawdown: {max_drawdown:.4f} ({max_drawdown*100:.2f}%)")

        if max_drawdown > 0.05:
            logger.warning("[Learning] Drawdown exceeded 5%. Auto-lowering risk parameter.")
            dyn = _load_json(DYNAMIC_CONFIG_PATH, {})
            old_risk = dyn.get("risk_percent", getattr(config, "RISK_PERCENT", 0.02))
            new_risk = max(float(old_risk) * 0.8, 0.005)
            dyn["risk_percent"] = new_risk
            dyn["last_update"] = datetime.utcnow().isoformat()
            _save_json(DYNAMIC_CONFIG_PATH, dyn)
            logger.warning(f"[Learning] risk_percent updated: {old_risk} -> {new_risk:.6f} (in dynamic_config.json)")
        else:
            logger.info("[Learning] Drawdown within acceptable limits. No risk adjustment.")
    except Exception as e:
        logger.error(f"[Learning] Autotune failed: {e}")

def rebuild_open_lots(api):
    lots = []
    try:
        orders = api.list_orders(status="filled", direction="asc", limit=1000)
    except Exception as exc:
        logger.error(f"Order history fetch failed: {exc}")
        return []
    for o in orders:
        qty_str   = getattr(o, "filled_qty", None) or getattr(o, "qty", None)
        price_str = getattr(o, "filled_avg_price", None)
        if not qty_str or not price_str:
            continue
        try:
            qty   = float(qty_str)
            price = float(price_str)
        except ValueError:
            continue
        if o.side == "buy":
            lots.append({
                "qty": qty,
                "entry_price": price,
                "stop": price * (1 - config.STOP_PERCENT),
            })
        else:
            remain = qty
            while remain > 0 and lots:
                lot = lots[0]
                take = min(lot["qty"], remain)
                lot["qty"] -= take
                remain    -= take
                if lot["qty"] <= 1e-8:
                    lots.pop(0)
    lots = [l for l in lots if l["qty"] > 1e-8]
    _save_json(LOTS_LEDGER_FILE, lots)
    return lots

def load_open_lots():
    return _load_json(LOTS_LEDGER_FILE, [])

def save_open_lots(lots):
    _save_json(LOTS_LEDGER_FILE, lots)

def cancel_open_btcusd_sell_orders(api):
    try:
        open_orders = api.list_orders(status='open', symbols=['BTCUSD'])
    except Exception as e:
        logger.warning(f"Could not fetch open orders: {e}")
        return
    for order in open_orders:
        if getattr(order, "side", None) == "sell":
            try:
                api.cancel_order(order.id)
                logger.info(f"Cancelled open BTCUSD sell order: {order.id}")
            except Exception as e:
                logger.warning(f"Could not cancel order {order.id}: {e}")

def sync_lots_with_exchange(api, open_lots):
    pos = alpaca_service.get_btc_position(api)
    total_qty = float(pos.get("qty", 0))
    try:
        available_qty = float(pos.get("available", total_qty))
    except Exception:
        available_qty = total_qty
    try:
        entry_price = float(pos.get("avg_entry_price", 0))
    except Exception:
        entry_price = 0.0

    DUST_THRESHOLD = 0.001
    if available_qty < DUST_THRESHOLD:
        open_lots.clear()
        logger.info(f"[Ledger Sync] No available BTC (available={available_qty}). Cleared all lots.")
    else:
        open_lots.clear()
        open_lots.append({"qty": available_qty, "entry_price": entry_price})
        logger.info(f"[Ledger Sync] Reset open_lots to single lot for available BTC: {available_qty} at entry {entry_price}")

    logger.warning(f"[Ledger Sync] After sync: lots={open_lots}, position_qty={total_qty}, available_qty={available_qty}")

def log_trade_to_csv(ts, action, qty, price, order_id, status, realised_pl, fee, exploratory=False):
    first = not os.path.isfile(TRADE_HISTORY_FILE)
    with open(TRADE_HISTORY_FILE, "a", newline="") as f:
        w = csv.writer(f)
        if first:
            w.writerow(
                ["timestamp", "action", "quantity", "price",
                 "order_id", "status", "realised_pl", "fee", "exploratory"]
            )
        w.writerow([ts, action, qty, price, order_id, status, realised_pl, fee, exploratory])

def calculate_fees(qty, price):
    qty_dec   = Decimal(str(qty))
    price_dec = Decimal(str(price))
    fee       = qty_dec * price_dec * Decimal(str(config.FEE_PERCENT))
    return float(fee)

def _place_tp_orders(api, symbol, lot, profit_target_pct):
    entry   = lot["entry_price"]
    placed  = 0
    try:
        pos = api.get_position(symbol)
        pos_available = float(pos.qty)
    except Exception:
        pos_available = lot["qty"]
    tp_qty = min(lot["qty"], pos_available) * 0.999
    tp_qty = round(tp_qty, 8)
    if tp_qty <= 0:
        logger.warning("TP order not placed: tp_qty is zero or negative.")
        return 0
    limit_px = round(entry * (1 + profit_target_pct), 2)
    fee      = calculate_fees(tp_qty, limit_px)
    if (limit_px - entry) * tp_qty <= 2 * fee:
        return 0
    for attempt in range(3):
        try:
            order = api.submit_order(
                symbol=symbol, qty=tp_qty, side="sell",
                type="limit", time_in_force="gtc",
                limit_price=limit_px
            )
            break
        except Exception as e:
            logger.error(f"TP order failure attempt {attempt+1}: {e}")
            time.sleep(1)
    else:
        return 0
    raw_fill     = getattr(order, 'filled_avg_price', None)
    filled_price = float(raw_fill) if raw_fill is not None else limit_px
    log_trade_to_csv(
        datetime.utcnow().isoformat(), "SELL",
        tp_qty, filled_price, order.id, order.status, 0.0, fee, False
    )
    lot["qty"] -= tp_qty
    placed += 1
    return placed
# -- SIGNALS/STRATEGY LOGIC (KEEP YOUR EXISTING/WORKING CODE) --

# You likely have the following logic (paste your latest version here if different):

def trend_following_signal(ind, ai_conf):
    trend = ind.get("trend", "")
    trend_5m = ind.get("trend_5m", "")
    momentum = ind.get("momentum", 0)
    rsi = ind.get("rsi", 50)
    if trend == "bullish" and trend_5m == "bullish" and momentum > 0 and rsi > 50:
        return "BUY"
    elif trend == "bearish" and trend_5m == "bearish" and momentum < 0 and rsi < 50:
        return "SELL"
    return "HOLD"

def mean_reversion_signal(ind, ai_conf):
    rsi = ind.get("rsi", 50)
    price = ind.get("avg_price", 0)
    sma = ind.get("sma_long", 0)
    if rsi < 30 and price < sma:
        return "BUY"
    elif rsi > 70 and price > sma:
        return "SELL"
    return "HOLD"

def scalping_signal(ind, ai_conf):
    volume_spike = ind.get("volume_spike", 0)
    rsi = ind.get("rsi", 50)
    momentum = ind.get("momentum", 0)
    if volume_spike > 0.4 and momentum > 0 and rsi < 40:
        return "BUY"
    elif volume_spike > 0.4 and momentum < 0 and rsi > 60:
        return "SELL"
    return "HOLD"

def select_strategy(regime, indicators):
    if regime in ("trending", "trending_volatile"):
        return "trend"
    elif regime in ("ranging", "quiet"):
        return "mean"
    elif regime == "volatile" and indicators.get("volume_spike", 0) > 0.3:
        return "scalp"
    else:
        return "trend"

open_lots = []
consecutive_losses = 0
win_streak = 0
loss_streak = 0
MAX_CONSEC_LOSSES = 3
COOLDOWN_CYCLES_ON_TRIGGER = 3
cooldown_cycles_remaining = 0

def determine_energy_state(ind, ai, consecutive_losses):
    regime = ind.get("regime", "unknown")
    ai_conf = ai.get("confidence_level", "MEDIUM")
    if (
        regime in ("trending", "trending_volatile")
        and ai_conf == "HIGH"
        and consecutive_losses == 0
        and cooldown_cycles_remaining == 0
    ):
        return "aggressive", f"{regime} regime & high confidence & no losses & not in cooldown"
    return "defensive", f"{regime} regime or AI confidence not high or after losses/cooldown"

def prune_lots_to_available(api):
    symbol = "BTCUSD"
    DUST_THRESHOLD = 0.001
    try:
        pos_available = float(api.get_position(symbol).qty)
    except Exception:
        pos_available = 0.0

    pruned = False
    total_lot_qty = sum(l["qty"] for l in open_lots)
    if pos_available < total_lot_qty:
        running = 0.0
        new_lots = []
        for lot in open_lots:
            if running + lot["qty"] <= pos_available + DUST_THRESHOLD:
                new_lots.append(lot)
                running += lot["qty"]
            elif pos_available > running + DUST_THRESHOLD:
                lot_qty = max(0.0, pos_available - running)
                if lot_qty > DUST_THRESHOLD:
                    lot["qty"] = lot_qty
                    new_lots.append(lot)
                    running += lot_qty
            else:
                logger.info(f"Pruning lot with qty={lot['qty']} as it exceeds available BTC.")
                pruned = True
                continue
        open_lots[:] = [l for l in new_lots if l["qty"] > DUST_THRESHOLD]
        if pruned:
            logger.warning("[Lot Prune] Adjusted open lots to match available BTC.")
        save_open_lots(open_lots)

def process_ai_decision_and_trade(api, ai, acct, pos, ind, buy_cnt, sell_cnt):
    global open_lots, consecutive_losses, cooldown_cycles_remaining, win_streak, loss_streak
    decision = ai.get("decision")
    symbol   = "BTCUSD"

    if cooldown_cycles_remaining > 0:
        logger.warning(f"[COOLDOWN] In recovery cooldown, {cooldown_cycles_remaining} cycles remaining.")
        cooldown_cycles_remaining -= 1
        if cooldown_cycles_remaining == 0:
            logger.warning("[COOLDOWN] Recovery cooldown complete. Trading re-enabled.")
            consecutive_losses = 0
            win_streak = 0
            loss_streak = 0
        analyze_and_autotune_parameters()
        return "Cooldown due to losses (auto-recovering)", buy_cnt, sell_cnt

    energy_state, energy_reason = determine_energy_state(ind, ai, consecutive_losses)
    logger.warning(f"[ENERGY] State: {energy_state} | Reason: {energy_reason}")

    if consecutive_losses >= MAX_CONSEC_LOSSES:
        dynamic_cooldown_cycles = min(consecutive_losses * 2, 6)
        logger.warning(f"Cooldown forced by loss streak. Defensive mode locked for {dynamic_cooldown_cycles} cycles.")
        cooldown_cycles_remaining = dynamic_cooldown_cycles
        win_streak = 0
        loss_streak = consecutive_losses
        analyze_and_autotune_parameters()
        return f"Cooldown due to losses ({dynamic_cooldown_cycles} cycles)", buy_cnt, sell_cnt

    regime = ind.get("regime", "unknown")
    ai_conf = ai.get("confidence_level", "MEDIUM")
    exploratory_trade = False

    strategy = select_strategy(regime, ind)
    logger.warning(f"[STRATEGY] Using strategy: {strategy}")

    if strategy == "trend":
        strategy_signal = trend_following_signal(ind, ai_conf)
    elif strategy == "mean":
        strategy_signal = mean_reversion_signal(ind, ai_conf)
    elif strategy == "scalp":
        strategy_signal = scalping_signal(ind, ai_conf)
    else:
        strategy_signal = "HOLD"

    if strategy_signal != "HOLD":
        decision = strategy_signal

    # You will have your profit_target_pct, stop_loss_pct, and risk logic here.
    # ... (keep your dynamic logic, minimum notional logic, etc.)

    # ----
    # Your BUY, SELL, and trailing stop order logic goes here.
    # ----

    # At the end of the function (after all logic, just before returning):
    analyze_and_autotune_parameters()
    # Return your result as before
    return "No action", buy_cnt, sell_cnt

# (End of file)