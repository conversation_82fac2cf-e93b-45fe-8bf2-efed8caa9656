"""
Critical Financial Safety Tests - Protect Against Money Loss

These tests ensure the trading bot never:
- Exceeds position size limits
- Violates cash reserve requirements  
- Bypasses daily loss limits
- Ignores stop-loss protections
- Miscalculates profit/loss
"""

import unittest
from unittest.mock import MagicMock, patch
from decimal import Decimal
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
import trade_logic
from trade_logic import calculate_fees
import bot_state


class TestFinancialSafety(unittest.TestCase):
    """Critical tests to prevent financial losses"""
    
    def setUp(self):
        """Reset state before each test"""
        # Clear any existing bot state
        bot_state._state_cache = {}
        trade_logic.open_lots = []
    
    def test_position_size_never_exceeds_max_trade_value(self):
        """CRITICAL: Ensure position size never exceeds MAX_TRADE_VALUE_USD"""
        # Test with your intentional $50 limit
        max_trade_value = Decimal(str(config.MAX_TRADE_VALUE_USD))

        # Test various scenarios that should respect the $50 limit
        test_cases = [
            {"trade_value": "25.00", "price": 50000},    # Half limit
            {"trade_value": "50.00", "price": 50000},    # At limit
            {"trade_value": "49.99", "price": 50000},    # Just under limit
        ]

        for case in test_cases:
            with self.subTest(case=case):
                trade_value = Decimal(case["trade_value"])
                price = Decimal(str(case["price"]))

                # Calculate quantity for this trade value
                position_qty = trade_value / price
                actual_value = position_qty * price

                # The critical check: position value must never exceed your $50 limit
                self.assertLessEqual(
                    actual_value,
                    max_trade_value,
                    f"Position value ${actual_value} exceeds your intentional limit ${max_trade_value}"
                )

        # Test that a trade over $50 would be rejected
        over_limit_value = max_trade_value + Decimal("0.01")
        self.assertGreater(
            over_limit_value,
            max_trade_value,
            f"Trade value ${over_limit_value} should exceed limit ${max_trade_value}"
        )
    
    def test_cash_reserve_always_maintained(self):
        """CRITICAL: Ensure CASH_RESERVE_USD is never violated"""
        account_cash = Decimal("1000")
        reserve = Decimal(str(config.CASH_RESERVE_USD))
        
        # Available cash should be account cash minus reserve
        available_cash = account_cash - reserve
        
        # If reserve is larger than account, available should be 0
        if available_cash < 0:
            available_cash = Decimal("0")
        
        # Test that we never use more than available cash
        max_position_value = available_cash * Decimal("0.5")  # 50% of available
        
        self.assertGreaterEqual(
            account_cash - max_position_value,
            reserve,
            f"Using ${max_position_value} would violate ${reserve} cash reserve"
        )
    
    def test_minimum_order_value_enforced(self):
        """CRITICAL: Ensure orders meet minimum value requirements"""
        min_value = Decimal(str(config.MIN_ORDER_VALUE_USD_FLOOR))
        
        # Test various small quantities
        test_cases = [
            {"qty": "0.0001", "price": "50000"},   # $5 order
            {"qty": "0.0002", "price": "50000"},   # $10 order  
            {"qty": "0.0005", "price": "50000"},   # $25 order
        ]
        
        for case in test_cases:
            with self.subTest(case=case):
                qty = Decimal(case["qty"])
                price = Decimal(case["price"])
                order_value = qty * price
                
                if order_value < min_value:
                    # Order should be rejected or adjusted
                    self.assertLess(
                        order_value,
                        min_value,
                        f"Order value ${order_value} below minimum ${min_value} should be rejected"
                    )
    
    def test_stop_loss_calculations_accurate(self):
        """CRITICAL: Ensure stop-loss calculations protect against losses"""
        entry_price = Decimal("50000")
        stop_percent = Decimal(str(config.STOP_PERCENT))
        
        # Calculate expected stop price
        expected_stop = entry_price * (Decimal("1") - stop_percent)
        
        # Test the calculation
        calculated_stop = entry_price - (entry_price * stop_percent)
        
        self.assertEqual(
            calculated_stop,
            expected_stop,
            f"Stop-loss calculation incorrect: {calculated_stop} != {expected_stop}"
        )
        
        # Ensure stop-loss actually limits losses
        loss_percent = (entry_price - calculated_stop) / entry_price
        self.assertLessEqual(
            loss_percent,
            stop_percent,
            f"Stop-loss allows {loss_percent:.4f} loss, exceeds {stop_percent:.4f} limit"
        )
    
    def test_fee_calculations_accurate(self):
        """CRITICAL: Ensure fee calculations are correct to prevent unexpected costs"""
        test_cases = [
            {"qty": "0.001", "price": "50000"},    # $50 trade
            {"qty": "0.01", "price": "50000"},     # $500 trade
            {"qty": "0.1", "price": "50000"},      # $5000 trade
        ]
        
        for case in test_cases:
            with self.subTest(case=case):
                qty = Decimal(case["qty"])
                price = Decimal(case["price"])
                
                # Calculate fees using the function
                calculated_fees = calculate_fees(qty, price)
                
                # Calculate expected fees manually
                trade_value = qty * price
                expected_fees = trade_value * Decimal(str(config.FEE_PERCENT))
                expected_fees = expected_fees.quantize(Decimal("0.00000001"))
                
                self.assertEqual(
                    calculated_fees,
                    expected_fees,
                    f"Fee calculation incorrect: {calculated_fees} != {expected_fees}"
                )
    
    def test_daily_loss_limits_enforced(self):
        """CRITICAL: Ensure daily loss limits prevent catastrophic losses"""
        # Simulate a trading session with losses
        initial_equity = Decimal("10000")
        max_loss_percent = Decimal(str(config.MAX_AI_LOSS_EXIT_PCT))
        max_allowed_loss = initial_equity * max_loss_percent
        
        # Test that losses exceeding limit trigger protection
        current_loss = max_allowed_loss + Decimal("1")  # Exceed by $1
        
        # Bot should pause trading when loss limit exceeded
        loss_percent = current_loss / initial_equity
        
        self.assertGreater(
            loss_percent,
            max_loss_percent,
            f"Loss {loss_percent:.4f} should trigger protection at {max_loss_percent:.4f}"
        )
    
    def test_dust_threshold_prevents_tiny_trades(self):
        """CRITICAL: Ensure dust threshold prevents uneconomical trades"""
        dust_threshold = Decimal(str(config.DUST_THRESHOLD_QTY))
        
        # Test quantities below dust threshold
        tiny_qty = dust_threshold / Decimal("2")  # Half the threshold
        
        self.assertLess(
            tiny_qty,
            dust_threshold,
            f"Quantity {tiny_qty} below dust threshold {dust_threshold} should be rejected"
        )
    
    def test_profit_calculations_accurate(self):
        """CRITICAL: Ensure profit/loss calculations are mathematically correct"""
        # Test a complete buy-sell cycle
        buy_price = Decimal("50000")
        buy_qty = Decimal("0.001")
        buy_fees = calculate_fees(buy_qty, buy_price)
        
        sell_price = Decimal("51000")  # $1000 profit per BTC
        sell_qty = buy_qty
        sell_fees = calculate_fees(sell_qty, sell_price)
        
        # Calculate expected profit
        buy_cost = (buy_qty * buy_price) + buy_fees
        sell_proceeds = (sell_qty * sell_price) - sell_fees
        expected_profit = sell_proceeds - buy_cost
        
        # Manual calculation for verification
        gross_profit = sell_qty * (sell_price - buy_price)  # $1 gross
        total_fees = buy_fees + sell_fees
        net_profit = gross_profit - total_fees
        
        self.assertEqual(
            expected_profit,
            net_profit,
            f"Profit calculation mismatch: {expected_profit} != {net_profit}"
        )
    
    def test_position_sizing_respects_account_limits(self):
        """CRITICAL: Ensure position sizing never exceeds account capabilities"""
        # Test with limited account
        account = {
            "cash": "1000",
            "buying_power": "2000",  # 2:1 margin
            "equity": "1500"
        }
        
        cash = Decimal(account["cash"])
        buying_power = Decimal(account["buying_power"])
        
        # Position should never exceed available buying power
        max_position_value = buying_power * Decimal("0.9")  # 90% of buying power
        
        self.assertLessEqual(
            max_position_value,
            buying_power,
            f"Position value ${max_position_value} exceeds buying power ${buying_power}"
        )
        
        # Position should also respect cash reserves
        available_cash = cash - Decimal(str(config.CASH_RESERVE_USD))
        if available_cash > 0:
            self.assertGreaterEqual(
                available_cash,
                Decimal("0"),
                "Available cash after reserves should be non-negative"
            )


if __name__ == "__main__":
    # Run with verbose output to see all test details
    unittest.main(verbosity=2)
