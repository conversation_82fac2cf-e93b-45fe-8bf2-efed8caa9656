#!/usr/bin/env python3
"""
Intelligent parameter optimizer that integrates with existing backtesting suite.
Replaces basic grid search with Bayesian optimization for much faster results.
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
from decimal import Decimal
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import our optimization modules
from optimization.bayesian_optimizer import BayesianOptimizer
from optimization.performance_metrics import PerformanceMetrics
from optimization.phase2_config_writer import Phase2ConfigWriter
from optimization.parameter_importance_analyzer import ParameterImportanceAnalyzer


class IntelligentParameterOptimizer:
    """
    Intelligent parameter optimizer that uses Bayesian optimization
    to find optimal trading parameters much faster than grid search.
    """
    
    def __init__(self, backtesting_suite_path: str = 'backtesting_suite'):
        """
        Initialize the intelligent optimizer.
        
        Args:
            backtesting_suite_path: Path to the backtesting suite folder (READ ONLY)
        """
        self.backtesting_suite_path = backtesting_suite_path
        self.backtester_path = os.path.join(backtesting_suite_path, 'backtester.py')
        self.results_dir = 'optimization/results'

        # Initialize Phase 2 config writer (writes to main project folder, not backtesting suite)
        self.config_writer = Phase2ConfigWriter(base_dir=os.getcwd())

        # Initialize parameter importance analyzer
        self.importance_analyzer = ParameterImportanceAnalyzer(results_dir=self.results_dir)

        # Ensure results directory exists
        os.makedirs(self.results_dir, exist_ok=True)

        # Verify backtester exists
        if not os.path.exists(self.backtester_path):
            raise FileNotFoundError(f"Backtester not found at: {self.backtester_path}")
    
    def create_parameter_space(self, phase: str = "phase1") -> Dict[str, Any]:
        """
        Define the parameter space for optimization.

        Args:
            phase: "phase1" for basic 2-parameter space, "phase2" for expanded 20-parameter space

        Returns:
            Dictionary defining parameter space for scikit-optimize
        """
        if phase == "phase1":
            # Phase 1: Basic 2-parameter space (proven working)
            return {
                'stop_percent': {
                    'type': 'real',
                    'low': 0.005,
                    'high': 0.05,
                    'description': 'Stop loss percentage'
                },
                'trail_profit_buffer_pct': {
                    'type': 'real',
                    'low': 0.001,
                    'high': 0.02,
                    'description': 'Trailing profit buffer percentage'
                }
            }

        elif phase == "phase2":
            # Phase 2: Expanded 20-parameter space
            return {
                # === EXISTING PARAMETERS (Phase 1) ===
                'stop_percent': {
                    'type': 'real',
                    'low': 0.005,
                    'high': 0.05,
                    'description': 'Stop loss percentage'
                },
                'trail_profit_buffer_pct': {
                    'type': 'real',
                    'low': 0.001,
                    'high': 0.02,
                    'description': 'Trailing profit buffer percentage'
                },

                # === TECHNICAL INDICATORS ===
                'rsi_period': {
                    'type': 'integer',
                    'low': 7,
                    'high': 21,
                    'description': 'RSI calculation period'
                },
                'rsi_overbought': {
                    'type': 'real',
                    'low': 70.0,
                    'high': 90.0,
                    'description': 'RSI overbought threshold'
                },
                'rsi_oversold': {
                    'type': 'real',
                    'low': 10.0,
                    'high': 30.0,
                    'description': 'RSI oversold threshold'
                },
                'short_sma_period': {
                    'type': 'integer',
                    'low': 3,
                    'high': 15,
                    'description': 'Short SMA period'
                },
                'long_sma_period': {
                    'type': 'integer',
                    'low': 8,
                    'high': 25,
                    'description': 'Long SMA period'
                },
                'macd_fast_period': {
                    'type': 'integer',
                    'low': 8,
                    'high': 16,
                    'description': 'MACD fast EMA period'
                },
                'macd_slow_period': {
                    'type': 'integer',
                    'low': 20,
                    'high': 35,
                    'description': 'MACD slow EMA period'
                },
                'atr_period': {
                    'type': 'integer',
                    'low': 10,
                    'high': 20,
                    'description': 'ATR calculation period'
                },

                # === RISK MANAGEMENT ===
                'max_trade_value_usd': {
                    'type': 'real',
                    'low': 25.0,
                    'high': 100.0,
                    'description': 'Maximum trade size in USD'
                },
                'cash_reserve_usd': {
                    'type': 'real',
                    'low': 100.0,
                    'high': 500.0,
                    'description': 'Cash reserve amount in USD'
                },
                'min_qty_pct': {
                    'type': 'real',
                    'low': 0.02,
                    'high': 0.10,
                    'description': 'Minimum AI quantity percentage'
                },
                'max_qty_pct': {
                    'type': 'real',
                    'low': 0.05,
                    'high': 0.15,
                    'description': 'Maximum AI quantity percentage'
                },

                # === PROFIT TARGETS ===
                'dip_buy_profit_target_pct': {
                    'type': 'real',
                    'low': 0.01,
                    'high': 0.05,
                    'description': 'Dip buy profit target percentage'
                },
                'momentum_buy_profit_target_pct': {
                    'type': 'real',
                    'low': 0.005,
                    'high': 0.03,
                    'description': 'Momentum buy profit target percentage'
                },
                'granular_take_profit_pct': {
                    'type': 'real',
                    'low': 0.005,
                    'high': 0.02,
                    'description': 'Granular take profit percentage'
                },

                # === TIMING & COOLDOWNS ===
                'trade_cycle_interval_minutes': {
                    'type': 'integer',
                    'low': 1,
                    'high': 5,
                    'description': 'Minutes between trade cycles'
                },
                'cooldown_buy_after_sell_minutes': {
                    'type': 'integer',
                    'low': 2,
                    'high': 10,
                    'description': 'Buy cooldown after sell in minutes'
                },
                'cooldown_sell_after_buy_minutes': {
                    'type': 'integer',
                    'low': 1,
                    'high': 5,
                    'description': 'Sell cooldown after buy in minutes'
                }
            }

        else:
            raise ValueError(f"Unknown phase: {phase}. Use 'phase1' or 'phase2'")

    def run_phase2_optimization(self, n_calls: int = 30, optimization_metric: str = 'sharpe_ratio',
                               random_state: int = None) -> Dict[str, Any]:
        """
        Run Phase 2 optimization with expanded 20-parameter space.

        Args:
            n_calls: Number of optimization iterations
            optimization_metric: Metric to optimize ('sharpe_ratio', 'total_return', etc.)
            random_state: Random seed for reproducibility

        Returns:
            Dictionary with optimization results
        """
        print("🚀 Starting Phase 2 Optimization - Expanded Parameter Space")
        print("   20 parameters: Technical indicators, risk management, profit targets, timing")
        print("="*70)

        # Create expanded parameter space
        param_space = self.create_parameter_space(phase="phase2")

        print(f"   📊 Parameter space: {len(param_space)} parameters")
        print(f"   🎯 Optimization metric: {optimization_metric}")
        print(f"   🔢 Iterations: {n_calls}")

        # Initialize Bayesian optimizer
        optimizer = BayesianOptimizer(
            objective_function=self.run_backtest_with_parameters,
            parameter_space=param_space,
            optimization_metric=optimization_metric,
            n_calls=n_calls
        )

        # Run optimization
        result = optimizer.optimize(random_state=random_state)

        if result['best_parameters'] is not None:
            print(f"\n🎉 Phase 2 optimization completed successfully!")
            print(f"   Best {optimization_metric}: {result['best_score']:.4f}")
            print(f"   🎯 Best parameters found across {len(param_space)} dimensions")

            # Save results with phase2 prefix
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"phase2_optimization_results_{timestamp}.csv"

            # Save detailed results
            optimization_history = result.get('optimization_history', [])
            self._save_compatible_results(optimization_history, result)

            print(f"   📄 Results saved to: {results_file}")

            # Run parameter importance analysis
            if len(optimization_history) >= 10:  # Need sufficient data for analysis
                print(f"\n🔍 Running parameter importance analysis...")
                importance_results = self.importance_analyzer.analyze_from_optimization_history(
                    optimization_history, optimization_metric
                )

                if 'error' not in importance_results:
                    print(f"   ✅ Parameter importance analysis completed")

                    # Add importance results to main result
                    result['parameter_importance'] = importance_results

                    # Show key insights
                    consensus = importance_results['summary']['consensus_ranking']
                    if consensus:
                        top_param = max(consensus.items(), key=lambda x: x[1])
                        print(f"   🏆 Most important parameter: {top_param[0]}")

                        recommendations = importance_results['recommendations']
                        if recommendations:
                            print(f"   💡 Key recommendation: {recommendations[0]}")
                else:
                    print(f"   ⚠️  Parameter importance analysis failed: {importance_results['error']}")
            else:
                print(f"   ⚠️  Skipping parameter importance analysis (need ≥10 runs, got {len(optimization_history)})")

        return result

    def run_backtest_with_parameters(self, params: Dict[str, float]) -> Dict[str, float]:
        """
        Run a single backtest with given parameters and return performance metrics.

        Args:
            params: Dictionary of parameters to test

        Returns:
            Dictionary of performance metrics
        """
        try:
            # Check if this is Phase 1 (2 parameters) or Phase 2 (20 parameters)
            if len(params) == 2:
                # Phase 1: Use existing 2-parameter approach
                stop_percent = params['stop_percent']
                trail_buffer = params['trail_profit_buffer_pct']
                backtester_args = [str(stop_percent), str(trail_buffer)]
                config_path = None

                print(f"  🔄 Running Phase 1 backtest with stop_percent={stop_percent:.4f}, trail_buffer={trail_buffer:.4f}")

            else:
                # Phase 2: Create config file with all parameters
                stop_percent = params['stop_percent']
                trail_buffer = params['trail_profit_buffer_pct']
                backtester_args = [str(stop_percent), str(trail_buffer)]

                # Create temporary config file for additional parameters
                config_id = f"opt_{os.getpid()}_{hash(str(params)) % 10000}"
                config_path = self.config_writer.write_config(params, config_id)

                print(f"  🔄 Running Phase 2 backtest ({len(params)} parameters)")
                print(f"     Core: stop_percent={stop_percent:.4f}, trail_buffer={trail_buffer:.4f}")
                print(f"     Config: {os.path.basename(config_path)} ({len(params)-2} additional params)")
            
            # Execute backtester.py and capture output
            # Run from main project directory, not backtesting_suite directory
            main_project_dir = os.path.dirname(os.path.dirname(os.path.abspath(self.backtester_path)))
            backtester_abs_path = os.path.abspath(self.backtester_path)
            process = subprocess.Popen(
                [sys.executable, backtester_abs_path] + backtester_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=main_project_dir
            )
            
            output_lines = []
            for line in process.stdout:
                output_lines.append(line.strip())
            
            process.wait()
            
            if process.returncode != 0:
                print(f"  ❌ Backtest failed with return code {process.returncode}")
                print(f"  📄 Last 5 lines of output:")
                for line in output_lines[-5:]:
                    print(f"     {line}")
                return self._create_failed_metrics()
            
            # Parse results from backtester output
            metrics = self._parse_backtest_output(output_lines)
            
            print(f"  ✅ Backtest completed - Sharpe: {metrics.get('sharpe_ratio', 0):.3f}, Return: {metrics.get('total_return', 0):.2f}%")

            return metrics

        except Exception as e:
            print(f"  ❌ Error running backtest: {e}")
            return self._create_failed_metrics()

        finally:
            # Cleanup Phase 2 config file if it was created
            if 'config_path' in locals() and config_path is not None:
                try:
                    self.config_writer.cleanup_config(config_path)
                except Exception:
                    pass  # Ignore cleanup errors
    
    def _parse_backtest_output(self, output_lines: List[str]) -> Dict[str, float]:
        """
        Parse backtester.py output to extract performance metrics.
        """
        # Initialize default values
        final_equity = Decimal("10000.0")  # Default starting capital
        total_pl = Decimal("0.0")
        wins = 0
        losses = 0
        
        # Parse the output lines
        for line in output_lines:
            try:
                if "Final Equity: $" in line:
                    final_equity = Decimal(line.split("$")[1].strip())
                elif "Total P/L: $" in line:
                    total_pl = Decimal(line.split("$")[1].strip())
                elif "Wins: " in line and "| Losses: " in line:
                    parts = line.split("|")
                    wins = int(parts[0].split(":")[1].strip())
                    losses = int(parts[1].split(":")[1].strip())
            except (ValueError, IndexError) as e:
                # Skip lines that can't be parsed
                continue
        
        # Calculate basic metrics
        initial_capital = 10000.0
        total_trades = wins + losses
        
        if total_trades == 0:
            # No trades executed - this is a valid result (conservative strategy)
            # Return neutral metrics based on actual final equity
            total_return = float((final_equity - Decimal(str(initial_capital))) / Decimal(str(initial_capital)) * 100)
            return {
                'total_return': total_return,
                'sharpe_ratio': 0.0,  # Neutral performance
                'max_drawdown': 0.0,  # No drawdown if no trades
                'win_rate': 0.0,
                'calmar_ratio': 0.0,
                'profit_factor': 1.0,  # Neutral
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'final_equity': float(final_equity),
                'total_pl': float(total_pl)
            }
        
        # Calculate performance metrics
        total_return = float((final_equity - Decimal(str(initial_capital))) / Decimal(str(initial_capital)) * 100)
        win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
        
        # Estimate Sharpe ratio (simplified calculation)
        # In a real implementation, we'd need the equity curve for proper calculation
        if total_trades > 10:  # Need reasonable sample size
            # Rough estimate: higher win rate and return = better Sharpe
            estimated_sharpe = (total_return / 100) * (win_rate / 50) * np.sqrt(total_trades / 100)
            estimated_sharpe = max(-3, min(5, estimated_sharpe))  # Clamp to reasonable range
        else:
            estimated_sharpe = 0.0
        
        # Calculate max drawdown estimate (simplified)
        # Assume worst case scenario based on losses
        if losses > 0:
            avg_loss_pct = abs(total_return) / max(1, losses) if total_return < 0 else 5.0
            estimated_max_drawdown = -min(50, avg_loss_pct * 2)  # Rough estimate
        else:
            estimated_max_drawdown = -1.0  # Minimal drawdown if no losses
        
        # Calculate Calmar ratio
        calmar_ratio = total_return / abs(estimated_max_drawdown) if estimated_max_drawdown != 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': estimated_sharpe,
            'max_drawdown': estimated_max_drawdown,
            'win_rate': win_rate,
            'calmar_ratio': calmar_ratio,
            'profit_factor': float(final_equity / Decimal(str(initial_capital))),
            'total_trades': total_trades,
            'winning_trades': wins,
            'losing_trades': losses,
            'final_equity': float(final_equity),
            'total_pl': float(total_pl)
        }
    
    def _create_failed_metrics(self) -> Dict[str, float]:
        """Create metrics for failed backtests."""
        return {
            'total_return': -50.0,  # Large negative return
            'sharpe_ratio': -2.0,   # Poor Sharpe ratio
            'max_drawdown': -50.0,  # Large drawdown
            'win_rate': 0.0,
            'calmar_ratio': -1.0,
            'profit_factor': 0.5,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'final_equity': 5000.0,  # 50% loss
            'total_pl': -5000.0
        }
    
    def run_intelligent_optimization(self, n_calls: int = 50, 
                                   optimization_metric: str = 'sharpe_ratio',
                                   random_state: int = 42) -> Dict[str, Any]:
        """
        Run intelligent Bayesian optimization to find best parameters.
        
        Args:
            n_calls: Number of optimization iterations
            optimization_metric: Metric to optimize ('sharpe_ratio', 'calmar_ratio', etc.)
            random_state: Random seed for reproducible results
            
        Returns:
            Dictionary with optimization results
        """
        print(f"\n🚀 Starting Intelligent Parameter Optimization")
        print(f"   Using Bayesian optimization instead of grid search")
        print(f"   Optimization metric: {optimization_metric}")
        print(f"   Number of iterations: {n_calls}")
        print("="*70)
        
        # Create parameter space
        param_space = self.create_parameter_space(phase="phase1")  # Default to phase1 for now
        
        # Initialize Bayesian optimizer
        optimizer = BayesianOptimizer(
            objective_function=self.run_backtest_with_parameters,
            parameter_space=param_space,
            optimization_metric=optimization_metric,
            n_calls=n_calls,
            results_dir=self.results_dir
        )
        
        # Run optimization
        result = optimizer.optimize(random_state=random_state)
        
        if result['best_parameters'] is not None:
            print(f"\n🎉 Optimization completed successfully!")
            print(f"   Best {optimization_metric}: {result['best_score']:.4f}")
            print(f"   Best parameters:")
            for param, value in result['best_parameters'].items():
                print(f"     {param}: {value:.6f}")
            
            # Analyze parameter importance
            importance = optimizer.get_parameter_importance()
            
            # Save results in format compatible with original optimizer
            self._save_compatible_results(optimizer.optimization_results, result)
            
            return result
        else:
            print(f"❌ Optimization failed: {result.get('error', 'Unknown error')}")
            return result
    
    def _save_compatible_results(self, optimization_results: List[Dict], 
                               final_result: Dict[str, Any]) -> None:
        """
        Save results in format compatible with original optimization_results.csv
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create CSV data in original format
        csv_data = []
        for result in optimization_results:
            params = result['parameters']
            metrics = result['metrics']
            
            csv_row = {
                'STOP_PERCENT': params['stop_percent'],
                'TRAIL_PROFIT_BUFFER_PCT': params['trail_profit_buffer_pct'],
                'Total_PL': metrics.get('total_pl', 0),
                'Wins': metrics.get('winning_trades', 0),
                'Losses': metrics.get('losing_trades', 0),
                'Total_Return': metrics.get('total_return', 0),
                'Sharpe_Ratio': metrics.get('sharpe_ratio', 0),
                'Max_Drawdown': metrics.get('max_drawdown', 0),
                'Win_Rate': metrics.get('win_rate', 0),
                'Calmar_Ratio': metrics.get('calmar_ratio', 0)
            }
            csv_data.append(csv_row)
        
        # Save to main project folder (not backtesting suite)
        results_file = f'intelligent_optimization_results_{timestamp}.csv'
        
        df = pd.DataFrame(csv_data)
        df.to_csv(results_file, index=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        print(f"   📊 {len(csv_data)} parameter combinations tested")
        print(f"   🎯 Best combination found in {len(csv_data)} iterations")
        if len(csv_data) > 0:
            print(f"   ⚡ Estimated speedup: {(6*5)/len(csv_data):.1f}x faster than grid search")
        else:
            print(f"   ⚡ No iterations completed for speedup calculation")


def main():
    """Main function to run intelligent optimization."""
    try:
        # Initialize optimizer
        optimizer = IntelligentParameterOptimizer()
        
        # Run optimization
        result = optimizer.run_intelligent_optimization(
            n_calls=30,  # Much fewer than grid search (6*5=30 combinations)
            optimization_metric='sharpe_ratio',
            random_state=42
        )
        
        if result['best_parameters'] is not None:
            print(f"\n✅ Intelligent optimization completed successfully!")
            print(f"   🎯 Found optimal parameters in {result['total_iterations']} iterations")
            print(f"   ⚡ This is much faster than testing all {6*5} grid combinations!")
        else:
            print(f"\n❌ Optimization failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in intelligent optimization: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
