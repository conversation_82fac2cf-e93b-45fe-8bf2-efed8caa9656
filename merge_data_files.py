import ccxt
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
import os
import sys
import logging

# Configure logging for merge_data_files.py
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # Set to DEBUG to capture all messages

# Create a FileHandler to write logs to a specific file
log_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "merge_data_files.log")
fh = logging.FileHandler(log_file_path)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
fh.setFormatter(formatter)

# Add the FileHandler to the logger
if not logger.handlers:
    logger.addHandler(fh)

exchanges = {
    'binance': {
        'symbol': 'BTC/USDT',
        'start_date': '2017-01-01T00:00:00Z', 
        'filename': 'binance_btcusdt_1m_NEW_DATA.csv', # This is a temporary file for new downloads
        'FORCE_REDOWNLOAD_FROM_START_DATE': False, 
        'DOWNLOAD_YEARS_ONE_BY_ONE': False, 
        'FORCE_RESUME_DATE': True, 
        'RESUME_FROM_DATE': '2021-03-03T08:38:00Z', # Start from one minute AFTER your backup ends
    }
}

timeframe = '1m'
limit = 1000 
sleep_time = 0.1 
REPORT_BATCH_INTERVAL = 500 # Keep this higher for less verbose output

MAX_NO_DATA_RETRIES = 5 
RETRY_SLEEP_MULTIPLIER = 5 

# Define the canonical (standard) column order for all merged data
CANONICAL_COLUMNS = ['timestamp', 'open', 'high', 'low', 'close', 'volume',
                     'close_time', 'quote_asset_volume', 'num_trades',
                     'taker_buy_base', 'taker_buy_quote', 'ignore']

def get_last_timestamp(filename: str) -> pd.Timestamp | None:
    logger.debug(f"Attempting to get last timestamp from {filename}")
    if not os.path.exists(filename):
        logger.debug(f"File {filename} does not exist. Returning None.")
        return None
    try:
        logger.debug(f"Opening file {filename} to read last line.")
        with open(filename, 'r', newline='') as f:
            f.seek(0, os.SEEK_END)
            position = f.tell()
            line = ''
            while position >= 0 and line == '':
                position -= 1
                f.seek(position)
                char = f.read(1)
                if char == '\n' and position < f.tell() - 1:
                    line = f.readline()
                elif position == 0:
                    line = f.readline()
            
            if not line or line.strip() == '':
                logger.warning(f"File {filename} is empty or contains only header. Returning None.")
                return None
            
            # Check if the line is just the header
            if line.strip().split(',')[0] == CANONICAL_COLUMNS[0] or line.strip().split(',')[0] == 'num_trades': # Added 'num_trades' check for old header
                logger.warning(f"File {filename} contains only header. Returning None.")
                return None 

            val_str = line.strip().split(',')[0]
            
            # Try to parse as integer (timestamp in ms) first, then as string date
            try:
                val = int(val_str)
                if val > 1e12: # Check if it looks like a Unix timestamp in milliseconds
                    last = pd.to_datetime(val, unit='ms', utc=True)
                else: # If it's a smaller number, assume it's part of a string date
                    last = pd.to_datetime(val_str, utc=True)
            except ValueError:
                # Not an integer, try parsing directly as a date string
                last = pd.to_datetime(val_str, utc=True)
            
            logger.debug(f"Successfully read last timestamp: {last}")
            return last
        
    except Exception as e:
        logger.error(f"Error reading last timestamp from {filename}: {e}. Defaulting to start_date from config.")
        return None


def fetch_and_save(exchange_id: str, config: dict, since_dt_override: pd.Timestamp | None = None, until_dt: pd.Timestamp | None = None, append_mode: bool = True):
    """
    Fetches historical OHLCV data from an exchange and saves it to a CSV file.
    Optimized for appending to large files and includes retry logic for no-data responses.
    """
    print(f"Starting download for {exchange_id} {config['symbol']}")

    exchange_class = getattr(ccxt, exchange_id)
    exchange = exchange_class()

    start_new_file_flag = False 

    # Prioritize FORCE_RESUME_DATE
    if config.get('FORCE_RESUME_DATE', False) and config.get('RESUME_FROM_DATE'):
        since_dt = pd.to_datetime(config['RESUME_FROM_DATE'], utc=True)
        print(f"{exchange_id}: FORCING RESUME from explicitly set date {since_dt}.")
        # The NEW_DATA file might not exist yet, or it might have some data already
        if not os.path.exists(config['filename']):
            start_new_file_flag = True 
            print(f"File {config['filename']} not found, will start a new file from RESUME_FROM_DATE.")
        # If the NEW_DATA file exists, we'll append to it. No need to set start_new_file_flag as True.
    elif config.get('FORCE_REDOWNLOAD_FROM_START_DATE', False): 
        since_dt = pd.to_datetime(config['start_date'], utc=True)
        print(f"{exchange_id}: FORCE REDOWNLOAD from {since_dt}. (This will delete {config['filename']}).")
        if os.path.exists(config['filename']):
            os.remove(config['filename'])
            print(f"Deleted existing {config['filename']}.")
        start_new_file_flag = True
    else: # Normal resume logic (will use this if RESUME_FROM_DATE is not set for NEW_DATA file)
        last_ts = get_last_timestamp(config['filename'])
        if last_ts is not None:
            since_dt = last_ts + pd.Timedelta(minutes=1)
            print(f"{exchange_id}: Resuming from {since_dt}.")
        else:
            since_dt = pd.to_datetime(config['start_date'], utc=True) # Fallback to original start if all else fails
            print(f"{exchange_id}: Starting fresh from {since_dt} (file not found or empty).")
            start_new_file_flag = True
            
    # If starting a new file because it didn't exist, mark to write header
    if not os.path.exists(config['filename']):
        start_new_file_flag = True


    current_fetch_end_dt = until_dt if until_dt is not None else pd.to_datetime(datetime.now(timezone.utc))
    
    batch_count = 0
    total_rows_fetched_current_run = 0
    no_data_retries = 0
    
    print(f"DEBUG: Initial since_dt: {since_dt}")
    print(f"DEBUG: Initial current_fetch_end_dt: {current_fetch_end_dt}")
    
    while since_dt < current_fetch_end_dt:
        since_ms = int(since_dt.timestamp() * 1000)
        try:
            ohlcv = exchange.fetch_ohlcv(config['symbol'], timeframe, since_ms, limit)
        except ccxt.RateLimitExceeded as e:
            print(f"Rate limit exceeded for {exchange_id}. Waiting {exchange.rateLimit / 1000 + 1}s and retrying... {e}")
            time.sleep(exchange.rateLimit / 1000 + 1)
            continue
        except Exception as e:
            print(f"Error fetching data from {exchange_id} at {since_dt}: {e}. Breaking download loop.")
            break

        if not ohlcv:
            if no_data_retries < MAX_NO_DATA_RETRIES:
                no_data_retries += 1
                sleep_duration = sleep_time * RETRY_SLEEP_MULTIPLIER * no_data_retries
                print(f"No new data from {exchange_id} at {since_dt}. Retrying (attempt {no_data_retries}/{MAX_NO_DATA_RETRIES}) after {sleep_duration:.1f}s.")
                time.sleep(sleep_duration)
                continue
            else:
                print(f"No new data from {exchange_id} at {since_dt} after {MAX_NO_DATA_RETRIES} retries. Assuming end of available history or current fetch window. Breaking download loop.")
                break

        no_data_retries = 0

        last_ts_ms = ohlcv[-1][0]
        since_dt = pd.to_datetime(last_ts_ms, unit='ms', utc=True) + pd.Timedelta(minutes=1)

        batch_count += 1
        total_rows_fetched_current_run += len(ohlcv)

        if batch_count % REPORT_BATCH_INTERVAL == 0:
            from_time = pd.to_datetime(ohlcv[0][0], unit='ms', utc=True)
            to_time = pd.to_datetime(last_ts_ms, unit='ms', utc=True)
            print(f"{exchange_id}: Fetched {len(ohlcv)} rows. Total batches: {batch_count}. Total rows this run: {total_rows_fetched_current_run}. Current range: {from_time} to {to_time}. Up to {since_dt}.")

        df_new = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df_new['timestamp'] = pd.to_datetime(df_new['timestamp'], unit='ms', utc=True)
        
        # Ensure all columns in CANONICAL_COLUMNS are present, adding missing ones with default values
        for col in CANONICAL_COLUMNS:
            if col not in df_new.columns:
                if col in ['close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote']:
                    df_new[col] = 0.0 # Assign 0.0 for numeric columns
                else:
                    df_new[col] = '' # Assign empty string for other missing columns (like 'ignore')
        
        # Reorder columns to the canonical set
        df_new = df_new[CANONICAL_COLUMNS]

        # Write header only if start_new_file_flag is True and it's the first batch
        write_header = start_new_file_flag and batch_count == 1
        df_new.to_csv(config['filename'], mode='a', header=write_header, index=False)
        
        if write_header:
            start_new_file_flag = False # Reset flag after writing header once

        time.sleep(sleep_time)

    print(f"{exchange_id}: Finished data collection for this range. Total rows fetched: {total_rows_fetched_current_run}.")
    

def merge_and_finalize_data(primary_file: str, backup_file: str, new_data_file: str, canonical_columns: list):
    logger.info("--- Starting Data Merging and Finalization ---")

    dfs_to_merge = []

    # 1. Load the backup file (older historical data)
    if os.path.exists(backup_file):
        logger.info(f"Loading backup data from {backup_file}...")
        try:
            # Use a specific date parser for performance if timestamp format is consistent
            df_backup = pd.read_csv(backup_file, parse_dates=['timestamp'], date_parser=lambda x: pd.to_datetime(x, utc=True))
            # Ensure columns are in the canonical order and fill missing with default values
            for col in canonical_columns:
                if col not in df_backup.columns:
                    if col in ['close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'volume', 'open', 'high', 'low', 'close']:
                        df_backup[col] = 0.0
                    else:
                        df_backup[col] = ''
            df_backup = df_backup[canonical_columns]
            dfs_to_merge.append(df_backup)
            logger.info(f"Loaded {len(df_backup)} rows from backup file.")
        except Exception as e:
            logger.warning(f"Could not load backup file {backup_file}: {e}. Skipping this file.")
    else:
        logger.info(f"Backup file {backup_file} not found. Skipping loading from backup.")

    # 2. Load the newly downloaded data
    if os.path.exists(new_data_file):
        logger.info(f"Loading newly downloaded data from {new_data_file}...")
        try:
            df_new_data = pd.read_csv(new_data_file, parse_dates=['timestamp'], date_parser=lambda x: pd.to_datetime(x, utc=True))
            # Ensure columns are in the canonical order and fill missing with default values
            for col in canonical_columns:
                if col not in df_new_data.columns:
                    if col in ['close_time', 'quote_asset_volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'volume', 'open', 'high', 'low', 'close']:
                        df_new_data[col] = 0.0
                    else:
                        df_new_data[col] = ''
            df_new_data = df_new_data[canonical_columns]
            dfs_to_merge.append(df_new_data)
            logger.info(f"Loaded {len(df_new_data)} rows from new data file.")
        except Exception as e:
            logger.warning(f"Could not load new data file {new_data_file}: {e}. Skipping this file.")
    else:
        logger.warning(f"New data file {new_data_file} not found. This is unexpected. Skipping loading new data.")

    if not dfs_to_merge:
        logger.error("No dataframes were loaded. Cannot perform merge. Exiting.")
        return

    # 3. Concatenate all loaded dataframes
    logger.info("Concatenating dataframes...")
    combined_df = pd.concat(dfs_to_merge, ignore_index=True)
    
    # 4. Sort by timestamp and drop duplicates (keeping the latest entry for any timestamp)
    logger.info("Sorting combined data by timestamp and removing duplicates...")
    combined_df = combined_df.sort_values('timestamp').drop_duplicates(subset=['timestamp'], keep='last')
    
    # 5. Save the final merged data to the primary file
    logger.info(f"Saving finalized merged data to {primary_file}...")
    try:
        combined_df.to_csv(primary_file, index=False, header=True)
        logger.info(f"Successfully merged and saved {len(combined_df)} rows to {primary_file}.")
        if not combined_df.empty:
            logger.info(f"First timestamp in merged file: {combined_df['timestamp'].iloc[0]}")
            logger.info(f"Last timestamp in merged file: {combined_df['timestamp'].iloc[-1]}")
        else:
            logger.info("Merged file is empty.")
    except Exception as e:
        logger.error(f"ERROR: Could not save finalized merged data to {primary_file}: {e}")

def main():
    logger.info("Starting merge_data_files.py script.")
    cfg = exchanges['binance']

    # Step 1: Download new data into the temporary NEW_DATA file
    # This part remains mostly the same, responsible for downloading
    try:
        # This part is handled by multi_exchange_downloader.py, so we just check for its output file
        if not os.path.exists(cfg['filename']):
            logger.error(f"Expected new data file {cfg['filename']} not found. Cannot proceed with merge.")
            sys.exit(1)
        else:
            logger.info(f"New data file {cfg['filename']} found. Proceeding with merge.")

    except Exception as e:
        logger.error(f"Error checking for new data file: {e}")
        sys.exit(1) # Exit if check fails

    # Step 2: After successful download, perform the merge operation
    primary_target_file = 'binance_btcusdt_1m.csv'
    backup_source_file = 'binance_btcusdt_1m_BACKUP_2021-03-03.csv' # Exact name from your Get-ChildItem
    new_data_source_file = cfg['filename'] # This is 'binance_btcusdt_1m_NEW_DATA.csv'

    # Call the new merge function to combine data into the primary file
    try:
        merge_and_finalize_data(primary_target_file, backup_source_file, new_data_source_file, CANONICAL_COLUMNS)
        logger.info("merge_data_files.py script completed successfully.")
    except Exception as e:
        logger.error(f"Error during data merging and finalization: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\nmerge_data_files.py: Detected KeyboardInterrupt. Exiting gracefully.")
        sys.exit(1)