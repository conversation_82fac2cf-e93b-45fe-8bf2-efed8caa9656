import logging
import time
from alpaca_trade_api.rest import REST, TimeFrame, APIError
from requests.exceptions import RequestException
from datetime import datetime, timedelta, timezone

from error_handler import ErrorSeverity, ErrorCategory
from error_utils import (
    AlpacaErrorHandler, alpaca_api_call, safe_execute,
    TradingError, APIError as CustomAPIError
)
from email_alert import send_enhanced_alert, AlertPriority, AlertCategory

logger = logging.getLogger("alpaca_service")
logger.setLevel(logging.INFO)

@alpaca_api_call(max_retries=3)
def get_account_info(api: REST):
    """Get account information with standardized error handling"""
    logger.debug("Attempting to fetch account information from Alpaca.")

    def fetch_account():
        try:
            account = api.get_account()
            account_info = {
                "equity": float(account.equity),
                "buying_power": float(account.buying_power),
                "cash": float(account.cash),
            }
            logger.info(f"Successfully fetched account info. Equity: {account_info['equity']:.2f}, Cash: {account_info['cash']:.2f}")
            return account_info

        except (APIError, RequestException) as e:
            AlpacaErrorHandler.handle_api_error("get_account_info", e)
            raise CustomAPIError(
                f"Alpaca API error fetching account info: {e}",
                "alpaca",
                ErrorSeverity.HIGH,
                {"operation": "get_account_info"},
                e
            )
        except (ValueError, TypeError) as e:
            context = {"operation": "get_account_info", "error_type": "data_conversion"}
            error = TradingError(
                f"Data conversion error processing account info: {e}",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
            AlpacaErrorHandler.handle_api_error("get_account_info", error, context)
            raise

    # Use safe_execute with default return value
    default_return = {"equity": 0.0, "buying_power": 0.0, "cash": 0.0}
    result = safe_execute(fetch_account, default_return, {"operation": "get_account_info"})

    # Log if we're returning default values (indicates API failure)
    if result == default_return:
        logger.critical("⚠️  ALPACA API FAILURE: Returning default equity $0.00 - Real account data not retrieved!")

    return result

@alpaca_api_call(max_retries=3)
def get_positions(api: REST, symbol: str):
    """Get positions for a symbol with standardized error handling"""
    logger.debug(f"Attempting to fetch positions for symbol: {symbol} from Alpaca.")

    def fetch_positions():
        try:
            positions = api.list_positions()
            for pos in positions:
                if pos.symbol == symbol:
                    position_info = {
                        "qty": float(pos.qty),
                        "avg_entry_price": float(pos.avg_entry_price),
                        "market_value": float(pos.market_value),
                        "unrealized_pl": float(pos.unrealized_pl),
                        "unrealized_intraday_pl": float(pos.unrealized_intraday_pl),
                        "current_price": float(pos.current_price)
                    }
                    logger.info(f"Found position for {symbol}. Qty: {position_info['qty']:.8f}, Avg Entry: {position_info['avg_entry_price']:.2f}")
                    return position_info

            logger.info(f"No open position found for symbol: {symbol}.")
            return {
                "qty": 0.0,
                "avg_entry_price": 0.0,
                "market_value": 0.0,
                "unrealized_pl": 0.0,
                "unrealized_intraday_pl": 0.0,
                "current_price": 0.0
            }

        except (APIError, RequestException) as e:
            AlpacaErrorHandler.handle_api_error("get_positions", e, {"symbol": symbol})
            raise CustomAPIError(
                f"Alpaca API error fetching positions for {symbol}: {e}",
                "alpaca",
                ErrorSeverity.HIGH,
                {"operation": "get_positions", "symbol": symbol},
                e
            )
        except (ValueError, TypeError) as e:
            context = {"operation": "get_positions", "symbol": symbol, "error_type": "data_conversion"}
            error = TradingError(
                f"Data conversion error processing position info for {symbol}: {e}",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
            AlpacaErrorHandler.handle_api_error("get_positions", error, context)
            raise

    # Use safe_execute with default return value
    default_return = {
        "qty": 0.0,
        "avg_entry_price": 0.0,
        "market_value": 0.0,
        "unrealized_pl": 0.0,
        "unrealized_intraday_pl": 0.0,
        "current_price": 0.0
    }
    return safe_execute(fetch_positions, default_return, {"operation": "get_positions", "symbol": symbol})

@alpaca_api_call(max_retries=2)
def get_historical_crypto_data(api, symbol="BTC/USD", timeframe_unit="Minute", num_bars=100):
    """Get historical crypto data with standardized error handling"""
    logger.debug(f"Attempting to fetch {num_bars} {timeframe_unit} historical crypto data for {symbol}.")

    def fetch_historical_data():
        try:
            tf_unit_lower = timeframe_unit.lower()
            if tf_unit_lower in ["minute", "1minute", "min", "1min"]:
                tf_value = TimeFrame.Minute
            elif tf_unit_lower in ["hour", "1hour", "h", "1h"]:
                tf_value = TimeFrame.Hour
            else:
                tf_value = TimeFrame.Day

            bars = api.get_crypto_bars(symbol, tf_value, limit=num_bars).df
            bars.reset_index(inplace=True)
            records = bars.to_dict(orient='records')
            logger.info(f"Successfully fetched {len(records)} historical {timeframe_unit} bars for {symbol}.")
            return records

        except (APIError, RequestException) as e:
            context = {
                "symbol": symbol,
                "timeframe": timeframe_unit,
                "num_bars": num_bars
            }
            AlpacaErrorHandler.handle_api_error("get_historical_crypto_data", e, context)
            raise CustomAPIError(
                f"Alpaca API error fetching historical data for {symbol}: {e}",
                "alpaca",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
        except Exception as e:
            context = {
                "symbol": symbol,
                "timeframe": timeframe_unit,
                "num_bars": num_bars,
                "error_type": "data_processing"
            }
            error = TradingError(
                f"Unexpected error fetching historical data for {symbol}: {e}",
                ErrorSeverity.MEDIUM,
                context,
                e
            )
            AlpacaErrorHandler.handle_api_error("get_historical_crypto_data", error, context)
            raise

    # Use safe_execute with empty list as default
    context = {"symbol": symbol, "timeframe": timeframe_unit, "num_bars": num_bars}
    return safe_execute(fetch_historical_data, [], context)