# START OF FILE ai_decision_service.py

"""AI Decision Service

Provides `get_ai_decision` which asks Google's Gemini model for a trading
recommendation, validates the JSON it returns, and falls back to HOLD if
anything goes wrong.
"""

from __future__ import annotations

import functools
import json
import logging
import os
import re
from typing import Any, Dict, Optional

import google.generativeai as genai
from google.api_core import exceptions as g_exc
from pydantic import BaseModel, ValidationError, field_validator

from error_handler import ErrorSeverity, ErrorCategory, handle_error
from error_utils import (
    AIServiceErrorHandler, gemini_api_call, safe_execute,
    APIError as CustomAPIError, DataError
)
from email_alert import send_enhanced_alert, AlertPriority, AlertCategory

try:
    from tenacity import (
        retry,
        retry_if_exception_type,
        stop_after_attempt,
        wait_exponential,
    )
except ModuleNotFoundError:
    def _noop_decorator(*_dargs, **_dkwargs):
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return wrapper
        return decorator
    retry = _noop_decorator
    retry_if_exception_type = lambda *a, **kw: None
    stop_after_attempt = lambda *_a, **_kw: None
    wait_exponential = lambda *_a, **_kw: None

    logging.getLogger(__name__).warning(
        "tenacity not installed – retry logic disabled; `pip install tenacity` recommended"
    )

import config
from security.secure_config import get_secure_api_key

logger = logging.getLogger(__name__)

# Lazy loading function for API key
def get_gemini_api_key() -> str:
    """Get Gemini API key with lazy loading"""
    api_key = get_secure_api_key("GEMINI_API_KEY") or getattr(config, "GEMINI_API_KEY", os.getenv("GEMINI_API_KEY", ""))
    if not api_key:
        raise RuntimeError("Gemini API key missing — configure secure credentials or define GEMINI_API_KEY in config/env")
    return api_key

MODEL_NAME: str = getattr(config, "GEMINI_MODEL", "gemini-1.5-flash")
REQUEST_TIMEOUT: int = getattr(config, "GEMINI_TIMEOUT", 60)

# Initialize Gemini AI with lazy loading
def _initialize_gemini():
    """Initialize Gemini AI with API key"""
    api_key = get_gemini_api_key()
    genai.configure(api_key=api_key)
    logger.debug("Gemini API key configured successfully")
    return genai.GenerativeModel(MODEL_NAME)

# Lazy initialization of model
_MODEL = None

def _get_model():
    """Get Gemini model with lazy initialization"""
    global _MODEL
    if _MODEL is None:
        _MODEL = _initialize_gemini()
    return _MODEL


_PROMPT_TEMPLATE: str = (
    "You are an expert BTC/USD trading AI assistant with a specific 'Hold the Dip' philosophy.\n"
    "You will receive the following trading data as JSON:\n"
    "{snapshot}\n"
    "The 'total_unrealized_pl' field shows the current profit or loss for the entire position. "
    "'open_lots_details' provides a list of individual buy lots, each with its own 'unrealized_pl_lot'.\n"
    "'regime' will be one of: trending, trending_volatile, volatile, quiet, or ranging.\n"
    "New fields for account liquidity and exposure are: 'total_account_equity', 'available_cash_usd', 'total_open_position_value_usd', and 'overall_exposure_percentage'.\n\n"

    "--- MASTER TRADING PHILOSOPHY ---\n"
    "1.  **NEVER SELL AT A LOSS (OVERALL).** The bot should never sell at a loss after considering all fees. However, profit-taking decisions should consider BOTH `total_unrealized_pl` AND overall account performance (`pnl_today`).\n"
    "2.  **SELLING IS ONLY FOR TAKING PROFIT (OVERALL).** Consider selling when:\n"
    "    - `pnl_today` is significantly positive (e.g., >$20-30) AND RSI is above 65-70, OR\n"
    "    - `total_unrealized_pl` is positive AND RSI is above 70, OR\n"
    "    - Daily target is achieved (`pnl_today` > `target_usd`) AND market shows overbought conditions (RSI >70)\n"
    "    - **CRITICAL**: Don't let unrealized losses on individual lots prevent profit-taking when the overall account is highly profitable!\n"
    "3.  **Time-Based Profit Taking (Conditional):** If a lot has been held for more than 24 hours AND `unrealized_pl_lot` for that lot is positive (indicating a profit *before* considering new sell fees), consider recommending a 'SELL' decision for a portion of that lot to lock in profits. The bot's internal logic will ensure the final realized profit, after all fees, is positive. This is especially relevant if other strong sell signals are not present.\n"
    "4.  **BUYING STRATEGY (AGGRESSIVE ACCUMULATION & MOMENTUM):** Your primary goal is to accumulate BTC. You are FORBIDDEN from issuing a normal 'BUY' decision (i.e., not a strategic buy). Your ONLY exception to buy is if you identify a clear opportunity based on the following criteria. **For any BUY decision, you MUST set the 'is_dip_buy' field in your JSON response to `true`.**\n"
    "    -   **Strategy A: Dip Accumulation (Buying the Dip - prioritized when `total_unrealized_pl` is negative, or for initial deep value entry when `total_open_qty` is 0):**\n"
    "        -   **Decision Priority:** This strategy is for accumulating during downturns. You are expected to BUY if ANY of the following conditions are met:\n"
    "            -   RSI is low (e.g., below 45, especially below 25) AND `rsi_slope` is positive.\n"
    "            -   OR RSI is low (e.g., below 45) AND `macd_hist` is positive or turning positive.\n"
    "            -   OR `volume_spike` is significantly high (> 1.5) AND RSI is low (e.g., below 45).\n"
    "        -   **Trend:** You MUST consider these 'buy the dip' signals even if `trend_5m` is 'bearish', as the strategy is to accumulate on all significant dips, taking a long-term view that price will recover. If multiple conditions are met, confidence should be HIGH.\n"
    "    -   **Strategy B: Momentum Buy (Buying the high, riding the rally - prioritized when `total_unrealized_pl` is positive or `total_open_qty` is 0):**\n"
    "        -   **Decision Priority:** This strategy is for riding established uptrends. **YOU MUST ISSUE A 'BUY' decision if all of the following conditions are met:**\n"
    "            -   RSI is in a bullish range (e.g., 50-85) AND `rsi_slope` is strongly positive (e.g., > -1.0).\n"
    "            -   AND `macd_hist` is positive or turning positive (e.g., > -5.0).\n"
    "            -   AND `volume_spike` is high (e.g., > 0.9) on upward price moves, indicating strong buying interest.\n"
    "            -   `trend_5m` MUST be 'bullish' for these entries.\n"
    "        -   Confidence should be HIGH for Momentum Buys.\n"
    "    -   **Accumulation Directive:** You MUST prioritize making a BUY decision over a HOLD decision if the conditions for either Strategy A or Strategy B are met, as the primary goal is accumulation. **Explicitly state which strategy triggers the BUY decision in your reasoning.**\n"
    "4.  **CAPITAL LIQUIDITY & ALLOCATION (CRITICAL):** Your paramount duty is to prevent capital paralysis and ensure strategic liquidity. You must dynamically adjust your `quantity_percentage` recommendation to manage overall portfolio exposure. This percentage represents a fraction of the `total_account_equity` to allocate for the proposed trade.\n"
    "    -   **Liquidity Preservation:** Always prioritize maintaining `available_cash_usd` for future strategic dip-buying opportunities or market reversals. Do NOT allocate all capital into current positions.\n"
    "    -   **Dynamic Quantity:** If `overall_exposure_percentage` (percentage of total equity currently in open positions) is high (e.g., > 70%), or `available_cash_usd` is low (e.g., < 20% of `total_account_equity`), you MUST recommend a significantly smaller `quantity_percentage` (e.g., 0.01-0.03), or `null` for `quantity_percentage` if a buy would overextend the portfolio. In extreme cases of high exposure and low cash, suggest 'HOLD' even if buy signals are present, explicitly stating the liquidity concern.\n"
    "    -   **Risk Tolerance:** Higher `market_volatility` or 'volatile'/'trending_volatile' `regime`s require a more conservative `quantity_percentage` and quicker liquidity preservation (e.g., lower `overall_exposure_percentage` tolerance).\n\n"

    "--- PROFIT-TAKING DECISION MATRIX ---\n"
    "**CRITICAL PROFIT-TAKING SCENARIOS (Always consider SELL in these cases):**\n"
    "1. **Daily Target Achieved + Overbought**: If `pnl_today` > `target_usd` AND RSI > 70 → Strong SELL signal\n"
    "2. **High Daily Profit + Moderate Overbought**: If `pnl_today` > $25 AND RSI > 65 → Consider SELL\n"
    "3. **Extreme Overbought**: If RSI > 75 AND `pnl_today` > $10 → Strong SELL signal\n"
    "4. **Combined Signals**: If `pnl_today` > $20 AND RSI > 65 AND MACD showing bearish divergence → SELL\n"
    "**Remember**: Don't let negative `total_unrealized_pl` prevent profit-taking when overall account performance is strong!\n\n"

    "Your task is to return a JSON object with these fields, **all fields are mandatory and must be present, precisely matching the field names provided, and in the specified data types. If a value is null, explicitly return `null`.**:\n"
    "  - decision: BUY, SELL, or HOLD (string)\n"
    "  - reasoning: A short explanation of your logic (string).\n"
    "  - confidence_level: LOW, MEDIUM, or HIGH (string)\n"
    "  - is_dip_buy: boolean. Set to `true` ONLY if this is a strategic 'BUY' (either for dip accumulation or momentum). Otherwise, set to `false`.\n"
    "  - quantity_percentage: decimal between 0 and 1 (float or null). **Adjust this dynamically based on capital liquidity and exposure directives.**\n"
    "  - stop_loss_price: number (float or null).\n"
    "  - take_profit_price: number (float or null).\n"
    "  - min_profit_override_usd: (DEPRECATED - ALWAYS SET TO NULL)\n\n"
    "Respond **only** with the JSON. No extra text."
)

class Decision(BaseModel):
    decision: str
    reasoning: str
    confidence_level: Optional[str] = "MEDIUM"
    is_dip_buy: bool | None = None
    quantity_percentage: float | None = None
    stop_loss_price: float | None = None
    take_profit_price: float | None = None
    min_profit_override_usd: float | None = None

    @field_validator("decision")
    @classmethod
    def _check_decision(cls, v: str) -> str:
        v_up = v.upper()
        if v_up not in {"BUY", "SELL", "HOLD"}:
            raise ValueError("decision must be BUY, SELL or HOLD")
        return v_up

    @field_validator("confidence_level")
    @classmethod
    def _check_confidence(cls, v: str) -> str:
        v_up = v.upper()
        if v_up not in {"LOW", "MEDIUM", "HIGH"}:
            raise ValueError("confidence_level must be LOW, MEDIUM or HIGH")
        return v_up

    @field_validator("quantity_percentage")
    @classmethod
    def _check_qty_pct(
        cls, v: float | None
    ) -> float | None:
        if v is not None and not 0 <= v <= 1:
            raise ValueError("quantity_percentage must be between 0 and 1 (inclusive)")
        return v

_HOLD_FALLBACK_DECISION = Decision(
    decision="HOLD",
    reasoning="Fallback because AI response could not be parsed or validated",
    confidence_level="LOW",
    is_dip_buy=False,
    quantity_percentage=None,
    stop_loss_price=None,
    take_profit_price=None,
    min_profit_override_usd=None,
)

def _extract_json_block(text: str) -> str:
    match = re.search(r"\{.*\}", text, re.S)
    if not match:
        raise ValueError("No JSON object found in model response")
    return match.group(0)

_retry_decision = retry(
    wait=wait_exponential(min=2, max=30),
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(
        (
            g_exc.ResourceExhausted,
            g_exc.DeadlineExceeded,
            g_exc.ServiceUnavailable,
            TimeoutError,
        )
    ),
    reraise=True,
)

@_retry_decision
@gemini_api_call(max_retries=3)
def _ask_gemini(prompt: str, timeout: int = REQUEST_TIMEOUT) -> Decision:
    """Ask Gemini for a trading decision with enhanced error handling"""
    logger.debug("Sending prompt to Gemini (chars=%d)", len(prompt))

    try:
        response = _get_model().generate_content(
            prompt,
            request_options={"timeout": timeout},
        )
        raw_text = response.text.strip()

        logger.info(f"Gemini Raw Response: \n{raw_text}")
    except (g_exc.GoogleAPICallError, g_exc.RetryError, TimeoutError) as e:
        context = {
            "prompt_length": len(prompt),
            "timeout": timeout,
            "model": MODEL_NAME
        }
        AIServiceErrorHandler.handle_gemini_error(e, context=context)
        raise CustomAPIError(
            f"Gemini API error: {e}",
            "gemini",
            ErrorSeverity.HIGH,
            context,
            e
        )

    try:
        json_block = _extract_json_block(raw_text)
        data = json.loads(json_block)

        if "confidence_level" not in data or not isinstance(data["confidence_level"], str) or data["confidence_level"].upper() not in {"LOW", "MEDIUM", "HIGH"}:
             logger.warning("AI response missing or invalid 'confidence_level' value. Defaulting to 'MEDIUM'.")
             data["confidence_level"] = "MEDIUM"

        # Ensure quantity_percentage is handled if AI explicitly returns 0.0 for it
        # If AI suggests BUY but with 0.0 quantity, it should be treated as HOLD for practical purposes
        if data.get("quantity_percentage") == 0.0 and data.get("decision", "").upper() == "BUY":
            logger.warning("AI suggested BUY but returned quantity_percentage as 0.0. Interpreting as HOLD (no trade).")
            data["decision"] = "HOLD"
            data["reasoning"] = "AI suggested BUY with 0.0 quantity, treating as HOLD."
            data["is_dip_buy"] = False # Not a strategic buy if quantity is zero
            data["quantity_percentage"] = None # Set to None for consistency in model

        validated = Decision.model_validate(data)
        logger.info(f"Gemini Validated Decision: {validated.model_dump()}")
        return validated
    except (ValueError, ValidationError, json.JSONDecodeError) as exc:
        logger.warning("Invalid AI response: %s", exc)
        context = {
            "raw_text": raw_text[:200] if 'raw_text' in locals() else "N/A",
            "prompt_length": len(prompt) if 'prompt' in locals() else 0
        }
        AIServiceErrorHandler.handle_validation_error(exc, context=context)
        raise DataError(
            f"AI response validation error: {exc}",
            ErrorSeverity.MEDIUM,
            context,
            exc
        )

def get_ai_decision(market_snapshot: Dict[str, Any], request_id: str | None = None) -> Dict[str, Any]:
    """Get AI trading decision with enhanced error handling and fallback"""
    if request_id:
        logger.info("AI decision requested (id=%s)", request_id)

    def get_decision():
        snapshot_str = json.dumps(market_snapshot, indent=2)
        prompt = _PROMPT_TEMPLATE.format(snapshot=snapshot_str)

        try:
            decision = _ask_gemini(prompt).model_dump()
            logger.info("AI decision=%s (id=%s)", decision.get("decision"), request_id)
            return decision

        except (g_exc.GoogleAPICallError, g_exc.RetryError, TimeoutError) as exc:
            context = {"request_id": request_id, "market_snapshot_keys": list(market_snapshot.keys())}
            AIServiceErrorHandler.handle_gemini_error(exc, request_id, context)
            raise CustomAPIError(
                f"Gemini API/network error: {exc}",
                "gemini",
                ErrorSeverity.HIGH,
                context,
                exc
            )
        except (ValueError, ValidationError, json.JSONDecodeError) as exc:
            context = {"request_id": request_id, "market_snapshot_keys": list(market_snapshot.keys())}
            AIServiceErrorHandler.handle_validation_error(exc, request_id, context)
            raise DataError(
                f"AI response validation/parsing error: {exc}",
                ErrorSeverity.MEDIUM,
                context,
                exc
            )
        except Exception as exc:
            context = {"request_id": request_id, "market_snapshot_keys": list(market_snapshot.keys())}
            error = CustomAPIError(
                f"Unexpected error in get_ai_decision: {exc}",
                "gemini",
                ErrorSeverity.CRITICAL,
                context,
                exc
            )
            handle_error(error, request_id=request_id)
            raise

    # Use safe_execute with HOLD fallback
    context = {"request_id": request_id, "operation": "get_ai_decision"}
    result = safe_execute(get_decision, _HOLD_FALLBACK_DECISION.model_dump(), context, request_id)

    if result == _HOLD_FALLBACK_DECISION.model_dump():
        logger.info(f"Returning HOLD Fallback due to error. Reason: {_HOLD_FALLBACK_DECISION.reasoning}")

        # Send alert for AI decision fallback
        send_enhanced_alert(
            "AI Decision Service Fallback",
            f"AI decision service returned fallback HOLD decision for request {request_id}. Check logs for details.",
            AlertPriority.HIGH,
            AlertCategory.API,
            {"request_id": request_id, "fallback_reason": _HOLD_FALLBACK_DECISION.reasoning}
        )

    return result

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    sample_snapshot = {
        "symbol": "BTCUSDT",
        "avg_price": 60750.1,
        "atr": 350.6,
        "rsi": 20.0,
        "timestamp_utc": "2025-06-15T12:34:56Z", # Corrected field name
        "open_lots_details": [
            {
                "lot_id": "lot1",
                "remaining_qty": 0.001,
                "cost_basis_per_unit": 61000.0,
                "current_stop": 60000.0,
                "unrealized_pl_lot": -2.50
            }
        ],
        "total_unrealized_pl": -2.50,
        "total_open_qty": 0.001,
        "regime": "volatile",
        "trend_5m": "bearish",
        "rsi_slope": 0.5,
        "macd_hist": 10.0,
        "volume_spike": 1.6,
        "exchanges_used": ["Binance"], # Added for completeness
        "volatility": 300.0, # Added for completeness
        "sma_short": 60700.0, # Added for completeness
        "sma_long": 60800.0, # Added for completeness
        "ema_short": 60720.0, # Added for completeness
        "ema_long": 60780.0, # Added for completeness
        "macd": -60.0, # Added for completeness
        "macd_signal": -50.0, # Added for completeness
        "macd_hist": -10.0, # Corrected sample value to fit prompt logic
        "rsi_overbought": 70.0, # Added for completeness
        "rsi_oversold": 30.0, # Added for completeness
        "trend_threshold": 0.002, # Added for completeness
        "momentum": 50.0, # Added for completeness
        # CAPITAL ALLOCATION METRICS
        "total_account_equity": 1000.0,
        "available_cash_usd": 500.0,
        "total_open_position_value_usd": 500.0,
        "overall_exposure_percentage": 50.0
    }

    result = get_ai_decision(sample_snapshot, request_id="demo-run-granular")
    print(json.dumps(result, indent=2))

# END OF FILE ai_decision_service.py