﻿**********************
Windows PowerShell transcript start
Start time: 20250606082904
Username: DESKTOP-ABIUAS4\<PERSON> Mcknight
RunAs User: DESKTOP-ABIUAS4\<PERSON> Mcknight
Configuration Name: 
Machine: DESKTOP-ABIUAS4 (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 4792
PSVersion: 5.1.26100.4061
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.26100.4061
BuildVersion: 10.0.26100.4061
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is analysis_report_full.txt
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) .\analyze_data.ps1

--- binance_btcusdt_1m.csv (Primary Merged File) ---
First 10 lines:
num_trades,low,quote_asset_volume,ignore,open,timestamp,high,taker_buy_quote,close,volume,close_time,taker_buy_base
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:00:00+00:00,4261.48,0.0,4261.48,1.775183,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:01:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4280.56,0.0,0.0,4280.56,2017-08-17 04:02:00+00:00,4280.56,0.0,4280.56,0.261074,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:03:00+00:00,4261.48,0.0,4261.48,0.012008,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:04:00+00:00,4261.48,0.0,4261.48,0.140796,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:05:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:06:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:07:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:08:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0

Last 10 lines:
2025-06-06 06:09:00+00:00,102933.0,102979.71,102933.0,102979.71,11.96628,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:10:00+00:00,102979.71,103067.0,102979.7,103066.99,45.69354,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:11:00+00:00,103067.0,103150.0,103015.79,103140.58,57.68922,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:12:00+00:00,103140.58,103140.59,103107.15,103120.66,21.21384,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:13:00+00:00,103120.66,103120.66,103083.46,103093.66,15.21662,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:14:00+00:00,103093.66,103199.0,103093.65,103190.98,25.42977,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:15:00+00:00,103190.97,103210.06,103190.97,103201.68,7.96229,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:16:00+00:00,103201.68,103201.69,103096.4,103096.4,10.90606,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:17:00+00:00,103096.4,103101.51,103082.7,103082.7,4.54593,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:18:00+00:00,103082.7,103108.22,103082.7,103108.21,9.85101,0.0,0.0,0.0,0.0,0.0,
Total lines (including header): 4095507

--- binance_btcusdt_1m_NEW_DATA.csv (Newly Downloaded Data) ---
First 10 lines:
timestamp,open,high,low,close,volume,close_time,quote_asset_volume,num_trades,taker_buy_base,taker_buy_quote,ignore
2021-03-03 08:38:00+00:00,50738.85,50753.24,50683.59,50736.39,99.103903,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:39:00+00:00,50736.4,50750.0,50700.43,50723.58,32.161484,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:40:00+00:00,50723.58,50776.67,50673.98,50679.1,48.402673,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:41:00+00:00,50679.09,50722.4,50650.15,50691.89,56.147928,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:42:00+00:00,50694.72,50780.0,50691.88,50753.72,48.076714,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:43:00+00:00,50753.72,50779.38,50650.09,50661.97,63.451321,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:44:00+00:00,50661.97,50708.6,50656.5,50708.59,68.378664,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:45:00+00:00,50708.6,50724.04,50656.42,50663.19,115.80963,0.0,0.0,0.0,0.0,0.0,
2021-03-03 08:46:00+00:00,50663.19,50673.94,50603.11,50639.9,64.264417,0.0,0.0,0.0,0.0,0.0,

Last 10 lines:
2025-06-06 06:40:00+00:00,103093.61,103115.32,103089.28,103115.31,7.754,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:41:00+00:00,103115.32,103115.32,103033.99,103034.0,11.06868,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:42:00+00:00,103033.99,103034.0,103000.16,103023.41,10.24809,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:43:00+00:00,103023.41,103024.25,102979.28,102979.28,11.62793,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:44:00+00:00,102979.29,103000.0,102979.28,102999.99,6.16294,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:45:00+00:00,102999.99,103082.92,102999.99,103082.91,4.27212,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:46:00+00:00,103082.92,103098.28,103070.3,103070.31,10.96937,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:47:00+00:00,103070.31,103070.31,103070.3,103070.31,1.81075,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:48:00+00:00,103070.31,103085.99,103070.3,103085.99,2.37999,0.0,0.0,0.0,0.0,0.0,
2025-06-06 06:49:00+00:00,103085.98,103110.53,103085.98,103110.53,8.53226,0.0,0.0,0.0,0.0,0.0,
Total lines (including header): 2239539

--- binance_btcusdt_1m_BACKUP_2021-03-03.csv (Backup File) ---
First 10 lines:
num_trades,low,quote_asset_volume,ignore,open,timestamp,high,taker_buy_quote,close,volume,close_time,taker_buy_base
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:00:00+00:00,4261.48,0.0,4261.48,1.775183,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:01:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4280.56,0.0,0.0,4280.56,2017-08-17 04:02:00+00:00,4280.56,0.0,4280.56,0.261074,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:03:00+00:00,4261.48,0.0,4261.48,0.012008,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:04:00+00:00,4261.48,0.0,4261.48,0.140796,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:05:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:06:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:07:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0
0.0,4261.48,0.0,0.0,4261.48,2017-08-17 04:08:00+00:00,4261.48,0.0,4261.48,0.0,0.0,0.0

Last 10 lines:
0.0,50556.03,0.0,0.0,50672.06,2021-03-03 08:28:00+00:00,50672.06,0.0,50575.42,103.813163,0.0,0.0
0.0,50569.93,0.0,0.0,50575.42,2021-03-03 08:29:00+00:00,50673.02,0.0,50655.34,74.828534,0.0,0.0
0.0,50655.35,0.0,0.0,50655.35,2021-03-03 08:30:00+00:00,50777.0,0.0,50726.8,111.938947,0.0,0.0
0.0,50701.0,0.0,0.0,50726.8,2021-03-03 08:31:00+00:00,50777.0,0.0,50701.0,80.915955,0.0,0.0
0.0,50598.27,0.0,0.0,50707.2,2021-03-03 08:32:00+00:00,50707.2,0.0,50608.28,80.190454,0.0,0.0
0.0,50550.48,0.0,0.0,50610.8,2021-03-03 08:33:00+00:00,50672.85,0.0,50648.76,96.644152,0.0,0.0
0.0,50637.15,0.0,0.0,50644.5,2021-03-03 08:34:00+00:00,50770.41,0.0,50705.11,68.864118,0.0,0.0
0.0,50642.39,0.0,0.0,50695.9,2021-03-03 08:35:00+00:00,50737.0,0.0,50736.99,43.478692,0.0,0.0
0.0,50700.0,0.0,0.0,50736.99,2021-03-03 08:36:00+00:00,50800.0,0.0,50799.99,77.442146,0.0,0.0
0.0,50723.19,0.0,0.0,50800.0,2021-03-03 08:37:00+00:00,50815.0,0.0,50738.26,78.387563,0.0,0.0
Total lines (including header): 1856001

--- Summary and Cross-Checks ---
Primary Merged File Data Rows: 4095506
Newly Downloaded File Data Rows: 2239538
Backup File Data Rows: 1856000
Expected total data rows if New Data + Backup are perfectly merged: 4095538
-> WARNING: The primary file's total data row count (4095506) does NOT exactly match the sum of new data and backup data (4095538). This could indicate a partial merge, data loss, overwrite, or another issue.

--- Analysis Limitations ---
This check verifies file existence, headers, start/end timestamps, and overall line counts. It does NOT thoroughly check every single line for numerical errors or subtle, intermittent gaps within the vast amount of data.
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python merge_data_files.py
Last timestamp in file: 2025-06-06 07:50:00+00:00

--- Starting Data Merging and Finalization ---
Loading backup data from binance_btcusdt_1m_BACKUP_2021-03-03.csv...
C:\Users\<USER>\Documents\Bitcoin ai agent\merge_data_files.py:208: FutureWarning: The argument 'date_parser' is deprecated and wi
ll be removed in a future version. Please use 'date_format' instead, or read your data in as 'object' dtype and then call 'to_datetime'.
  df_backup = pd.read_csv(backup_file, parse_dates=['timestamp'], date_parser=lambda x: pd.to_datetime(x, utc=True))
Loaded 1856000 rows from backup file.
Loading newly downloaded data from binance_btcusdt_1m_NEW_DATA.csv...
C:\Users\<USER>\Documents\Bitcoin ai agent\merge_data_files.py:228: FutureWarning: The argument 'date_parser' is deprecated and wi
ll be removed in a future version. Please use 'date_format' instead, or read your data in as 'object' dtype and then call 'to_datetime'.
  df_new_data = pd.read_csv(new_data_file, parse_dates=['timestamp'], date_parser=lambda x: pd.to_datetime(x, utc=True))
Loaded 4479137 rows from new data file.
Concatenating dataframes...
Sorting combined data by timestamp and removing duplicates...
Saving finalized merged data to binance_btcusdt_1m.csv...
Successfully merged and saved 4095599 rows to binance_btcusdt_1m.csv.
First timestamp in merged file: 2017-08-17 04:00:00+00:00
Last timestamp in merged file: 2025-06-06 07:50:00+00:00
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) Remove-Item binance_btcusdt_1m_NEW_DATA.csv, binance_btcusdt_1m_BACKUP_2021-03-03.csv, analyze_data.ps1, analysis_report.txt, analysis_report_full.txt, conversation.md -ErrorAction SilentlyContinue
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
 will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  df['close'] = pd.to_numeric(df['close'], errors='coerce').fillna(method='ffill').fillna(df['close'].mean())
Calculating RSI with period 7...
Generating 'BUY', 'SELL', 'HOLD' labels based on RSI thresholds...
Generated 4095599 labels. Label distribution:
label
HOLD    3159581
SELL     478502
BUY      457516
Name: count, dtype: int64
Labeled data saved to 'C:\Users\<USER>\Documents\Bitcoin ai agent\rsi_labeled_data.csv'.
Data range: 2017-08-17 04:00:00+00:00 to 2025-06-06 07:50:00+00:00
First few rows with new 'rsi' and 'label' columns:
                  timestamp    close   rsi label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  HOLD
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
macd_signal                 0
macd_hist                   0
dtype: int64
------------------------------------------------------------

Cleaned data shape ready for ML: (4095599, 22)
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...  atr  momentum_10  macd  macd_signal  macd_hist  label
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...  0.0          0.0   0.0          0.0        0.0   HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...  0.0          0.0   0.0          0.0        0.0   HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...  0.0          0.0   0.0          0.0        0.0   HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...  0.0          0.0   0.0          0.0        0.0   HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...  0.0          0.0   0.0          0.0        0.0   HOLD

[5 rows x 22 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
SELL     478502
BUY      457516
Name: count, dtype: int64
Labeled data saved to 'C:\Users\<USER>\Documents\Bitcoin ai agent\rsi_labeled_data.csv'.
Data range: 2017-08-17 04:00:00+00:00 to 2025-06-06 07:50:00+00:00
First few rows with new 'rsi' and 'label' columns:
                  timestamp    close   rsi label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  HOLD
Example rows with new technical indicators:
                  timestamp    close   rsi    sma_7        ema_7       atr      macd label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  4280.56  4266.250000  2.544000  1.522051  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  4261.48  4265.057500  4.748800  1.175145  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  4261.48  4264.163125  4.115627  0.889960  HOLD
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...       atr  momentum_10      macd  macd_signal  macd_hist  l
abel
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...  2.544000          0.0  1.522051     0.304410   1.217641
HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...  4.748800          0.0  1.175145     0.478557   0.696588
HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...  4.115627          0.0  0.889960     0.560838   0.329123
HOLD

[5 rows x 22 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
Accuracy on test set: 0.9996

Classification Report (Test Set):
              precision    recall  f1-score   support

         BUY       1.00      1.00      1.00    455514
        HOLD       1.00      1.00      1.00   3152102
        SELL       1.00      1.00      1.00    476703

    accuracy                           1.00   4084319
   macro avg       1.00      1.00      1.00   4084319
weighted avg       1.00      1.00      1.00   4084319


Confusion Matrix (Test Set):
[[ 454646     868       0]
 [      0 3152095       7]
 [      0     587  476116]]
Model saved successfully as 'trade_action_model.joblib'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$9.68
  Win/Loss Count  : 0 / 120
  Win/Loss Streak : 0 / 120
==================================================
binance: Fetched 1000 rows. Total batches: 2000. Total rows this run: 2000000. Current range: 2024-12-21 05:52:00+00:00 to 2024-12-21 22:3
1:00+00:00. Up to 2024-12-21 22:32:00+00:00.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 223, in <module>
2025-06-06 11:39:23,685 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
    main()
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 204, in main
    fetch_and_save('binance', cfg)
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 139, in fetch_and_save
    ohlcv = exchange.fetch_ohlcv(config['symbol'], timeframe, since_ms, limit)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\ccxt\binance.py", line 4538, in fetch_ohlcv
    candles = self.parse_ohlcvs(response, market, timeframe, since, limit)
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
2025-06-06 11:40:26,131 - INFO     - [Initial Sync] Reconstructed 0 aggregated lots from historical orders. Total: 0.00000000 BTC.
2025-06-06 11:40:26,229 - CRITICAL - [Initial Sync] CRITICAL: Could not get current Alpaca position for full reconciliation: module 'confi
g' has no attribute 'MACD_SLOW_PERIOD'. Ledger may remain out of sync. Proceeding with historical data only.
2025-06-06 11:40:26,232 - WARNING  - Bot: Performing initial trade cycle run...
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\main_bot.py", line 680, in <module>
    main() # Start the bot
    ^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\main_bot.py", line 472, in main
    robust_run_trade_cycle(api_client) # Run first cycle immediately
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\main_bot.py", line 515, in robust_run_trade_cycle
    ind = market_analysis.analyze_market()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\market_analysis.py", line 511, in analyze_market
    required_bars_1m = max(config.SHORT_SMA_PERIOD, config.LONG_SMA_PERIOD, ATR_PERIOD, config.RSI_PERIOD, config.MACD_SLOW_PERIOD + confi
g.MACD_SIGNAL_PERIOD) + 10 # Adding a small buffer
                                                                                                           ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'config' has no attribute 'MACD_SLOW_PERIOD'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\urllib3\response.py", line 567, in read
    data = self._fp_read(amt) if not fp_closed else b""
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\urllib3\response.py", line 533, in _fp_read
    return self._fp.read(amt) if amt is not None else self._fp.read()
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\http\client.py", line 479, in read
    s = self.fp.read(amt)
        ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\ssl.py", line 1251, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\ssl.py", line 1103, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : BUY
--------------------------------------------------
Current Equity    : $256.55
Unrealized P/L    : -$0.02
Open Lots         : 2
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $258.34
  Session Return  : -0.69%
  Max Equity      : $258.34
  Min Equity      : $256.55
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$16.42
  Win/Loss Count  : 0 / 127
  Win/Loss Streak : 0 / 127
==================================================
2025-06-06 15:00:37,804 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : BUY
--------------------------------------------------
Current Equity    : $255.34
Unrealized P/L    : $0.11
Open Lots         : 5
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $256.55
  Session Return  : -0.47%
  Max Equity      : $256.57
  Min Equity      : $255.31
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$17.41
  Win/Loss Count  : 0 / 143
  Win/Loss Streak : 0 / 143
==================================================
2025-06-06 17:23:14,951 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : BUY
--------------------------------------------------
Current Equity    : $254.44
Unrealized P/L    : -$0.32
Open Lots         : 8
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $255.34
  Session Return  : -0.35%
  Max Equity      : $255.39
  Min Equity      : $254.44
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$17.73
  Win/Loss Count  : 0 / 144
  Win/Loss Streak : 0 / 144
==================================================
2025-06-06 18:14:06,982 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : SELL
--------------------------------------------------
Current Equity    : $253.84
Unrealized P/L    : $0.01
Open Lots         : 2
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $254.44
  Session Return  : -0.24%
  Max Equity      : $254.44
  Min Equity      : $253.84
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$18.78
  Win/Loss Count  : 0 / 151
  Win/Loss Streak : 0 / 151
==================================================
2025-06-06 19:07:35,521 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\frame.py", line 1410, in _get_values_for_csv
    mgr = self._mgr.get_values_for_csv(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\internals\managers.py", line 466, in get_val
ues_for_csv
    return self.apply(
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\internals\managers.py", line 363, in apply
    applied = getattr(b, f)(**kwargs)
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\internals\blocks.py", line 780, in get_value
s_for_csv
    result = get_values_for_csv(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\indexes\base.py", line 7834, in get_values_f
or_csv
    values = values.astype(str)
             ^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
SELL     269449
BUY      256510
Name: count, dtype: int64
Labeled data saved to 'C:\Users\<USER>\Documents\Bitcoin ai agent\rsi_labeled_data.csv'.
Data range: 2017-08-17 04:00:00+00:00 to 2025-06-06 07:50:00+00:00
First few rows with new 'rsi' and 'label' columns:
                  timestamp    close   rsi label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  HOLD
Example rows with new technical indicators:
                  timestamp    close   rsi    sma_7        ema_7       atr      macd label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  4280.56  4266.250000  2.544000  1.522051  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  4261.48  4265.057500  4.748800  1.175145  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  4261.48  4264.163125  4.115627  0.889960  HOLD
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...       atr  momentum_10      macd  macd_signal  macd_hist  l
abel
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...  2.544000          0.0  1.522051     0.304410   1.217641
HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...  4.748800          0.0  1.175145     0.478557   0.696588
HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...  4.115627          0.0  0.889960     0.560838   0.329123
HOLD

[5 rows x 22 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
Accuracy on test set: 1.0000

Classification Report (Test Set):
              precision    recall  f1-score   support

         BUY       1.00      1.00      1.00    147785
        HOLD       1.00      1.00      1.00   1939234
        SELL       1.00      1.00      1.00    152580

    accuracy                           1.00   2239599
   macro avg       1.00      1.00      1.00   2239599
weighted avg       1.00      1.00      1.00   2239599


Confusion Matrix (Test Set):
[[ 147785       0       0]
 [      0 1939234       0]
 [      0       6  152574]]
Model saved successfully as 'trade_action_model.joblib'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
    result = ResamplerWindowApply(self, func, args=args, kwargs=kwargs).agg()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\apply.py", line 190, in agg
    return self.agg_dict_like()
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\apply.py", line 423, in agg_dict_like
    return self.agg_or_apply_dict_like(op_name="agg")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\apply.py", line 1608, in agg_or_apply_dict_l
ike
    result_index, result_data = self.compute_dict_like(
                                ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\apply.py", line 462, in compute_dict_like
    func = self.normalize_dictlike_arg(op_name, selected_obj, func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\apply.py", line 663, in normalize_dictlike_a
rg
    raise KeyError(f"Column(s) {list(cols)} do not exist")
KeyError: "Column(s) ['sma_10'] do not exist"
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
SELL     269449
BUY      256510
Name: count, dtype: int64
Labeled data saved to 'C:\Users\<USER>\Documents\Bitcoin ai agent\rsi_labeled_data.csv'.
Data range: 2017-08-17 04:00:00+00:00 to 2025-06-06 07:50:00+00:00
First few rows with new 'rsi' and 'label' columns:
                  timestamp    close   rsi label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  HOLD
Example rows with new technical indicators and market context:
                  timestamp    close   rsi    sma_7        ema_7       atr      macd    trend trend_5m    regime label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  neutral  neutral  volatile  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  neutral  neutral  volatile  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  4280.56  4266.250000  2.544000  1.522051  neutral  neutral  volatile  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  4261.48  4265.057500  4.748800  1.175145  neutral  neutral  volatile  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  4261.48  4264.163125  4.115627  0.889960  neutral  neutral  volatile  HOLD
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...       atr  momentum_10      macd  macd_signal  macd_hist  l
abel
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...  0.000000          0.0  0.000000     0.000000   0.000000
HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...  2.544000          0.0  1.522051     0.304410   1.217641
HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...  4.748800          0.0  1.175145     0.478557   0.696588
HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...  4.115627          0.0  0.889960     0.560838   0.329123
HOLD

[5 rows x 22 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
Accuracy on test set: 1.0000

Classification Report (Test Set):
              precision    recall  f1-score   support

         BUY       1.00      1.00      1.00    147785
        HOLD       1.00      1.00      1.00   1939234
        SELL       1.00      1.00      1.00    152580

    accuracy                           1.00   2239599
   macro avg       1.00      1.00      1.00   2239599
weighted avg       1.00      1.00      1.00   2239599


Confusion Matrix (Test Set):
[[ 147785       0       0]
 [      0 1939234       0]
 [      0       6  152574]]
Model saved successfully as 'trade_action_model.joblib'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
label                 0
dtype: int64
------------------------------------------------------------

Cleaned data shape ready for ML: (4095599, 27)
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_10', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'volatility', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 't
rend', 'trend_5m', 'regime', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...  macd_signal  macd_hist    trend  trend_5m    regime  label
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...     0.000000   0.000000  neutral   neutral  volatile   HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...     0.000000   0.000000  neutral   neutral  volatile   HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...     0.304410   1.217641  neutral   neutral  volatile   HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...     0.478557   0.696588  neutral   neutral  volatile   HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...     0.560838   0.329123  neutral   neutral  volatile   HOLD

[5 rows x 27 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\sklearn\utils\validation.py", line 2961, in validate_dat
a
    X, y = check_X_y(X, y, **check_params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\sklearn\utils\validation.py", line 1370, in check_X_y
    X = check_array(
        ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\sklearn\utils\validation.py", line 1055, in check_array
    array = _asarray_with_order(array, order=order, dtype=dtype, xp=xp)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\sklearn\utils\_array_api.py", line 839, in _asarray_with
_order
    array = numpy.asarray(array, order=order, dtype=dtype)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\venv\Lib\site-packages\pandas\core\generic.py", line 2153, in __array__
    arr = np.asarray(values, dtype=dtype)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: could not convert string to float: 'neutral'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
Accuracy on test set: 1.0000

Classification Report (Test Set):
              precision    recall  f1-score   support

         BUY       1.00      1.00      1.00    147785
        HOLD       1.00      1.00      1.00   1939234
        SELL       1.00      1.00      1.00    152580

    accuracy                           1.00   2239599
   macro avg       1.00      1.00      1.00   2239599
weighted avg       1.00      1.00      1.00   2239599


Confusion Matrix (Test Set):
[[ 147784       1       0]
 [      0 1939234       0]
 [      0       7  152573]]
Model saved successfully as 'trade_action_model.joblib'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : BUY
--------------------------------------------------
Current Equity    : $253.35
Unrealized P/L    : -$0.21
Open Lots         : 7
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $253.83
  Session Return  : -0.19%
  Max Equity      : $253.83
  Min Equity      : $253.35
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$18.86
  Win/Loss Count  : 0 / 152
  Win/Loss Streak : 0 / 152
==================================================
2025-06-06 20:29:21,597 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
2025-06-06 20:29:44,031 - config_logger - WARNING - ############################################################
2025-06-06 20:29:44,031 - config_logger - WARNING - ##           PAPER TRADING MODE IS ACTIVE!                ##
2025-06-06 20:29:44,031 - config_logger - WARNING - ##           NO REAL MONEY WILL BE USED.                  ##
2025-06-06 20:29:44,031 - config_logger - WARNING - ############################################################
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\main_bot.py", line 19, in <module>
    import config
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\config.py", line 48, in <module>
    raise ValueError(f"Alpaca API Key ID not found for {'LIVE' if IS_LIVE_TRADING else 'PAPER'} mode.")
ValueError: Alpaca API Key ID not found for PAPER mode.
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
                       ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\http\client.py", line 1428, in getresponse
    response.begin()
  File "C:\Users\<USER>\miniconda3\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\ssl.py", line 1251, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\ssl.py", line 1103, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
  Session Return  : 0.00%
  Max Equity      : $252.89
  Min Equity      : $252.87
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$19.64
  Win/Loss Count  : 0 / 153
  Win/Loss Streak : 0 / 153
==================================================
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 223, in <module>
2025-06-06 21:09:26,101 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
    main()
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 204, in main
    fetch_and_save('binance', cfg)
  File "C:\Users\<USER>\Documents\Bitcoin ai agent\multi_exchange_downloader.py", line 191, in fetch_and_save
    time.sleep(sleep_time)
KeyboardInterrupt
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python generate_rsi_labels.py
SELL     269449
BUY      256510
Name: count, dtype: int64
Labeled data saved to 'C:\Users\<USER>\Documents\Bitcoin ai agent\rsi_labeled_data.csv'.
Data range: 2017-08-17 04:00:00+00:00 to 2025-06-06 07:50:00+00:00
First few rows with new 'rsi' and 'label' columns:
                  timestamp    close   rsi label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  HOLD
Example rows with new technical indicators and market context:
                  timestamp    close   rsi    sma_7        ema_7       atr      macd    trend trend_5m    regime label
0 2017-08-17 04:00:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  neutral  neutral  volatile  HOLD
1 2017-08-17 04:01:00+00:00  4261.48  50.0  4261.48  4261.480000  0.000000  0.000000  neutral  neutral  volatile  HOLD
2 2017-08-17 04:02:00+00:00  4280.56  50.0  4280.56  4266.250000  2.544000  1.522051  neutral  neutral  volatile  HOLD
3 2017-08-17 04:03:00+00:00  4261.48  50.0  4261.48  4265.057500  4.748800  1.175145  neutral  neutral  volatile  HOLD
4 2017-08-17 04:04:00+00:00  4261.48  50.0  4261.48  4264.163125  4.115627  0.889960  neutral  neutral  volatile  HOLD
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python prepare_ml_data.py
label                 0
dtype: int64
------------------------------------------------------------

Cleaned data shape ready for ML: (4095599, 27)
Columns in ML data: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'num_trades', 'taker_buy_base', 'taker_buy_quote', 'rsi', 'sma
_7', 'sma_10', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50', 'atr', 'volatility', 'momentum_10', 'macd', 'macd_signal', 'macd_hist', 't
rend', 'trend_5m', 'regime', 'label']
Sample cleaned data (for ML):
                  timestamp     open     high      low    close    volume  ...  macd_signal  macd_hist    trend  trend_5m    regime  label
0 2017-08-17 04:00:00+00:00  4261.48  4261.48  4261.48  4261.48  1.775183  ...     0.000000   0.000000  neutral   neutral  volatile   HOLD
1 2017-08-17 04:01:00+00:00  4261.48  4261.48  4261.48  4261.48  0.000000  ...     0.000000   0.000000  neutral   neutral  volatile   HOLD
2 2017-08-17 04:02:00+00:00  4280.56  4280.56  4280.56  4280.56  0.261074  ...     0.304410   1.217641  neutral   neutral  volatile   HOLD
3 2017-08-17 04:03:00+00:00  4261.48  4261.48  4261.48  4261.48  0.012008  ...     0.478557   0.696588  neutral   neutral  volatile   HOLD
4 2017-08-17 04:04:00+00:00  4261.48  4261.48  4261.48  4261.48  0.140796  ...     0.560838   0.329123  neutral   neutral  volatile   HOLD

[5 rows x 27 columns]
Smart ML dataset ready! Saved to 'prepared_training_data.csv'.
Rows: 4095599
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python train_trade_model.py
Accuracy on test set: 1.0000

Classification Report (Test Set):
              precision    recall  f1-score   support

         BUY       1.00      1.00      1.00     50370
        HOLD       1.00      1.00      1.00    647675
        SELL       1.00      1.00      1.00     54106

    accuracy                           1.00    752151
   macro avg       1.00      1.00      1.00    752151
weighted avg       1.00      1.00      1.00    752151


Confusion Matrix (Test Set):
[[ 50370      0      0]
 [     0 647675      0]
 [     0      0  54106]]
Model saved successfully as 'trade_action_model.joblib'
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) clear
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
(venv) python main_bot.py
AI Decision       : BUY
--------------------------------------------------
Current Equity    : $252.58
Unrealized P/L    : -$0.08
Open Lots         : 8
--------------------------------------------------
Session Stats (Current Run):
  Start Equity    : $252.88
  Session Return  : -0.12%
  Max Equity      : $252.95
  Min Equity      : $252.58
  Max Drawdown    : 0.69%
--------------------------------------------------
Cumulative Stats (All Runs):
  Realized P/L    : -$19.64
  Win/Loss Count  : 0 / 153
  Win/Loss Streak : 0 / 153
==================================================
2025-06-06 22:28:05,156 - WARNING  - Main Bot: Signal SIGINT received. Shutting down.
(venv) TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
(base)
]633;D;0]633;A]633;P;Cwd=C:\x5cUsers\x5cPeter Mcknight\x5cDocuments\x5cBitcoin ai agentPS C:\Users\<USER>\Documents\Bitcoin ai agent> ]633;B
