import csv
import json
import os
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime

# Configuration
TRADE_HISTORY_FILE = "trade_history.csv"
SESSION_STATS_FILE = "session_stats.json"
FEE_PERCENT = Decimal("0.0025") # Assuming this is your fee, adjust if different (from config.py)

def calculate_fees_for_script(qty: Decimal, price: Decimal) -> Decimal:
    return (qty * price * FEE_PERCENT).quantize(Decimal("0.00000001"), rounding=ROUND_HALF_UP)

def recalculate_streaks():
    print(f"Analyzing '{TRADE_HISTORY_FILE}' to recalculate streaks...")
    
    trades_for_streak = []
    if not os.path.exists(TRADE_HISTORY_FILE):
        print(f"Error: '{TRADE_HISTORY_FILE}' not found.")
        return

    with open(TRADE_HISTORY_FILE, 'r', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            action = row.get('action', '').upper()
            status = row.get('status', '').lower()
            
            # Consider only 'SELL' type trades that are 'filled' for streak calculation
            if ('SELL' in action or 'LIQUIDATION' in action) and status == 'filled':
                try:
                    realized_pl_str = row.get('realized_pl')
                    entry_price_str = row.get('entry_price') # Used if realized_pl is 0.0 for LIQUIDATION
                    sell_price_str = row.get('price')
                    qty_str = row.get('quantity')

                    pnl = Decimal("0.0")
                    is_pnl_valid = False

                    if realized_pl_str and realized_pl_str.strip() and realized_pl_str.lower() != 'none':
                        try:
                            pnl_val = Decimal(realized_pl_str)
                            # Only consider non-zero P/L for streaks unless it's a calculated liquidation
                            if pnl_val != Decimal("0.0"):
                                pnl = pnl_val
                                is_pnl_valid = True
                        except:
                            pass # Could not convert, will try to calculate if applicable

                    # If P/L is zero (e.g. for older LIQUIDATION entries) but we have entry/sell prices, calculate it
                    if not is_pnl_valid and 'LIQUIDATION' in action and entry_price_str and sell_price_str and qty_str:
                        try:
                            entry_price = Decimal(entry_price_str)
                            sell_price = Decimal(sell_price_str)
                            qty = Decimal(qty_str)
                            
                            if entry_price > 0 and qty > 0:
                                buy_fee = calculate_fees_for_script(qty, entry_price)
                                current_sell_fee = calculate_fees_for_script(qty, sell_price)
                                pnl = (sell_price - entry_price) * qty - (buy_fee + current_sell_fee)
                                is_pnl_valid = True
                        except Exception as e_calc:
                            print(f"Warning: Could not calculate P/L for LIQUIDATION row: {row}. Error: {e_calc}")
                    
                    if is_pnl_valid:
                        trades_for_streak.append({'pnl': pnl, 'timestamp': row.get('timestamp')})
                    else:
                        # This might happen for BUYs or non-filled orders, which is fine.
                        # Or if P/L is genuinely 0.0 and not a liquidation where we can recalculate.
                        pass

                except Exception as e_row:
                    print(f"Error processing row: {row}. Error: {e_row}")
                    continue
    
    if not trades_for_streak:
        print("No valid SELL/LIQUIDATION trades found in history to calculate streaks.")
        return

    trades_for_streak.sort(key=lambda x: x['timestamp'])

    current_win_streak = 0
    current_loss_streak = 0

    for trade in trades_for_streak:
        if trade['pnl'] > Decimal("0"):
            current_win_streak += 1
            current_loss_streak = 0
        else: # Loss or break-even (P/L <= 0)
            current_loss_streak += 1
            current_win_streak = 0
            
    print(f"\nRecalculated Streaks from '{TRADE_HISTORY_FILE}':")
    print(f"  Current Win Streak: {current_win_streak}")
    print(f"  Current Loss Streak: {current_loss_streak}")

    if not os.path.exists(SESSION_STATS_FILE):
        print(f"Error: '{SESSION_STATS_FILE}' not found. Cannot update.")
        return

    try:
        with open(SESSION_STATS_FILE, 'r') as f: session_data = json.load(f)
        print(f"\nOriginal streaks in '{SESSION_STATS_FILE}': Win: {session_data.get('win_streak')}, Loss: {session_data.get('loss_streak')}")
        session_data['win_streak'] = current_win_streak
        session_data['loss_streak'] = current_loss_streak
        with open(SESSION_STATS_FILE, 'w') as f: json.dump(session_data, f, indent=4)
        print(f"\nSuccessfully updated '{SESSION_STATS_FILE}' with recalculated streaks: Win: {current_win_streak}, Loss: {current_loss_streak}")
    except Exception as e:
        print(f"Error updating '{SESSION_STATS_FILE}': {e}")

if __name__ == "__main__":
    recalculate_streaks()

