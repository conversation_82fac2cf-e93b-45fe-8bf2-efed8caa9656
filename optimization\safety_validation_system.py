#!/usr/bin/env python3
"""
Safety and Validation System - Phase 4 Implementation

Comprehensive safety checks, emergency stops, and automatic rollback mechanisms
for the live trading bot integration with Phase 3 regime-aware optimization.

Key Features:
- Multi-layer parameter validation
- Emergency stop mechanisms
- Automatic rollback on performance degradation
- Real-time safety monitoring
- Circuit breaker patterns
- Performance-based safety triggers

Author: Bitcoin AI Trading Bot - Phase 4 Safety System
Date: July 29, 2025
"""

import os
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
import threading
import time

logger = logging.getLogger("TradingBotApp.SafetySystem")

class SafetyLevel(Enum):
    """Safety alert levels."""
    GREEN = "green"      # Normal operation
    YELLOW = "yellow"    # Caution - monitoring required
    ORANGE = "orange"    # Warning - intervention may be needed
    RED = "red"          # Critical - immediate action required

class SafetyTrigger(Enum):
    """Types of safety triggers."""
    PARAMETER_VALIDATION = "parameter_validation"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    EXCESSIVE_LOSSES = "excessive_losses"
    SYSTEM_ERROR = "system_error"
    MANUAL_OVERRIDE = "manual_override"
    REGIME_CONFIDENCE = "regime_confidence"
    DATA_QUALITY = "data_quality"

class SafetyValidationSystem:
    """
    Comprehensive safety and validation system for live trading.
    
    Provides multiple layers of protection against parameter errors,
    performance degradation, and system failures.
    """
    
    def __init__(self, base_dir: str = None):
        """Initialize the safety validation system."""
        
        self.base_dir = base_dir or os.path.dirname(os.path.dirname(__file__))
        self.safety_log_file = os.path.join(self.base_dir, "safety_system.log")
        self.emergency_stop_file = os.path.join(self.base_dir, "EMERGENCY_STOP.txt")
        
        # Safety state
        self.current_safety_level = SafetyLevel.GREEN
        self.emergency_stop_active = False
        self.safety_triggers = []
        self.last_safety_check = None
        
        # Parameter validation limits
        self.parameter_limits = {
            'risk_percent': {'min': 0.001, 'max': 0.1, 'critical_max': 0.15},
            'stop_percent': {'min': 0.005, 'max': 0.05, 'critical_max': 0.1},
            'profit_target_pct': {'min': 0.005, 'max': 0.1, 'critical_max': 0.2},
            'max_trade_value_usd': {'min': 10, 'max': 500, 'critical_max': 1000},
            'cash_reserve_usd': {'min': 50, 'max': 1000, 'critical_max': 2000}
        }
        
        # Performance monitoring
        self.performance_history = []
        self.max_performance_history = 100
        self.performance_degradation_threshold = 0.3  # 30% degradation triggers warning

        # CRITICAL: Loss prevention enforcement
        # These settings ensure the bot NEVER sells at a loss
        self.loss_prevention_config = {
            'MIN_PROFIT_THRESHOLD_USD': 0.01,  # Bot never sells unless profit > $0.01 after fees
            'FEE_PERCENT': 0.0025,  # 0.25% trading fee always included in calculations
            'enforce_profit_threshold': True,  # Always enforce minimum profit requirement
            'note': 'These settings are NEVER modified by optimization - they are hard safety limits'
        }
        self.critical_loss_threshold = 0.05  # 5% loss triggers emergency stop
        
        # System monitoring
        self.error_count = 0
        self.max_errors_per_hour = 10
        self.error_timestamps = []
        
        # Circuit breaker settings
        self.circuit_breaker_active = False
        self.circuit_breaker_timeout = timedelta(minutes=30)
        self.circuit_breaker_triggered_time = None
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        logger.info("Safety Validation System initialized")
    
    def validate_parameters(self, parameters: Dict[str, Any], 
                          source: str = "unknown") -> Tuple[bool, List[str], SafetyLevel]:
        """
        Comprehensive parameter validation.
        
        Args:
            parameters: Parameters to validate
            source: Source of the parameters
            
        Returns:
            Tuple of (is_valid, error_messages, safety_level)
        """
        
        try:
            errors = []
            safety_level = SafetyLevel.GREEN
            
            # Check each parameter against limits
            for param_name, value in parameters.items():
                if param_name in self.parameter_limits:
                    limits = self.parameter_limits[param_name]
                    
                    # Convert to float for comparison
                    try:
                        float_value = float(value)
                    except (ValueError, TypeError):
                        errors.append(f"Parameter {param_name} has invalid value: {value}")
                        safety_level = SafetyLevel.RED
                        continue
                    
                    # Check critical limits
                    if float_value > limits['critical_max']:
                        errors.append(f"Parameter {param_name}={float_value} exceeds critical maximum {limits['critical_max']}")
                        safety_level = SafetyLevel.RED
                    elif float_value > limits['max']:
                        errors.append(f"Parameter {param_name}={float_value} exceeds safe maximum {limits['max']}")
                        safety_level = max(safety_level, SafetyLevel.ORANGE)
                    elif float_value < limits['min']:
                        errors.append(f"Parameter {param_name}={float_value} below safe minimum {limits['min']}")
                        safety_level = max(safety_level, SafetyLevel.ORANGE)
            
            # Check parameter relationships
            relationship_errors, relationship_safety = self._validate_parameter_relationships(parameters)
            errors.extend(relationship_errors)

            # Compare safety levels properly
            if relationship_safety.value == 'red' or safety_level.value == 'red':
                safety_level = SafetyLevel.RED
            elif relationship_safety.value == 'orange' or safety_level.value == 'orange':
                safety_level = SafetyLevel.ORANGE
            elif relationship_safety.value == 'yellow' or safety_level.value == 'yellow':
                safety_level = SafetyLevel.YELLOW
            else:
                safety_level = SafetyLevel.GREEN
            
            # Log validation result
            self._log_safety_event(
                trigger=SafetyTrigger.PARAMETER_VALIDATION,
                level=safety_level,
                message=f"Parameter validation from {source}: {len(errors)} errors",
                details={'source': source, 'errors': errors, 'parameter_count': len(parameters)}
            )
            
            is_valid = len(errors) == 0 or safety_level in [SafetyLevel.GREEN, SafetyLevel.YELLOW]
            return is_valid, errors, safety_level
            
        except Exception as e:
            logger.error(f"Error in parameter validation: {e}")
            return False, [f"Validation system error: {e}"], SafetyLevel.RED
    
    def _validate_parameter_relationships(self, parameters: Dict[str, Any]) -> Tuple[List[str], SafetyLevel]:
        """Validate relationships between parameters."""
        
        errors = []
        safety_level = SafetyLevel.GREEN
        
        try:
            # Risk vs Stop Loss relationship
            risk_percent = float(parameters.get('risk_percent', 0.02))
            stop_percent = float(parameters.get('stop_percent', 0.02))
            
            if stop_percent < risk_percent * 0.5:
                errors.append(f"Stop loss {stop_percent:.3f} too tight for risk {risk_percent:.3f}")
                safety_level = SafetyLevel.ORANGE
            
            if stop_percent > risk_percent * 5:
                errors.append(f"Stop loss {stop_percent:.3f} too wide for risk {risk_percent:.3f}")
                safety_level = SafetyLevel.YELLOW
            
            # Profit targets validation
            profit_targets = parameters.get('profit_targets', [])
            if isinstance(profit_targets, list) and len(profit_targets) > 0:
                for i, target in enumerate(profit_targets):
                    try:
                        target_float = float(target)
                        if target_float < stop_percent:
                            errors.append(f"Profit target {i+1} ({target_float:.3f}) less than stop loss ({stop_percent:.3f})")
                            safety_level = SafetyLevel.ORANGE
                    except (ValueError, TypeError):
                        errors.append(f"Invalid profit target {i+1}: {target}")
                        safety_level = SafetyLevel.ORANGE
            
            return errors, safety_level
            
        except Exception as e:
            logger.error(f"Error validating parameter relationships: {e}")
            return [f"Relationship validation error: {e}"], SafetyLevel.RED
    
    def check_performance_safety(self, current_performance: Dict[str, Any]) -> Tuple[bool, SafetyLevel]:
        """
        Check if current performance indicates safety issues.
        
        Args:
            current_performance: Current performance metrics
            
        Returns:
            Tuple of (is_safe, safety_level)
        """
        
        try:
            # Add to performance history
            performance_entry = {
                'timestamp': datetime.now().isoformat(),
                'metrics': current_performance.copy()
            }
            self.performance_history.append(performance_entry)
            
            # Keep only recent history
            if len(self.performance_history) > self.max_performance_history:
                self.performance_history = self.performance_history[-self.max_performance_history:]
            
            safety_level = SafetyLevel.GREEN
            
            # Check for critical losses
            current_pnl = current_performance.get('total_pnl_pct', 0)
            if current_pnl < -self.critical_loss_threshold:
                self._trigger_emergency_stop(
                    trigger=SafetyTrigger.EXCESSIVE_LOSSES,
                    message=f"Critical loss threshold exceeded: {current_pnl:.2%}"
                )
                return False, SafetyLevel.RED
            
            # Check for performance degradation
            if len(self.performance_history) >= 10:
                recent_performance = [p['metrics'].get('sharpe_ratio', 0) for p in self.performance_history[-10:]]
                older_performance = [p['metrics'].get('sharpe_ratio', 0) for p in self.performance_history[-20:-10]] if len(self.performance_history) >= 20 else []
                
                if older_performance:
                    recent_avg = sum(recent_performance) / len(recent_performance)
                    older_avg = sum(older_performance) / len(older_performance)
                    
                    if older_avg > 0 and (recent_avg - older_avg) / older_avg < -self.performance_degradation_threshold:
                        safety_level = SafetyLevel.ORANGE
                        self._log_safety_event(
                            trigger=SafetyTrigger.PERFORMANCE_DEGRADATION,
                            level=safety_level,
                            message=f"Performance degradation detected: {recent_avg:.3f} vs {older_avg:.3f}",
                            details={'recent_avg': recent_avg, 'older_avg': older_avg}
                        )
            
            return True, safety_level
            
        except Exception as e:
            logger.error(f"Error checking performance safety: {e}")
            return False, SafetyLevel.RED
    
    def check_system_health(self) -> Tuple[bool, SafetyLevel]:
        """Check overall system health."""
        
        try:
            safety_level = SafetyLevel.GREEN
            
            # Check error rate
            current_time = datetime.now()
            one_hour_ago = current_time - timedelta(hours=1)
            
            # Clean old error timestamps
            self.error_timestamps = [ts for ts in self.error_timestamps if ts > one_hour_ago]
            
            if len(self.error_timestamps) > self.max_errors_per_hour:
                safety_level = SafetyLevel.ORANGE
                self._log_safety_event(
                    trigger=SafetyTrigger.SYSTEM_ERROR,
                    level=safety_level,
                    message=f"High error rate: {len(self.error_timestamps)} errors in last hour",
                    details={'error_count': len(self.error_timestamps)}
                )
            
            # Check circuit breaker status
            if self.circuit_breaker_active:
                if current_time - self.circuit_breaker_triggered_time > self.circuit_breaker_timeout:
                    self._reset_circuit_breaker()
                else:
                    safety_level = SafetyLevel.RED
            
            # Check emergency stop file
            if os.path.exists(self.emergency_stop_file):
                self._trigger_emergency_stop(
                    trigger=SafetyTrigger.MANUAL_OVERRIDE,
                    message="Emergency stop file detected"
                )
                return False, SafetyLevel.RED
            
            return True, safety_level
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            self.error_timestamps.append(datetime.now())
            return False, SafetyLevel.RED
    
    def _trigger_emergency_stop(self, trigger: SafetyTrigger, message: str):
        """Trigger emergency stop."""
        
        try:
            self.emergency_stop_active = True
            
            # Create emergency stop file
            with open(self.emergency_stop_file, 'w') as f:
                f.write(f"Emergency stop triggered at {datetime.now().isoformat()}\n")
                f.write(f"Trigger: {trigger.value}\n")
                f.write(f"Message: {message}\n")
            
            # Log emergency stop
            self._log_safety_event(
                trigger=trigger,
                level=SafetyLevel.RED,
                message=f"EMERGENCY STOP TRIGGERED: {message}",
                details={'trigger': trigger.value}
            )
            
            logger.critical(f"🚨 EMERGENCY STOP TRIGGERED: {message}")
            
        except Exception as e:
            logger.error(f"Error triggering emergency stop: {e}")
    
    def _trigger_circuit_breaker(self, trigger: SafetyTrigger, message: str):
        """Trigger circuit breaker."""
        
        try:
            self.circuit_breaker_active = True
            self.circuit_breaker_triggered_time = datetime.now()
            
            self._log_safety_event(
                trigger=trigger,
                level=SafetyLevel.RED,
                message=f"CIRCUIT BREAKER TRIGGERED: {message}",
                details={'trigger': trigger.value, 'timeout_minutes': self.circuit_breaker_timeout.total_seconds() / 60}
            )
            
            logger.error(f"⚡ CIRCUIT BREAKER TRIGGERED: {message}")
            
        except Exception as e:
            logger.error(f"Error triggering circuit breaker: {e}")
    
    def _reset_circuit_breaker(self):
        """Reset circuit breaker."""
        
        try:
            self.circuit_breaker_active = False
            self.circuit_breaker_triggered_time = None
            
            logger.info("✅ Circuit breaker reset - system ready")
            
        except Exception as e:
            logger.error(f"Error resetting circuit breaker: {e}")
    
    def clear_emergency_stop(self) -> bool:
        """Clear emergency stop condition."""
        
        try:
            if os.path.exists(self.emergency_stop_file):
                os.remove(self.emergency_stop_file)
            
            self.emergency_stop_active = False
            
            self._log_safety_event(
                trigger=SafetyTrigger.MANUAL_OVERRIDE,
                level=SafetyLevel.GREEN,
                message="Emergency stop cleared manually",
                details={}
            )
            
            logger.info("✅ Emergency stop cleared")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing emergency stop: {e}")
            return False
    
    def _log_safety_event(self, trigger: SafetyTrigger, level: SafetyLevel, 
                         message: str, details: Dict[str, Any]):
        """Log safety event."""
        
        try:
            event = {
                'timestamp': datetime.now().isoformat(),
                'trigger': trigger.value,
                'level': level.value,
                'message': message,
                'details': details
            }
            
            self.safety_triggers.append(event)
            
            # Keep only recent triggers
            if len(self.safety_triggers) > 1000:
                self.safety_triggers = self.safety_triggers[-1000:]
            
            # Log to file
            with open(self.safety_log_file, 'a') as f:
                f.write(json.dumps(event) + '\n')
            
            # Update current safety level
            if level.value in ['red', 'orange'] and level != self.current_safety_level:
                self.current_safety_level = level
                logger.warning(f"Safety level changed to {level.value}: {message}")
            
        except Exception as e:
            logger.error(f"Error logging safety event: {e}")
    
    def get_safety_status(self) -> Dict[str, Any]:
        """Get current safety system status."""
        
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'safety_level': self.current_safety_level.value,
                'emergency_stop_active': self.emergency_stop_active,
                'circuit_breaker_active': self.circuit_breaker_active,
                'recent_triggers': self.safety_triggers[-10:] if self.safety_triggers else [],
                'error_count_last_hour': len(self.error_timestamps),
                'performance_history_count': len(self.performance_history),
                'last_safety_check': self.last_safety_check.isoformat() if self.last_safety_check else None
            }
            
            if self.circuit_breaker_active and self.circuit_breaker_triggered_time:
                time_remaining = self.circuit_breaker_timeout - (datetime.now() - self.circuit_breaker_triggered_time)
                status['circuit_breaker_time_remaining'] = max(0, time_remaining.total_seconds())
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting safety status: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def start_monitoring(self, check_interval: int = 60):
        """Start background safety monitoring."""
        
        try:
            def monitoring_loop():
                logger.info(f"Starting safety monitoring (interval: {check_interval}s)")
                
                while self.monitoring_active:
                    try:
                        # Perform safety checks
                        system_healthy, health_level = self.check_system_health()
                        
                        if not system_healthy:
                            logger.warning(f"System health check failed: {health_level.value}")
                        
                        self.last_safety_check = datetime.now()
                        
                        # Wait for next check
                        time.sleep(check_interval)
                        
                    except Exception as e:
                        logger.error(f"Error in safety monitoring: {e}")
                        self.error_timestamps.append(datetime.now())
                        time.sleep(60)  # Wait 1 minute before retrying
                
                logger.info("Safety monitoring stopped")
            
            # Start monitoring thread
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            logger.info("✅ Safety monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting safety monitoring: {e}")
    
    def stop_monitoring(self):
        """Stop safety monitoring."""
        
        self.monitoring_active = False
        logger.info("Safety monitoring stopped")
    
    def shutdown(self):
        """Shutdown the safety system."""
        
        try:
            self.stop_monitoring()
            logger.info("Safety Validation System shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during safety system shutdown: {e}")

# Global instance
_safety_system = None

def validate_parameters(parameters: Dict[str, Any], source: str = "unknown") -> Tuple[bool, List[str], SafetyLevel]:
    """Validate parameters using the global safety system."""
    
    global _safety_system
    
    if _safety_system is None:
        _safety_system = SafetyValidationSystem()
    
    return _safety_system.validate_parameters(parameters, source)

def check_performance_safety(current_performance: Dict[str, Any]) -> Tuple[bool, SafetyLevel]:
    """Check performance safety using the global safety system."""
    
    global _safety_system
    
    if _safety_system is None:
        _safety_system = SafetyValidationSystem()
    
    return _safety_system.check_performance_safety(current_performance)

def get_safety_status() -> Dict[str, Any]:
    """Get current safety status."""
    
    global _safety_system
    
    if _safety_system is None:
        return {'status': 'not_initialized', 'timestamp': datetime.now().isoformat()}
    
    return _safety_system.get_safety_status()

def clear_emergency_stop() -> bool:
    """Clear emergency stop condition."""
    
    global _safety_system
    
    if _safety_system is None:
        _safety_system = SafetyValidationSystem()
    
    return _safety_system.clear_emergency_stop()

def shutdown_safety_system():
    """Shutdown the safety system."""

    global _safety_system

    if _safety_system is not None:
        _safety_system.shutdown()
        _safety_system = None
