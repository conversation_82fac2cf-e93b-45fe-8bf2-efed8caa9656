import csv

input_file = "trade_cycle_log.csv"
output_file = "trade_cycle_log_cleaned.csv"

def clean_csv(input_path, output_path):
    with open(input_path, "r", newline="", encoding="utf-8") as infile, \
         open(output_path, "w", newline="", encoding="utf-8") as outfile:
        
        reader = csv.reader(infile)
        writer = csv.writer(outfile, quoting=csv.QUOTE_MINIMAL)
        
        header = next(reader)
        writer.writerow(header)
        expected_cols = len(header)
        
        for i, row in enumerate(reader, start=2):
            if len(row) != expected_cols:
                # Try to fix by joining extra columns to last field
                if len(row) > expected_cols:
                    # Join excess columns to last column with comma
                    fixed_row = row[:expected_cols-1] + [','.join(row[expected_cols-1:])]
                    print(f"Fixed line {i}: combined extra columns")
                else:
                    # If fewer columns, pad with empty strings
                    fixed_row = row + [''] * (expected_cols - len(row))
                    print(f"Fixed line {i}: padded missing columns")
            else:
                fixed_row = row
            writer.writerow(fixed_row)

    print(f"Cleaning complete. Cleaned CSV saved as: {output_path}")

if __name__ == "__main__":
    clean_csv(input_file, output_file)
