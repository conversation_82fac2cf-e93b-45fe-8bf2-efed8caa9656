import pandas as pd

# The file you just uploaded
df = pd.read_csv('rsi_labeled_data.csv')

# FIXED: Only use market data and technical indicators (NO BOT OUTCOMES)
# Removed bot outcome features that cause data leakage:
# - decision, confidence, quantity_pct, trade_action (bot decisions)
# - session_pnl, net_win, net_loss, max_drawdown, loss_streak, win_streak (bot outcomes)
# - open_lots (bot state)
model_features = [
    'open', 'high', 'low', 'close', 'volume', 'num_trades',
    'quote_asset_volume', 'taker_buy_base', 'taker_buy_quote',
    'rsi', 'sma_7', 'sma_10', 'sma_14', 'sma_50',
    'ema_7', 'ema_14', 'ema_50', 'atr', 'volatility',
    'momentum_10', 'macd', 'macd_signal', 'macd_hist',
    'trend', 'trend_5m', 'regime'
]

# Make sure all features exist; fill missing with appropriate defaults
for col in model_features:
    if col not in df.columns:
        if col in ['trend', 'trend_5m', 'regime']:
            df[col] = 'neutral'  # Default for categorical features
        else:
            df[col] = 0.0  # Default for numeric features

# Add label if missing, using RSI as a simple logic
if 'label' not in df.columns or df['label'].isnull().all():
    def auto_label(row):
        try:
            rsi = float(row['rsi'])
            if rsi < 30:
                return 1   # BUY
            elif rsi > 70:
                return -1  # SELL
            else:
                return 0   # HOLD
        except:
            return 0
    df['label'] = df.apply(auto_label, axis=1)

# Make final ML dataset
dataset = df[model_features + ['label']]
dataset.to_csv('prepared_training_data.csv', index=False)
print(f"Full ML training data ready: {len(dataset)} rows saved to prepared_training_data.csv.")
print(dataset.head())
