#!/usr/bin/env python3
"""
Manual Profit-Taking Override System

This module provides a manual override mechanism that allows forced profit-taking
when clear opportunities exist, bypassing restrictive AI logic.

Usage:
    python manual_profit_override.py --action check_opportunity
    python manual_profit_override.py --action force_sell --percentage 25
    python manual_profit_override.py --action emergency_sell --percentage 50
"""

import json
import logging
import argparse
from datetime import datetime, timezone
from decimal import Decimal
from pathlib import Path
from typing import Dict, Any, List

# Import your existing modules
import config
from trade_logic import load_open_lots, save_open_lots
from alpaca_service import AlpacaService
from binance_data_service import BinanceDataService
from indicator_service import IndicatorService
from email_service import send_alert

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ManualProfitOverride:
    def __init__(self):
        self.alpaca = AlpacaService()
        self.binance = BinanceDataService()
        self.indicators = IndicatorService()
        
    def check_profit_opportunity(self) -> Dict[str, Any]:
        """Check current market conditions and profit opportunity"""
        try:
            # Get current market data
            current_price = self.binance.get_current_price()
            account = self.alpaca.get_account()
            position = self.alpaca.get_position()
            
            # Get technical indicators
            indicators = self.indicators.get_indicators()
            
            # Load open lots
            open_lots = load_open_lots()
            
            # Calculate current P/L
            total_unrealized_pl = Decimal("0")
            total_lot_value = Decimal("0")
            profitable_lots = []
            
            for lot in open_lots:
                remaining_qty = lot.get('remaining_qty', Decimal("0"))
                cost_basis = lot.get('cost_basis_per_unit', lot.get('buy_price', Decimal("0")))
                
                if remaining_qty > Decimal("0"):
                    lot_value = current_price * remaining_qty
                    lot_pl = (current_price - cost_basis) * remaining_qty
                    total_unrealized_pl += lot_pl
                    total_lot_value += lot_value
                    
                    if lot_pl > Decimal("0"):
                        profitable_lots.append({
                            'lot_id': lot.get('lot_id'),
                            'qty': remaining_qty,
                            'cost_basis': cost_basis,
                            'current_value': lot_value,
                            'unrealized_pl': lot_pl,
                            'profit_pct': (lot_pl / lot_value * 100) if lot_value > 0 else 0
                        })
            
            # Get daily P/L
            daily_start_equity = Decimal(str(account.last_equity))
            current_equity = Decimal(str(account.equity))
            daily_pnl = current_equity - daily_start_equity
            
            opportunity_score = self._calculate_opportunity_score(
                daily_pnl=daily_pnl,
                total_unrealized_pl=total_unrealized_pl,
                rsi=indicators.get('rsi', 50),
                macd_hist=indicators.get('macd_histogram', 0),
                current_price=current_price
            )
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'current_price': float(current_price),
                'daily_pnl': float(daily_pnl),
                'total_unrealized_pl': float(total_unrealized_pl),
                'total_lot_value': float(total_lot_value),
                'rsi': indicators.get('rsi', 50),
                'macd_histogram': indicators.get('macd_histogram', 0),
                'opportunity_score': opportunity_score,
                'profitable_lots_count': len(profitable_lots),
                'total_lots_count': len(open_lots),
                'recommendation': self._get_recommendation(opportunity_score, daily_pnl, indicators.get('rsi', 50))
            }
            
        except Exception as e:
            logger.error(f"Error checking profit opportunity: {e}")
            return {'error': str(e)}
    
    def _calculate_opportunity_score(self, daily_pnl: Decimal, total_unrealized_pl: Decimal, 
                                   rsi: float, macd_hist: float, current_price: Decimal) -> float:
        """Calculate a score from 0-100 indicating profit-taking opportunity strength"""
        score = 0
        
        # Daily P/L component (0-40 points)
        if daily_pnl > 50:
            score += 40
        elif daily_pnl > 30:
            score += 30
        elif daily_pnl > 20:
            score += 20
        elif daily_pnl > 10:
            score += 10
        
        # RSI component (0-30 points)
        if rsi > 80:
            score += 30
        elif rsi > 75:
            score += 25
        elif rsi > 70:
            score += 20
        elif rsi > 65:
            score += 10
        
        # MACD component (0-20 points)
        if macd_hist < -50:
            score += 20  # Strong bearish divergence
        elif macd_hist < -20:
            score += 15
        elif macd_hist < 0:
            score += 10
        
        # Unrealized P/L component (0-10 points)
        if total_unrealized_pl > 0:
            score += 10
        elif total_unrealized_pl > -20:
            score += 5
        
        return min(score, 100)
    
    def _get_recommendation(self, score: float, daily_pnl: Decimal, rsi: float) -> str:
        """Get recommendation based on opportunity score"""
        if score >= 80:
            return "STRONG SELL - Excellent profit-taking opportunity"
        elif score >= 60:
            return "SELL - Good profit-taking opportunity"
        elif score >= 40:
            return "CONSIDER SELL - Moderate opportunity"
        elif score >= 20:
            return "HOLD - Weak signals"
        else:
            return "HOLD - No clear opportunity"
    
    def force_sell(self, percentage: float, reason: str = "Manual override") -> Dict[str, Any]:
        """Force sell a percentage of profitable positions"""
        try:
            if not 0 < percentage <= 100:
                raise ValueError("Percentage must be between 0 and 100")
            
            open_lots = load_open_lots()
            current_price = self.binance.get_current_price()
            
            sell_percentage = Decimal(str(percentage / 100))
            executed_sells = []
            total_sell_value = Decimal("0")
            
            for lot in open_lots:
                remaining_qty = lot.get('remaining_qty', Decimal("0"))
                cost_basis = lot.get('cost_basis_per_unit', lot.get('buy_price', Decimal("0")))
                
                if remaining_qty > Decimal("0"):
                    # Calculate if this lot is profitable
                    lot_pl = (current_price - cost_basis) * remaining_qty
                    
                    if lot_pl > Decimal("0"):  # Only sell profitable lots
                        qty_to_sell = (remaining_qty * sell_percentage).quantize(Decimal("0.00000001"))
                        
                        if qty_to_sell >= Decimal("0.00001"):  # Minimum sell threshold
                            try:
                                # Execute sell order
                                sell_order = self.alpaca.submit_order(
                                    symbol="BTCUSD",
                                    qty=str(qty_to_sell),
                                    side="sell",
                                    type="market",
                                    time_in_force="gtc"
                                )
                                
                                sell_value = current_price * qty_to_sell
                                total_sell_value += sell_value
                                
                                executed_sells.append({
                                    'lot_id': lot.get('lot_id'),
                                    'qty_sold': float(qty_to_sell),
                                    'sell_price': float(current_price),
                                    'sell_value': float(sell_value),
                                    'order_id': sell_order.id
                                })
                                
                                # Update lot quantity
                                lot['remaining_qty'] = remaining_qty - qty_to_sell
                                
                                logger.info(f"Manual sell executed: Lot {lot.get('lot_id')}, Qty: {qty_to_sell}, Price: ${current_price}")
                                
                            except Exception as e:
                                logger.error(f"Failed to sell lot {lot.get('lot_id')}: {e}")
            
            # Save updated lots
            save_open_lots()
            
            # Send alert
            send_alert(
                subject=f"Manual Profit Override: {percentage}% Sell Executed",
                message=f"Manual sell executed: {len(executed_sells)} lots sold, Total value: ${total_sell_value:.2f}. Reason: {reason}"
            )
            
            return {
                'success': True,
                'executed_sells': executed_sells,
                'total_sell_value': float(total_sell_value),
                'lots_sold': len(executed_sells),
                'reason': reason
            }
            
        except Exception as e:
            logger.error(f"Error executing manual sell: {e}")
            return {'success': False, 'error': str(e)}

def main():
    parser = argparse.ArgumentParser(description='Manual Profit-Taking Override System')
    parser.add_argument('--action', choices=['check_opportunity', 'force_sell', 'emergency_sell'], 
                       required=True, help='Action to perform')
    parser.add_argument('--percentage', type=float, help='Percentage to sell (for force_sell/emergency_sell)')
    parser.add_argument('--reason', type=str, default='Manual override', help='Reason for manual action')
    
    args = parser.parse_args()
    
    override = ManualProfitOverride()
    
    if args.action == 'check_opportunity':
        result = override.check_profit_opportunity()
        print(json.dumps(result, indent=2))
        
    elif args.action in ['force_sell', 'emergency_sell']:
        if not args.percentage:
            print("Error: --percentage is required for sell actions")
            return
        
        reason = f"{args.action}: {args.reason}"
        result = override.force_sell(args.percentage, reason)
        print(json.dumps(result, indent=2))
    
if __name__ == "__main__":
    main()
