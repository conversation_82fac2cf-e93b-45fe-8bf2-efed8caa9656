#!/usr/bin/env python3
"""
Parameter validation system to ensure logical trading parameter combinations.
Prevents invalid or dangerous parameter settings during optimization.
"""

from typing import Dict, List, Tuple, Any, Optional
import warnings
from decimal import Decimal


class ParameterValidator:
    """
    Validates trading parameters to ensure they are logical and safe.
    """
    
    def __init__(self):
        """Initialize parameter validator with default rules."""
        self.validation_rules = self._create_default_rules()
        self.warnings_enabled = True
    
    def _create_default_rules(self) -> Dict[str, Dict[str, Any]]:
        """Create default validation rules for trading parameters."""
        return {
            # === PHASE 1 PARAMETERS ===
            'stop_percent': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.20,   # 20% maximum
                'type': float,
                'description': 'Stop loss percentage must be between 0.1% and 20%'
            },
            'trail_profit_buffer_pct': {
                'min': 0.0001, # 0.01% minimum
                'max': 0.05,   # 5% maximum
                'type': float,
                'description': 'Trailing profit buffer must be between 0.01% and 5%'
            },

            # === TECHNICAL INDICATORS ===
            'rsi_period': {
                'min': 2,      # Minimum meaningful RSI period
                'max': 50,     # Maximum reasonable RSI period
                'type': int,
                'description': 'RSI period must be between 2 and 50'
            },
            'rsi_overbought': {
                'min': 60.0,   # Minimum overbought level
                'max': 95.0,   # Maximum overbought level
                'type': float,
                'description': 'RSI overbought threshold must be between 60 and 95'
            },
            'rsi_oversold': {
                'min': 5.0,    # Minimum oversold level
                'max': 40.0,   # Maximum oversold level
                'type': float,
                'description': 'RSI oversold threshold must be between 5 and 40'
            },
            'short_sma_period': {
                'min': 2,      # Minimum SMA period
                'max': 50,     # Maximum short SMA period
                'type': int,
                'description': 'Short SMA period must be between 2 and 50'
            },
            'long_sma_period': {
                'min': 5,      # Minimum long SMA period
                'max': 200,    # Maximum long SMA period
                'type': int,
                'description': 'Long SMA period must be between 5 and 200'
            },
            'macd_fast_period': {
                'min': 5,      # Minimum MACD fast period
                'max': 25,     # Maximum MACD fast period
                'type': int,
                'description': 'MACD fast period must be between 5 and 25'
            },
            'macd_slow_period': {
                'min': 15,     # Minimum MACD slow period
                'max': 50,     # Maximum MACD slow period
                'type': int,
                'description': 'MACD slow period must be between 15 and 50'
            },
            'atr_period': {
                'min': 5,      # Minimum ATR period
                'max': 50,     # Maximum ATR period
                'type': int,
                'description': 'ATR period must be between 5 and 50'
            },

            # === RISK MANAGEMENT ===
            'max_trade_value_usd': {
                'min': 10.0,   # Minimum trade size
                'max': 1000.0, # Maximum trade size
                'type': float,
                'description': 'Maximum trade value must be between $10 and $1000'
            },
            'cash_reserve_usd': {
                'min': 50.0,   # Minimum cash reserve
                'max': 2000.0, # Maximum cash reserve
                'type': float,
                'description': 'Cash reserve must be between $50 and $2000'
            },
            'min_qty_pct': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.20,   # 20% maximum
                'type': float,
                'description': 'Minimum quantity percentage must be between 0.1% and 20%'
            },
            'max_qty_pct': {
                'min': 0.01,   # 1% minimum
                'max': 0.50,   # 50% maximum
                'type': float,
                'description': 'Maximum quantity percentage must be between 1% and 50%'
            },

            # === PROFIT TARGETS ===
            'dip_buy_profit_target_pct': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.20,   # 20% maximum
                'type': float,
                'description': 'Dip buy profit target must be between 0.1% and 20%'
            },
            'momentum_buy_profit_target_pct': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.15,   # 15% maximum
                'type': float,
                'description': 'Momentum buy profit target must be between 0.1% and 15%'
            },
            'granular_take_profit_pct': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.10,   # 10% maximum
                'type': float,
                'description': 'Granular take profit must be between 0.1% and 10%'
            },

            # === TIMING & COOLDOWNS ===
            'trade_cycle_interval_minutes': {
                'min': 1,      # 1 minute minimum
                'max': 60,     # 60 minutes maximum
                'type': int,
                'description': 'Trade cycle interval must be between 1 and 60 minutes'
            },
            'cooldown_buy_after_sell_minutes': {
                'min': 0,      # No cooldown minimum
                'max': 120,    # 2 hours maximum
                'type': int,
                'description': 'Buy cooldown must be between 0 and 120 minutes'
            },
            'cooldown_sell_after_buy_minutes': {
                'min': 0,      # No cooldown minimum
                'max': 60,     # 1 hour maximum
                'type': int,
                'description': 'Sell cooldown must be between 0 and 60 minutes'
            },

            # === LEGACY PARAMETERS ===
            'stop_loss_percent': {
                'min': 0.001,  # 0.1% minimum
                'max': 0.20,   # 20% maximum
                'type': float,
                'description': 'Stop loss percentage must be between 0.1% and 20%'
            },
            'take_profit_percent': {
                'min': 0.005,  # 0.5% minimum
                'max': 0.50,   # 50% maximum
                'type': float,
                'description': 'Take profit percentage must be between 0.5% and 50%'
            },
            'position_size_percent': {
                'min': 0.01,   # 1% minimum
                'max': 1.0,    # 100% maximum
                'type': float,
                'description': 'Position size must be between 1% and 100% of capital'
            },
            'daily_loss_limit': {
                'min': 0.01,   # 1% minimum
                'max': 0.50,   # 50% maximum
                'type': float,
                'description': 'Daily loss limit must be between 1% and 50%'
            },
            
            # Technical Indicator Rules
            'rsi_period': {
                'min': 5,      # 5 periods minimum
                'max': 50,     # 50 periods maximum
                'type': int,
                'description': 'RSI period must be between 5 and 50'
            },
            'rsi_buy_threshold': {
                'min': 10.0,   # 10 minimum
                'max': 50.0,   # 50 maximum
                'type': float,
                'description': 'RSI buy threshold must be between 10 and 50'
            },
            'rsi_sell_threshold': {
                'min': 50.0,   # 50 minimum
                'max': 90.0,   # 90 maximum
                'type': float,
                'description': 'RSI sell threshold must be between 50 and 90'
            },
            'macd_fast': {
                'min': 5,      # 5 periods minimum
                'max': 20,     # 20 periods maximum
                'type': int,
                'description': 'MACD fast period must be between 5 and 20'
            },
            'macd_slow': {
                'min': 15,     # 15 periods minimum
                'max': 50,     # 50 periods maximum
                'type': int,
                'description': 'MACD slow period must be between 15 and 50'
            },
            'macd_signal': {
                'min': 5,      # 5 periods minimum
                'max': 15,     # 15 periods maximum
                'type': int,
                'description': 'MACD signal period must be between 5 and 15'
            },
            
            # Timing Rules
            'max_trades_per_day': {
                'min': 1,      # 1 trade minimum
                'max': 100,    # 100 trades maximum
                'type': int,
                'description': 'Max trades per day must be between 1 and 100'
            },
            'min_time_between_trades': {
                'min': 1,      # 1 minute minimum
                'max': 1440,   # 24 hours maximum
                'type': int,
                'description': 'Min time between trades must be between 1 and 1440 minutes'
            },
            
            # AI Parameters
            'ai_confidence_threshold': {
                'min': 0.1,    # 10% minimum
                'max': 1.0,    # 100% maximum
                'type': float,
                'description': 'AI confidence threshold must be between 0.1 and 1.0'
            }
        }
    
    def validate_parameter(self, param_name: str, value: Any) -> Tuple[bool, str]:
        """
        Validate a single parameter.
        
        Args:
            param_name: Name of the parameter
            value: Value to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if param_name not in self.validation_rules:
            return True, ""  # No rule defined, assume valid
        
        rule = self.validation_rules[param_name]
        
        try:
            # Type validation
            if rule['type'] == int:
                value = int(value)
            elif rule['type'] == float:
                value = float(value)
            
            # Range validation
            if 'min' in rule and value < rule['min']:
                return False, f"{param_name} value {value} is below minimum {rule['min']}. {rule['description']}"
            
            if 'max' in rule and value > rule['max']:
                return False, f"{param_name} value {value} is above maximum {rule['max']}. {rule['description']}"
            
            return True, ""
            
        except (ValueError, TypeError) as e:
            return False, f"{param_name} has invalid type. Expected {rule['type'].__name__}, got {type(value).__name__}"
    
    def validate_parameters(self, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a dictionary of parameters.
        
        Args:
            params: Dictionary of parameter names and values
            
        Returns:
            Tuple of (all_valid, list_of_errors)
        """
        errors = []
        
        # Validate individual parameters
        for param_name, value in params.items():
            is_valid, error = self.validate_parameter(param_name, value)
            if not is_valid:
                errors.append(error)
        
        # Validate parameter relationships
        relationship_errors = self._validate_parameter_relationships(params)
        errors.extend(relationship_errors)
        
        return len(errors) == 0, errors
    
    def _validate_parameter_relationships(self, params: Dict[str, Any]) -> List[str]:
        """
        Validate relationships between parameters for Phase 1 and Phase 2.
        """
        errors = []

        # === PHASE 2 PARAMETER RELATIONSHIPS ===

        # RSI thresholds: oversold should be less than overbought
        if 'rsi_oversold' in params and 'rsi_overbought' in params:
            oversold = float(params['rsi_oversold'])
            overbought = float(params['rsi_overbought'])

            if oversold >= overbought:
                errors.append(f"RSI oversold ({oversold}) must be less than overbought ({overbought})")

            # Ensure reasonable gap between thresholds
            if overbought - oversold < 20:
                if self.warnings_enabled:
                    warnings.warn(f"RSI thresholds are very close ({oversold:.1f} vs {overbought:.1f}). Consider wider gap.")

        # SMA periods: short should be less than long
        if 'short_sma_period' in params and 'long_sma_period' in params:
            short_sma = int(params['short_sma_period'])
            long_sma = int(params['long_sma_period'])

            if short_sma >= long_sma:
                errors.append(f"Short SMA period ({short_sma}) must be less than long SMA period ({long_sma})")

            # Ensure reasonable gap between periods
            if long_sma - short_sma < 3:
                if self.warnings_enabled:
                    warnings.warn(f"SMA periods are very close ({short_sma} vs {long_sma}). Consider wider gap.")

        # MACD periods: fast should be less than slow
        if 'macd_fast_period' in params and 'macd_slow_period' in params:
            fast_period = int(params['macd_fast_period'])
            slow_period = int(params['macd_slow_period'])

            if fast_period >= slow_period:
                errors.append(f"MACD fast period ({fast_period}) must be less than slow period ({slow_period})")

            # Ensure reasonable gap
            if slow_period - fast_period < 5:
                if self.warnings_enabled:
                    warnings.warn(f"MACD periods are very close ({fast_period} vs {slow_period}). Consider wider gap.")

        # Quantity percentages: min should be less than max
        if 'min_qty_pct' in params and 'max_qty_pct' in params:
            min_qty = float(params['min_qty_pct'])
            max_qty = float(params['max_qty_pct'])

            if min_qty >= max_qty:
                errors.append(f"Min quantity percentage ({min_qty}) must be less than max quantity percentage ({max_qty})")

        # Trade value vs cash reserve relationship
        if 'max_trade_value_usd' in params and 'cash_reserve_usd' in params:
            max_trade = float(params['max_trade_value_usd'])
            cash_reserve = float(params['cash_reserve_usd'])

            if max_trade > cash_reserve * 0.8:  # Trade shouldn't be more than 80% of reserve
                if self.warnings_enabled:
                    warnings.warn(f"Max trade value (${max_trade:.2f}) is very high compared to cash reserve (${cash_reserve:.2f})")

        # Profit target relationships
        profit_targets = ['dip_buy_profit_target_pct', 'momentum_buy_profit_target_pct', 'granular_take_profit_pct']
        profit_values = []
        for target in profit_targets:
            if target in params:
                profit_values.append((target, float(params[target])))

        # Check that profit targets are reasonable relative to each other
        if len(profit_values) >= 2:
            profit_values.sort(key=lambda x: x[1])  # Sort by value
            min_target, min_val = profit_values[0]
            max_target, max_val = profit_values[-1]

            if max_val > min_val * 10:  # Max shouldn't be more than 10x min
                if self.warnings_enabled:
                    warnings.warn(f"Large gap between profit targets: {min_target}={min_val:.4f} vs {max_target}={max_val:.4f}")

        # Stop loss vs profit targets
        if 'stop_percent' in params:
            stop_loss = float(params['stop_percent'])
            for target_name in profit_targets:
                if target_name in params:
                    profit_target = float(params[target_name])
                    if stop_loss > profit_target:
                        if self.warnings_enabled:
                            warnings.warn(f"Stop loss ({stop_loss:.4f}) is higher than {target_name} ({profit_target:.4f})")

        # === LEGACY PARAMETER RELATIONSHIPS ===

        # RSI thresholds: buy should be less than sell (legacy)
        if 'rsi_buy_threshold' in params and 'rsi_sell_threshold' in params:
            buy_threshold = float(params['rsi_buy_threshold'])
            sell_threshold = float(params['rsi_sell_threshold'])

            if buy_threshold >= sell_threshold:
                errors.append(f"RSI buy threshold ({buy_threshold}) must be less than sell threshold ({sell_threshold})")

            # Ensure reasonable gap between thresholds
            if sell_threshold - buy_threshold < 10:
                if self.warnings_enabled:
                    warnings.warn(f"RSI thresholds are very close ({buy_threshold:.1f} vs {sell_threshold:.1f}). Consider wider gap.")

        # MACD periods: fast should be less than slow (legacy)
        if 'macd_fast' in params and 'macd_slow' in params:
            fast_period = int(params['macd_fast'])
            slow_period = int(params['macd_slow'])
            
            if fast_period >= slow_period:
                errors.append(f"MACD fast period ({fast_period}) must be less than slow period ({slow_period})")
        
        # Stop loss vs Take profit relationship
        if 'stop_loss_percent' in params and 'take_profit_percent' in params:
            stop_loss = float(params['stop_loss_percent'])
            take_profit = float(params['take_profit_percent'])
            
            # Risk-reward ratio should be reasonable
            risk_reward_ratio = take_profit / stop_loss
            if risk_reward_ratio < 0.5:
                if self.warnings_enabled:
                    warnings.warn(f"Risk-reward ratio is very low ({risk_reward_ratio:.2f}). Consider higher take profit or lower stop loss.")
            elif risk_reward_ratio > 10:
                if self.warnings_enabled:
                    warnings.warn(f"Risk-reward ratio is very high ({risk_reward_ratio:.2f}). This might be unrealistic.")
        
        # Position size vs daily loss limit
        if 'position_size_percent' in params and 'daily_loss_limit' in params:
            position_size = float(params['position_size_percent'])
            daily_limit = float(params['daily_loss_limit'])
            
            # Position size shouldn't be larger than daily loss limit
            if position_size > daily_limit:
                errors.append(f"Position size ({position_size:.1%}) should not exceed daily loss limit ({daily_limit:.1%})")
        
        # Trailing profit buffer vs stop loss
        if 'trail_profit_buffer_pct' in params and 'stop_loss_percent' in params:
            trail_buffer = float(params['trail_profit_buffer_pct'])
            stop_loss = float(params['stop_loss_percent'])
            
            # Trail buffer should be smaller than stop loss
            if trail_buffer >= stop_loss:
                errors.append(f"Trailing profit buffer ({trail_buffer:.3%}) should be smaller than stop loss ({stop_loss:.3%})")
        
        return errors
    
    def suggest_parameter_fixes(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Suggest fixes for invalid parameters.
        
        Args:
            params: Dictionary of parameters to fix
            
        Returns:
            Dictionary of suggested parameter values
        """
        fixed_params = params.copy()
        
        # Fix individual parameter ranges
        for param_name, value in params.items():
            if param_name in self.validation_rules:
                rule = self.validation_rules[param_name]
                
                try:
                    # Convert to correct type
                    if rule['type'] == int:
                        value = int(value)
                    elif rule['type'] == float:
                        value = float(value)
                    
                    # Clamp to valid range
                    if 'min' in rule and value < rule['min']:
                        fixed_params[param_name] = rule['min']
                    elif 'max' in rule and value > rule['max']:
                        fixed_params[param_name] = rule['max']
                    else:
                        fixed_params[param_name] = value
                        
                except (ValueError, TypeError):
                    # Use default value (middle of range)
                    if 'min' in rule and 'max' in rule:
                        fixed_params[param_name] = (rule['min'] + rule['max']) / 2
        
        # Fix parameter relationships
        if 'rsi_buy_threshold' in fixed_params and 'rsi_sell_threshold' in fixed_params:
            buy_threshold = float(fixed_params['rsi_buy_threshold'])
            sell_threshold = float(fixed_params['rsi_sell_threshold'])
            
            if buy_threshold >= sell_threshold:
                # Adjust to maintain 20-point gap
                fixed_params['rsi_buy_threshold'] = 30.0
                fixed_params['rsi_sell_threshold'] = 70.0
        
        if 'macd_fast' in fixed_params and 'macd_slow' in fixed_params:
            fast_period = int(fixed_params['macd_fast'])
            slow_period = int(fixed_params['macd_slow'])
            
            if fast_period >= slow_period:
                fixed_params['macd_fast'] = 12
                fixed_params['macd_slow'] = 26
        
        return fixed_params
    
    def add_custom_rule(self, param_name: str, rule: Dict[str, Any]) -> None:
        """
        Add a custom validation rule.
        
        Args:
            param_name: Name of the parameter
            rule: Validation rule dictionary
        """
        self.validation_rules[param_name] = rule
    
    def print_validation_summary(self, params: Dict[str, Any]) -> None:
        """
        Print a summary of parameter validation.
        """
        is_valid, errors = self.validate_parameters(params)
        
        print(f"\n📋 Parameter Validation Summary")
        print("="*50)
        
        if is_valid:
            print(f"✅ All {len(params)} parameters are valid!")
        else:
            print(f"❌ Found {len(errors)} validation errors:")
            for i, error in enumerate(errors, 1):
                print(f"   {i}. {error}")
        
        # Show parameter values
        print(f"\n📊 Parameter Values:")
        for param_name, value in params.items():
            status = "✅" if param_name not in [e.split()[0] for e in errors] else "❌"
            print(f"   {status} {param_name}: {value}")
        
        if not is_valid:
            print(f"\n💡 Suggested fixes:")
            fixed_params = self.suggest_parameter_fixes(params)
            for param_name, value in fixed_params.items():
                if param_name in params and params[param_name] != value:
                    print(f"   🔧 {param_name}: {params[param_name]} → {value}")


def create_safe_parameter_space() -> Dict[str, Dict[str, Any]]:
    """
    Create a parameter space with built-in validation constraints.
    """
    validator = ParameterValidator()
    
    safe_space = {}
    for param_name, rule in validator.validation_rules.items():
        if 'min' in rule and 'max' in rule:
            safe_space[param_name] = {
                'type': 'real' if rule['type'] == float else 'integer',
                'low': rule['min'],
                'high': rule['max'],
                'description': rule['description']
            }
    
    return safe_space


if __name__ == "__main__":
    # Test the validator
    validator = ParameterValidator()
    
    # Test valid parameters
    valid_params = {
        'stop_loss_percent': 0.02,
        'rsi_buy_threshold': 30.0,
        'rsi_sell_threshold': 70.0,
        'position_size_percent': 0.15
    }
    
    print("Testing valid parameters:")
    validator.print_validation_summary(valid_params)
    
    # Test invalid parameters
    invalid_params = {
        'stop_loss_percent': 0.5,  # Too high
        'rsi_buy_threshold': 80.0,  # Higher than sell threshold
        'rsi_sell_threshold': 70.0,
        'position_size_percent': 1.5  # Over 100%
    }
    
    print("\n" + "="*60)
    print("Testing invalid parameters:")
    validator.print_validation_summary(invalid_params)
