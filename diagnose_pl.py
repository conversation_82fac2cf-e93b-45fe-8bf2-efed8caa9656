import os
import pandas as pd
import logging
from decimal import Decimal, InvalidOperation

# --- CONFIGURATION ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- FILE PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TRADE_HISTORY_FILE = os.path.join(BASE_DIR, "trade_history.csv")

def analyze_realized_pl():
    """
    Analyzes the trade_history.csv file to diagnose the Realized P/L calculation.
    This is a read-only diagnostic tool.
    """
    if not os.path.exists(TRADE_HISTORY_FILE):
        logging.error(f"Trade history file not found at: {TRADE_HISTORY_FILE}")
        return

    logging.info(f"Analyzing trade history from: {TRADE_HISTORY_FILE}")

    try:
        df = pd.read_csv(TRADE_HISTORY_FILE)

        # --- Data Validation and Cleaning ---
        if 'action' not in df.columns or 'realized_pl' not in df.columns:
            logging.error("The trade history CSV is missing required columns: 'action' or 'realized_pl'.")
            return

        # Filter for sell trades only
        sells_df = df[df['action'].str.contains("SELL", na=False)].copy()
        
        # Convert realized_pl to a numeric type, coercing errors to NaN (Not a Number)
        sells_df['realized_pl_numeric'] = pd.to_numeric(sells_df['realized_pl'], errors='coerce')

        # Identify and report rows where conversion failed
        invalid_pl_rows = sells_df[sells_df['realized_pl_numeric'].isna()]
        if not invalid_pl_rows.empty:
            logging.warning(f"Found {len(invalid_pl_rows)} rows with non-numeric 'realized_pl' values. These will be ignored.")
            logging.warning("Problematic rows:\n" + invalid_pl_rows.to_string())

        # Drop the rows with invalid P/L for calculation
        sells_df.dropna(subset=['realized_pl_numeric'], inplace=True)

        # --- Calculation ---
        total_realized_pl = sells_df['realized_pl_numeric'].sum()
        win_count = (sells_df['realized_pl_numeric'] > 0).sum()
        loss_count = (sells_df['realized_pl_numeric'] < 0).sum()
        zero_pl_count = (sells_df['realized_pl_numeric'] == 0).sum()

        # Sort by P/L to find the largest wins and losses
        sorted_sells = sells_df.sort_values(by='realized_pl_numeric', ascending=False)

        top_5_wins = sorted_sells.head(5)
        top_5_losses = sorted_sells.tail(5)

        # --- Reporting ---
        print("\n" + "="*60)
        print("           Realized Profit & Loss Diagnostic Report")
        print("="*60)
        print(f"\nTotal Calculated Realized P/L: ${total_realized_pl:,.2f}")
        print("-" * 60)
        print(f"Total Winning Trades: {win_count}")
        print(f"Total Losing Trades:  {loss_count}")
        print(f"Total Zero P/L Trades: {zero_pl_count}")
        
        print("\n--- Top 5 Largest Winning Trades ---")
        if not top_5_wins.empty:
            print(top_5_wins[['timestamp', 'action', 'realized_pl_numeric']].to_string(index=False))
        else:
            print("No winning trades found.")
            
        print("\n--- Top 5 Largest Losing Trades ---")
        if not top_5_losses.empty:
            # Reverse the order for intuitive display (biggest loss first)
            print(top_5_losses.iloc[::-1][['timestamp', 'action', 'realized_pl_numeric']].to_string(index=False))
        else:
            print("No losing trades found.")

        print("\n" + "="*60)
        logging.info("Diagnostic analysis complete.")

    except Exception as e:
        logging.error(f"An unexpected error occurred during analysis: {e}", exc_info=True)

if __name__ == "__main__":
    analyze_realized_pl()
