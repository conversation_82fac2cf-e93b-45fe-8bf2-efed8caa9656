#!/usr/bin/env python3
"""
Bayesian optimization wrapper for intelligent parameter optimization.
Replaces basic grid search with smart, efficient parameter exploration.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Callable, Any, Optional
from decimal import Decimal
import json
import os
from datetime import datetime

# Optimization libraries
from skopt import gp_minimize
from skopt.space import Real, Integer, Categorical
from skopt.utils import use_named_args
from skopt.acquisition import gaussian_ei

# Local imports
from performance_metrics import PerformanceMetrics


class BayesianOptimizer:
    """
    Intelligent parameter optimization using Bayesian optimization.
    Finds optimal trading parameters much faster than grid search.
    """
    
    def __init__(self, objective_function: Callable, parameter_space: Dict[str, Any],
                 optimization_metric: str = 'sharpe_ratio', n_calls: int = 50,
                 results_dir: str = 'optimization/results'):
        """
        Initialize Bayesian optimizer.
        
        Args:
            objective_function: Function that takes parameters and returns performance metrics
            parameter_space: Dictionary defining parameter ranges and types
            optimization_metric: Metric to optimize (e.g., 'sharpe_ratio', 'calmar_ratio')
            n_calls: Number of optimization iterations
            results_dir: Directory to save optimization results
        """
        self.objective_function = objective_function
        self.parameter_space = parameter_space
        self.optimization_metric = optimization_metric
        self.n_calls = n_calls
        self.results_dir = results_dir
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        
        # Convert parameter space to skopt format
        self.dimensions = self._create_dimensions()
        self.dimension_names = list(parameter_space.keys())
        
        # Storage for results
        self.optimization_results = []
        self.best_params = None
        self.best_score = float('-inf')
        
    def _create_dimensions(self) -> List:
        """Convert parameter space to skopt dimensions."""
        dimensions = []
        
        for param_name, param_config in self.parameter_space.items():
            if param_config['type'] == 'real':
                dimensions.append(Real(
                    low=param_config['low'],
                    high=param_config['high'],
                    name=param_name
                ))
            elif param_config['type'] == 'integer':
                dimensions.append(Integer(
                    low=param_config['low'],
                    high=param_config['high'],
                    name=param_name
                ))
            elif param_config['type'] == 'categorical':
                dimensions.append(Categorical(
                    categories=param_config['categories'],
                    name=param_name
                ))
            else:
                raise ValueError(f"Unknown parameter type: {param_config['type']}")
        
        return dimensions
    
    def _objective_wrapper(self, params: List) -> float:
        """
        Wrapper for objective function that handles parameter conversion.
        Returns negative metric value (since skopt minimizes).
        """
        try:
            # Convert parameter list to dictionary
            param_dict = dict(zip(self.dimension_names, params))
            
            print(f"🔍 Testing parameters: {param_dict}")
            
            # Call the objective function (should return performance metrics dict)
            metrics = self.objective_function(param_dict)
            
            if not isinstance(metrics, dict):
                print(f"❌ Objective function returned invalid type: {type(metrics)}")
                return float('inf')  # Bad score for invalid results
            
            # Get the optimization metric
            if self.optimization_metric not in metrics:
                print(f"❌ Optimization metric '{self.optimization_metric}' not found in results")
                return float('inf')
            
            score = metrics[self.optimization_metric]
            
            # Store results
            result = {
                'parameters': param_dict.copy(),
                'metrics': metrics.copy(),
                'score': score,
                'timestamp': datetime.now().isoformat()
            }
            self.optimization_results.append(result)
            
            # Update best score
            if score > self.best_score:
                self.best_score = score
                self.best_params = param_dict.copy()
                print(f"🎉 New best {self.optimization_metric}: {score:.4f}")
            
            print(f"📊 {self.optimization_metric}: {score:.4f}")
            
            # Return negative score (skopt minimizes)
            return -score
            
        except Exception as e:
            print(f"❌ Error in objective function: {e}")
            import traceback
            traceback.print_exc()
            return float('inf')  # Bad score for errors
    
    def optimize(self, random_state: int = 42) -> Dict[str, Any]:
        """
        Run Bayesian optimization to find best parameters.
        
        Args:
            random_state: Random seed for reproducible results
            
        Returns:
            Dictionary with optimization results
        """
        print(f"\n🚀 Starting Bayesian Optimization")
        print(f"   Metric to optimize: {self.optimization_metric}")
        print(f"   Number of iterations: {self.n_calls}")
        print(f"   Parameter space: {len(self.parameter_space)} parameters")
        print("="*60)
        
        try:
            # Run Bayesian optimization
            result = gp_minimize(
                func=self._objective_wrapper,
                dimensions=self.dimensions,
                n_calls=self.n_calls,
                random_state=random_state,
                acq_func='EI',  # Expected Improvement
                n_initial_points=min(10, self.n_calls // 3),  # Initial random points
                verbose=False
            )
            
            print(f"\n✅ Optimization completed!")
            print(f"   Best {self.optimization_metric}: {self.best_score:.4f}")
            print(f"   Best parameters: {self.best_params}")
            
            # Save results
            self._save_results(result)
            
            return {
                'best_parameters': self.best_params,
                'best_score': self.best_score,
                'optimization_metric': self.optimization_metric,
                'total_iterations': len(self.optimization_results),
                'optimization_history': self.optimization_results,
                'skopt_result': result
            }
            
        except Exception as e:
            print(f"❌ Optimization failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'best_parameters': None,
                'best_score': float('-inf'),
                'error': str(e)
            }
    
    def _save_results(self, skopt_result) -> None:
        """Save optimization results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed results as JSON
        results_file = os.path.join(self.results_dir, f'optimization_results_{timestamp}.json')
        with open(results_file, 'w') as f:
            json.dump({
                'best_parameters': self.best_params,
                'best_score': self.best_score,
                'optimization_metric': self.optimization_metric,
                'parameter_space': self.parameter_space,
                'all_results': self.optimization_results
            }, f, indent=2, default=str)
        
        # Save CSV summary for easy analysis
        csv_file = os.path.join(self.results_dir, f'optimization_summary_{timestamp}.csv')
        
        if self.optimization_results:
            # Flatten results for CSV
            csv_data = []
            for result in self.optimization_results:
                row = result['parameters'].copy()
                row.update(result['metrics'])
                row['timestamp'] = result['timestamp']
                csv_data.append(row)
            
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False)
        
        print(f"💾 Results saved:")
        print(f"   JSON: {results_file}")
        print(f"   CSV:  {csv_file}")
    
    def get_parameter_importance(self) -> Dict[str, float]:
        """
        Analyze which parameters have the most impact on performance.
        """
        if len(self.optimization_results) < 10:
            print("⚠️  Need at least 10 optimization results for parameter importance analysis")
            return {}
        
        try:
            # Create DataFrame from results
            data = []
            for result in self.optimization_results:
                row = result['parameters'].copy()
                row['score'] = result['score']
                data.append(row)
            
            df = pd.DataFrame(data)
            
            # Calculate correlation between each parameter and the score
            importance = {}
            for param in self.dimension_names:
                if param in df.columns:
                    correlation = abs(df[param].corr(df['score']))
                    importance[param] = correlation if not pd.isna(correlation) else 0.0
            
            # Sort by importance
            importance = dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))
            
            print(f"\n📊 Parameter Importance Analysis:")
            for param, importance_score in importance.items():
                print(f"   {param}: {importance_score:.3f}")
            
            return importance
            
        except Exception as e:
            print(f"❌ Error calculating parameter importance: {e}")
            return {}
    
    def suggest_next_parameters(self, n_suggestions: int = 3) -> List[Dict[str, Any]]:
        """
        Suggest next parameter combinations to test based on optimization history.
        """
        if not self.optimization_results:
            print("⚠️  No optimization history available for suggestions")
            return []
        
        try:
            # Use the optimization model to suggest next points
            suggestions = []
            
            # Simple approach: suggest variations around best parameters
            if self.best_params:
                for i in range(n_suggestions):
                    suggestion = self.best_params.copy()
                    
                    # Add small random variations
                    for param_name, param_config in self.parameter_space.items():
                        if param_config['type'] == 'real':
                            current_val = suggestion[param_name]
                            range_size = param_config['high'] - param_config['low']
                            variation = np.random.normal(0, range_size * 0.1)  # 10% of range
                            new_val = max(param_config['low'], 
                                        min(param_config['high'], current_val + variation))
                            suggestion[param_name] = new_val
                    
                    suggestions.append(suggestion)
            
            return suggestions
            
        except Exception as e:
            print(f"❌ Error generating suggestions: {e}")
            return []


def create_example_parameter_space() -> Dict[str, Any]:
    """
    Create an example parameter space for testing.
    """
    return {
        'stop_loss_percent': {
            'type': 'real',
            'low': 0.005,
            'high': 0.05
        },
        'trail_profit_buffer_pct': {
            'type': 'real',
            'low': 0.001,
            'high': 0.02
        },
        'rsi_buy_threshold': {
            'type': 'real',
            'low': 20.0,
            'high': 40.0
        },
        'rsi_sell_threshold': {
            'type': 'real',
            'low': 60.0,
            'high': 80.0
        },
        'position_size_percent': {
            'type': 'real',
            'low': 0.05,
            'high': 0.25
        }
    }


if __name__ == "__main__":
    # Example usage
    def dummy_objective(params):
        """Dummy objective function for testing."""
        # Simulate some performance metrics
        return {
            'sharpe_ratio': np.random.normal(1.0, 0.5),
            'max_drawdown': np.random.uniform(-20, -5),
            'total_return': np.random.normal(10, 15)
        }
    
    # Test the optimizer
    param_space = create_example_parameter_space()
    optimizer = BayesianOptimizer(dummy_objective, param_space, n_calls=10)
    
    result = optimizer.optimize()
    print(f"\n🎯 Optimization Result: {result}")
