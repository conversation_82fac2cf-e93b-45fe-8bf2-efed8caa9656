import logging
from decimal import Decimal
from typing import Dict, Any

logger = logging.getLogger("adaptive_tuner")

def get_tuned_parameters(current_params: Dict[str, Any], performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
    tuned_params = {}
    # Example logic: Adjust stop loss and profit targets based on volatility
    volatility = performance_metrics.get("volatility", 0)
    win_ratio = performance_metrics.get("win_ratio", 0)
    avg_profit = performance_metrics.get("average_profit", 0)
    avg_loss = performance_metrics.get("average_loss", 0)

    # Adjust stop loss based on volatility
    if volatility > 0.05:
        tuned_params["STOP_LOSS_PCT"] = min(current_params.get("STOP_LOSS_PCT", 0.02) * 1.5, 0.05)
        tuned_params["PROFIT_TARGET_PCT"] = max(current_params.get("PROFIT_TARGET_PCT", 0.03) * 0.75, 0.015)
    elif volatility < 0.02:
        tuned_params["STOP_LOSS_PCT"] = max(current_params.get("STOP_LOSS_PCT", 0.02) * 0.75, 0.005)
        tuned_params["PROFIT_TARGET_PCT"] = min(current_params.get("PROFIT_TARGET_PCT", 0.03) * 1.25, 0.08)

    # Adjust based on win ratio
    if win_ratio < 0.4:
        # Too many losses, be more conservative
        tuned_params["STOP_LOSS_PCT"] = max(tuned_params.get("STOP_LOSS_PCT", current_params.get("STOP_LOSS_PCT", 0.02)) * 1.2, 0.01)
        tuned_params["PROFIT_TARGET_PCT"] = max(tuned_params.get("PROFIT_TARGET_PCT", 0.03) * 0.8, 0.015)
    elif win_ratio > 0.6:
        # Good win rate, can be more aggressive
        tuned_params["STOP_LOSS_PCT"] = min(tuned_params.get("STOP_LOSS_PCT", current_params.get("STOP_LOSS_PCT", 0.02)) * 0.8, 0.04)
        tuned_params["PROFIT_TARGET_PCT"] = min(tuned_params.get("PROFIT_TARGET_PCT", 0.03) * 1.2, 0.06)

    # Adjust profit target based on average profit/loss
    if avg_profit > abs(avg_loss):
        tuned_params["PROFIT_TARGET_PCT"] = min(current_params.get("PROFIT_TARGET_PCT", 0.03) * 1.1, 0.08)
    else:
        tuned_params["PROFIT_TARGET_PCT"] = max(current_params.get("PROFIT_TARGET_PCT", 0.03) * 0.9, 0.015)

    # Ensure parameters are within bounds
    tuned_params["STOP_LOSS_PCT"] = max(min(tuned_params.get("STOP_LOSS_PCT", 0.02), 0.05), 0.005)
    tuned_params["PROFIT_TARGET_PCT"] = max(min(tuned_params.get("PROFIT_TARGET_PCT", 0.03), 0.08), 0.015)

    logger.info(f"Adaptive tuning results: {tuned_params}")
    return tuned_params
