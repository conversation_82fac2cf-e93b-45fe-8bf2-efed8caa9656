#!/usr/bin/env python3
"""
Phase 3 Integration System - Market Regime-Aware Trading

Complete integration system that combines all Phase 3 components:
- Market regime detection
- Regime-specific parameter optimization
- Smooth regime transitions
- Performance monitoring and validation

This system provides the main interface for regime-aware trading optimization.

Author: Bitcoin AI Trading Bot - Phase 3 Integration
Date: July 29, 2025
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import os

# Phase 3 components
from optimization.market_regime_analyzer import MarketRegimeAnalyzer, MarketRegime, RegimeAnalysis
from optimization.regime_specific_optimizer import RegimeSpecificOptimizer, RegimeOptimizationResult
from optimization.regime_transition_manager import RegimeTransitionManager

logger = logging.getLogger("TradingBotApp.Phase3Integration")

class Phase3IntegrationSystem:
    """
    Complete Phase 3 integration system for regime-aware trading.
    
    This system:
    1. Continuously monitors market regimes
    2. Provides regime-specific optimized parameters
    3. Handles smooth transitions between regimes
    4. Tracks performance across different market conditions
    5. Provides comprehensive monitoring and reporting
    """
    
    def __init__(self, 
                 optimization_interval_hours: int = 24,
                 regime_check_interval_minutes: int = 5,
                 enable_auto_optimization: bool = True):
        """
        Initialize the Phase 3 integration system.
        
        Args:
            optimization_interval_hours: How often to re-optimize parameters
            regime_check_interval_minutes: How often to check regime changes
            enable_auto_optimization: Whether to automatically re-optimize
        """
        
        # Initialize components
        self.regime_analyzer = MarketRegimeAnalyzer()
        self.regime_optimizer = RegimeSpecificOptimizer()
        self.transition_manager = RegimeTransitionManager()
        
        # Configuration
        self.optimization_interval = timedelta(hours=optimization_interval_hours)
        self.regime_check_interval = timedelta(minutes=regime_check_interval_minutes)
        self.auto_optimization_enabled = enable_auto_optimization
        
        # State tracking
        self.last_optimization_time: Optional[datetime] = None
        self.last_regime_check: Optional[datetime] = None
        self.current_regime_analysis: Optional[RegimeAnalysis] = None
        self.optimization_results: Dict[MarketRegime, RegimeOptimizationResult] = {}
        
        # Performance tracking
        self.regime_performance_history: List[Dict[str, Any]] = []
        self.parameter_change_log: List[Dict[str, Any]] = []
        
        # System status
        self.system_active = False
        self.initialization_complete = False
        
        # Cache settings
        self.cache_file = os.path.join(os.path.dirname(__file__), 'results', 'phase3_optimization_cache.json')
        self.cache_validity_hours = 12  # Cache valid for 12 hours

        logger.info("Phase 3 Integration System initialized")
    
    def initialize_system(self, initial_market_data: Dict[str, Any]) -> bool:
        """
        Initialize the system with initial market data and optimization.
        
        Args:
            initial_market_data: Current market analysis data
            
        Returns:
            True if initialization successful
        """
        
        try:
            print("PHASE 3: Initializing Regime-Aware Trading System")
            print("="*70)

            # Step 1: Initial regime detection
            print("Step 1: Detecting initial market regime...")
            regime_analysis = self.regime_analyzer.analyze_current_regime(initial_market_data)
            self.current_regime_analysis = regime_analysis
            
            print(f"   Current regime: {regime_analysis.regime.value}")
            print(f"   Confidence: {regime_analysis.confidence:.2f}")
            
            # Step 2: Load cached optimization or run if needed
            if self.auto_optimization_enabled:
                print("Step 2: Loading optimization results...")

                # Try to load cached results first
                cached_results = self._load_cached_optimization_results()
                if cached_results and self._is_cache_valid(cached_results):
                    print("   SUCCESS: Using cached optimization results")
                    self.optimization_results = self._deserialize_optimization_results(cached_results['results'])
                    self.last_optimization_time = datetime.fromisoformat(cached_results['timestamp'])
                else:
                    print("   Running fresh regime optimization...")
                    self.optimization_results = self.regime_optimizer.run_regime_optimization(
                        n_calls_per_regime=20,  # Reduced for initialization
                        optimization_metric='sharpe_ratio'
                    )
                    self.last_optimization_time = datetime.now()
                    self._save_optimization_cache()

                print(f"   Optimized {len(self.optimization_results)} regimes")
            
            # Step 3: Initialize transition manager
            print("Step 3: Initializing transition management...")
            if regime_analysis.regime in self.optimization_results:
                initial_params = self.optimization_results[regime_analysis.regime].best_parameters

                # Ensure initial_params is a dictionary
                if not isinstance(initial_params, dict):
                    logger.error(f"initial_params is not a dict: {type(initial_params)} = {initial_params}")
                    initial_params = self._get_fallback_parameters()

                self.transition_manager.current_regime = regime_analysis.regime
                self.transition_manager.current_parameters = initial_params.copy()
                print(f"   Initial parameters set for {regime_analysis.regime.value}")
            else:
                print("   WARNING: Using default parameters (no optimization results)")
                self.transition_manager.current_parameters = self._get_fallback_parameters()
            
            # Step 4: Set emergency fallback
            self._set_emergency_fallback_parameters()
            
            # Mark system as active
            self.system_active = True
            self.initialization_complete = True
            self.last_regime_check = datetime.now()
            
            print("✅ Phase 3 system initialization complete!")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Phase 3 system: {e}")
            print(f"❌ Initialization failed: {e}")
            return False
    
    def get_current_parameters(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Get current trading parameters based on market regime.
        
        Args:
            market_data: Current market analysis data
            
        Returns:
            Current optimized parameters to use
        """
        
        if not self.system_active:
            logger.warning("Phase 3 system not active, using fallback parameters")
            return self._get_fallback_parameters()
        
        try:
            # Check if we need to update regime analysis
            if self._should_check_regime():
                self._update_regime_analysis(market_data)
            
            # Check if we need to re-optimize
            if self._should_reoptimize():
                self._run_background_optimization()
            
            # Get current parameters from transition manager
            current_params = self.transition_manager.update_regime(
                self.current_regime_analysis,
                self._get_regime_parameters(self.current_regime_analysis.regime)
            )
            
            # Log parameter changes
            self._log_parameter_changes(current_params)
            
            return current_params
            
        except Exception as e:
            logger.error(f"Error getting current parameters: {e}")
            self.transition_manager.force_emergency_fallback()
            return self.transition_manager.current_parameters
    
    def _should_check_regime(self) -> bool:
        """Check if we should update regime analysis."""
        
        if not self.last_regime_check:
            return True
        
        return datetime.now() - self.last_regime_check >= self.regime_check_interval
    
    def _should_reoptimize(self) -> bool:
        """Check if we should re-run optimization."""
        
        if not self.auto_optimization_enabled:
            return False
        
        if not self.last_optimization_time:
            return True
        
        return datetime.now() - self.last_optimization_time >= self.optimization_interval
    
    def _update_regime_analysis(self, market_data: Dict[str, Any]):
        """Update current regime analysis."""
        
        try:
            new_analysis = self.regime_analyzer.analyze_current_regime(market_data)
            
            # Log regime changes
            if (self.current_regime_analysis and 
                new_analysis.regime != self.current_regime_analysis.regime):
                logger.info(f"Regime change detected: {self.current_regime_analysis.regime.value} -> {new_analysis.regime.value}")
            
            self.current_regime_analysis = new_analysis
            self.last_regime_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error updating regime analysis: {e}")
    
    def _run_background_optimization(self):
        """Run optimization in background (simplified for demo)."""
        
        try:
            logger.info("Running background regime optimization...")
            
            # In a full implementation, this would run in a separate thread
            new_results = self.regime_optimizer.run_regime_optimization(
                n_calls_per_regime=10,  # Reduced for background operation
                optimization_metric='sharpe_ratio'
            )
            
            if new_results:
                self.optimization_results.update(new_results)
                self.last_optimization_time = datetime.now()
                logger.info(f"Background optimization completed: {len(new_results)} regimes updated")
            
        except Exception as e:
            logger.error(f"Error in background optimization: {e}")
    
    def _get_regime_parameters(self, regime: MarketRegime) -> Dict[str, float]:
        """Get optimized parameters for a specific regime."""
        
        if regime in self.optimization_results:
            return self.optimization_results[regime].best_parameters
        
        # Fallback to default parameters
        logger.warning(f"No optimized parameters for regime {regime.value}, using defaults")
        return self._get_fallback_parameters()
    
    def _get_fallback_parameters(self) -> Dict[str, float]:
        """Get fallback parameters for emergency situations."""
        
        # Conservative default parameters
        return {
            'stop_percent': 0.02,
            'trail_profit_buffer_pct': 0.005,
            'rsi_period': 14,
            'rsi_overbought': 75,
            'rsi_oversold': 25,
            'short_sma_period': 8,
            'long_sma_period': 16,
            'macd_fast_period': 12,
            'macd_slow_period': 26,
            'atr_period': 14,
            'max_trade_value_usd': 50,
            'cash_reserve_usd': 300,
            'min_qty_pct': 0.02,
            'max_qty_pct': 0.08,
            'dip_buy_profit_target_pct': 0.02,
            'momentum_buy_profit_target_pct': 0.015,
            'granular_take_profit_pct': 0.01,
            'trade_cycle_interval_minutes': 2,
            'cooldown_buy_after_sell_minutes': 3,
            'cooldown_sell_after_buy_minutes': 2
        }
    
    def _set_emergency_fallback_parameters(self):
        """Set emergency fallback parameters in transition manager."""
        
        fallback_params = self._get_fallback_parameters()
        self.transition_manager.set_emergency_fallback_parameters(fallback_params)
    
    def _log_parameter_changes(self, current_params: Dict[str, float]):
        """Log parameter changes for monitoring."""
        
        # Only log if parameters have changed significantly
        if not hasattr(self, '_last_logged_params'):
            self._last_logged_params = current_params.copy()
            return
        
        significant_changes = {}
        for param, value in current_params.items():
            old_value = self._last_logged_params.get(param, value)
            if abs(value - old_value) / old_value > 0.05:  # 5% change threshold
                significant_changes[param] = {'from': old_value, 'to': value}
        
        if significant_changes:
            self.parameter_change_log.append({
                'timestamp': datetime.now(),
                'regime': self.current_regime_analysis.regime.value if self.current_regime_analysis else 'unknown',
                'changes': significant_changes
            })
            self._last_logged_params = current_params.copy()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        
        status = {
            'system_active': self.system_active,
            'initialization_complete': self.initialization_complete,
            'last_optimization': self.last_optimization_time.isoformat() if self.last_optimization_time else None,
            'last_regime_check': self.last_regime_check.isoformat() if self.last_regime_check else None,
            'optimized_regimes': len(self.optimization_results),
            'auto_optimization_enabled': self.auto_optimization_enabled
        }
        
        # Current regime info
        if self.current_regime_analysis:
            status['current_regime'] = {
                'regime': self.current_regime_analysis.regime.value,
                'confidence': self.current_regime_analysis.confidence,
                'duration_bars': self.current_regime_analysis.duration_bars
            }
        
        # Transition status
        status['transition_status'] = self.transition_manager.get_transition_status()
        
        # Recent parameter changes
        status['recent_parameter_changes'] = len([
            log for log in self.parameter_change_log 
            if datetime.now() - log['timestamp'] < timedelta(hours=1)
        ])
        
        return status
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary across regimes."""
        
        if not self.optimization_results:
            return {"status": "No optimization results available"}
        
        regime_scores = {
            regime.value: result.best_score 
            for regime, result in self.optimization_results.items()
        }
        
        return {
            'regime_performance': regime_scores,
            'best_regime': max(regime_scores.keys(), key=lambda k: regime_scores[k]),
            'worst_regime': min(regime_scores.keys(), key=lambda k: regime_scores[k]),
            'average_score': sum(regime_scores.values()) / len(regime_scores),
            'total_regimes': len(regime_scores),
            'optimization_date': self.last_optimization_time.isoformat() if self.last_optimization_time else None
        }
    
    def force_regime_reoptimization(self, regime: Optional[MarketRegime] = None) -> bool:
        """Force re-optimization of specific regime or all regimes."""
        
        try:
            if regime:
                logger.info(f"Forcing re-optimization of regime: {regime.value}")
                # In a full implementation, would optimize just this regime
                return True
            else:
                logger.info("Forcing re-optimization of all regimes")
                self.optimization_results = self.regime_optimizer.run_regime_optimization()
                self.last_optimization_time = datetime.now()
                return True
                
        except Exception as e:
            logger.error(f"Error in forced re-optimization: {e}")
            return False
    
    def shutdown_system(self):
        """Safely shutdown the Phase 3 system."""
        
        logger.info("Shutting down Phase 3 Integration System")
        
        # Save current state
        self._save_system_state()
        
        # Mark as inactive
        self.system_active = False
        
        print("✅ Phase 3 system shutdown complete")
    
    def _save_system_state(self):
        """Save current system state for recovery."""
        
        try:
            state = {
                'last_optimization_time': self.last_optimization_time.isoformat() if self.last_optimization_time else None,
                'current_regime': self.current_regime_analysis.regime.value if self.current_regime_analysis else None,
                'current_parameters': self.transition_manager.current_parameters,
                'optimization_results_count': len(self.optimization_results),
                'parameter_changes_count': len(self.parameter_change_log)
            }
            
            # Save to file
            results_dir = os.path.join(os.path.dirname(__file__), 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'phase3_system_state_{timestamp}.json'
            filepath = os.path.join(results_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(state, f, indent=2)
            
            logger.info(f"System state saved to: {filename}")
            
        except Exception as e:
            logger.error(f"Error saving system state: {e}")

    def _load_cached_optimization_results(self) -> Optional[Dict[str, Any]]:
        """Load cached optimization results if available."""

        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                return cache_data
        except Exception as e:
            logger.warning(f"Failed to load optimization cache: {e}")

        return None

    def _is_cache_valid(self, cache_data: Dict[str, Any]) -> bool:
        """Check if cached optimization results are still valid."""

        try:
            cache_time = datetime.fromisoformat(cache_data['timestamp'])
            age_hours = (datetime.now() - cache_time).total_seconds() / 3600

            is_valid = age_hours < self.cache_validity_hours
            if not is_valid:
                logger.info(f"Cache expired: {age_hours:.1f} hours old (max: {self.cache_validity_hours})")

            return is_valid

        except Exception as e:
            logger.warning(f"Error checking cache validity: {e}")
            return False

    def _save_optimization_cache(self):
        """Save current optimization results to cache."""

        try:
            # Ensure results directory exists
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)

            # Convert optimization results to serializable format
            serializable_results = {}
            for regime, result in self.optimization_results.items():
                serializable_results[regime.value] = {
                    'best_parameters': result.best_parameters,
                    'best_score': result.best_score,
                    'optimization_time': result.optimization_time
                }

            cache_data = {
                'timestamp': self.last_optimization_time.isoformat(),
                'results': serializable_results,
                'cache_version': '1.0'
            }

            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.info(f"Optimization results cached to: {self.cache_file}")

        except Exception as e:
            logger.warning(f"Failed to save optimization cache: {e}")

    def _deserialize_optimization_results(self, serialized_results: Dict[str, Any]) -> Dict[Any, Any]:
        """Convert serialized optimization results back to proper format."""

        from optimization.regime_specific_optimizer import RegimeOptimizationResult
        from optimization.market_regime_analyzer import MarketRegime

        results = {}
        for regime_str, result_data in serialized_results.items():
            try:
                # Convert string back to enum
                regime = MarketRegime(regime_str)

                # Create RegimeOptimizationResult object
                opt_result = RegimeOptimizationResult(
                    regime=regime,
                    best_parameters=result_data['best_parameters'],
                    best_score=result_data['best_score'],
                    optimization_history=[],  # Default empty history
                    regime_confidence=0.8,  # Default value
                    sample_size=100,  # Default value
                    optimization_time=result_data.get('optimization_time', 0.0)
                )

                results[regime] = opt_result

            except Exception as e:
                logger.warning(f"Failed to deserialize result for regime {regime_str}: {e}")

        return results

# Convenience function for easy integration
def create_phase3_system(market_data: Dict[str, Any]) -> Phase3IntegrationSystem:
    """
    Create and initialize a Phase 3 system with current market data.
    
    Args:
        market_data: Current market analysis data
        
    Returns:
        Initialized Phase 3 integration system
    """
    
    system = Phase3IntegrationSystem()
    
    if system.initialize_system(market_data):
        return system
    else:
        raise RuntimeError("Failed to initialize Phase 3 system")
