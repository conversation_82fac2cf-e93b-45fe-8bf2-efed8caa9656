"""
Access Control System for Bitcoin AI Trading Bot
Provides authentication and authorization for bot operations
"""

import os
import sys
import hashlib
import getpass
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class AccessController:
    """
    Simple access control system for bot operations
    """
    
    def __init__(self):
        """Initialize access controller"""
        self._authenticated = False
        self._auth_timestamp = None
        self._session_timeout = timedelta(hours=8)  # 8-hour session
        self._max_attempts = 3
        self._failed_attempts = 0
        
        # Default credentials (should be changed in production)
        self._default_username = "trader"
        self._default_password_hash = self._hash_password("SecureTrading2025!")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _get_stored_credentials(self) -> tuple:
        """Get stored credentials from environment or defaults"""
        username = os.getenv("TRADING_BOT_USERNAME", self._default_username)
        password_hash = os.getenv("TRADING_BOT_PASSWORD_HASH", self._default_password_hash)
        return username, password_hash
    
    def _is_session_valid(self) -> bool:
        """Check if current session is still valid"""
        if not self._authenticated or not self._auth_timestamp:
            return False
        
        elapsed = datetime.now() - self._auth_timestamp
        return elapsed < self._session_timeout
    
    def authenticate(self, username: str = None, password: str = None) -> bool:
        """
        Authenticate user for bot access
        
        Args:
            username: Username (will prompt if not provided)
            password: Password (will prompt if not provided)
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            # Check if already authenticated with valid session
            if self._is_session_valid():
                logger.info("✅ Already authenticated with valid session")
                return True
            
            # Check for too many failed attempts
            if self._failed_attempts >= self._max_attempts:
                logger.error("❌ Too many failed authentication attempts. Please restart the application.")
                return False
            
            # Get credentials
            stored_username, stored_password_hash = self._get_stored_credentials()
            
            # Prompt for credentials if not provided
            if not username:
                username = input("🔐 Username: ").strip()
            
            if not password:
                password = getpass.getpass("🔐 Password: ")
            
            # Validate credentials
            if username != stored_username:
                self._failed_attempts += 1
                logger.error(f"❌ Invalid username. Attempts remaining: {self._max_attempts - self._failed_attempts}")
                return False
            
            password_hash = self._hash_password(password)
            if password_hash != stored_password_hash:
                self._failed_attempts += 1
                logger.error(f"❌ Invalid password. Attempts remaining: {self._max_attempts - self._failed_attempts}")
                return False
            
            # Authentication successful
            self._authenticated = True
            self._auth_timestamp = datetime.now()
            self._failed_attempts = 0
            
            logger.info("✅ Authentication successful")
            logger.info(f"🕒 Session valid until: {self._auth_timestamp + self._session_timeout}")
            return True
            
        except KeyboardInterrupt:
            logger.info("Authentication cancelled by user")
            return False
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def is_authorized(self, operation: str = "trading") -> bool:
        """
        Check if user is authorized for specific operation
        
        Args:
            operation: Operation to check authorization for
        
        Returns:
            True if authorized, False otherwise
        """
        if not self._is_session_valid():
            logger.warning(f"❌ Not authorized for {operation}: Session expired or not authenticated")
            return False
        
        logger.debug(f"✅ Authorized for {operation}")
        return True
    
    def logout(self):
        """Logout and invalidate session"""
        self._authenticated = False
        self._auth_timestamp = None
        logger.info("🔓 Logged out successfully")
    
    def require_auth(self, operation: str = "trading") -> bool:
        """
        Require authentication for operation (authenticate if needed)
        
        Args:
            operation: Operation requiring authentication
        
        Returns:
            True if authenticated and authorized, False otherwise
        """
        if self.is_authorized(operation):
            return True
        
        print(f"\n🔐 Authentication required for {operation}")
        return self.authenticate()
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information"""
        if not self._authenticated:
            return {"authenticated": False}
        
        remaining_time = None
        if self._auth_timestamp:
            expires_at = self._auth_timestamp + self._session_timeout
            remaining_time = expires_at - datetime.now()
        
        return {
            "authenticated": self._authenticated,
            "auth_timestamp": self._auth_timestamp,
            "session_expires": self._auth_timestamp + self._session_timeout if self._auth_timestamp else None,
            "remaining_time": remaining_time,
            "valid": self._is_session_valid()
        }

# Global access controller instance
_access_controller = None

def get_access_controller() -> AccessController:
    """Get the global access controller instance"""
    global _access_controller
    if _access_controller is None:
        _access_controller = AccessController()
    return _access_controller

def require_authentication(operation: str = "trading") -> bool:
    """
    Convenience function to require authentication
    
    Args:
        operation: Operation requiring authentication
    
    Returns:
        True if authenticated, False otherwise
    """
    return get_access_controller().require_auth(operation)

def is_authenticated() -> bool:
    """Check if user is currently authenticated"""
    return get_access_controller().is_authorized()

def logout():
    """Logout current user"""
    get_access_controller().logout()

def emergency_shutdown():
    """Emergency shutdown with authentication check"""
    if not require_authentication("emergency_shutdown"):
        logger.error("❌ Emergency shutdown denied: Authentication failed")
        return False
    
    logger.warning("🚨 EMERGENCY SHUTDOWN INITIATED")
    return True
