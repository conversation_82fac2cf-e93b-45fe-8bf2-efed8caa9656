import csv
import os
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, List
import logging
import pandas as pd
import joblib # NEW: Import joblib to load the ML model

logger = logging.getLogger("performance_analyzer")

def analyze_recent_performance(trade_history_path: str = "trade_history.csv", trade_count: int = 20) -> Dict[str, Any]:
    # This is your original function, left untouched.
    if not os.path.exists(trade_history_path):
        logger.warning(f"Trade history file not found: {trade_history_path}")
        return {}

    try:
        with open(trade_history_path, "r") as f:
            reader = csv.DictReader(f)
            trades = list(reader)
    except Exception as e:
        logger.error(f"Error reading trade history: {e}")
        return {}

    recent_trades = trades[-trade_count:] if len(trades) >= trade_count else trades

    total_profit = Decimal("0")
    total_loss = Decimal("0")
    wins = 0
    losses = 0
    profits = []
    losses_list = []

    for trade in recent_trades:
        profit_str = trade.get("realized_pl", "0")
        try:
            profit = Decimal(profit_str)
        except:
            profit = Decimal("0")
        profits.append(profit)
        if profit > 0:
            wins += 1
            total_profit += profit
        elif profit < 0:
            losses += 1
            total_loss += profit

    total_trades = len(recent_trades)
    win_ratio = wins / total_trades if total_trades > 0 else 0
    avg_profit = sum([p for p in profits if p > 0], Decimal("0")) / wins if wins > 0 else Decimal("0")
    avg_loss = sum([p for p in profits if p < 0], Decimal("0")) / losses if losses > 0 else Decimal("0")
    volatility = Decimal("0")
    if len(profits) > 1:
        mean = sum(profits) / len(profits)
        variance = sum((p - mean) ** 2 for p in profits) / (len(profits) - 1)
        volatility = variance.sqrt()

    return {
        "win_ratio": float(win_ratio),
        "average_profit": float(avg_profit),
        "average_loss": float(avg_loss),
        "volatility": float(volatility),
        "total_profit": float(total_profit),
        "total_loss": float(total_loss),
        "total_trades": total_trades
    }

# --- EXISTING, POWERFUL FUNCTION FOR DEEP ANALYSIS ---

def analyze_performance_from_files(trade_history_path: str, decision_log_path: str) -> Dict[str, Any]:
    """
    Performs a deep analysis by merging trade history with market decision logs
    to find correlations between indicator values and trade outcomes.
    """
    logger.info("Starting deep performance analysis...")
    try:
        logger.info(f"Loading trade history from '{trade_history_path}'...")
        trades_df = pd.read_csv(trade_history_path, parse_dates=['timestamp'])
        
        logger.info(f"Loading decision log from '{decision_log_path}'...")
        decisions_df = pd.read_csv(decision_log_path, parse_dates=['timestamp'])
        
        trades_df['realized_pl'] = pd.to_numeric(trades_df['realized_pl'], errors='coerce').fillna(0)

        logger.info("Merging trade history with decision log...")
        merged_df = pd.merge_asof(
            left=trades_df.sort_values('timestamp'),
            right=decisions_df.sort_values('timestamp'),
            on='timestamp',
            direction='nearest',
            tolerance=pd.Timedelta('5 minutes')
        )
        
        merged_df.dropna(subset=['rsi', 'macd_hist'], inplace=True)
        logger.info(f"Successfully merged {len(merged_df)} trades with market data.")

        if merged_df.empty:
            logger.warning("Merged DataFrame is empty. No trades could be matched to market conditions.")
            return {}

        wins_df = merged_df[merged_df['realized_pl'] > 0]
        losses_df = merged_df[merged_df['realized_pl'] <= 0]

        win_count = len(wins_df)
        loss_count = len(losses_df)
        total_trades = win_count + loss_count
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0

        indicator_cols = ['rsi', 'macd_hist', 'rsi_slope', 'volume_spike', 'atr']
        analysis = {}

        for col in indicator_cols:
            if col in merged_df.columns:
                analysis[col] = {
                    'win_avg': wins_df[col].mean(),
                    'loss_avg': losses_df[col].mean(),
                    'overall_avg': merged_df[col].mean()
                }

        results = {
            'total_trades': total_trades,
            'win_count': win_count,
            'loss_count': loss_count,
            'win_rate_pct': win_rate,
            'avg_win_pl': wins_df['realized_pl'].mean(),
            'avg_loss_pl': losses_df['realized_pl'].mean(),
            'indicator_analysis': analysis
        }

        logger.info("Deep performance analysis complete.")
        return results

    except Exception as e:
        logger.error(f"An error occurred during deep performance analysis: {e}", exc_info=True)
        return {}

# --- NEW FUNCTION TO SIMULATE AND BENCHMARK THE ML MODEL ---

def simulate_ml_model_performance(decision_log_path: str, model_path: str = "trade_action_model.joblib") -> Dict[str, Any]:
    """
    Loads a trained ML model and simulates its performance against historical data.

    Args:
        decision_log_path (str): Path to the historical market data log.
        model_path (str): Path to the trained .joblib model file.

    Returns:
        A dictionary summarizing the hypothetical performance of the ML model.
    """
    logger.info(f"Starting ML model performance simulation using '{model_path}'...")
    if not os.path.exists(model_path):
        logger.error(f"ML model file not found at '{model_path}'. Cannot run simulation.")
        return {}
        
    if not os.path.exists(decision_log_path):
        logger.error(f"Decision log file not found at '{decision_log_path}'. Cannot run simulation.")
        return {}

    try:
        # Load the trained model and the historical data
        model = joblib.load(model_path)
        data_df = pd.read_csv(decision_log_path)
        
        # Define the features the model was trained on
        features = ['rsi', 'macd', 'macd_hist', 'macd_signal', 'atr', 'rsi_slope', 'volume_spike']
        # Ensure all required feature columns exist
        if not all(feature in data_df.columns for feature in features):
            logger.error("Decision log is missing one or more required feature columns for the ML model.")
            return {}

        # Prepare the feature set for prediction
        X_test = data_df[features].copy()
        X_test.fillna(0, inplace=True) # Handle any potential missing values

        # Generate predictions for the entire dataset
        logger.info(f"Generating predictions for {len(X_test)} data points...")
        predictions = model.predict(X_test)
        data_df['ml_prediction'] = predictions
        
        # Simulate the financial outcome of these predictions
        # This is a simplified simulation assuming a fixed profit/loss per trade
        # A more complex simulation could use subsequent price action, but this is a robust benchmark
        profit_per_win = 0.01 # Assume a 1% profit for a correct BUY signal
        loss_per_loss = -0.005 # Assume a 0.5% loss for an incorrect BUY signal
        
        # We are only interested in when the model would have BOUGHT
        buy_signals = data_df[data_df['ml_prediction'] == 'BUY']
        
        if buy_signals.empty:
            logger.warning("ML model did not generate any 'BUY' signals in the simulation.")
            return {"total_trades": 0, "win_rate_pct": 0, "simulated_net_pl_pct": 0}

        # Simulate P/L by checking if the price increased after the buy signal
        # We check the 'close' price 10 bars later as a simple proxy for a profitable exit
        data_df['price_change_future'] = data_df['close'].shift(-10) - data_df['close']
        
        sim_results = data_df.loc[buy_signals.index]
        
        wins = sim_results[sim_results['price_change_future'] > 0]
        losses = sim_results[sim_results['price_change_future'] <= 0]
        
        win_count = len(wins)
        loss_count = len(losses)
        total_trades = win_count + loss_count
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0
        
        total_pl = (win_count * profit_per_win) + (loss_count * loss_per_loss)
        
        return {
            "total_trades": total_trades,
            "win_rate_pct": win_rate,
            "simulated_net_pl_pct": total_pl
        }

    except Exception as e:
        logger.error(f"An error occurred during ML model simulation: {e}", exc_info=True)
        return {}