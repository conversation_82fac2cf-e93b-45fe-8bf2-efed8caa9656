# START OF FILE prepare_ml_data.py

import pandas as pd
import numpy as np
import sys
import os 
import time 

# --- Configuration for Labeling (FIXED: No Future Looking) ---
# This defines how a BUY/SELL/HOLD label is generated based on CURRENT market conditions.
# NO FUTURE LOOKING - Labels based on technical indicators at the current timestamp.

# RSI thresholds for labeling (similar to generate_rsi_labels.py)
RSI_BUY_THRESHOLD = 30   # RSI below this = BUY signal
RSI_SELL_THRESHOLD = 70  # RSI above this = SELL signal

# Additional momentum/trend confirmation thresholds
MOMENTUM_THRESHOLD = 0.001  # Minimum momentum for trend confirmation
MACD_HIST_THRESHOLD = 0.0   # MACD histogram threshold for momentum

# --- File Save Retry Configuration ---
MAX_SAVE_RETRIES = 5
SAVE_RETRY_DELAY_SECONDS = 5


def prepare_data():
    """
    Loads raw market data, cleans it, calculates additional technical indicators (if necessary),
    applies forward-looking labeling, and prepares the final dataset for machine learning model training.
    """
    print("Loading data from rsi_labeled_data.csv ...")
    try:
        # Assuming rsi_labeled_data.csv contains all raw OHLCV and technical indicators
        df = pd.read_csv("rsi_labeled_data.csv")
    except FileNotFoundError:
        print("Error: 'rsi_labeled_data.csv' not found. Please ensure your data pipeline (e.g., generate_rsi_labels.py, or other data collection script) has been run successfully to create this file.")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading 'rsi_labeled_data.csv': {e}")
        sys.exit(1)

    print(f"Initial data shape: {df.shape}")
    print(f"Columns in input data: {list(df.columns)}")
    print("Sample data (before cleaning/labeling):")
    print(df.head())

    # Ensure 'timestamp' is proper datetime objects and handle NaNs
    df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
    df.dropna(subset=['timestamp'], inplace=True)
    df.sort_values('timestamp', inplace=True) # CRUCIAL for time-series operations

    # List of numeric columns expected to be used as features.
    # This list should include all raw OHLCV data and technical indicators.
    numeric_cols_for_processing = [
        'open', 'high', 'low', 'close', 'volume', 'num_trades',
        'quote_asset_volume', 'taker_buy_base', 'taker_buy_quote',
        'rsi', 'sma_7', 'sma_10', 'sma_14', 'sma_50', 'ema_7', 'ema_14', 'ema_50',
        'atr', 'volatility', 'momentum_10', 'macd', 'macd_signal', 'macd_hist'
    ]
    
    for col in numeric_cols_for_processing:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # Fill NaNs created by 'coerce' using forward fill, then fill remaining with 0
            df[col] = df[col].ffill().fillna(0)
        else:
            print(f"Warning: Numeric feature column '{col}' not found in input data. It will be created with zeros.")
            df[col] = 0.0 # Create the column and fill with 0 if it's missing entirely

    # Handle categorical features that will be LabelEncoded later in train_trade_model.py
    # Ensure they exist and fill NaNs if needed (though train_trade_model handles encoding NaNs as a new category)
    categorical_cols = ['trend', 'trend_5m', 'regime']
    for col in categorical_cols:
        if col not in df.columns:
            print(f"Warning: Categorical column '{col}' not found. It will be added with 'neutral' values.")
            df[col] = 'neutral' # Default if missing
        df[col] = df[col].fillna('neutral').astype(str) # Ensure string type and fill NaNs


    # --- DEBUGGING STEP: Print NaN counts before final cleaning ---
    print("\n--- NaN counts before final feature selection and dropna ---")
    print(df.isna().sum())
    print("------------------------------------------------------------\n")


    # --- FIXED: Current-State Labeling Logic (NO FUTURE LOOKING) ---
    # Ensure required columns exist for current-state labeling
    required_cols = ['close', 'rsi', 'macd_hist', 'momentum_10']
    for col in required_cols:
        if col not in df.columns:
            print(f"Warning: Required column '{col}' missing for labeling. Creating with default values.")
            if col == 'rsi':
                df[col] = 50.0  # Neutral RSI
            elif col == 'macd_hist':
                df[col] = 0.0   # Neutral MACD
            elif col == 'momentum_10':
                df[col] = 0.0   # Neutral momentum
        else:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

    # Initialize 'label' column. Default is HOLD (0).
    df['label'] = 0

    # Apply simpler, more realistic labeling based on RSI only
    # This creates a more challenging prediction task for the model
    df.loc[df['rsi'] < RSI_BUY_THRESHOLD, 'label'] = 1   # BUY when oversold
    df.loc[df['rsi'] > RSI_SELL_THRESHOLD, 'label'] = -1  # SELL when overbought

    # Add some noise to make it more realistic - not all oversold/overbought conditions should trigger
    # Randomly convert 30% of BUY/SELL signals back to HOLD to simulate market complexity
    import numpy as np
    np.random.seed(42)  # For reproducible results

    buy_indices = df[df['label'] == 1].index
    sell_indices = df[df['label'] == -1].index

    # Convert 30% of BUY signals to HOLD
    noise_buy_count = int(len(buy_indices) * 0.3)
    noise_buy_indices = np.random.choice(buy_indices, noise_buy_count, replace=False)
    df.loc[noise_buy_indices, 'label'] = 0

    # Convert 30% of SELL signals to HOLD
    noise_sell_count = int(len(sell_indices) * 0.3)
    noise_sell_indices = np.random.choice(sell_indices, noise_sell_count, replace=False)
    df.loc[noise_sell_indices, 'label'] = 0

    df['label'] = df['label'].astype(int) # Ensure label is integer type for machine learning.
    # --- END FIXED LABELING LOGIC ---


    # Select only the features needed for the ML model, and the newly created 'label'.
    # This list should precisely match the features expected by train_trade_model.py
    # and should NOT include any columns that look into the future or are bot outcomes.
    model_features_for_ml = [
        'timestamp', # Keep timestamp for chronological splitting in train_trade_model.py
        'open', 'high', 'low', 'close', 'volume', 'num_trades',
        'quote_asset_volume', 'taker_buy_base', 'taker_buy_quote',
        'rsi',
        'sma_7', 'sma_10', 'sma_14', 'sma_50', 
        'ema_7', 'ema_14', 'ema_50',
        'atr', 'volatility', 
        'momentum_10', 'macd', 'macd_signal', 'macd_hist',
        'trend', 'trend_5m', 'regime', # Categorical features
        'label' # The target variable
    ]

    # Filter `df` to only include columns from `model_features_for_ml` that are actually present.
    actual_features_to_select = [f for f in model_features_for_ml if f in df.columns]
    
    # Report if any expected features are missing
    if len(actual_features_to_select) < len(model_features_for_ml):
        missing_features = set(model_features_for_ml) - set(actual_features_to_select)
        print(f"WARNING: The following defined features/target were expected but are missing from the DataFrame: {missing_features}. They will be excluded from the ML dataset.")

    df_ml = df[actual_features_to_select].copy() # Use .copy() to avoid SettingWithCopyWarning

    # Final check for critical columns after all processing
    critical_subset_for_dropna = ['label', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
    for col in critical_subset_for_dropna:
        if col not in df_ml.columns:
            print(f"FATAL ERROR: Critical column '{col}' is missing from the final ML dataset. Cannot proceed.")
            sys.exit(1)

    # Final dropna focusing only on critical features and the label.
    df_ml.dropna(subset=critical_subset_for_dropna, inplace=True)
    if df_ml.empty:
        print("Error: ML DataFrame is empty after dropping rows with missing labels or critical OHLCV/timestamp features. Check data quality and labeling thresholds.")
        sys.exit(1)

    print(f"\nCleaned data shape ready for ML: {df_ml.shape}")
    print(f"Columns in ML data: {list(df_ml.columns)}")
    print("Sample cleaned data (for ML):")
    print(df_ml.head())

    # Save the prepared dataset with retry mechanism
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "prepared_training_data.csv") # Ensure full path for saving
    for i in range(MAX_SAVE_RETRIES):
        try:
            df_ml.to_csv(output_path, index=False)
            print(f"Smart ML dataset ready! Saved successfully to '{output_path}'.")
            break # Exit loop if save is successful
        except OSError as e:
            print(f"Warning: Could not save to '{output_path}' (attempt {i+1}/{MAX_SAVE_RETRIES}): {e}")
            if i < MAX_SAVE_RETRIES - 1:
                print(f"Retrying in {SAVE_RETRY_DELAY_SECONDS} seconds...")
                time.sleep(SAVE_RETRY_DELAY_SECONDS)
            else:
                print(f"Error: Failed to save prepared data to '{output_path}' after {MAX_SAVE_RETRIES} attempts. Please ensure the file is not locked or in use by another program.")
                sys.exit(1)
        except Exception as e:
            print(f"An unexpected error occurred while saving to '{output_path}': {e}")
            sys.exit(1)
            

if __name__ == "__main__":
    # Check for pandas and numpy dependencies
    try:
        import pandas as pd
        import numpy as np
    except ImportError:
        print("\n'pandas' and/or 'numpy' not found. Installing now...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "numpy"])
            print("Dependencies installed successfully. Please re-run the script.")
            sys.exit(0)
        except Exception as e:
            print(f"Failed to install dependencies: {e}")
            print("Please install them manually: pip install pandas numpy")
            sys.exit(1)
    
    prepare_data()