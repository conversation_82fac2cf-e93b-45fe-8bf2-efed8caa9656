"""
Live Backtesting Integration System

This system creates a continuous backtesting engine that runs parallel to live trading,
providing real-time strategy validation and automatic parameter adjustment recommendations.
It maintains a shadow portfolio that mirrors live trading decisions and compares performance
against alternative strategies and parameter sets.

Key Features:
- Parallel backtesting engine running alongside live trading
- Real-time strategy validation and performance comparison
- Automatic parameter adjustment recommendations
- Shadow portfolio tracking with multiple strategy variants
- Performance attribution and analysis
- Risk-adjusted strategy optimization
- Continuous learning and adaptation

Author: Augment Agent
Date: 2025-07-30
"""

import asyncio
import json
import logging
import threading
import time
import numpy as np
import pandas as pd
from collections import deque, defaultdict
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Callable
from concurrent.futures import ThreadPoolExecutor
import copy
import os
import csv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LiveTrade:
    """Live trading decision and execution data."""
    timestamp: datetime
    decision: str  # BUY, SELL, HOLD
    ai_reasoning: str
    market_data: Dict[str, Any]
    parameters: Dict[str, Any]
    execution_price: Optional[float] = None
    execution_quantity: Optional[float] = None
    execution_status: str = "PENDING"
    trade_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class ShadowTrade:
    """Shadow portfolio trade execution."""
    timestamp: datetime
    strategy_id: str
    decision: str
    price: float
    quantity: float
    portfolio_value: float
    parameters: Dict[str, Any]
    pnl: float = 0.0
    cumulative_pnl: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class StrategyPerformance:
    """Performance metrics for a strategy variant."""
    strategy_id: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration: float
    current_drawdown: float
    last_update: datetime
    
    def calculate_metrics(self, trades: List[ShadowTrade]) -> None:
        """Calculate performance metrics from trade history."""
        if not trades:
            return
        
        self.total_trades = len(trades)
        winning_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        
        self.winning_trades = len(winning_trades)
        self.losing_trades = len(losing_trades)
        self.total_pnl = sum(t.pnl for t in trades)
        
        self.win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        
        # Profit factor
        gross_profit = sum(t.pnl for t in winning_trades)
        gross_loss = abs(sum(t.pnl for t in losing_trades))
        self.profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        if len(trades) > 1:
            returns = [t.pnl for t in trades]
            self.sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        else:
            self.sharpe_ratio = 0
        
        # Max drawdown
        cumulative_pnl = 0
        peak = 0
        max_dd = 0
        for trade in trades:
            cumulative_pnl += trade.pnl
            if cumulative_pnl > peak:
                peak = cumulative_pnl
            drawdown = (peak - cumulative_pnl) / peak if peak > 0 else 0
            max_dd = max(max_dd, drawdown)
        
        self.max_drawdown = max_dd
        self.current_drawdown = (peak - cumulative_pnl) / peak if peak > 0 else 0
        
        # Average trade duration (simplified - assume 3 minutes per trade)
        self.avg_trade_duration = 3.0  # minutes
        
        self.last_update = datetime.now(timezone.utc)

@dataclass
class ParameterRecommendation:
    """Parameter adjustment recommendation."""
    parameter_name: str
    current_value: float
    recommended_value: float
    confidence: float
    reasoning: str
    expected_improvement: float
    risk_level: str  # LOW, MEDIUM, HIGH
    
@dataclass
class BacktestingReport:
    """Comprehensive backtesting report."""
    timestamp: datetime
    live_performance: StrategyPerformance
    best_shadow_performance: StrategyPerformance
    performance_gap: float
    recommendations: List[ParameterRecommendation]
    risk_assessment: Dict[str, Any]
    market_conditions: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['live_performance'] = asdict(self.live_performance)
        result['best_shadow_performance'] = asdict(self.best_shadow_performance)
        result['recommendations'] = [asdict(r) for r in self.recommendations]
        return result

class ShadowPortfolio:
    """
    Shadow portfolio that tracks alternative trading strategies.
    
    This portfolio runs parallel strategies with different parameters
    to evaluate potential improvements to the live trading system.
    """
    
    def __init__(self, strategy_id: str, initial_capital: float, parameters: Dict[str, Any]):
        """
        Initialize shadow portfolio.
        
        Args:
            strategy_id: Unique identifier for this strategy
            initial_capital: Starting capital amount
            parameters: Trading parameters for this strategy
        """
        self.strategy_id = strategy_id
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.parameters = parameters.copy()
        
        # Portfolio state
        self.position_size = 0.0  # BTC position
        self.cash_balance = initial_capital
        self.last_price = 0.0
        
        # Trade tracking
        self.trades: List[ShadowTrade] = []
        self.open_position: Optional[Dict[str, Any]] = None
        
        # Performance metrics
        self.performance = StrategyPerformance(
            strategy_id=strategy_id,
            total_trades=0, winning_trades=0, losing_trades=0,
            total_pnl=0.0, win_rate=0.0, profit_factor=0.0,
            sharpe_ratio=0.0, max_drawdown=0.0, avg_trade_duration=0.0,
            current_drawdown=0.0, last_update=datetime.now(timezone.utc)
        )
        
        logger.info(f"Shadow portfolio {strategy_id} initialized with ${initial_capital:.2f}")
    
    def execute_decision(self, decision: str, price: float, market_data: Dict[str, Any]) -> Optional[ShadowTrade]:
        """
        Execute a trading decision in the shadow portfolio.
        
        Args:
            decision: Trading decision (BUY, SELL, HOLD)
            price: Current market price
            market_data: Market indicators and data
            
        Returns:
            ShadowTrade if a trade was executed, None otherwise
        """
        try:
            self.last_price = price
            timestamp = datetime.now(timezone.utc)
            
            if decision == "BUY" and self.open_position is None:
                return self._execute_buy(timestamp, price, market_data)
            elif decision == "SELL" and self.open_position is not None:
                return self._execute_sell(timestamp, price, market_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error executing decision in shadow portfolio {self.strategy_id}: {e}")
            return None
    
    def _execute_buy(self, timestamp: datetime, price: float, market_data: Dict[str, Any]) -> ShadowTrade:
        """Execute a buy order in shadow portfolio."""
        # Calculate position size based on parameters
        risk_percent = self.parameters.get('risk_percent', 0.02)
        position_value = self.cash_balance * risk_percent
        quantity = position_value / price
        
        # Apply fees
        fee_percent = 0.0025  # 0.25% fee
        fee = position_value * fee_percent
        
        # Update portfolio state
        self.position_size = quantity
        self.cash_balance -= (position_value + fee)
        
        # Create open position
        self.open_position = {
            'entry_price': price,
            'entry_time': timestamp,
            'quantity': quantity,
            'entry_value': position_value
        }
        
        # Create trade record
        trade = ShadowTrade(
            timestamp=timestamp,
            strategy_id=self.strategy_id,
            decision="BUY",
            price=price,
            quantity=quantity,
            portfolio_value=self.get_portfolio_value(),
            parameters=self.parameters.copy(),
            pnl=0.0,  # No PnL on entry
            cumulative_pnl=self.performance.total_pnl
        )
        
        self.trades.append(trade)
        logger.debug(f"Shadow portfolio {self.strategy_id}: BUY {quantity:.6f} BTC @ ${price:.2f}")
        
        return trade
    
    def _execute_sell(self, timestamp: datetime, price: float, market_data: Dict[str, Any]) -> ShadowTrade:
        """Execute a sell order in shadow portfolio."""
        if not self.open_position:
            return None
        
        # Calculate PnL
        entry_price = self.open_position['entry_price']
        quantity = self.open_position['quantity']
        
        # Calculate gross proceeds
        gross_proceeds = quantity * price
        
        # Apply fees
        fee_percent = 0.0025  # 0.25% fee
        fee = gross_proceeds * fee_percent
        net_proceeds = gross_proceeds - fee
        
        # Calculate PnL
        entry_value = self.open_position['entry_value']
        pnl = net_proceeds - entry_value
        
        # Update portfolio state
        self.cash_balance += net_proceeds
        self.position_size = 0.0
        
        # Create trade record
        trade = ShadowTrade(
            timestamp=timestamp,
            strategy_id=self.strategy_id,
            decision="SELL",
            price=price,
            quantity=quantity,
            portfolio_value=self.get_portfolio_value(),
            parameters=self.parameters.copy(),
            pnl=pnl,
            cumulative_pnl=self.performance.total_pnl + pnl
        )
        
        self.trades.append(trade)
        
        # Update performance
        self.performance.total_pnl += pnl
        
        # Clear open position
        self.open_position = None
        
        logger.debug(f"Shadow portfolio {self.strategy_id}: SELL {quantity:.6f} BTC @ ${price:.2f}, PnL: ${pnl:.2f}")
        
        return trade
    
    def get_portfolio_value(self) -> float:
        """Calculate current portfolio value."""
        cash_value = self.cash_balance
        position_value = self.position_size * self.last_price if self.position_size > 0 else 0
        return cash_value + position_value
    
    def update_performance_metrics(self) -> None:
        """Update performance metrics based on trade history."""
        self.performance.calculate_metrics(self.trades)
    
    def get_unrealized_pnl(self) -> float:
        """Calculate unrealized PnL for open position."""
        if not self.open_position:
            return 0.0
        
        entry_price = self.open_position['entry_price']
        quantity = self.open_position['quantity']
        current_value = quantity * self.last_price
        entry_value = self.open_position['entry_value']
        
        return current_value - entry_value


class LiveBacktestingIntegration:
    """
    Live Backtesting Integration System

    This system runs continuous backtesting parallel to live trading,
    providing real-time strategy validation and parameter optimization
    recommendations. It maintains multiple shadow portfolios with different
    parameter sets and compares their performance against live trading.
    """

    def __init__(self,
                 initial_capital: float = 10000.0,
                 num_shadow_strategies: int = 5,
                 parameter_variation_range: float = 0.2,
                 update_interval_seconds: int = 60,
                 max_trade_history: int = 1000):
        """
        Initialize the live backtesting integration system.

        Args:
            initial_capital: Starting capital for shadow portfolios
            num_shadow_strategies: Number of shadow strategies to run
            parameter_variation_range: Range for parameter variations (0.2 = ±20%)
            update_interval_seconds: How often to update and analyze performance
            max_trade_history: Maximum number of trades to keep in history
        """
        self.initial_capital = initial_capital
        self.num_shadow_strategies = num_shadow_strategies
        self.parameter_variation_range = parameter_variation_range
        self.update_interval_seconds = update_interval_seconds
        self.max_trade_history = max_trade_history

        # Live trading tracking
        self.live_trades: deque = deque(maxlen=max_trade_history)
        self.live_performance = StrategyPerformance(
            strategy_id="LIVE",
            total_trades=0, winning_trades=0, losing_trades=0,
            total_pnl=0.0, win_rate=0.0, profit_factor=0.0,
            sharpe_ratio=0.0, max_drawdown=0.0, avg_trade_duration=0.0,
            current_drawdown=0.0, last_update=datetime.now(timezone.utc)
        )

        # Shadow portfolios
        self.shadow_portfolios: Dict[str, ShadowPortfolio] = {}
        self.base_parameters: Dict[str, Any] = {}

        # Performance tracking
        self.performance_history: deque = deque(maxlen=100)
        self.recommendations_history: deque = deque(maxlen=50)

        # Threading and control
        self.is_running = False
        self.shutdown_event = threading.Event()
        self.analysis_thread: Optional[threading.Thread] = None
        self.data_lock = threading.RLock()

        # Statistics
        self.stats = {
            'total_decisions_processed': 0,
            'recommendations_generated': 0,
            'best_shadow_performance': 0.0,
            'live_vs_best_gap': 0.0,
            'last_analysis_time': None
        }

        logger.info("Live Backtesting Integration system initialized")

    def start_backtesting(self, base_parameters: Dict[str, Any]) -> bool:
        """
        Start the live backtesting system.

        Args:
            base_parameters: Base trading parameters to use as reference

        Returns:
            bool: True if started successfully
        """
        try:
            if self.is_running:
                logger.warning("Live backtesting already running")
                return False

            self.base_parameters = base_parameters.copy()

            # Create shadow portfolios with parameter variations
            self._create_shadow_portfolios()

            # Start analysis thread
            self.is_running = True
            self.shutdown_event.clear()

            self.analysis_thread = threading.Thread(
                target=self._analysis_loop,
                daemon=True,
                name="LiveBacktestingAnalysis"
            )
            self.analysis_thread.start()

            logger.info("Live backtesting system started")
            return True

        except Exception as e:
            logger.error(f"Error starting live backtesting: {e}")
            return False

    def stop_backtesting(self) -> bool:
        """Stop the live backtesting system."""
        try:
            self.is_running = False
            self.shutdown_event.set()

            # Wait for analysis thread to finish
            if self.analysis_thread and self.analysis_thread.is_alive():
                self.analysis_thread.join(timeout=5)

            logger.info("Live backtesting system stopped")
            return True

        except Exception as e:
            logger.error(f"Error stopping live backtesting: {e}")
            return False

    def process_live_decision(self,
                            decision: str,
                            ai_reasoning: str,
                            market_data: Dict[str, Any],
                            parameters: Dict[str, Any],
                            execution_price: Optional[float] = None,
                            execution_quantity: Optional[float] = None) -> None:
        """
        Process a live trading decision and run it through shadow portfolios.

        Args:
            decision: Trading decision (BUY, SELL, HOLD)
            ai_reasoning: AI reasoning for the decision
            market_data: Current market data and indicators
            parameters: Trading parameters used
            execution_price: Actual execution price (if executed)
            execution_quantity: Actual execution quantity (if executed)
        """
        try:
            with self.data_lock:
                # Create live trade record
                live_trade = LiveTrade(
                    timestamp=datetime.now(timezone.utc),
                    decision=decision,
                    ai_reasoning=ai_reasoning,
                    market_data=market_data.copy(),
                    parameters=parameters.copy(),
                    execution_price=execution_price,
                    execution_quantity=execution_quantity,
                    execution_status="EXECUTED" if execution_price else "NO_EXECUTION"
                )

                self.live_trades.append(live_trade)
                self.stats['total_decisions_processed'] += 1

                # Process decision through all shadow portfolios
                current_price = market_data.get('avg_price', market_data.get('close', 45000))

                for portfolio in self.shadow_portfolios.values():
                    # Apply decision with portfolio's specific parameters
                    shadow_decision = self._adapt_decision_for_portfolio(
                        decision, market_data, portfolio.parameters
                    )

                    # Execute in shadow portfolio
                    shadow_trade = portfolio.execute_decision(
                        shadow_decision, current_price, market_data
                    )

                    if shadow_trade:
                        # Update portfolio performance
                        portfolio.update_performance_metrics()

                logger.debug(f"Processed live decision: {decision} @ ${current_price:.2f}")

        except Exception as e:
            logger.error(f"Error processing live decision: {e}")

    def get_performance_comparison(self) -> Dict[str, Any]:
        """Get performance comparison between live and shadow portfolios."""
        try:
            with self.data_lock:
                # Update live performance
                self._update_live_performance()

                # Get shadow performances
                shadow_performances = {}
                for portfolio_id, portfolio in self.shadow_portfolios.items():
                    portfolio.update_performance_metrics()
                    shadow_performances[portfolio_id] = asdict(portfolio.performance)

                # Find best performing shadow
                best_shadow = None
                best_performance = float('-inf')

                for portfolio in self.shadow_portfolios.values():
                    if portfolio.performance.total_pnl > best_performance:
                        best_performance = portfolio.performance.total_pnl
                        best_shadow = portfolio.performance

                # Calculate performance gap
                performance_gap = 0.0
                if best_shadow:
                    performance_gap = best_shadow.total_pnl - self.live_performance.total_pnl

                return {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'live_performance': asdict(self.live_performance),
                    'shadow_performances': shadow_performances,
                    'best_shadow_performance': asdict(best_shadow) if best_shadow else None,
                    'performance_gap': performance_gap,
                    'stats': self.stats.copy()
                }

        except Exception as e:
            logger.error(f"Error getting performance comparison: {e}")
            return {}

    def get_parameter_recommendations(self) -> List[ParameterRecommendation]:
        """Generate parameter adjustment recommendations."""
        try:
            recommendations = []

            with self.data_lock:
                # Find best performing shadow portfolio
                best_portfolio = None
                best_performance = float('-inf')

                for portfolio in self.shadow_portfolios.values():
                    if portfolio.performance.total_pnl > best_performance:
                        best_performance = portfolio.performance.total_pnl
                        best_portfolio = portfolio

                if not best_portfolio:
                    return recommendations

                # Compare parameters and generate recommendations
                for param_name, base_value in self.base_parameters.items():
                    if param_name in best_portfolio.parameters:
                        best_value = best_portfolio.parameters[param_name]

                        if abs(best_value - base_value) > 0.001:  # Significant difference
                            # Calculate confidence based on performance difference
                            performance_improvement = best_performance - self.live_performance.total_pnl
                            confidence = min(0.95, max(0.1, performance_improvement / 100))

                            # Determine risk level
                            change_magnitude = abs(best_value - base_value) / base_value
                            if change_magnitude < 0.1:
                                risk_level = "LOW"
                            elif change_magnitude < 0.3:
                                risk_level = "MEDIUM"
                            else:
                                risk_level = "HIGH"

                            recommendation = ParameterRecommendation(
                                parameter_name=param_name,
                                current_value=base_value,
                                recommended_value=best_value,
                                confidence=confidence,
                                reasoning=f"Best shadow portfolio shows {performance_improvement:.2f} improvement",
                                expected_improvement=performance_improvement,
                                risk_level=risk_level
                            )

                            recommendations.append(recommendation)

                self.stats['recommendations_generated'] += len(recommendations)

            return recommendations

        except Exception as e:
            logger.error(f"Error generating parameter recommendations: {e}")
            return []

    def generate_backtesting_report(self) -> BacktestingReport:
        """Generate comprehensive backtesting report."""
        try:
            with self.data_lock:
                # Update performances
                self._update_live_performance()

                # Find best shadow portfolio
                best_shadow = None
                best_performance = float('-inf')

                for portfolio in self.shadow_portfolios.values():
                    portfolio.update_performance_metrics()
                    if portfolio.performance.total_pnl > best_performance:
                        best_performance = portfolio.performance.total_pnl
                        best_shadow = portfolio.performance

                # Get recommendations
                recommendations = self.get_parameter_recommendations()

                # Calculate performance gap
                performance_gap = 0.0
                if best_shadow:
                    performance_gap = best_shadow.total_pnl - self.live_performance.total_pnl

                # Risk assessment
                risk_assessment = self._calculate_risk_assessment()

                # Market conditions
                market_conditions = self._analyze_market_conditions()

                report = BacktestingReport(
                    timestamp=datetime.now(timezone.utc),
                    live_performance=self.live_performance,
                    best_shadow_performance=best_shadow or self.live_performance,
                    performance_gap=performance_gap,
                    recommendations=recommendations,
                    risk_assessment=risk_assessment,
                    market_conditions=market_conditions
                )

                # Store in history
                self.performance_history.append(report)

                return report

        except Exception as e:
            logger.error(f"Error generating backtesting report: {e}")
            return None

    def _create_shadow_portfolios(self) -> None:
        """Create shadow portfolios with parameter variations."""
        try:
            self.shadow_portfolios.clear()

            # Create base portfolio (exact copy of live parameters)
            base_portfolio = ShadowPortfolio(
                strategy_id="SHADOW_BASE",
                initial_capital=self.initial_capital,
                parameters=self.base_parameters.copy()
            )
            self.shadow_portfolios["SHADOW_BASE"] = base_portfolio

            # Create variation portfolios
            for i in range(self.num_shadow_strategies - 1):
                strategy_id = f"SHADOW_VAR_{i+1}"
                varied_parameters = self._create_parameter_variation(i)

                portfolio = ShadowPortfolio(
                    strategy_id=strategy_id,
                    initial_capital=self.initial_capital,
                    parameters=varied_parameters
                )

                self.shadow_portfolios[strategy_id] = portfolio

            logger.info(f"Created {len(self.shadow_portfolios)} shadow portfolios")

        except Exception as e:
            logger.error(f"Error creating shadow portfolios: {e}")

    def _create_parameter_variation(self, variation_index: int) -> Dict[str, Any]:
        """Create a parameter variation for shadow portfolio."""
        try:
            varied_params = self.base_parameters.copy()

            # Define parameter variation strategies
            variation_strategies = [
                {'risk_percent': 1.2, 'stop_percent': 0.8, 'trail_profit_buffer_pct': 1.1},
                {'risk_percent': 0.8, 'stop_percent': 1.2, 'trail_profit_buffer_pct': 0.9},
                {'risk_percent': 1.1, 'stop_percent': 1.1, 'trail_profit_buffer_pct': 1.2},
                {'risk_percent': 0.9, 'stop_percent': 0.9, 'trail_profit_buffer_pct': 0.8},
            ]

            # Apply variation strategy
            if variation_index < len(variation_strategies):
                strategy = variation_strategies[variation_index]

                for param_name, multiplier in strategy.items():
                    if param_name in varied_params:
                        original_value = varied_params[param_name]
                        varied_params[param_name] = original_value * multiplier
            else:
                # Random variation for additional portfolios
                np.random.seed(variation_index + 42)  # Consistent variations

                for param_name, value in varied_params.items():
                    if isinstance(value, (int, float)):
                        # Apply random variation within range
                        variation = np.random.uniform(
                            1 - self.parameter_variation_range,
                            1 + self.parameter_variation_range
                        )
                        varied_params[param_name] = value * variation

            return varied_params

        except Exception as e:
            logger.error(f"Error creating parameter variation: {e}")
            return self.base_parameters.copy()

    def _adapt_decision_for_portfolio(self,
                                    decision: str,
                                    market_data: Dict[str, Any],
                                    portfolio_params: Dict[str, Any]) -> str:
        """
        Adapt trading decision based on portfolio-specific parameters.

        This method can implement portfolio-specific decision logic
        based on different parameter sets.
        """
        try:
            # For now, use the same decision
            # In future, this could implement parameter-specific decision logic
            # For example, different RSI thresholds, different risk tolerances, etc.

            # Example: Adjust decision based on risk parameters
            risk_percent = portfolio_params.get('risk_percent', 0.02)

            # More conservative portfolios might skip some BUY signals
            if decision == "BUY" and risk_percent < 0.015:
                rsi = market_data.get('rsi', 50)
                if rsi > 70:  # Overbought - skip buy for conservative portfolio
                    return "HOLD"

            # More aggressive portfolios might take more BUY signals
            elif decision == "HOLD" and risk_percent > 0.025:
                rsi = market_data.get('rsi', 50)
                macd_hist = market_data.get('macd_hist', 0)
                if rsi < 40 and macd_hist > 0:  # Potential buy signal
                    return "BUY"

            return decision

        except Exception as e:
            logger.error(f"Error adapting decision for portfolio: {e}")
            return decision

    def _update_live_performance(self) -> None:
        """Update live performance metrics from trade history."""
        try:
            if not self.live_trades:
                return

            # Convert live trades to performance metrics
            executed_trades = [t for t in self.live_trades if t.execution_price is not None]

            if not executed_trades:
                return

            # Calculate basic metrics (simplified)
            self.live_performance.total_trades = len(executed_trades)

            # For more accurate metrics, we would need actual PnL data
            # This is a simplified version for demonstration
            self.live_performance.last_update = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"Error updating live performance: {e}")

    def _calculate_risk_assessment(self) -> Dict[str, Any]:
        """Calculate risk assessment for current strategies."""
        try:
            risk_metrics = {
                'overall_risk_level': 'MEDIUM',
                'portfolio_correlation': 0.0,
                'volatility_risk': 0.0,
                'drawdown_risk': 0.0,
                'parameter_sensitivity': 0.0
            }

            # Calculate portfolio correlation
            if len(self.shadow_portfolios) > 1:
                performances = [p.performance.total_pnl for p in self.shadow_portfolios.values()]
                if len(performances) > 1:
                    risk_metrics['portfolio_correlation'] = np.corrcoef(performances)[0, 1] if len(set(performances)) > 1 else 1.0

            # Calculate volatility risk
            if self.shadow_portfolios:
                pnl_values = [p.performance.total_pnl for p in self.shadow_portfolios.values()]
                risk_metrics['volatility_risk'] = np.std(pnl_values) if pnl_values else 0.0

            # Calculate drawdown risk
            max_drawdown = max(
                (p.performance.max_drawdown for p in self.shadow_portfolios.values()),
                default=0.0
            )
            risk_metrics['drawdown_risk'] = max_drawdown

            # Determine overall risk level
            if max_drawdown > 0.2 or risk_metrics['volatility_risk'] > 100:
                risk_metrics['overall_risk_level'] = 'HIGH'
            elif max_drawdown > 0.1 or risk_metrics['volatility_risk'] > 50:
                risk_metrics['overall_risk_level'] = 'MEDIUM'
            else:
                risk_metrics['overall_risk_level'] = 'LOW'

            return risk_metrics

        except Exception as e:
            logger.error(f"Error calculating risk assessment: {e}")
            return {'overall_risk_level': 'UNKNOWN'}

    def _analyze_market_conditions(self) -> Dict[str, Any]:
        """Analyze current market conditions."""
        try:
            if not self.live_trades:
                return {'status': 'NO_DATA'}

            # Get latest market data
            latest_trade = self.live_trades[-1]
            market_data = latest_trade.market_data

            conditions = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'price': market_data.get('avg_price', 0),
                'rsi': market_data.get('rsi', 50),
                'macd_hist': market_data.get('macd_hist', 0),
                'volume_spike': market_data.get('volume_spike', 1.0),
                'trend_5m': market_data.get('trend_5m', 'NEUTRAL'),
                'market_regime': 'UNKNOWN'
            }

            # Determine market regime
            rsi = conditions['rsi']
            macd_hist = conditions['macd_hist']

            if rsi > 70 and macd_hist < 0:
                conditions['market_regime'] = 'OVERBOUGHT_BEARISH'
            elif rsi < 30 and macd_hist > 0:
                conditions['market_regime'] = 'OVERSOLD_BULLISH'
            elif rsi > 60:
                conditions['market_regime'] = 'BULLISH'
            elif rsi < 40:
                conditions['market_regime'] = 'BEARISH'
            else:
                conditions['market_regime'] = 'NEUTRAL'

            return conditions

        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            return {'status': 'ERROR'}

    def _analysis_loop(self) -> None:
        """Main analysis loop running in background thread."""
        logger.info("Live backtesting analysis loop started")

        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Generate performance report
                report = self.generate_backtesting_report()

                if report:
                    # Update statistics
                    self.stats['last_analysis_time'] = datetime.now(timezone.utc).isoformat()

                    if report.best_shadow_performance:
                        self.stats['best_shadow_performance'] = report.best_shadow_performance.total_pnl
                        self.stats['live_vs_best_gap'] = report.performance_gap

                    # Log performance summary
                    logger.info(f"Live Backtesting Analysis - Gap: ${report.performance_gap:.2f}, "
                              f"Recommendations: {len(report.recommendations)}")

                # Wait for next analysis cycle
                self.shutdown_event.wait(self.update_interval_seconds)

            except Exception as e:
                logger.error(f"Error in analysis loop: {e}")
                self.shutdown_event.wait(10)  # Wait 10 seconds on error

        logger.info("Live backtesting analysis loop stopped")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and statistics."""
        try:
            with self.data_lock:
                status = {
                    'is_running': self.is_running,
                    'num_shadow_portfolios': len(self.shadow_portfolios),
                    'live_trades_count': len(self.live_trades),
                    'performance_history_count': len(self.performance_history),
                    'stats': self.stats.copy(),
                    'shadow_portfolio_status': {}
                }

                # Add shadow portfolio status
                for portfolio_id, portfolio in self.shadow_portfolios.items():
                    status['shadow_portfolio_status'][portfolio_id] = {
                        'portfolio_value': portfolio.get_portfolio_value(),
                        'total_trades': portfolio.performance.total_trades,
                        'total_pnl': portfolio.performance.total_pnl,
                        'win_rate': portfolio.performance.win_rate,
                        'has_open_position': portfolio.open_position is not None
                    }

                return status

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}


# Global instance and interface functions
_live_backtesting_integration: Optional[LiveBacktestingIntegration] = None


def initialize_live_backtesting_integration(initial_capital: float = 10000.0,
                                          num_shadow_strategies: int = 5,
                                          parameter_variation_range: float = 0.2) -> bool:
    """
    Initialize the live backtesting integration system.

    Args:
        initial_capital: Starting capital for shadow portfolios
        num_shadow_strategies: Number of shadow strategies to run
        parameter_variation_range: Range for parameter variations

    Returns:
        bool: True if initialized successfully
    """
    global _live_backtesting_integration

    try:
        if _live_backtesting_integration is not None:
            logger.warning("Live backtesting integration already initialized")
            return True

        _live_backtesting_integration = LiveBacktestingIntegration(
            initial_capital=initial_capital,
            num_shadow_strategies=num_shadow_strategies,
            parameter_variation_range=parameter_variation_range
        )

        logger.info("Live backtesting integration initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing live backtesting integration: {e}")
        return False


def start_live_backtesting(base_parameters: Dict[str, Any]) -> bool:
    """
    Start the live backtesting system.

    Args:
        base_parameters: Base trading parameters to use as reference

    Returns:
        bool: True if started successfully
    """
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        logger.error("Live backtesting integration not initialized")
        return False

    return _live_backtesting_integration.start_backtesting(base_parameters)


def stop_live_backtesting() -> bool:
    """Stop the live backtesting system."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return True

    return _live_backtesting_integration.stop_backtesting()


def process_live_trading_decision(decision: str,
                                ai_reasoning: str,
                                market_data: Dict[str, Any],
                                parameters: Dict[str, Any],
                                execution_price: Optional[float] = None,
                                execution_quantity: Optional[float] = None) -> None:
    """
    Process a live trading decision through the backtesting system.

    Args:
        decision: Trading decision (BUY, SELL, HOLD)
        ai_reasoning: AI reasoning for the decision
        market_data: Current market data and indicators
        parameters: Trading parameters used
        execution_price: Actual execution price (if executed)
        execution_quantity: Actual execution quantity (if executed)
    """
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return

    _live_backtesting_integration.process_live_decision(
        decision=decision,
        ai_reasoning=ai_reasoning,
        market_data=market_data,
        parameters=parameters,
        execution_price=execution_price,
        execution_quantity=execution_quantity
    )


def get_live_backtesting_performance() -> Dict[str, Any]:
    """Get performance comparison between live and shadow portfolios."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return {}

    return _live_backtesting_integration.get_performance_comparison()


def get_live_backtesting_recommendations() -> List[Dict[str, Any]]:
    """Get parameter adjustment recommendations."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return []

    recommendations = _live_backtesting_integration.get_parameter_recommendations()
    return [asdict(rec) for rec in recommendations]


def get_live_backtesting_report() -> Optional[Dict[str, Any]]:
    """Generate comprehensive backtesting report."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return None

    report = _live_backtesting_integration.generate_backtesting_report()
    return report.to_dict() if report else None


def get_live_backtesting_status() -> Dict[str, Any]:
    """Get current system status and statistics."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return {'status': 'NOT_INITIALIZED'}

    return _live_backtesting_integration.get_system_status()


def stop_live_backtesting_integration() -> bool:
    """Stop and cleanup the live backtesting integration system."""
    global _live_backtesting_integration

    try:
        if _live_backtesting_integration is not None:
            _live_backtesting_integration.stop_backtesting()
            _live_backtesting_integration = None

        logger.info("Live backtesting integration stopped and cleaned up")
        return True

    except Exception as e:
        logger.error(f"Error stopping live backtesting integration: {e}")
        return False

    def generate_backtesting_report(self) -> BacktestingReport:
        """Generate comprehensive backtesting report."""
        try:
            with self.data_lock:
                # Update performances
                self._update_live_performance()

                # Find best shadow portfolio
                best_shadow = None
                best_performance = float('-inf')

                for portfolio in self.shadow_portfolios.values():
                    portfolio.update_performance_metrics()
                    if portfolio.performance.total_pnl > best_performance:
                        best_performance = portfolio.performance.total_pnl
                        best_shadow = portfolio.performance

                # Get recommendations
                recommendations = self.get_parameter_recommendations()

                # Calculate performance gap
                performance_gap = 0.0
                if best_shadow:
                    performance_gap = best_shadow.total_pnl - self.live_performance.total_pnl

                # Risk assessment
                risk_assessment = self._calculate_risk_assessment()

                # Market conditions
                market_conditions = self._analyze_market_conditions()

                report = BacktestingReport(
                    timestamp=datetime.now(timezone.utc),
                    live_performance=self.live_performance,
                    best_shadow_performance=best_shadow or self.live_performance,
                    performance_gap=performance_gap,
                    recommendations=recommendations,
                    risk_assessment=risk_assessment,
                    market_conditions=market_conditions
                )

                # Store in history
                self.performance_history.append(report)

                return report

        except Exception as e:
            logger.error(f"Error generating backtesting report: {e}")
            return None

    def _create_shadow_portfolios(self) -> None:
        """Create shadow portfolios with parameter variations."""
        try:
            self.shadow_portfolios.clear()

            # Create base portfolio (exact copy of live parameters)
            base_portfolio = ShadowPortfolio(
                strategy_id="SHADOW_BASE",
                initial_capital=self.initial_capital,
                parameters=self.base_parameters.copy()
            )
            self.shadow_portfolios["SHADOW_BASE"] = base_portfolio

            # Create variation portfolios
            for i in range(self.num_shadow_strategies - 1):
                strategy_id = f"SHADOW_VAR_{i+1}"
                varied_parameters = self._create_parameter_variation(i)

                portfolio = ShadowPortfolio(
                    strategy_id=strategy_id,
                    initial_capital=self.initial_capital,
                    parameters=varied_parameters
                )

                self.shadow_portfolios[strategy_id] = portfolio

            logger.info(f"Created {len(self.shadow_portfolios)} shadow portfolios")

        except Exception as e:
            logger.error(f"Error creating shadow portfolios: {e}")

    def _create_parameter_variation(self, variation_index: int) -> Dict[str, Any]:
        """Create a parameter variation for shadow portfolio."""
        try:
            varied_params = self.base_parameters.copy()

            # Define parameter variation strategies
            variation_strategies = [
                {'risk_percent': 1.2, 'stop_percent': 0.8, 'trail_profit_buffer_pct': 1.1},
                {'risk_percent': 0.8, 'stop_percent': 1.2, 'trail_profit_buffer_pct': 0.9},
                {'risk_percent': 1.1, 'stop_percent': 1.1, 'trail_profit_buffer_pct': 1.2},
                {'risk_percent': 0.9, 'stop_percent': 0.9, 'trail_profit_buffer_pct': 0.8},
            ]

            # Apply variation strategy
            if variation_index < len(variation_strategies):
                strategy = variation_strategies[variation_index]

                for param_name, multiplier in strategy.items():
                    if param_name in varied_params:
                        original_value = varied_params[param_name]
                        varied_params[param_name] = original_value * multiplier
            else:
                # Random variation for additional portfolios
                np.random.seed(variation_index + 42)  # Consistent variations

                for param_name, value in varied_params.items():
                    if isinstance(value, (int, float)):
                        # Apply random variation within range
                        variation = np.random.uniform(
                            1 - self.parameter_variation_range,
                            1 + self.parameter_variation_range
                        )
                        varied_params[param_name] = value * variation

            return varied_params

        except Exception as e:
            logger.error(f"Error creating parameter variation: {e}")
            return self.base_parameters.copy()

    def _adapt_decision_for_portfolio(self,
                                    decision: str,
                                    market_data: Dict[str, Any],
                                    portfolio_params: Dict[str, Any]) -> str:
        """
        Adapt trading decision based on portfolio-specific parameters.

        This method can implement portfolio-specific decision logic
        based on different parameter sets.
        """
        try:
            # For now, use the same decision
            # In future, this could implement parameter-specific decision logic
            # For example, different RSI thresholds, different risk tolerances, etc.

            # Example: Adjust decision based on risk parameters
            risk_percent = portfolio_params.get('risk_percent', 0.02)

            # More conservative portfolios might skip some BUY signals
            if decision == "BUY" and risk_percent < 0.015:
                rsi = market_data.get('rsi', 50)
                if rsi > 70:  # Overbought - skip buy for conservative portfolio
                    return "HOLD"

            # More aggressive portfolios might take more BUY signals
            elif decision == "HOLD" and risk_percent > 0.025:
                rsi = market_data.get('rsi', 50)
                macd_hist = market_data.get('macd_hist', 0)
                if rsi < 40 and macd_hist > 0:  # Potential buy signal
                    return "BUY"

            return decision

        except Exception as e:
            logger.error(f"Error adapting decision for portfolio: {e}")
            return decision

    def _update_live_performance(self) -> None:
        """Update live performance metrics from trade history."""
        try:
            if not self.live_trades:
                return

            # Convert live trades to performance metrics
            executed_trades = [t for t in self.live_trades if t.execution_price is not None]

            if not executed_trades:
                return

            # Calculate basic metrics (simplified)
            self.live_performance.total_trades = len(executed_trades)

            # For more accurate metrics, we would need actual PnL data
            # This is a simplified version for demonstration
            self.live_performance.last_update = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"Error updating live performance: {e}")

    def _calculate_risk_assessment(self) -> Dict[str, Any]:
        """Calculate risk assessment for current strategies."""
        try:
            risk_metrics = {
                'overall_risk_level': 'MEDIUM',
                'portfolio_correlation': 0.0,
                'volatility_risk': 0.0,
                'drawdown_risk': 0.0,
                'parameter_sensitivity': 0.0
            }

            # Calculate portfolio correlation
            if len(self.shadow_portfolios) > 1:
                performances = [p.performance.total_pnl for p in self.shadow_portfolios.values()]
                if len(performances) > 1:
                    risk_metrics['portfolio_correlation'] = np.corrcoef(performances)[0, 1] if len(set(performances)) > 1 else 1.0

            # Calculate volatility risk
            if self.shadow_portfolios:
                pnl_values = [p.performance.total_pnl for p in self.shadow_portfolios.values()]
                risk_metrics['volatility_risk'] = np.std(pnl_values) if pnl_values else 0.0

            # Calculate drawdown risk
            max_drawdown = max(
                (p.performance.max_drawdown for p in self.shadow_portfolios.values()),
                default=0.0
            )
            risk_metrics['drawdown_risk'] = max_drawdown

            # Determine overall risk level
            if max_drawdown > 0.2 or risk_metrics['volatility_risk'] > 100:
                risk_metrics['overall_risk_level'] = 'HIGH'
            elif max_drawdown > 0.1 or risk_metrics['volatility_risk'] > 50:
                risk_metrics['overall_risk_level'] = 'MEDIUM'
            else:
                risk_metrics['overall_risk_level'] = 'LOW'

            return risk_metrics

        except Exception as e:
            logger.error(f"Error calculating risk assessment: {e}")
            return {'overall_risk_level': 'UNKNOWN'}

    def _analyze_market_conditions(self) -> Dict[str, Any]:
        """Analyze current market conditions."""
        try:
            if not self.live_trades:
                return {'status': 'NO_DATA'}

            # Get latest market data
            latest_trade = self.live_trades[-1]
            market_data = latest_trade.market_data

            conditions = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'price': market_data.get('avg_price', 0),
                'rsi': market_data.get('rsi', 50),
                'macd_hist': market_data.get('macd_hist', 0),
                'volume_spike': market_data.get('volume_spike', 1.0),
                'trend_5m': market_data.get('trend_5m', 'NEUTRAL'),
                'market_regime': 'UNKNOWN'
            }

            # Determine market regime
            rsi = conditions['rsi']
            macd_hist = conditions['macd_hist']

            if rsi > 70 and macd_hist < 0:
                conditions['market_regime'] = 'OVERBOUGHT_BEARISH'
            elif rsi < 30 and macd_hist > 0:
                conditions['market_regime'] = 'OVERSOLD_BULLISH'
            elif rsi > 60:
                conditions['market_regime'] = 'BULLISH'
            elif rsi < 40:
                conditions['market_regime'] = 'BEARISH'
            else:
                conditions['market_regime'] = 'NEUTRAL'

            return conditions

        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            return {'status': 'ERROR'}

    def _analysis_loop(self) -> None:
        """Main analysis loop running in background thread."""
        logger.info("Live backtesting analysis loop started")

        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Generate performance report
                report = self.generate_backtesting_report()

                if report:
                    # Update statistics
                    self.stats['last_analysis_time'] = datetime.now(timezone.utc).isoformat()

                    if report.best_shadow_performance:
                        self.stats['best_shadow_performance'] = report.best_shadow_performance.total_pnl
                        self.stats['live_vs_best_gap'] = report.performance_gap

                    # Log performance summary
                    logger.info(f"Live Backtesting Analysis - Gap: ${report.performance_gap:.2f}, "
                              f"Recommendations: {len(report.recommendations)}")

                # Wait for next analysis cycle
                self.shutdown_event.wait(self.update_interval_seconds)

            except Exception as e:
                logger.error(f"Error in analysis loop: {e}")
                self.shutdown_event.wait(10)  # Wait 10 seconds on error

        logger.info("Live backtesting analysis loop stopped")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and statistics."""
        try:
            with self.data_lock:
                status = {
                    'is_running': self.is_running,
                    'num_shadow_portfolios': len(self.shadow_portfolios),
                    'live_trades_count': len(self.live_trades),
                    'performance_history_count': len(self.performance_history),
                    'stats': self.stats.copy(),
                    'shadow_portfolio_status': {}
                }

                # Add shadow portfolio status
                for portfolio_id, portfolio in self.shadow_portfolios.items():
                    status['shadow_portfolio_status'][portfolio_id] = {
                        'portfolio_value': portfolio.get_portfolio_value(),
                        'total_trades': portfolio.performance.total_trades,
                        'total_pnl': portfolio.performance.total_pnl,
                        'win_rate': portfolio.performance.win_rate,
                        'has_open_position': portfolio.open_position is not None
                    }

                return status

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}


# Global instance and interface functions
_live_backtesting_integration: Optional[LiveBacktestingIntegration] = None


def initialize_live_backtesting_integration(initial_capital: float = 10000.0,
                                          num_shadow_strategies: int = 5,
                                          parameter_variation_range: float = 0.2) -> bool:
    """
    Initialize the live backtesting integration system.

    Args:
        initial_capital: Starting capital for shadow portfolios
        num_shadow_strategies: Number of shadow strategies to run
        parameter_variation_range: Range for parameter variations

    Returns:
        bool: True if initialized successfully
    """
    global _live_backtesting_integration

    try:
        if _live_backtesting_integration is not None:
            logger.warning("Live backtesting integration already initialized")
            return True

        _live_backtesting_integration = LiveBacktestingIntegration(
            initial_capital=initial_capital,
            num_shadow_strategies=num_shadow_strategies,
            parameter_variation_range=parameter_variation_range
        )

        logger.info("Live backtesting integration initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing live backtesting integration: {e}")
        return False


def start_live_backtesting(base_parameters: Dict[str, Any]) -> bool:
    """
    Start the live backtesting system.

    Args:
        base_parameters: Base trading parameters to use as reference

    Returns:
        bool: True if started successfully
    """
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        logger.error("Live backtesting integration not initialized")
        return False

    return _live_backtesting_integration.start_backtesting(base_parameters)


def stop_live_backtesting() -> bool:
    """Stop the live backtesting system."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return True

    return _live_backtesting_integration.stop_backtesting()


def process_live_trading_decision(decision: str,
                                ai_reasoning: str,
                                market_data: Dict[str, Any],
                                parameters: Dict[str, Any],
                                execution_price: Optional[float] = None,
                                execution_quantity: Optional[float] = None) -> None:
    """
    Process a live trading decision through the backtesting system.

    Args:
        decision: Trading decision (BUY, SELL, HOLD)
        ai_reasoning: AI reasoning for the decision
        market_data: Current market data and indicators
        parameters: Trading parameters used
        execution_price: Actual execution price (if executed)
        execution_quantity: Actual execution quantity (if executed)
    """
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return

    _live_backtesting_integration.process_live_decision(
        decision=decision,
        ai_reasoning=ai_reasoning,
        market_data=market_data,
        parameters=parameters,
        execution_price=execution_price,
        execution_quantity=execution_quantity
    )


def get_live_backtesting_performance() -> Dict[str, Any]:
    """Get performance comparison between live and shadow portfolios."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return {}

    return _live_backtesting_integration.get_performance_comparison()


def get_live_backtesting_recommendations() -> List[Dict[str, Any]]:
    """Get parameter adjustment recommendations."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return []

    recommendations = _live_backtesting_integration.get_parameter_recommendations()
    return [asdict(rec) for rec in recommendations]


def get_live_backtesting_report() -> Optional[Dict[str, Any]]:
    """Generate comprehensive backtesting report."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return None

    report = _live_backtesting_integration.generate_backtesting_report()
    return report.to_dict() if report else None


def get_live_backtesting_status() -> Dict[str, Any]:
    """Get current system status and statistics."""
    global _live_backtesting_integration

    if _live_backtesting_integration is None:
        return {'status': 'NOT_INITIALIZED'}

    return _live_backtesting_integration.get_system_status()


def stop_live_backtesting_integration() -> bool:
    """Stop and cleanup the live backtesting integration system."""
    global _live_backtesting_integration

    try:
        if _live_backtesting_integration is not None:
            _live_backtesting_integration.stop_backtesting()
            _live_backtesting_integration = None

        logger.info("Live backtesting integration stopped and cleaned up")
        return True

    except Exception as e:
        logger.error(f"Error stopping live backtesting integration: {e}")
        return False


if __name__ == "__main__":
    # Example usage and testing
    print("Live Backtesting Integration System")
    print("===================================")

    # Initialize system
    if initialize_live_backtesting_integration():
        print("✓ System initialized")

        # Example base parameters
        base_params = {
            'risk_percent': 0.02,
            'stop_percent': 0.015,
            'trail_profit_buffer_pct': 0.01
        }

        # Start backtesting
        if start_live_backtesting(base_params):
            print("✓ Backtesting started")

            # Simulate some trading decisions
            market_data = {
                'avg_price': 45000.0,
                'rsi': 65.0,
                'macd_hist': 0.5,
                'volume_spike': 1.2
            }

            # Process decisions
            process_live_trading_decision("BUY", "RSI oversold", market_data, base_params, 45000.0, 0.001)
            time.sleep(2)
            process_live_trading_decision("SELL", "Take profit", market_data, base_params, 45500.0, 0.001)

            # Get performance
            performance = get_live_backtesting_performance()
            print(f"✓ Performance data: {len(performance)} metrics")

            # Get recommendations
            recommendations = get_live_backtesting_recommendations()
            print(f"✓ Recommendations: {len(recommendations)} suggestions")

            # Stop system
            if stop_live_backtesting():
                print("✓ System stopped")

        print("\nLive Backtesting Integration test completed successfully!")
    else:
        print("✗ Failed to initialize system")
